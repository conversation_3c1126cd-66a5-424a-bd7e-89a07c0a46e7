# Newzora Production Environment Configuration Template
# Copy this file to Backend/.env for production deployment
# Replace all placeholder values with actual production values

# ================================
# Server Configuration
# ================================
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://newzora.com

# ================================
# Database Configuration (PostgreSQL)
# ================================
DB_HOST=postgres
DB_PORT=5432
DB_NAME=newzora_production
DB_USER=newzora_user
# Strong password: at least 12 characters with uppercase, lowercase, numbers and special characters
DB_PASSWORD=CHANGE_ME_STRONG_PASSWORD_123!

# 数据库连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# ================================
# 安全配置
# ================================
# JWT密钥：使用 openssl rand -base64 32 生成
JWT_SECRET=CHANGE_ME_JWT_SECRET_32_CHARS_BASE64

# Session密钥：使用 openssl rand -base64 32 生成
SESSION_SECRET=CHANGE_ME_SESSION_SECRET_32_CHARS_BASE64

# 密码加密轮数 (生产环境建议12-15)
BCRYPT_ROUNDS=12

# ================================
# Redis 缓存配置
# ================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD

# ================================
# 邮件服务配置
# ================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
EMAIL_FROM=OneNews <<EMAIL>>

# ================================
# SSL/TLS 配置
# ================================
SSL_CERT_PATH=/etc/nginx/ssl/prod/certificate.crt
SSL_KEY_PATH=/etc/nginx/ssl/prod/private.key
SSL_CA_PATH=/etc/nginx/ssl/prod/ca-bundle.crt

# ================================
# 文件上传配置
# ================================
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# ================================
# 推送通知配置
# ================================
# 使用 npx web-push generate-vapid-keys 生成
VAPID_PUBLIC_KEY=CHANGE_ME_VAPID_PUBLIC_KEY
VAPID_PRIVATE_KEY=CHANGE_ME_VAPID_PRIVATE_KEY
VAPID_SUBJECT=mailto:<EMAIL>

# ================================
# 第三方服务配置
# ================================
# Sentry 错误追踪
SENTRY_DSN=https://<EMAIL>/project-id

# Google Analytics
GA_TRACKING_ID=G-XXXXXXXXXX

# Cloudflare API (如果使用)
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id

# ================================
# 监控和日志配置
# ================================
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Prometheus 监控
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# ================================
# 性能配置
# ================================
# 请求超时 (毫秒)
REQUEST_TIMEOUT=30000

# 连接超时 (毫秒)
CONNECTION_TIMEOUT=5000

# 最大请求体大小
MAX_REQUEST_SIZE=10mb

# ================================
# 速率限制配置
# ================================
# 全局速率限制 (每15分钟)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# 登录速率限制 (每15分钟)
LOGIN_RATE_LIMIT_MAX=10

# API速率限制 (每分钟)
API_RATE_LIMIT_MAX=60

# ================================
# 缓存配置
# ================================
# 缓存TTL (秒)
CACHE_TTL=3600

# 静态资源缓存 (秒)
STATIC_CACHE_TTL=86400

# API响应缓存 (秒)
API_CACHE_TTL=300

# ================================
# 社交功能配置
# ================================
# 最大关注数
MAX_FOLLOWS=1000

# 最大私信长度
MAX_MESSAGE_LENGTH=1000

# 文章最大长度
MAX_ARTICLE_LENGTH=50000

# ================================
# 内容审核配置
# ================================
# 自动审核阈值
AUTO_REVIEW_THRESHOLD=0.8

# 人工审核阈值
MANUAL_REVIEW_THRESHOLD=0.5

# 敏感词检测
SENSITIVE_WORDS_ENABLED=true

# ================================
# 备份配置
# ================================
# 数据库备份
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 2 * * *
DB_BACKUP_RETENTION_DAYS=30

# 文件备份
FILE_BACKUP_ENABLED=true
FILE_BACKUP_SCHEDULE=0 3 * * *

# ================================
# 开发和调试配置 (生产环境应禁用)
# ================================
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_CORS_ALL=false
DISABLE_RATE_LIMITING=false

# ================================
# 健康检查配置
# ================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# ================================
# 集群配置
# ================================
CLUSTER_MODE=true
CLUSTER_WORKERS=auto

# ================================
# 安全头配置
# ================================
CSP_ENABLED=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000

# ================================
# API版本配置
# ================================
API_VERSION=v1
API_PREFIX=/api

# ================================
# 时区配置
# ================================
TZ=Asia/Shanghai

# ================================
# 特性开关
# ================================
FEATURE_SOCIAL_ENABLED=true
FEATURE_NOTIFICATIONS_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_CONTENT_REVIEW_ENABLED=true
