# Newzora 稳定服务器环境配置
# 支持多种网络环境，包括翻墙网络

# 基础配置
NODE_ENV=development
PORT=5000
HTTPS_PORT=5443
HOST=0.0.0.0

# 网络配置
ENABLE_HTTPS=false
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMIT=true
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://localhost:3000,https://127.0.0.1:3000

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/newzora
DB_HOST=localhost
DB_PORT=5432
DB_NAME=newzora
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_TIMEOUT=30000

# JWT配置
JWT_SECRET=newzora-stable-jwt-secret-key-2024
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Session配置
SESSION_SECRET=newzora-stable-session-secret-key-2024
SESSION_MAX_AGE=86400000

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Supabase配置（可选）
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
USE_REAL_SUPABASE=false

# 文件上传配置
UPLOAD_MAX_SIZE=50mb
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf
UPLOAD_PATH=./uploads

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MAX_AUTH=5
RATE_LIMIT_MAX_REGISTER=3

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 缓存配置
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# 监控配置
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=30000

# 开发配置
ENABLE_CORS_ALL=true
ENABLE_DEBUG_LOGS=true
DISABLE_AUTH_FOR_TESTING=false

# 生产配置（当NODE_ENV=production时使用）
# DATABASE_URL=*************************************/newzora_prod
# JWT_SECRET=your-super-secure-jwt-secret
# SESSION_SECRET=your-super-secure-session-secret
# ENABLE_HTTPS=true
# SSL_CERT_PATH=./ssl/cert.pem
# SSL_KEY_PATH=./ssl/key.pem
