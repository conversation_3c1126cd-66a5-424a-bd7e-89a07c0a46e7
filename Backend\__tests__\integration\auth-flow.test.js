const request = require('supertest')
const express = require('express')
const { sequelize } = require('../../config/database')
const User = require('../../models/User')
const userRoutes = require('../../routes/users')

// Create test app with full middleware stack
const app = express()
app.use(express.json())
app.use('/api/users', userRoutes)

describe('Authentication Flow Integration Tests', () => {
  beforeAll(async () => {
    await sequelize.authenticate()
    await sequelize.sync({ force: true })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  beforeEach(async () => {
    await User.destroy({ where: {}, truncate: true })
  })

  describe('Complete User Registration and Login Flow', () => {
    const userData = {
      username: 'integrationuser',
      email: '<EMAIL>',
      password: 'IntegrationTest123!'
    }

    it('should complete full registration -> login -> profile access flow', async () => {
      // Step 1: Register new user
      const registerResponse = await request(app)
        .post('/api/users/register')
        .send(userData)
        .expect(201)

      expect(registerResponse.body).toMatchObject({
        success: true,
        message: expect.stringContaining('注册成功')
      })

      // Step 2: Login with registered credentials
      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: userData.password
        })
        .expect(200)

      expect(loginResponse.body).toMatchObject({
        success: true,
        user: {
          username: userData.username,
          email: userData.email,
          role: 'user'
        },
        token: expect.any(String)
      })

      const { token, user } = loginResponse.body

      // Step 3: Access protected profile endpoint
      const profileResponse = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)

      expect(profileResponse.body).toMatchObject({
        success: true,
        user: {
          id: user.id,
          username: userData.username,
          email: userData.email,
          role: 'user'
        }
      })

      // Step 4: Verify user data consistency
      expect(profileResponse.body.user.id).toBe(user.id)
      expect(profileResponse.body.user.username).toBe(userData.username)
      expect(profileResponse.body.user.email).toBe(userData.email)
    })

    it('should handle registration with existing email gracefully', async () => {
      // Register first user
      await request(app)
        .post('/api/users/register')
        .send(userData)
        .expect(201)

      // Try to register with same email
      const duplicateResponse = await request(app)
        .post('/api/users/register')
        .send(userData)
        .expect(400)

      expect(duplicateResponse.body).toMatchObject({
        success: false,
        message: expect.stringContaining('邮箱已被注册')
      })

      // Verify original user can still login
      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: userData.password
        })
        .expect(200)

      expect(loginResponse.body.success).toBe(true)
    })

    it('should maintain session consistency across requests', async () => {
      // Register and login
      await request(app)
        .post('/api/users/register')
        .send(userData)
        .expect(201)

      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: userData.password
        })
        .expect(200)

      const { token } = loginResponse.body

      // Make multiple authenticated requests
      const requests = Array(5).fill().map(() =>
        request(app)
          .get('/api/users/profile')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)
      )

      const responses = await Promise.all(requests)

      // All responses should be consistent
      responses.forEach(response => {
        expect(response.body).toMatchObject({
          success: true,
          user: {
            username: userData.username,
            email: userData.email,
            role: 'user'
          }
        })
      })
    })
  })

  describe('Security and Error Handling', () => {
    it('should handle malformed requests gracefully', async () => {
      // Test with malformed JSON
      const response = await request(app)
        .post('/api/users/register')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400)

      expect(response.body).toHaveProperty('success', false)
    })

    it('should handle SQL injection attempts', async () => {
      const maliciousData = {
        username: "'; DROP TABLE users; --",
        email: '<EMAIL>',
        password: 'Password123!'
      }

      const response = await request(app)
        .post('/api/users/register')
        .send(maliciousData)
        .expect(400)

      // Should reject due to validation, not cause SQL injection
      expect(response.body.success).toBe(false)

      // Verify users table still exists by trying a valid registration
      const validData = {
        username: 'validuser',
        email: '<EMAIL>',
        password: 'Password123!'
      }

      await request(app)
        .post('/api/users/register')
        .send(validData)
        .expect(201)
    })

    it('should handle concurrent registration attempts', async () => {
      const userData1 = {
        username: 'user1',
        email: '<EMAIL>',
        password: 'Password123!'
      }

      const userData2 = {
        username: 'user2',
        email: '<EMAIL>',
        password: 'Password123!'
      }

      // Concurrent registration requests
      const [response1, response2] = await Promise.all([
        request(app).post('/api/users/register').send(userData1),
        request(app).post('/api/users/register').send(userData2)
      ])

      // Both should succeed
      expect(response1.body.success).toBe(true)
      expect(response2.body.success).toBe(true)

      // Verify both users exist
      const user1 = await User.findOne({ where: { email: userData1.email } })
      const user2 = await User.findOne({ where: { email: userData2.email } })

      expect(user1).toBeTruthy()
      expect(user2).toBeTruthy()
      expect(user1.id).not.toBe(user2.id)
    })
  })

  describe('Performance and Load Testing', () => {
    it('should handle multiple login attempts efficiently', async () => {
      // Register a user first
      const userData = {
        username: 'loadtestuser',
        email: '<EMAIL>',
        password: 'Password123!'
      }

      await request(app)
        .post('/api/users/register')
        .send(userData)
        .expect(201)

      // Measure login performance
      const startTime = Date.now()
      
      const loginPromises = Array(10).fill().map(() =>
        request(app)
          .post('/api/users/login')
          .send({
            identifier: userData.email,
            password: userData.password
          })
      )

      const responses = await Promise.all(loginPromises)
      const endTime = Date.now()

      // All logins should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
        expect(response.body.success).toBe(true)
      })

      // Performance check (should complete within reasonable time)
      const totalTime = endTime - startTime
      expect(totalTime).toBeLessThan(5000) // 5 seconds for 10 concurrent logins
    })
  })
})
