#!/usr/bin/env node

/**
 * 简单的登录测试脚本
 */

const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 Testing login API...');
    
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'Demo123456!'
    });

    console.log('✅ Login successful!');
    console.log('📊 Response status:', response.status);
    console.log('📋 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.token) {
      console.log('🎯 Token found:', response.data.token.substring(0, 20) + '...');
      
      // 测试使用token访问受保护的端点
      console.log('\n🔒 Testing protected endpoint...');
      const meResponse = await axios.get('http://localhost:5000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${response.data.token}`
        }
      });

      console.log('✅ Protected endpoint access successful!');
      console.log('👤 User data:', JSON.stringify(meResponse.data, null, 2));

      // 测试用户资料API
      console.log('\n👤 Testing user profile endpoint...');
      const profileResponse = await axios.get('http://localhost:5000/api/users/profile', {
        headers: {
          'Authorization': `Bearer ${response.data.token}`
        }
      });

      console.log('✅ User profile access successful!');
      console.log('📋 Profile data:', JSON.stringify(profileResponse.data, null, 2));
    } else {
      console.log('❌ No token in response');
    }
    
  } catch (error) {
    console.error('❌ Login failed:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testLogin();
