#!/usr/bin/env node

/**
 * 简单的服务器测试脚本
 */

console.log('🚀 Starting simple server test...');

try {
  // 测试基本模块加载
  console.log('📦 Testing module imports...');
  
  const express = require('express');
  console.log('✅ Express loaded');
  
  const cors = require('cors');
  console.log('✅ CORS loaded');
  
  const dotenv = require('dotenv');
  console.log('✅ Dotenv loaded');
  
  // 加载环境变量
  dotenv.config({ path: './Backend/.env' });
  console.log('✅ Environment variables loaded');
  console.log('🔧 NODE_ENV:', process.env.NODE_ENV);
  console.log('🔧 PORT:', process.env.PORT);
  console.log('🔧 USE_REAL_SUPABASE:', process.env.USE_REAL_SUPABASE);
  
  // 创建简单的Express应用
  const app = express();
  
  // 基本中间件
  app.use(cors());
  app.use(express.json());
  
  // 简单的健康检查路由
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });
  
  // 启动服务器
  const PORT = process.env.PORT || 5000;
  const server = app.listen(PORT, () => {
    console.log(`✅ Simple server running on port ${PORT}`);
    console.log(`🌐 Health check: http://localhost:${PORT}/health`);
    
    // 5秒后关闭服务器
    setTimeout(() => {
      console.log('🛑 Shutting down test server...');
      server.close(() => {
        console.log('✅ Test server stopped successfully');
        process.exit(0);
      });
    }, 5000);
  });
  
  server.on('error', (error) => {
    console.error('❌ Server error:', error);
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Test failed:', error);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
