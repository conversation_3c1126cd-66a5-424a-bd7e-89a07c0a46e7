#!/usr/bin/env node

/**
 * 测试单个API调用
 */

const axios = require('axios');

async function testSingleAPI() {
  try {
    console.log('🔍 测试单个API调用...\n');

    // 1. 登录获取token
    console.log('1. 登录...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'demo123456'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 登录失败:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.token;
    console.log('✅ 登录成功，Token:', token.substring(0, 20) + '...');

    // 2. 测试 /auth/me
    console.log('\n2. 测试 /auth/me...');
    try {
      const meResponse = await axios.get('http://localhost:5000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ /auth/me 成功:', meResponse.status);
      console.log('用户信息:', JSON.stringify(meResponse.data, null, 2));
    } catch (meError) {
      console.error('❌ /auth/me 失败:');
      console.error('  状态码:', meError.response?.status);
      console.error('  错误信息:', meError.response?.data?.message);
      console.error('  完整响应:', JSON.stringify(meError.response?.data, null, 2));
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testSingleAPI();
