{"development": {"username": "postgres", "password": "wasd080980!", "database": "newzora", "host": "127.0.0.1", "dialect": "postgres", "logging": false}, "test": {"username": "postgres", "password": "wasd080980!", "database": "newzora_test", "host": "127.0.0.1", "dialect": "postgres", "logging": false}, "production": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "logging": false, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}}