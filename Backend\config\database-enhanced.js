const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// 数据库连接重试配置
const RETRY_CONFIG = {
  maxRetries: 5,
  retryDelay: 2000, // 2秒
  backoffMultiplier: 1.5,
  maxRetryDelay: 30000 // 30秒
};

// 连接池优化配置
const getOptimizedPoolConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 20 : 10),
    min: parseInt(process.env.DB_POOL_MIN) || (isProduction ? 5 : 2),
    acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 60000, // 60秒
    idle: parseInt(process.env.DB_POOL_IDLE) || 10000, // 10秒
    evict: parseInt(process.env.DB_POOL_EVICT) || 1000, // 1秒
    handleDisconnects: true,
    validate: (client) => {
      // 验证连接是否有效
      return client && !client._ending;
    }
  };
};

// 获取数据库配置
const getDatabaseConfig = () => {
  const baseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'newzora',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'wasd080980!',
    dialect: 'postgres',
    
    // 连接池配置
    pool: getOptimizedPoolConfig(),
    
    // 查询配置
    query: {
      timeout: 60000, // 60秒查询超时
      retry: {
        max: 3,
        match: [
          /SQLITE_BUSY/,
          /SQLITE_LOCKED/,
          /connection terminated/,
          /Connection lost/,
          /ECONNRESET/,
          /ETIMEDOUT/,
          /ENOTFOUND/,
          /ENETUNREACH/
        ]
      }
    },
    
    // 方言特定选项
    dialectOptions: {
      ssl: process.env.DB_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false,
      
      // PostgreSQL 特定配置
      statement_timeout: 60000, // 60秒语句超时
      query_timeout: 60000, // 60秒查询超时
      application_name: 'Newzora',
      
      // 连接配置
      keepAlive: true,
      keepAliveInitialDelayMillis: 0,
      
      // 字符集配置
      charset: 'utf8',
      timezone: '+08:00'
    },
    
    // 日志配置
    logging: process.env.NODE_ENV === 'production' 
      ? (sql, timing) => {
          if (timing > 1000) {
            console.warn(`[SLOW QUERY] ${timing}ms: ${sql.substring(0, 200)}...`);
          }
        }
      : process.env.DB_LOGGING === 'true' ? console.log : false,
    
    // 基准测试
    benchmark: process.env.NODE_ENV === 'production',
    
    // 事务配置
    transactionType: 'IMMEDIATE',
    isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    
    // 重试配置
    retry: {
      max: RETRY_CONFIG.maxRetries,
      match: [
        Sequelize.ConnectionError,
        Sequelize.ConnectionTimedOutError,
        Sequelize.TimeoutError,
        /ECONNRESET/,
        /ETIMEDOUT/,
        /ENOTFOUND/,
        /connection terminated/
      ]
    }
  };

  return baseConfig;
};

// 创建增强的Sequelize实例
const createEnhancedSequelize = () => {
  const config = getDatabaseConfig();
  const sequelize = new Sequelize(config);

  // 连接事件监听 - 使用正确的事件监听方式
  sequelize.addHook('afterConnect', (connection, config) => {
    console.log('✅ New database connection established');
  });

  sequelize.addHook('beforeDisconnect', (connection) => {
    console.log('⚠️ Database connection will be closed');
  });

  return sequelize;
};

// 创建Sequelize实例
const sequelize = createEnhancedSequelize();

// 连接重试函数
const connectWithRetry = async (retryCount = 0) => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');
    return true;
  } catch (error) {
    console.error(`❌ Database connection attempt ${retryCount + 1} failed:`, error.message);
    
    if (retryCount < RETRY_CONFIG.maxRetries) {
      const delay = Math.min(
        RETRY_CONFIG.retryDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, retryCount),
        RETRY_CONFIG.maxRetryDelay
      );
      
      console.log(`⏳ Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return connectWithRetry(retryCount + 1);
    } else {
      console.error('❌ Max retry attempts reached. Database connection failed.');
      throw error;
    }
  }
};

// 健康检查函数
const performHealthCheck = async () => {
  try {
    const startTime = Date.now();
    
    // 执行简单查询测试连接
    const result = await sequelize.query('SELECT NOW() as current_time, version() as version');
    const responseTime = Date.now() - startTime;
    
    // 获取连接池状态
    const poolStats = {
      total: sequelize.connectionManager.pool?._allObjects?.length || 0,
      used: sequelize.connectionManager.pool?._inUseObjects?.length || 0,
      waiting: sequelize.connectionManager.pool?._pendingAcquires?.length || 0
    };
    
    return {
      status: 'healthy',
      responseTime,
      timestamp: result[0][0].current_time,
      version: result[0][0].version.split(' ')[0], // 只取版本号部分
      pool: poolStats,
      config: {
        host: sequelize.config.host,
        database: sequelize.config.database,
        dialect: sequelize.config.dialect
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
      pool: {
        total: 0,
        used: 0,
        waiting: 0
      }
    };
  }
};

// 数据库性能监控
const getPerformanceMetrics = async () => {
  try {
    const queries = [
      // 活动连接统计
      `SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
       FROM pg_stat_activity 
       WHERE datname = current_database()`,
      
      // 数据库大小
      `SELECT pg_size_pretty(pg_database_size(current_database())) as database_size`,
      
      // 慢查询统计
      `SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows
       FROM pg_stat_statements 
       WHERE mean_time > 100 
       ORDER BY mean_time DESC 
       LIMIT 5`,
       
      // 表大小统计
      `SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
       FROM pg_tables 
       WHERE schemaname = 'public' 
       ORDER BY size_bytes DESC 
       LIMIT 10`
    ];

    const results = await Promise.allSettled(
      queries.map(query => sequelize.query(query, { type: Sequelize.QueryTypes.SELECT }))
    );

    return {
      connections: results[0].status === 'fulfilled' ? results[0].value[0] : null,
      database_size: results[1].status === 'fulfilled' ? results[1].value[0]?.database_size : 'Unknown',
      slow_queries: results[2].status === 'fulfilled' ? results[2].value : [],
      table_sizes: results[3].status === 'fulfilled' ? results[3].value : []
    };
  } catch (error) {
    console.error('获取性能指标失败:', error.message);
    return null;
  }
};

// 优雅关闭函数
const gracefulShutdown = async () => {
  try {
    console.log('🔄 Closing database connections...');
    await sequelize.close();
    console.log('✅ Database connections closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connections:', error.message);
  }
};

// 数据库维护函数
const performMaintenance = async () => {
  try {
    console.log('🔧 Starting database maintenance...');
    
    // 更新表统计信息
    await sequelize.query('ANALYZE;');
    console.log('✅ Table statistics updated');
    
    // 清理连接
    await sequelize.query('SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = current_database() AND state = \'idle\' AND state_change < now() - interval \'1 hour\';');
    console.log('✅ Idle connections cleaned');
    
    console.log('✅ Database maintenance completed');
  } catch (error) {
    console.error('❌ Database maintenance failed:', error.message);
  }
};

module.exports = {
  sequelize,
  connectWithRetry,
  performHealthCheck,
  getPerformanceMetrics,
  gracefulShutdown,
  performMaintenance,
  RETRY_CONFIG
};
