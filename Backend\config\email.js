const nodemailer = require('nodemailer');
const sgMail = require('@sendgrid/mail');

// Email service configuration
const EMAIL_CONFIG = {
  // Default sender information
  DEFAULT_FROM: {
    name: 'Newzora',
    email: process.env.EMAIL_FROM || '<EMAIL>'
  },
  
  // Email service providers
  PROVIDERS: {
    SMTP: 'smtp',
    SENDGRID: 'sendgrid',
    GMAIL: 'gmail'
  },
  
  // Current provider (can be changed via environment variable)
  CURRENT_PROVIDER: process.env.EMAIL_PROVIDER || 'smtp',
  
  // SMTP Configuration
  SMTP: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  },
  
  // SendGrid Configuration
  SENDGRID: {
    apiKey: process.env.SENDGRID_API_KEY
  },
  
  // Gmail Configuration (OAuth2)
  GMAIL: {
    clientId: process.env.GMAIL_CLIENT_ID,
    clientSecret: process.env.GMAIL_CLIENT_SECRET,
    refreshToken: process.env.GMAIL_REFRESH_TOKEN,
    accessToken: process.env.GMAIL_ACCESS_TOKEN
  },
  
  // Email templates configuration
  TEMPLATES: {
    BASE_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
    TEMPLATE_DIR: './templates/email',
    COMPILED_DIR: './templates/compiled'
  },
  
  // Rate limiting for emails
  RATE_LIMITS: {
    PER_USER_PER_HOUR: 10,
    PER_USER_PER_DAY: 50,
    GLOBAL_PER_MINUTE: 100
  },
  
  // Email types and their configurations
  EMAIL_TYPES: {
    VERIFICATION: {
      subject: 'Verify your email address',
      template: 'email-verification',
      expiresIn: '24h'
    },
    PASSWORD_RESET: {
      subject: 'Reset your password',
      template: 'password-reset',
      expiresIn: '1h'
    },
    WELCOME: {
      subject: 'Welcome to Newzora!',
      template: 'welcome'
    },
    NOTIFICATION: {
      subject: 'New notification',
      template: 'notification'
    },
    ARTICLE_PUBLISHED: {
      subject: 'Your article has been published',
      template: 'article-published'
    },
    COMMENT_NOTIFICATION: {
      subject: 'New comment on your article',
      template: 'comment-notification'
    },
    FOLLOW_NOTIFICATION: {
      subject: 'Someone started following you',
      template: 'follow-notification'
    },
    WEEKLY_DIGEST: {
      subject: 'Your weekly digest',
      template: 'weekly-digest'
    }
  }
};

// Initialize email transporter based on provider
let transporter = null;

const initializeEmailService = () => {
  try {
    switch (EMAIL_CONFIG.CURRENT_PROVIDER) {
      case EMAIL_CONFIG.PROVIDERS.SENDGRID:
        if (EMAIL_CONFIG.SENDGRID.apiKey) {
          sgMail.setApiKey(EMAIL_CONFIG.SENDGRID.apiKey);
          console.log('✅ SendGrid email service initialized');
        } else {
          console.warn('⚠️ SendGrid API key not provided');
        }
        break;
        
      case EMAIL_CONFIG.PROVIDERS.GMAIL:
        if (EMAIL_CONFIG.GMAIL.clientId && EMAIL_CONFIG.GMAIL.clientSecret) {
          transporter = nodemailer.createTransporter({
            service: 'gmail',
            auth: {
              type: 'OAuth2',
              user: EMAIL_CONFIG.DEFAULT_FROM.email,
              clientId: EMAIL_CONFIG.GMAIL.clientId,
              clientSecret: EMAIL_CONFIG.GMAIL.clientSecret,
              refreshToken: EMAIL_CONFIG.GMAIL.refreshToken,
              accessToken: EMAIL_CONFIG.GMAIL.accessToken
            }
          });
          console.log('✅ Gmail email service initialized');
        } else {
          console.warn('⚠️ Gmail OAuth credentials not provided');
        }
        break;
        
      case EMAIL_CONFIG.PROVIDERS.SMTP:
      default:
        if (EMAIL_CONFIG.SMTP.auth.user && EMAIL_CONFIG.SMTP.auth.pass) {
          transporter = nodemailer.createTransporter(EMAIL_CONFIG.SMTP);
          console.log('✅ SMTP email service initialized');
        } else {
          console.warn('⚠️ SMTP credentials not provided, using development mode');
          // Create test account for development
          createTestAccount();
        }
        break;
    }
  } catch (error) {
    console.error('❌ Failed to initialize email service:', error.message);
  }
};

// Create test account for development
const createTestAccount = async () => {
  try {
    const testAccount = await nodemailer.createTestAccount();
    
    transporter = nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass
      }
    });
    
    console.log('✅ Test email account created for development');
    console.log(`   User: ${testAccount.user}`);
    console.log(`   Pass: ${testAccount.pass}`);
    console.log('   Preview emails at: https://ethereal.email');
  } catch (error) {
    console.error('❌ Failed to create test email account:', error.message);
  }
};

// Verify email service connection
const verifyEmailService = async () => {
  try {
    if (EMAIL_CONFIG.CURRENT_PROVIDER === EMAIL_CONFIG.PROVIDERS.SENDGRID) {
      // SendGrid doesn't have a verify method, so we'll just check if API key is set
      return !!EMAIL_CONFIG.SENDGRID.apiKey;
    } else if (transporter) {
      await transporter.verify();
      return true;
    }
    return false;
  } catch (error) {
    console.error('Email service verification failed:', error.message);
    return false;
  }
};

// Get email service status
const getEmailServiceStatus = () => {
  return {
    provider: EMAIL_CONFIG.CURRENT_PROVIDER,
    configured: EMAIL_CONFIG.CURRENT_PROVIDER === EMAIL_CONFIG.PROVIDERS.SENDGRID 
      ? !!EMAIL_CONFIG.SENDGRID.apiKey 
      : !!transporter,
    defaultFrom: EMAIL_CONFIG.DEFAULT_FROM,
    rateLimits: EMAIL_CONFIG.RATE_LIMITS
  };
};

// Export configuration and functions
module.exports = {
  EMAIL_CONFIG,
  transporter,
  sgMail,
  initializeEmailService,
  verifyEmailService,
  getEmailServiceStatus,
  createTestAccount
};
