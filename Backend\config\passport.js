const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const User = require('../models/User');
const { generateToken } = require('../middleware/auth');

// Google OAuth Strategy - only in non-test environment
if (process.env.NODE_ENV !== 'test' && process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: "/api/users/auth/google/callback"
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      // Check if user already exists with this Google ID
      let user = await User.findOne({ where: { googleId: profile.id } });

      if (user) {
        // User exists, update last login
        await user.update({ lastLogin: new Date() });
        return done(null, user);
      }

      // Check if user exists with same email
      user = await User.findOne({ where: { email: profile.emails[0].value.toLowerCase() } });

      if (user) {
        // Link Google account to existing user
        await user.update({
          googleId: profile.id,
          lastLogin: new Date(),
          isEmailVerified: true // Google emails are pre-verified
        });
        return done(null, user);
      }

      // Create new user
      user = await User.create({
        googleId: profile.id,
        username: profile.displayName.replace(/\s+/g, '_').toLowerCase() + '_' + Date.now(),
        email: profile.emails[0].value.toLowerCase(),
        avatar: profile.photos[0]?.value || '',
        isEmailVerified: true, // Google emails are pre-verified
        lastLogin: new Date()
      });

      return done(null, user);
    } catch (error) {
      console.error('Google OAuth error:', error);
      return done(error, null);
    }
  }));
}

// Facebook OAuth Strategy - only in non-test environment
if (process.env.NODE_ENV !== 'test' && process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
  passport.use(new FacebookStrategy({
    clientID: process.env.FACEBOOK_APP_ID,
    clientSecret: process.env.FACEBOOK_APP_SECRET,
    callbackURL: "/api/users/auth/facebook/callback",
    profileFields: ['id', 'displayName', 'emails', 'photos']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      // Check if user already exists with this Facebook ID
      let user = await User.findOne({ where: { facebookId: profile.id } });

      if (user) {
        // User exists, update last login
        await user.update({ lastLogin: new Date() });
        return done(null, user);
      }

      // Check if user exists with same email
      const email = profile.emails?.[0]?.value;
      if (email) {
        user = await User.findOne({ where: { email: email.toLowerCase() } });

        if (user) {
          // Link Facebook account to existing user
          await user.update({
            facebookId: profile.id,
            lastLogin: new Date(),
            isEmailVerified: true // Facebook emails are pre-verified
          });
          return done(null, user);
        }
      }

      // Create new user
      user = await User.create({
        facebookId: profile.id,
        username: profile.displayName.replace(/\s+/g, '_').toLowerCase() + '_' + Date.now(),
        email: email ? email.toLowerCase() : null,
        avatar: profile.photos?.[0]?.value || '',
        isEmailVerified: !!email, // Only verified if email is provided
        lastLogin: new Date()
      });

      return done(null, user);
    } catch (error) {
      console.error('Facebook OAuth error:', error);
      return done(error, null);
    }
  }));
}

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

module.exports = passport;
