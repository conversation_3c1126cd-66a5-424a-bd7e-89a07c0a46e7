# Newzora API 路由汇总文档

## 概述
本文档汇总了 Newzora 平台所有的 API 路由，包括新增的高级功能路由。

## 基础路由

### 认证相关 (`/api/auth`)
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/auth/profile` - 获取用户资料
- `PUT /api/auth/profile` - 更新用户资料

### 文章管理 (`/api/articles`)
- `GET /api/articles` - 获取文章列表
- `POST /api/articles` - 创建文章
- `GET /api/articles/:id` - 获取单篇文章
- `PUT /api/articles/:id` - 更新文章
- `DELETE /api/articles/:id` - 删除文章
- `POST /api/articles/:id/like` - 点赞文章
- `POST /api/articles/:id/bookmark` - 收藏文章

### 用户管理 (`/api/users`)
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `POST /api/users/:id/follow` - 关注用户
- `DELETE /api/users/:id/follow` - 取消关注

### 评论系统 (`/api/comments`)
- `GET /api/comments/:targetType/:targetId` - 获取评论列表
- `POST /api/comments` - 创建评论
- `PUT /api/comments/:id` - 更新评论
- `DELETE /api/comments/:id` - 删除评论
- `POST /api/comments/:commentId/interact` - 评论交互（点赞等）
- `POST /api/comments/:commentId/report` - 举报评论
- `GET /api/comments/enhanced/:targetType/:targetId` - 获取增强评论列表
- `POST /api/comments/enhanced` - 创建增强评论
- `GET /api/comments/stats/:targetType/:targetId` - 获取评论统计
- `GET /api/comments/interaction-types` - 获取交互类型
- `GET /api/comments/report-types` - 获取举报类型

### 媒体管理 (`/api/media`)
- `POST /api/media/upload` - 上传媒体文件
- `GET /api/media` - 获取媒体列表
- `GET /api/media/:id` - 获取媒体详情
- `DELETE /api/media/:id` - 删除媒体文件
- `POST /api/media/convert/:id` - 转换媒体格式
- `POST /api/media/compress/:id` - 压缩媒体文件
- `POST /api/media/extract-audio/:id` - 提取音频

## 高级功能路由

### 智能推荐系统 (`/api/recommendations`)
- `GET /api/recommendations/personalized` - 获取个性化推荐
- `GET /api/recommendations/realtime` - 获取实时推荐
- `GET /api/recommendations/contextual` - 获取上下文感知推荐
- `GET /api/recommendations/deep-learning` - 获取深度学习推荐
- `GET /api/recommendations/multi-armed-bandit` - 多臂老虎机推荐
- `POST /api/recommendations/feedback` - 记录推荐反馈
- `GET /api/recommendations/stats` - 获取推荐统计
- `GET /api/recommendations/popular` - 获取热门推荐
- `GET /api/recommendations/trending` - 获取趋势推荐
- `PUT /api/recommendations/weights` - 更新推荐权重（管理员）

### AI 内容审核 (`/api/ai-moderation`)
- `POST /api/ai-moderation/moderate` - 审核单个内容
- `POST /api/ai-moderation/moderate-batch` - 批量审核内容
- `POST /api/ai-moderation/detect-sensitive-words` - 检测敏感词
- `POST /api/ai-moderation/detect-toxicity` - 毒性检测
- `POST /api/ai-moderation/semantic-analysis` - 语义分析
- `POST /api/ai-moderation/detect-image-content` - 图像内容检测
- `POST /api/ai-moderation/detect-malicious-urls` - URL安全检测
- `GET /api/ai-moderation/stats` - 获取审核统计（管理员）
- `POST /api/ai-moderation/update-sensitive-words` - 更新敏感词库（管理员）
- `GET /api/ai-moderation/test` - 测试AI审核系统（管理员）

### 国际化支持 (`/api/i18n`)
- `GET /api/i18n/detect-language` - 检测用户语言
- `GET /api/i18n/languages` - 获取支持的语言列表
- `GET /api/i18n/translations/:language` - 获取翻译
- `POST /api/i18n/translate` - 翻译文本
- `POST /api/i18n/translate-batch` - 批量翻译
- `POST /api/i18n/detect-text-language` - 检测文本语言
- `POST /api/i18n/format-datetime` - 格式化日期时间
- `GET /api/i18n/stats` - 获取国际化统计
- `POST /api/i18n/clear-cache` - 清理翻译缓存

### 创作者生态系统 (`/api/creators`)
- `GET /api/creators/levels` - 获取创作者等级信息
- `GET /api/creators/evaluate-level` - 评估用户创作者等级
- `GET /api/creators/verification-types` - 获取认证类型
- `POST /api/creators/apply-verification` - 申请创作者认证
- `GET /api/creators/incentive-programs` - 获取激励计划
- `GET /api/creators/calculate-rewards` - 计算激励奖励
- `GET /api/creators/recommendations` - 获取创作者推荐
- `GET /api/creators/stats` - 获取创作者统计
- `GET /api/creators/global-support` - 获取全球化支持信息
- `GET /api/creators/search` - 搜索创作者
- `POST /api/creators/follow/:creatorId` - 关注创作者
- `DELETE /api/creators/follow/:creatorId` - 取消关注创作者

### 搜索功能 (`/api/search`)
- `GET /api/search` - 全局搜索
- `GET /api/search/articles` - 搜索文章
- `GET /api/search/users` - 搜索用户
- `GET /api/search/suggestions` - 搜索建议

### 邮件服务 (`/api/email`)
- `POST /api/email/send` - 发送邮件
- `GET /api/email/templates` - 获取邮件模板
- `POST /api/email/verify` - 邮箱验证

### 系统监控 (`/api/monitoring`)
- `GET /api/monitoring/health` - 系统健康检查
- `GET /api/monitoring/stats` - 系统统计信息

### 报告系统 (`/api/reports`)
- `POST /api/reports` - 创建报告
- `GET /api/reports` - 获取报告列表
- `GET /api/reports/:id` - 获取报告详情

## 前端路由对应关系

### 页面路由
- `/` - 首页 (使用推荐API)
- `/explore` - 探索页面 (使用搜索和推荐API)
- `/create` - 创作页面 (使用媒体上传API)
- `/article/:id` - 文章详情页 (使用文章和评论API)
- `/profile/:id` - 用户资料页 (使用用户API)
- `/settings` - 设置页面 (使用用户和国际化API)
- `/creators` - 创作者页面 (使用创作者生态API)

### API 调用示例

#### 获取个性化推荐
```javascript
const response = await fetch('/api/recommendations/personalized?limit=20', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

#### 创建评论
```javascript
const response = await fetch('/api/comments/enhanced', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    targetType: 'article',
    targetId: articleId,
    content: commentText
  })
});
```

#### 上传媒体文件
```javascript
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/media/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

## 认证要求

### 需要认证的路由
- 所有 POST、PUT、DELETE 操作
- 个人资料相关路由
- 推荐系统个性化功能
- 创作者功能
- 媒体上传和管理

### 管理员权限路由
- `/api/ai-moderation/stats`
- `/api/ai-moderation/update-sensitive-words`
- `/api/ai-moderation/test`
- `/api/recommendations/weights`
- `/api/monitoring/*`

## 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "code": "ERROR_CODE"
}
```

### 常见错误代码
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 验证失败
- `500` - 服务器内部错误

## 性能优化建议

1. **缓存策略**
   - 推荐结果缓存 15 分钟
   - 翻译结果缓存 24 小时
   - 用户资料缓存 5 分钟

2. **分页参数**
   - 默认 `page=1, limit=20`
   - 最大 `limit=100`

3. **请求频率限制**
   - 普通用户：100 请求/分钟
   - 认证用户：500 请求/分钟
   - 管理员：无限制

## 部署说明

### 环境变量
```bash
NODE_ENV=production
PORT=5000
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-key
```

### 启动命令
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## 更新日志

### v2.0.0 (当前版本)
- ✅ 添加智能推荐系统
- ✅ 集成AI内容审核
- ✅ 实现多语言国际化
- ✅ 支持音视频创作
- ✅ 建立创作者生态系统
- ✅ 完善评论交互系统
- ✅ 优化用户体验细节

### 下一版本计划
- 实时通知系统
- 高级数据分析
- 移动端优化
- 性能监控增强
