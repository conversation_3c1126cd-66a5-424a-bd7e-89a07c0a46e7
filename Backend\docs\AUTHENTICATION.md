# Newzora 认证系统文档

## 概述

Newzora 的认证系统经过全面优化升级，提供了安全、可靠、用户友好的身份验证功能。

## 主要功能

### 🔐 核心认证功能
- **用户注册** - 支持邮箱验证的安全注册
- **用户登录** - 支持邮箱/用户名登录，记住我功能
- **密码重置** - 安全的密码找回和重置流程
- **邮箱验证** - 新用户邮箱验证和重新发送验证邮件
- **Token刷新** - JWT访问令牌和刷新令牌机制

### 🛡️ 安全特性
- **密码强度验证** - 实时密码强度检查和要求
- **账户锁定** - 防止暴力破解的账户锁定机制
- **速率限制** - API调用频率限制
- **安全日志** - 登录尝试和安全事件记录
- **密码历史** - 防止重复使用最近的密码

### 🌐 社交登录
- **Google OAuth** - Google账户登录
- **Facebook OAuth** - Facebook账户登录  
- **Twitter OAuth** - Twitter账户登录
- **Apple ID** - Apple账户登录（计划中）

### 👤 用户管理
- **个人资料管理** - 用户信息更新
- **密码修改** - 安全的密码更改
- **账户绑定** - 社交账户绑定和解绑
- **会话管理** - 多设备会话管理

## API 端点

### 认证相关 (`/api/auth-enhanced`)

#### 用户注册
```http
POST /api/auth-enhanced/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

#### 用户登录
```http
POST /api/auth-enhanced/login
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "SecurePassword123!",
  "rememberMe": false
}
```

#### 忘记密码
```http
POST /api/auth-enhanced/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### 重置密码
```http
POST /api/auth-enhanced/reset-password
Content-Type: application/json

{
  "token": "reset_token_here",
  "newPassword": "NewSecurePassword123!"
}
```

#### 邮箱验证
```http
POST /api/auth-enhanced/verify-email
Content-Type: application/json

{
  "token": "verification_token_here"
}
```

#### 刷新Token
```http
POST /api/auth-enhanced/refresh-token
Content-Type: application/json

{
  "refreshToken": "refresh_token_here"
}
```

#### 修改密码
```http
POST /api/auth-enhanced/change-password
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewPassword123!"
}
```

#### 获取用户信息
```http
GET /api/auth-enhanced/me
Authorization: Bearer <access_token>
```

#### 更新个人资料
```http
PUT /api/auth-enhanced/profile
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "bio": "Software Developer"
}
```

#### 检查密码强度
```http
POST /api/auth-enhanced/check-password-strength
Content-Type: application/json

{
  "password": "password_to_check"
}
```

### 社交登录 (`/api/auth/social`)

#### Google登录
```http
GET /api/auth/social/google
```

#### Facebook登录
```http
GET /api/auth/social/facebook
```

#### Twitter登录
```http
GET /api/auth/social/twitter
```

## 密码要求

新的密码必须满足以下要求：
- 至少8个字符长
- 包含大写字母
- 包含小写字母
- 包含数字
- 包含特殊字符 (!@#$%^&*(),.?":{}|<>)

密码强度等级：
- **Weak** (弱) - 不满足基本要求
- **Medium** (中等) - 满足基本要求
- **Strong** (强) - 满足所有要求且长度适中
- **Very Strong** (非常强) - 满足所有要求且长度较长

## 安全机制

### 账户锁定
- 连续5次登录失败后账户被锁定30分钟
- 锁定期间无法登录
- 锁定时间过后自动解锁

### 速率限制
- 认证相关API：15分钟内最多5次请求
- 密码重置：1小时内最多3次请求
- 超出限制返回429状态码

### Token管理
- **访问Token**: 7天有效期（记住我：30天）
- **刷新Token**: 30天有效期
- Token包含用户ID、签发时间等信息
- 支持Token黑名单（计划中）

## 环境变量配置

```env
# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key

# 社交登录配置
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

TWITTER_CONSUMER_KEY=your_twitter_consumer_key
TWITTER_CONSUMER_SECRET=your_twitter_consumer_secret

# 前端URL
FRONTEND_URL=http://localhost:3000

# 邮件服务配置
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
```

## 数据库表结构

### users表新增字段
- `passwordResetToken` - 密码重置令牌
- `passwordResetExpires` - 密码重置令牌过期时间
- `emailVerificationToken` - 邮箱验证令牌
- `emailVerificationExpires` - 邮箱验证令牌过期时间
- `isEmailVerified` - 邮箱是否已验证
- `loginAttempts` - 登录失败次数
- `lockUntil` - 账户锁定截止时间
- `lastLogin` - 最后登录时间
- `googleId` - Google账户ID
- `facebookId` - Facebook账户ID
- `twitterId` - Twitter账户ID
- `appleId` - Apple账户ID

### 新增表
- `user_sessions` - 用户会话管理
- `login_logs` - 登录日志
- `password_history` - 密码历史记录

## 前端集成

### React组件
- `Login.jsx` - 登录页面
- `Register.jsx` - 注册页面
- `ForgotPassword.jsx` - 忘记密码页面
- `ResetPassword.jsx` - 重置密码页面

### 使用示例
```jsx
import { useAuth } from '../contexts/AuthContext';

const { login, logout, user, isAuthenticated } = useAuth();

// 登录
await login({ identifier: '<EMAIL>', password: 'password' });

// 登出
await logout();

// 检查认证状态
if (isAuthenticated) {
  // 用户已登录
}
```

## 测试

运行认证系统测试：
```bash
node Backend/scripts/test-auth-system.js
```

测试包括：
- 密码强度检查
- 用户注册和登录
- Token验证和刷新
- 密码重置流程
- 个人资料更新
- 速率限制验证

## 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置
   - 确认邮件服务商设置
   - 查看邮件服务日志

2. **社交登录失败**
   - 验证OAuth应用配置
   - 检查回调URL设置
   - 确认客户端ID和密钥

3. **Token验证失败**
   - 检查JWT_SECRET配置
   - 确认Token格式正确
   - 验证Token是否过期

4. **数据库连接问题**
   - 运行数据库迁移脚本
   - 检查数据库连接配置
   - 确认表结构正确

## 安全建议

1. **定期更新密钥** - 定期轮换JWT密钥
2. **监控异常登录** - 设置异常登录告警
3. **启用HTTPS** - 生产环境必须使用HTTPS
4. **备份数据** - 定期备份用户数据和日志
5. **安全审计** - 定期进行安全审计和渗透测试

## 更新日志

### v2.0.0 (2024-01-XX)
- ✅ 全面重构认证系统
- ✅ 增强密码安全要求
- ✅ 添加社交登录支持
- ✅ 实现邮箱验证功能
- ✅ 添加账户锁定机制
- ✅ 优化Token管理
- ✅ 完善安全日志记录
