const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { SECURITY_CONFIG, generateJWTPayload, secureCompare } = require('../config/security');

// Verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'Access token required' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: [SECURITY_CONFIG.JWT.ALGORITHM],
      issuer: SECURITY_CONFIG.JWT.ISSUER,
      audience: SECURITY_CONFIG.JWT.AUDIENCE
    });

    const user = await User.findByPk(decoded.userId, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    if (!user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token - user not found' 
      });
    }

    if (!user.isActive) {
      return res.status(401).json({ 
        success: false, 
        message: 'Account is deactivated' 
      });
    }

    if (user.isAccountLocked()) {
      return res.status(401).json({ 
        success: false, 
        message: 'Account is temporarily locked due to too many failed login attempts' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token' 
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Token expired' 
      });
    }
    
    console.error('Auth middleware error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Authentication error' 
    });
  }
};

// Optional authentication - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: [SECURITY_CONFIG.JWT.ALGORITHM],
      issuer: SECURITY_CONFIG.JWT.ISSUER,
      audience: SECURITY_CONFIG.JWT.AUDIENCE
    });

    const user = await User.findByPk(decoded.userId, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    req.user = user && user.isActive && !user.isAccountLocked() ? user : null;
    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// Check if user has required role
const requireRole = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        success: false, 
        message: 'Insufficient permissions' 
      });
    }

    next();
  };
};

// Admin only middleware
const requireAdmin = requireRole('admin');

// Admin or moderator middleware
const requireModerator = requireRole('admin', 'moderator');

// Check if user has specific permission
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // For super_admin, allow all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has the specific permission
      const hasPermission = await checkUserPermission(req.user.id, permission);

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Permission denied: ${permission}`
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
};

// Check multiple permissions (user must have ALL)
const requireAllPermissions = (permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // For super_admin, allow all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has all required permissions
      for (const permission of permissions) {
        const hasPermission = await checkUserPermission(req.user.id, permission);
        if (!hasPermission) {
          return res.status(403).json({
            success: false,
            message: `Permission denied: ${permission}`
          });
        }
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
};

// Check multiple permissions (user must have ANY)
const requireAnyPermission = (permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // For super_admin, allow all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has any of the required permissions
      let hasAnyPermission = false;
      for (const permission of permissions) {
        const hasPermission = await checkUserPermission(req.user.id, permission);
        if (hasPermission) {
          hasAnyPermission = true;
          break;
        }
      }

      if (!hasAnyPermission) {
        return res.status(403).json({
          success: false,
          message: `Permission denied. Required permissions: ${permissions.join(' OR ')}`
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
};

// Helper function to check user permission
const checkUserPermission = async (userId, permission) => {
  try {
    // This would typically query the database for user permissions
    // For now, we'll use a simplified role-based approach

    const { User } = require('../models');

    // Get user with role information
    const user = await User.findByPk(userId, {
      attributes: ['id', 'role']
    });

    if (!user) {
      return false;
    }

    // Define role-based permissions
    const rolePermissions = {
      'super_admin': ['*'], // All permissions
      'admin': [
        'user:read', 'user:create', 'user:update', 'user:delete', 'user:ban',
        'article:read', 'article:create', 'article:update', 'article:delete', 'article:publish',
        'comment:read', 'comment:create', 'comment:update', 'comment:delete', 'comment:moderate',
        'role:read', 'role:create', 'role:update', 'role:delete',
        'permission:read', 'permission:create', 'permission:update', 'permission:delete',
        'system:read', 'system:update', 'analytics:read'
      ],
      'moderator': [
        'user:read', 'user:update', 'user:ban',
        'article:read', 'article:update', 'article:publish',
        'comment:read', 'comment:update', 'comment:delete', 'comment:moderate',
        'system:read'
      ],
      'editor': [
        'article:read', 'article:create', 'article:update',
        'comment:read', 'comment:update'
      ],
      'user': [
        'article:read', 'article:create_own', 'article:update_own',
        'comment:read', 'comment:create', 'comment:update_own', 'comment:delete_own',
        'profile:read', 'profile:update_own'
      ]
    };

    const userPermissions = rolePermissions[user.role] || [];

    // Check if user has the specific permission or wildcard
    return userPermissions.includes('*') || userPermissions.includes(permission);
  } catch (error) {
    console.error('Permission check error:', error);
    return false;
  }
};

// Resource ownership check
const requireResourceOwnership = (resourceModel, resourceIdParam = 'id', userIdField = 'userId') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // Super admin can access any resource
      if (req.user.role === 'super_admin') {
        return next();
      }

      const resourceId = req.params[resourceIdParam];
      const { [resourceModel]: Model } = require('../models');

      const resource = await Model.findByPk(resourceId);

      if (!resource) {
        return res.status(404).json({
          success: false,
          message: 'Resource not found'
        });
      }

      // Check if user owns the resource
      if (resource[userIdField] !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You can only access your own resources'
        });
      }

      // Attach resource to request for use in route handler
      req.resource = resource;
      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      res.status(500).json({
        success: false,
        message: 'Ownership check failed'
      });
    }
  };
};

// Check if user owns resource or is admin
const requireOwnershipOrAdmin = (getResourceUserId) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }

    // Admin can access everything
    if (req.user.role === 'admin') {
      return next();
    }

    try {
      const resourceUserId = await getResourceUserId(req);
      
      if (req.user.id !== resourceUserId) {
        return res.status(403).json({ 
          success: false, 
          message: 'Access denied - you can only access your own resources' 
        });
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Authorization error' 
      });
    }
  };
};

// Generate JWT token
const generateToken = (user) => {
  const payload = generateJWTPayload(user);
  return jwt.sign(
    payload,
    process.env.JWT_SECRET,
    {
      expiresIn: SECURITY_CONFIG.JWT.ACCESS_TOKEN_EXPIRES,
      algorithm: SECURITY_CONFIG.JWT.ALGORITHM
      // issuer and audience are already included in the payload
    }
  );
};

// Verify email verification token
const verifyEmailToken = async (req, res, next) => {
  try {
    const { token } = req.params;
    
    if (!token) {
      return res.status(400).json({ 
        success: false, 
        message: 'Verification token required' 
      });
    }

    const user = await User.findOne({
      where: {
        emailVerificationToken: token,
        emailVerificationExpires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid or expired verification token' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Verification error' 
    });
  }
};

// Verify password reset token
const verifyResetToken = async (req, res, next) => {
  try {
    const { token } = req.params;
    
    if (!token) {
      return res.status(400).json({ 
        success: false, 
        message: 'Reset token required' 
      });
    }

    const user = await User.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid or expired reset token' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Password reset verification error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Reset verification error' 
    });
  }
};



module.exports = {
  authenticateToken,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireModerator,
  requirePermission,
  requireAllPermissions,
  requireAnyPermission,
  requireResourceOwnership,
  requireOwnershipOrAdmin,
  checkUserPermission,
  generateToken,
  verifyEmailToken,
  verifyResetToken
};
