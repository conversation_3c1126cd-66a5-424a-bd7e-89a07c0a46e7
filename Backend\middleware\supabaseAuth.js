const jwt = require('jsonwebtoken');
const { supabase } = require('../config/supabase');
const authService = require('../services/authService');
const mockSupabaseAuth = require('../services/mockSupabaseAuth');

// 验证Supabase JWT Token
const authenticateToken = async (req, res, next) => {
  console.log('🔍 Auth middleware called for:', req.method, req.url);
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    console.log('🔍 Auth middleware debug:');
    console.log('  - Auth header:', authHeader ? 'Present' : 'Missing');
    console.log('  - Token:', token ? `Present (${token.substring(0, 20)}...)` : 'Missing');

    if (!token) {
      console.log('  - Result: No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // 检查是否使用真实的 Supabase 还是模拟认证
    const useRealSupabase = process.env.USE_REAL_SUPABASE === 'true';
    console.log('  - Use real Supabase:', useRealSupabase);
    console.log('  - JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Not set');
    let user = null;

    if (useRealSupabase) {
      // 使用真实的 JWT 验证
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        user = {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role || 'user'
        };
      } catch (jwtError) {
        return res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
      }
    } else {
      // 使用模拟Supabase验证token
      console.log('  - Using mock Supabase auth');
      const { data: { user: mockUser }, error } = await mockSupabaseAuth.getUser(token);

      console.log('  - Mock auth result:', error ? `Error: ${error.message}` : 'Success');
      console.log('  - Mock user:', mockUser ? `Found (${mockUser.email})` : 'Not found');

      if (error || !mockUser) {
        console.log('  - Result: Mock auth failed');
        return res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
      }

      user = mockUser;
    }

    // 获取用户详细信息
    try {
      const userProfile = await authService.getUser(user.id);
      req.user = {
        ...user,
        ...userProfile
      };
    } catch (profileError) {
      // 如果获取用户资料失败，使用基本用户信息
      req.user = user;
    }

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// 可选认证 - 如果有token则验证，没有则继续
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (!error && user) {
        try {
          const userProfile = await authService.getUser(user.id);
          req.user = {
            ...user,
            ...userProfile
          };
        } catch (profileError) {
          req.user = user;
        }
      }
    }

    next();
  } catch (error) {
    // 忽略认证错误，继续处理请求
    next();
  }
};

// 检查用户角色
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// 检查资源所有权
const requireOwnership = (getResourceUserId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // 管理员可以访问所有资源
      if (req.user.role === 'admin') {
        return next();
      }

      const resourceUserId = await getResourceUserId(req);
      
      if (req.user.id !== resourceUserId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied - not resource owner'
        });
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Authorization error'
      });
    }
  };
};

// 验证会话并刷新token
const refreshSession = async (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token required'
      });
    }

    const { data, error } = await supabase.auth.refreshSession({
      refresh_token
    });

    if (error) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    res.json({
      success: true,
      message: 'Session refreshed successfully',
      session: data.session,
      user: data.user
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    return res.status(500).json({
      success: false,
      message: 'Session refresh failed'
    });
  }
};

// 验证邮箱确认token
const verifyEmailToken = async (req, res) => {
  try {
    const { token_hash, type } = req.query;

    if (!token_hash || type !== 'email') {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification parameters'
      });
    }

    const { data, error } = await supabase.auth.verifyOtp({
      token_hash,
      type: 'email'
    });

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Email verification failed'
      });
    }

    // 更新用户邮箱验证状态
    if (data.user) {
      await supabase
        .from('users')
        .update({ email_verified_at: new Date().toISOString() })
        .eq('id', data.user.id);
    }

    res.json({
      success: true,
      message: 'Email verified successfully',
      user: data.user
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Email verification failed'
    });
  }
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireRole,
  requireOwnership,
  refreshSession,
  verifyEmailToken
};
