'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建草稿表
    await queryInterface.createTable('Drafts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      title: {
        type: Sequelize.STRING(500),
        allowNull: false,
        defaultValue: '未命名草稿'
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      contentHtml: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      excerpt: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      category: {
        type: Sequelize.ENUM('technology', 'business', 'health', 'sports', 'entertainment', 'politics', 'science', 'lifestyle'),
        allowNull: true
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      featuredImage: {
        type: Sequelize.STRING,
        allowNull: true
      },
      authorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      status: {
        type: Sequelize.ENUM('draft', 'auto_saved', 'ready_for_review', 'under_review', 'approved', 'rejected', 'published'),
        allowNull: false,
        defaultValue: 'draft'
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1
      },
      parentDraftId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Drafts',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      publishedArticleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Articles',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      wordCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      readingTime: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      scheduledPublishAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      lastAutoSaveAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 创建媒体文件表
    await queryInterface.createTable('MediaFiles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      filename: {
        type: Sequelize.STRING,
        allowNull: false
      },
      originalName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      mimeType: {
        type: Sequelize.STRING,
        allowNull: false
      },
      fileType: {
        type: Sequelize.ENUM('image', 'video', 'audio', 'document'),
        allowNull: false
      },
      size: {
        type: Sequelize.BIGINT,
        allowNull: false
      },
      path: {
        type: Sequelize.STRING,
        allowNull: false
      },
      url: {
        type: Sequelize.STRING,
        allowNull: false
      },
      thumbnailUrl: {
        type: Sequelize.STRING,
        allowNull: true
      },
      duration: {
        type: Sequelize.FLOAT,
        allowNull: true
      },
      dimensions: {
        type: Sequelize.JSON,
        allowNull: true
      },
      uploaderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      folder: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'uploads'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      altText: {
        type: Sequelize.STRING,
        allowNull: true
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      isPublic: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      status: {
        type: Sequelize.ENUM('uploading', 'processing', 'ready', 'error', 'deleted'),
        allowNull: false,
        defaultValue: 'uploading'
      },
      processingInfo: {
        type: Sequelize.JSON,
        allowNull: true
      },
      viewCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      downloadCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 创建内容审核表
    await queryInterface.createTable('ContentReviews', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      contentType: {
        type: Sequelize.ENUM('article', 'draft', 'comment', 'media_file', 'user_profile'),
        allowNull: false
      },
      contentId: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      submitterId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      reviewerId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      status: {
        type: Sequelize.ENUM('pending', 'in_review', 'approved', 'rejected', 'needs_revision', 'escalated'),
        allowNull: false,
        defaultValue: 'pending'
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'normal'
      },
      riskLevel: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'critical'),
        allowNull: false,
        defaultValue: 'low'
      },
      aiFlags: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      aiScore: {
        type: Sequelize.FLOAT,
        allowNull: true
      },
      reviewNotes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      rejectionReason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      suggestions: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      autoApproved: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      escalationReason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      reviewStartedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      reviewCompletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 创建审核规则表
    await queryInterface.createTable('ReviewRules', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      contentType: {
        type: Sequelize.ENUM('article', 'draft', 'comment', 'media_file', 'user_profile', 'all'),
        allowNull: false,
        defaultValue: 'all'
      },
      ruleType: {
        type: Sequelize.ENUM('keyword', 'pattern', 'ai_model', 'user_behavior', 'content_length', 'custom'),
        allowNull: false
      },
      conditions: {
        type: Sequelize.JSON,
        allowNull: false
      },
      action: {
        type: Sequelize.ENUM('auto_approve', 'auto_reject', 'flag_for_review', 'escalate', 'quarantine'),
        allowNull: false,
        defaultValue: 'flag_for_review'
      },
      severity: {
        type: Sequelize.ENUM('info', 'warning', 'error', 'critical'),
        allowNull: false,
        defaultValue: 'warning'
      },
      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 100
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      triggerCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      lastTriggeredAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      updatedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('Drafts', ['authorId']);
    await queryInterface.addIndex('Drafts', ['status']);
    await queryInterface.addIndex('Drafts', ['parentDraftId']);
    await queryInterface.addIndex('Drafts', ['publishedArticleId']);
    await queryInterface.addIndex('Drafts', ['isDeleted']);

    await queryInterface.addIndex('MediaFiles', ['uploaderId']);
    await queryInterface.addIndex('MediaFiles', ['fileType']);
    await queryInterface.addIndex('MediaFiles', ['folder']);
    await queryInterface.addIndex('MediaFiles', ['isPublic']);
    await queryInterface.addIndex('MediaFiles', ['status']);
    await queryInterface.addIndex('MediaFiles', ['isDeleted']);

    await queryInterface.addIndex('ContentReviews', ['contentType', 'contentId']);
    await queryInterface.addIndex('ContentReviews', ['submitterId']);
    await queryInterface.addIndex('ContentReviews', ['reviewerId']);
    await queryInterface.addIndex('ContentReviews', ['status']);
    await queryInterface.addIndex('ContentReviews', ['priority']);
    await queryInterface.addIndex('ContentReviews', ['riskLevel']);

    await queryInterface.addIndex('ReviewRules', ['contentType']);
    await queryInterface.addIndex('ReviewRules', ['ruleType']);
    await queryInterface.addIndex('ReviewRules', ['isActive']);
    await queryInterface.addIndex('ReviewRules', ['priority']);
    await queryInterface.addIndex('ReviewRules', ['createdBy']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ReviewRules');
    await queryInterface.dropTable('ContentReviews');
    await queryInterface.dropTable('MediaFiles');
    await queryInterface.dropTable('Drafts');
  }
};
