'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // User Behaviors indexes
    await queryInterface.addIndex('user_behaviors', ['userId']);
    await queryInterface.addIndex('user_behaviors', ['sessionId']);
    await queryInterface.addIndex('user_behaviors', ['eventType']);
    await queryInterface.addIndex('user_behaviors', ['targetType', 'targetId']);
    await queryInterface.addIndex('user_behaviors', ['createdAt']);
    await queryInterface.addIndex('user_behaviors', ['userId', 'eventType']);
    await queryInterface.addIndex('user_behaviors', ['sessionId', 'eventType']);
    await queryInterface.addIndex('user_behaviors', ['eventType', 'createdAt']);
    await queryInterface.addIndex('user_behaviors', ['deviceType']);
    await queryInterface.addIndex('user_behaviors', ['ipAddress']);

    // Reading Stats indexes
    await queryInterface.addIndex('reading_stats', ['articleId']);
    await queryInterface.addIndex('reading_stats', ['userId']);
    await queryInterface.addIndex('reading_stats', ['sessionId']);
    await queryInterface.addIndex('reading_stats', ['startTime']);
    await queryInterface.addIndex('reading_stats', ['isCompleted']);
    await queryInterface.addIndex('reading_stats', ['articleId', 'userId']);
    await queryInterface.addIndex('reading_stats', ['articleId', 'isCompleted']);
    await queryInterface.addIndex('reading_stats', ['createdAt']);
    await queryInterface.addIndex('reading_stats', ['deviceType']);
    await queryInterface.addIndex('reading_stats', ['engagementScore']);

    // Search Logs indexes
    await queryInterface.addIndex('search_logs', ['userId']);
    await queryInterface.addIndex('search_logs', ['sessionId']);
    await queryInterface.addIndex('search_logs', ['query']);
    await queryInterface.addIndex('search_logs', ['normalizedQuery']);
    await queryInterface.addIndex('search_logs', ['category']);
    await queryInterface.addIndex('search_logs', ['hasClicked']);
    await queryInterface.addIndex('search_logs', ['isSuccessful']);
    await queryInterface.addIndex('search_logs', ['createdAt']);
    await queryInterface.addIndex('search_logs', ['deviceType']);
    await queryInterface.addIndex('search_logs', ['searchType']);
    await queryInterface.addIndex('search_logs', ['normalizedQuery', 'createdAt']);

    // User Profiles indexes
    await queryInterface.addIndex('user_profiles', ['userId']);
    await queryInterface.addIndex('user_profiles', ['userSegment']);
    await queryInterface.addIndex('user_profiles', ['engagementScore']);
    await queryInterface.addIndex('user_profiles', ['lastActiveAt']);
    await queryInterface.addIndex('user_profiles', ['activeDays']);
    await queryInterface.addIndex('user_profiles', ['trustScore']);
    await queryInterface.addIndex('user_profiles', ['lastUpdatedAt']);
  },

  async down(queryInterface, Sequelize) {
    // Remove User Behaviors indexes
    await queryInterface.removeIndex('user_behaviors', ['userId']);
    await queryInterface.removeIndex('user_behaviors', ['sessionId']);
    await queryInterface.removeIndex('user_behaviors', ['eventType']);
    await queryInterface.removeIndex('user_behaviors', ['targetType', 'targetId']);
    await queryInterface.removeIndex('user_behaviors', ['createdAt']);
    await queryInterface.removeIndex('user_behaviors', ['userId', 'eventType']);
    await queryInterface.removeIndex('user_behaviors', ['sessionId', 'eventType']);
    await queryInterface.removeIndex('user_behaviors', ['eventType', 'createdAt']);
    await queryInterface.removeIndex('user_behaviors', ['deviceType']);
    await queryInterface.removeIndex('user_behaviors', ['ipAddress']);

    // Remove Reading Stats indexes
    await queryInterface.removeIndex('reading_stats', ['articleId']);
    await queryInterface.removeIndex('reading_stats', ['userId']);
    await queryInterface.removeIndex('reading_stats', ['sessionId']);
    await queryInterface.removeIndex('reading_stats', ['startTime']);
    await queryInterface.removeIndex('reading_stats', ['isCompleted']);
    await queryInterface.removeIndex('reading_stats', ['articleId', 'userId']);
    await queryInterface.removeIndex('reading_stats', ['articleId', 'isCompleted']);
    await queryInterface.removeIndex('reading_stats', ['createdAt']);
    await queryInterface.removeIndex('reading_stats', ['deviceType']);
    await queryInterface.removeIndex('reading_stats', ['engagementScore']);

    // Remove Search Logs indexes
    await queryInterface.removeIndex('search_logs', ['userId']);
    await queryInterface.removeIndex('search_logs', ['sessionId']);
    await queryInterface.removeIndex('search_logs', ['query']);
    await queryInterface.removeIndex('search_logs', ['normalizedQuery']);
    await queryInterface.removeIndex('search_logs', ['category']);
    await queryInterface.removeIndex('search_logs', ['hasClicked']);
    await queryInterface.removeIndex('search_logs', ['isSuccessful']);
    await queryInterface.removeIndex('search_logs', ['createdAt']);
    await queryInterface.removeIndex('search_logs', ['deviceType']);
    await queryInterface.removeIndex('search_logs', ['searchType']);
    await queryInterface.removeIndex('search_logs', ['normalizedQuery', 'createdAt']);

    // Remove User Profiles indexes
    await queryInterface.removeIndex('user_profiles', ['userId']);
    await queryInterface.removeIndex('user_profiles', ['userSegment']);
    await queryInterface.removeIndex('user_profiles', ['engagementScore']);
    await queryInterface.removeIndex('user_profiles', ['lastActiveAt']);
    await queryInterface.removeIndex('user_profiles', ['activeDays']);
    await queryInterface.removeIndex('user_profiles', ['trustScore']);
    await queryInterface.removeIndex('user_profiles', ['lastUpdatedAt']);
  }
};
