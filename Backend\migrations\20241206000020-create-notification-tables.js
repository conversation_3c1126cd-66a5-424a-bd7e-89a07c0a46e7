'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建通知表
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM(
          'like', 'comment', 'follow', 'message', 'article_published',
          'article_approved', 'article_rejected', 'system', 'promotion',
          'reminder', 'security', 'newsletter'
        ),
        allowNull: false
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      data: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      isRead: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      readAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        defaultValue: 'normal'
      },
      channels: {
        type: Sequelize.ARRAY(Sequelize.ENUM('web', 'email', 'push')),
        defaultValue: ['web']
      },
      scheduledAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      sentAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      expiresAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      actionUrl: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      imageUrl: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // 创建通知偏好设置表
    await queryInterface.createTable('notification_preferences', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      preferences: {
        type: Sequelize.JSONB,
        defaultValue: {
          like: { web: true, email: false, push: true },
          comment: { web: true, email: true, push: true },
          follow: { web: true, email: true, push: true },
          message: { web: true, email: true, push: true },
          article_published: { web: true, email: false, push: false },
          article_approved: { web: true, email: true, push: true },
          article_rejected: { web: true, email: true, push: false },
          system: { web: true, email: true, push: true },
          promotion: { web: false, email: false, push: false },
          reminder: { web: true, email: false, push: true },
          security: { web: true, email: true, push: true },
          newsletter: { web: false, email: true, push: false }
        }
      },
      globalSettings: {
        type: Sequelize.JSONB,
        defaultValue: {
          enableWeb: true,
          enableEmail: true,
          enablePush: true,
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '08:00',
            timezone: 'Asia/Shanghai'
          },
          frequency: {
            email: 'immediate',
            push: 'immediate'
          },
          language: 'zh-CN'
        }
      },
      emailSettings: {
        type: Sequelize.JSONB,
        defaultValue: {
          verified: false,
          unsubscribeToken: null,
          bounced: false,
          lastBounceAt: null
        }
      },
      pushSettings: {
        type: Sequelize.JSONB,
        defaultValue: {
          enabled: false,
          subscriptions: []
        }
      },
      lastUpdated: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // 创建推送订阅表
    await queryInterface.createTable('push_subscriptions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      endpoint: {
        type: Sequelize.TEXT,
        allowNull: false,
        unique: true
      },
      p256dh: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      auth: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      deviceInfo: {
        type: Sequelize.JSONB,
        defaultValue: {}
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      lastUsed: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      failureCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      lastFailureAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      lastFailureReason: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('push_subscriptions');
    await queryInterface.dropTable('notification_preferences');
    await queryInterface.dropTable('notifications');
  }
};
