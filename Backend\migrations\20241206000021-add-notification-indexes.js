'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 通知表索引
    await queryInterface.addIndex('notifications', ['userId'], {
      name: 'idx_notifications_user_id'
    });
    
    await queryInterface.addIndex('notifications', ['type'], {
      name: 'idx_notifications_type'
    });
    
    await queryInterface.addIndex('notifications', ['isRead'], {
      name: 'idx_notifications_is_read'
    });
    
    await queryInterface.addIndex('notifications', ['priority'], {
      name: 'idx_notifications_priority'
    });
    
    await queryInterface.addIndex('notifications', ['scheduledAt'], {
      name: 'idx_notifications_scheduled_at'
    });
    
    await queryInterface.addIndex('notifications', ['createdAt'], {
      name: 'idx_notifications_created_at'
    });
    
    await queryInterface.addIndex('notifications', ['userId', 'isRead'], {
      name: 'idx_notifications_user_read'
    });
    
    await queryInterface.addIndex('notifications', ['userId', 'type'], {
      name: 'idx_notifications_user_type'
    });
    
    await queryInterface.addIndex('notifications', ['expiresAt'], {
      name: 'idx_notifications_expires_at'
    });

    // 通知偏好设置表索引
    await queryInterface.addIndex('notification_preferences', ['userId'], {
      name: 'idx_notification_preferences_user_id',
      unique: true
    });

    // 推送订阅表索引
    await queryInterface.addIndex('push_subscriptions', ['userId'], {
      name: 'idx_push_subscriptions_user_id'
    });
    
    await queryInterface.addIndex('push_subscriptions', ['endpoint'], {
      name: 'idx_push_subscriptions_endpoint',
      unique: true
    });
    
    await queryInterface.addIndex('push_subscriptions', ['isActive'], {
      name: 'idx_push_subscriptions_is_active'
    });
    
    await queryInterface.addIndex('push_subscriptions', ['lastUsed'], {
      name: 'idx_push_subscriptions_last_used'
    });
    
    await queryInterface.addIndex('push_subscriptions', ['userId', 'isActive'], {
      name: 'idx_push_subscriptions_user_active'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 删除通知表索引
    await queryInterface.removeIndex('notifications', 'idx_notifications_user_id');
    await queryInterface.removeIndex('notifications', 'idx_notifications_type');
    await queryInterface.removeIndex('notifications', 'idx_notifications_is_read');
    await queryInterface.removeIndex('notifications', 'idx_notifications_priority');
    await queryInterface.removeIndex('notifications', 'idx_notifications_scheduled_at');
    await queryInterface.removeIndex('notifications', 'idx_notifications_created_at');
    await queryInterface.removeIndex('notifications', 'idx_notifications_user_read');
    await queryInterface.removeIndex('notifications', 'idx_notifications_user_type');
    await queryInterface.removeIndex('notifications', 'idx_notifications_expires_at');

    // 删除通知偏好设置表索引
    await queryInterface.removeIndex('notification_preferences', 'idx_notification_preferences_user_id');

    // 删除推送订阅表索引
    await queryInterface.removeIndex('push_subscriptions', 'idx_push_subscriptions_user_id');
    await queryInterface.removeIndex('push_subscriptions', 'idx_push_subscriptions_endpoint');
    await queryInterface.removeIndex('push_subscriptions', 'idx_push_subscriptions_is_active');
    await queryInterface.removeIndex('push_subscriptions', 'idx_push_subscriptions_last_used');
    await queryInterface.removeIndex('push_subscriptions', 'idx_push_subscriptions_user_active');
  }
};
