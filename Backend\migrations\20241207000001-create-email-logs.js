'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('email_logs', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: '关联的用户ID（如果适用）'
      },
      recipient: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '收件人邮箱地址'
      },
      sender: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '发件人邮箱地址'
      },
      subject: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '邮件主题'
      },
      template: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '使用的邮件模板名称'
      },
      templateData: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {},
        comment: '模板渲染数据'
      },
      status: {
        type: Sequelize.ENUM(
          'pending',
          'sent',
          'delivered',
          'opened',
          'clicked',
          'bounced',
          'failed',
          'spam',
          'unsubscribed'
        ),
        allowNull: false,
        defaultValue: 'pending',
        comment: '邮件状态'
      },
      provider: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: '邮件服务提供商（smtp, sendgrid等）'
      },
      messageId: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
        comment: '邮件服务商返回的消息ID'
      },
      providerResponse: {
        type: Sequelize.JSONB,
        allowNull: true,
        comment: '邮件服务商的响应数据'
      },
      errorMessage: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '错误信息（如果发送失败）'
      },
      retryCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '重试次数'
      },
      maxRetries: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 3,
        comment: '最大重试次数'
      },
      scheduledAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '计划发送时间'
      },
      sentAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '实际发送时间'
      },
      deliveredAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '送达时间'
      },
      openedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '首次打开时间'
      },
      clickedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '首次点击时间'
      },
      bouncedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '退回时间'
      },
      bounceReason: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '退回原因'
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'normal',
        comment: '邮件优先级'
      },
      tags: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: [],
        comment: '邮件标签'
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {},
        comment: '额外的元数据'
      },
      ipAddress: {
        type: Sequelize.INET,
        allowNull: true,
        comment: '发送请求的IP地址'
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '发送请求的用户代理'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes
    await queryInterface.addIndex('email_logs', ['userId'], {
      name: 'email_logs_user_id_idx'
    });

    await queryInterface.addIndex('email_logs', ['recipient'], {
      name: 'email_logs_recipient_idx'
    });

    await queryInterface.addIndex('email_logs', ['status'], {
      name: 'email_logs_status_idx'
    });

    await queryInterface.addIndex('email_logs', ['template'], {
      name: 'email_logs_template_idx'
    });

    await queryInterface.addIndex('email_logs', ['messageId'], {
      name: 'email_logs_message_id_idx',
      unique: true
    });

    await queryInterface.addIndex('email_logs', ['sentAt'], {
      name: 'email_logs_sent_at_idx'
    });

    await queryInterface.addIndex('email_logs', ['createdAt'], {
      name: 'email_logs_created_at_idx'
    });

    await queryInterface.addIndex('email_logs', ['status', 'scheduledAt'], {
      name: 'email_logs_status_scheduled_idx'
    });

    await queryInterface.addIndex('email_logs', ['provider', 'status'], {
      name: 'email_logs_provider_status_idx'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Drop indexes first
    await queryInterface.removeIndex('email_logs', 'email_logs_user_id_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_recipient_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_status_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_template_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_message_id_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_sent_at_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_created_at_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_status_scheduled_idx');
    await queryInterface.removeIndex('email_logs', 'email_logs_provider_status_idx');

    // Drop table
    await queryInterface.dropTable('email_logs');
  }
};
