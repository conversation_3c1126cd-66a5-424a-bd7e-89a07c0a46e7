// 最简化的测试服务器
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

console.log('Starting minimal server...');

// 基础中间件
app.use(cors());
app.use(express.json());

// 简单的健康检查
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Minimal server is running'
  });
});

// 根路径
app.get('/', (req, res) => {
  console.log('Root path requested');
  res.json({
    message: 'Newzora Minimal Server',
    status: 'running',
    port: PORT
  });
});

// 网络测试
app.get('/test', (req, res) => {
  console.log('Test endpoint requested');
  res.json({
    success: true,
    message: 'Network test successful',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
try {
  const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Minimal server running on port ${PORT}`);
    console.log(`🌐 Access URLs:`);
    console.log(`   http://localhost:${PORT}`);
    console.log(`   http://127.0.0.1:${PORT}`);
    console.log(`🔗 Test endpoints:`);
    console.log(`   http://localhost:${PORT}/api/health`);
    console.log(`   http://localhost:${PORT}/test`);
  });

  server.on('error', (error) => {
    console.error('❌ Server error:', error.message);
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use`);
    }
  });

} catch (error) {
  console.error('❌ Failed to start server:', error.message);
}
