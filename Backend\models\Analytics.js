const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Analytics = sequelize.define('Analytics', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  eventType: {
    type: DataTypes.ENUM(
      'page_view',      // 页面浏览
      'article_view',   // 文章浏览
      'article_read',   // 文章阅读完成
      'user_login',     // 用户登录
      'user_register',  // 用户注册
      'search',         // 搜索
      'click',          // 点击
      'share',          // 分享
      'download',       // 下载
      'error',          // 错误
      'performance',    // 性能指标
      'custom'          // 自定义事件
    ),
    allowNull: false,
    comment: '事件类型'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID（可为空，支持匿名用户）'
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '会话ID'
  },
  targetType: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '目标对象类型'
  },
  targetId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '目标对象ID'
  },
  properties: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '事件属性'
  },
  value: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: '数值（如阅读时长、响应时间等）'
  },
  url: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '页面URL'
  },
  referer: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '来源页面'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理'
  },
  ipAddress: {
    type: DataTypes.INET,
    allowNull: true,
    comment: 'IP地址'
  },
  location: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '地理位置信息'
  },
  device: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '设备信息'
  },
  browser: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '浏览器信息'
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '事件时间戳'
  }
}, {
  tableName: 'analytics',
  timestamps: true,
  indexes: [
    {
      fields: ['eventType']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['sessionId']
    },
    {
      fields: ['targetType', 'targetId']
    },
    {
      fields: ['timestamp']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['eventType', 'timestamp'],
      name: 'analytics_event_time_idx'
    },
    {
      fields: ['userId', 'timestamp'],
      name: 'analytics_user_time_idx'
    },
    {
      fields: ['eventType', 'userId', 'timestamp'],
      name: 'analytics_event_user_time_idx'
    }
  ]
});

// Static methods
Analytics.trackEvent = function(eventData) {
  return this.create({
    eventType: eventData.eventType,
    userId: eventData.userId || null,
    sessionId: eventData.sessionId || null,
    targetType: eventData.targetType || null,
    targetId: eventData.targetId || null,
    properties: eventData.properties || {},
    value: eventData.value || null,
    url: eventData.url || null,
    referer: eventData.referer || null,
    userAgent: eventData.userAgent || null,
    ipAddress: eventData.ipAddress || null,
    location: eventData.location || null,
    device: eventData.device || null,
    browser: eventData.browser || null,
    timestamp: eventData.timestamp || new Date()
  });
};

Analytics.getEventCounts = function(eventType, startDate, endDate, groupBy = 'day') {
  const dateFormat = {
    hour: 'YYYY-MM-DD HH24:00:00',
    day: 'YYYY-MM-DD',
    week: 'YYYY-"W"WW',
    month: 'YYYY-MM'
  };

  return sequelize.query(`
    SELECT 
      TO_CHAR(timestamp, :dateFormat) as period,
      COUNT(*) as count,
      COUNT(DISTINCT user_id) as unique_users,
      COUNT(DISTINCT session_id) as unique_sessions
    FROM analytics 
    WHERE event_type = :eventType
      AND timestamp BETWEEN :startDate AND :endDate
    GROUP BY TO_CHAR(timestamp, :dateFormat)
    ORDER BY period
  `, {
    replacements: { 
      eventType, 
      startDate, 
      endDate, 
      dateFormat: dateFormat[groupBy] || dateFormat.day 
    },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getUserBehaviorAnalysis = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      event_type,
      COUNT(*) as event_count,
      AVG(value) as avg_value,
      MIN(timestamp) as first_event,
      MAX(timestamp) as last_event,
      COUNT(DISTINCT DATE(timestamp)) as active_days
    FROM analytics 
    WHERE user_id = :userId 
      AND timestamp >= :startDate
    GROUP BY event_type
    ORDER BY event_count DESC
  `, {
    replacements: { userId, startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getContentPerformance = function(targetType, days = 30, limit = 20) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      target_id,
      COUNT(*) as total_events,
      COUNT(CASE WHEN event_type = 'article_view' THEN 1 END) as views,
      COUNT(CASE WHEN event_type = 'article_read' THEN 1 END) as reads,
      COUNT(CASE WHEN event_type = 'share' THEN 1 END) as shares,
      COUNT(DISTINCT user_id) as unique_users,
      AVG(CASE WHEN event_type = 'article_read' THEN value END) as avg_read_time
    FROM analytics 
    WHERE target_type = :targetType
      AND timestamp >= :startDate
    GROUP BY target_id
    ORDER BY total_events DESC
    LIMIT :limit
  `, {
    replacements: { targetType, startDate, limit },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getTrafficSources = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      CASE 
        WHEN referer IS NULL OR referer = '' THEN 'Direct'
        WHEN referer LIKE '%google%' THEN 'Google'
        WHEN referer LIKE '%facebook%' THEN 'Facebook'
        WHEN referer LIKE '%twitter%' THEN 'Twitter'
        WHEN referer LIKE '%linkedin%' THEN 'LinkedIn'
        ELSE 'Other'
      END as source,
      COUNT(*) as sessions,
      COUNT(DISTINCT user_id) as unique_users
    FROM analytics 
    WHERE event_type = 'page_view'
      AND timestamp >= :startDate
    GROUP BY source
    ORDER BY sessions DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getDeviceStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      device->>'type' as device_type,
      device->>'os' as operating_system,
      browser->>'name' as browser_name,
      COUNT(*) as sessions,
      COUNT(DISTINCT user_id) as unique_users
    FROM analytics 
    WHERE event_type = 'page_view'
      AND timestamp >= :startDate
      AND device IS NOT NULL
    GROUP BY device->>'type', device->>'os', browser->>'name'
    ORDER BY sessions DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getGeographicStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      location->>'country' as country,
      location->>'city' as city,
      COUNT(*) as sessions,
      COUNT(DISTINCT user_id) as unique_users
    FROM analytics 
    WHERE event_type = 'page_view'
      AND timestamp >= :startDate
      AND location IS NOT NULL
    GROUP BY location->>'country', location->>'city'
    ORDER BY sessions DESC
    LIMIT 50
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getPerformanceMetrics = function(days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    SELECT 
      DATE(timestamp) as date,
      AVG(CASE WHEN properties->>'metric' = 'page_load_time' THEN value END) as avg_page_load_time,
      AVG(CASE WHEN properties->>'metric' = 'api_response_time' THEN value END) as avg_api_response_time,
      COUNT(CASE WHEN event_type = 'error' THEN 1 END) as error_count,
      COUNT(*) as total_events
    FROM analytics 
    WHERE timestamp >= :startDate
    GROUP BY DATE(timestamp)
    ORDER BY date
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getFunnelAnalysis = function(funnelSteps, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // 构建漏斗分析查询
  const stepConditions = funnelSteps.map((step, index) => 
    `COUNT(CASE WHEN event_type = '${step}' THEN 1 END) as step_${index + 1}_${step}`
  ).join(', ');

  return sequelize.query(`
    SELECT 
      user_id,
      ${stepConditions}
    FROM analytics 
    WHERE timestamp >= :startDate
      AND event_type IN (:funnelSteps)
    GROUP BY user_id
    HAVING COUNT(DISTINCT event_type) > 1
  `, {
    replacements: { startDate, funnelSteps },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getCohortAnalysis = function(cohortType = 'weekly', periods = 12) {
  return sequelize.query(`
    WITH cohorts AS (
      SELECT 
        user_id,
        DATE_TRUNC(:cohortType, MIN(timestamp)) as cohort_period
      FROM analytics 
      WHERE event_type = 'user_register'
      GROUP BY user_id
    ),
    user_activities AS (
      SELECT 
        a.user_id,
        c.cohort_period,
        DATE_TRUNC(:cohortType, a.timestamp) as activity_period
      FROM analytics a
      JOIN cohorts c ON a.user_id = c.user_id
      WHERE a.event_type = 'page_view'
    )
    SELECT 
      cohort_period,
      activity_period,
      COUNT(DISTINCT user_id) as active_users
    FROM user_activities
    WHERE activity_period >= cohort_period
      AND activity_period <= cohort_period + INTERVAL ':periods ${cohortType}s'
    GROUP BY cohort_period, activity_period
    ORDER BY cohort_period, activity_period
  `, {
    replacements: { cohortType, periods },
    type: sequelize.QueryTypes.SELECT
  });
};

Analytics.getRetentionAnalysis = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return sequelize.query(`
    WITH first_visits AS (
      SELECT 
        user_id,
        DATE(MIN(timestamp)) as first_visit_date
      FROM analytics 
      WHERE timestamp >= :startDate
        AND user_id IS NOT NULL
      GROUP BY user_id
    ),
    return_visits AS (
      SELECT 
        fv.user_id,
        fv.first_visit_date,
        DATE(a.timestamp) as visit_date,
        DATE(a.timestamp) - fv.first_visit_date as days_since_first_visit
      FROM first_visits fv
      JOIN analytics a ON fv.user_id = a.user_id
      WHERE a.timestamp >= :startDate
        AND DATE(a.timestamp) > fv.first_visit_date
    )
    SELECT 
      first_visit_date,
      COUNT(DISTINCT user_id) as cohort_size,
      COUNT(DISTINCT CASE WHEN days_since_first_visit = 1 THEN user_id END) as day_1_retention,
      COUNT(DISTINCT CASE WHEN days_since_first_visit = 7 THEN user_id END) as day_7_retention,
      COUNT(DISTINCT CASE WHEN days_since_first_visit = 30 THEN user_id END) as day_30_retention
    FROM return_visits
    GROUP BY first_visit_date
    ORDER BY first_visit_date
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

module.exports = Analytics;
