const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ArticleVersion = sequelize.define('ArticleVersion', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  articleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'articles',
      key: 'id'
    },
    comment: '关联的文章ID'
  },
  version: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '版本号'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '文章标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '文章描述'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '文章内容'
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '文章分类'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: '文章标签'
  },
  featuredImage: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '特色图片URL'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '文章元数据'
  },
  changeType: {
    type: DataTypes.ENUM(
      'created',     // 创建
      'updated',     // 更新
      'published',   // 发布
      'unpublished', // 取消发布
      'deleted',     // 删除
      'restored'     // 恢复
    ),
    allowNull: false,
    defaultValue: 'updated',
    comment: '变更类型'
  },
  changeDescription: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '变更描述'
  },
  authorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '作者ID'
  },
  editorId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '编辑者ID'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否为当前活跃版本'
  },
  wordCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '字数统计'
  },
  readingTime: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '预估阅读时间（分钟）'
  },
  seoTitle: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'SEO标题'
  },
  seoDescription: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'SEO描述'
  },
  seoKeywords: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: 'SEO关键词'
  }
}, {
  tableName: 'article_versions',
  timestamps: true,
  indexes: [
    {
      fields: ['articleId']
    },
    {
      fields: ['version']
    },
    {
      fields: ['authorId']
    },
    {
      fields: ['editorId']
    },
    {
      fields: ['changeType']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['articleId', 'version'],
      unique: true,
      name: 'article_versions_article_version_unique'
    },
    {
      fields: ['articleId', 'isActive'],
      name: 'article_versions_article_active_idx'
    }
  ]
});

// Instance methods
ArticleVersion.prototype.activate = async function() {
  const transaction = await sequelize.transaction();
  
  try {
    // 将同一文章的其他版本设为非活跃
    await ArticleVersion.update(
      { isActive: false },
      { 
        where: { 
          articleId: this.articleId,
          id: { [sequelize.Sequelize.Op.ne]: this.id }
        },
        transaction
      }
    );
    
    // 激活当前版本
    await this.update({ isActive: true }, { transaction });
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

ArticleVersion.prototype.calculateReadingTime = function() {
  if (!this.content) return 0;
  
  // 假设平均阅读速度为每分钟200字
  const wordsPerMinute = 200;
  const wordCount = this.content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  const readingTime = Math.ceil(wordCount / wordsPerMinute);
  
  return readingTime;
};

ArticleVersion.prototype.generateSEO = function() {
  if (!this.seoTitle && this.title) {
    this.seoTitle = this.title.substring(0, 60);
  }
  
  if (!this.seoDescription && this.description) {
    this.seoDescription = this.description.substring(0, 160);
  }
  
  if (!this.seoKeywords || this.seoKeywords.length === 0) {
    // 从标题和内容中提取关键词
    const text = `${this.title} ${this.description || ''}`.toLowerCase();
    const words = text.match(/\b\w{3,}\b/g) || [];
    const wordCount = {};
    
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    this.seoKeywords = Object.keys(wordCount)
      .sort((a, b) => wordCount[b] - wordCount[a])
      .slice(0, 10);
  }
};

// Static methods
ArticleVersion.getVersionHistory = function(articleId, options = {}) {
  return this.findAll({
    where: { articleId },
    order: [['version', 'DESC']],
    include: [
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName']
      },
      {
        model: sequelize.models.User,
        as: 'editor',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ],
    ...options
  });
};

ArticleVersion.getActiveVersion = function(articleId) {
  return this.findOne({
    where: { 
      articleId,
      isActive: true 
    },
    include: [
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName']
      },
      {
        model: sequelize.models.User,
        as: 'editor',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

ArticleVersion.createVersion = async function(articleId, versionData, userId) {
  const transaction = await sequelize.transaction();
  
  try {
    // 获取最新版本号
    const latestVersion = await this.findOne({
      where: { articleId },
      order: [['version', 'DESC']],
      transaction
    });
    
    const nextVersion = latestVersion ? latestVersion.version + 1 : 1;
    
    // 计算字数和阅读时间
    const wordCount = versionData.content ? 
      versionData.content.replace(/<[^>]*>/g, '').split(/\s+/).length : 0;
    const readingTime = Math.ceil(wordCount / 200);
    
    // 创建新版本
    const newVersion = await this.create({
      ...versionData,
      articleId,
      version: nextVersion,
      editorId: userId,
      wordCount,
      readingTime,
      isActive: false
    }, { transaction });
    
    // 生成SEO信息
    newVersion.generateSEO();
    await newVersion.save({ transaction });
    
    await transaction.commit();
    return newVersion;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

ArticleVersion.compareVersions = function(version1Id, version2Id) {
  return Promise.all([
    this.findByPk(version1Id),
    this.findByPk(version2Id)
  ]).then(([v1, v2]) => {
    if (!v1 || !v2) {
      throw new Error('One or both versions not found');
    }
    
    return {
      version1: v1,
      version2: v2,
      differences: {
        title: v1.title !== v2.title,
        description: v1.description !== v2.description,
        content: v1.content !== v2.content,
        category: v1.category !== v2.category,
        tags: JSON.stringify(v1.tags) !== JSON.stringify(v2.tags),
        featuredImage: v1.featuredImage !== v2.featuredImage
      }
    };
  });
};

module.exports = ArticleVersion;
