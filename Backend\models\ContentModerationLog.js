const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ContentModerationLog = sequelize.define('ContentModerationLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  contentType: {
    type: DataTypes.ENUM(
      'article',
      'comment',
      'message',
      'user_profile',
      'image'
    ),
    allowNull: false,
    comment: '内容类型'
  },
  contentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '内容ID'
  },
  ruleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'content_moderation_rules',
      key: 'id'
    },
    comment: '触发的规则ID'
  },
  moderationType: {
    type: DataTypes.ENUM(
      'automatic',  // 自动审核
      'manual',     // 人工审核
      'appeal'      // 申诉处理
    ),
    allowNull: false,
    defaultValue: 'automatic',
    comment: '审核类型'
  },
  action: {
    type: DataTypes.ENUM(
      'approved',
      'rejected',
      'flagged',
      'quarantined',
      'warning_issued',
      'user_notified',
      'escalated'
    ),
    allowNull: false,
    comment: '执行的动作'
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核原因'
  },
  details: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '详细信息'
  },
  confidence: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'AI审核置信度'
  },
  aiModel: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '使用的AI模型'
  },
  moderatorId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '审核员ID（人工审核时）'
  },
  authorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '内容作者ID'
  },
  severity: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    allowNull: false,
    defaultValue: 'medium',
    comment: '严重程度'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',    // 待处理
      'processed',  // 已处理
      'appealed',   // 已申诉
      'overturned', // 已推翻
      'confirmed'   // 已确认
    ),
    allowNull: false,
    defaultValue: 'processed',
    comment: '处理状态'
  },
  appealedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '申诉时间'
  },
  appealReason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '申诉原因'
  },
  reviewedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '复审时间'
  },
  reviewedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '复审员ID'
  },
  originalContent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '原始内容（用于记录）'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外元数据'
  }
}, {
  tableName: 'content_moderation_logs',
  timestamps: true,
  indexes: [
    {
      fields: ['contentType', 'contentId']
    },
    {
      fields: ['ruleId']
    },
    {
      fields: ['moderationType']
    },
    {
      fields: ['action']
    },
    {
      fields: ['moderatorId']
    },
    {
      fields: ['authorId']
    },
    {
      fields: ['severity']
    },
    {
      fields: ['status']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['contentType', 'contentId', 'action'],
      name: 'content_moderation_logs_content_action_idx'
    },
    {
      fields: ['authorId', 'createdAt'],
      name: 'content_moderation_logs_author_time_idx'
    }
  ]
});

// Instance methods
ContentModerationLog.prototype.appeal = function(appealReason, userId = null) {
  return this.update({
    status: 'appealed',
    appealedAt: new Date(),
    appealReason: appealReason,
    reviewedBy: userId
  });
};

ContentModerationLog.prototype.review = function(newAction, reviewReason, reviewerId) {
  const isOverturned = newAction !== this.action;
  
  return this.update({
    status: isOverturned ? 'overturned' : 'confirmed',
    reviewedAt: new Date(),
    reviewedBy: reviewerId,
    metadata: {
      ...this.metadata,
      reviewReason: reviewReason,
      originalAction: this.action,
      ...(isOverturned && { newAction: newAction })
    }
  });
};

// Static methods
ContentModerationLog.logModeration = function(data) {
  return this.create({
    contentType: data.contentType,
    contentId: data.contentId,
    ruleId: data.ruleId || null,
    moderationType: data.moderationType || 'automatic',
    action: data.action,
    reason: data.reason || null,
    details: data.details || {},
    confidence: data.confidence || null,
    aiModel: data.aiModel || null,
    moderatorId: data.moderatorId || null,
    authorId: data.authorId,
    severity: data.severity || 'medium',
    originalContent: data.originalContent || null,
    metadata: data.metadata || {}
  });
};

ContentModerationLog.getContentHistory = function(contentType, contentId) {
  return this.findAll({
    where: { contentType, contentId },
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: sequelize.models.ContentModerationRule,
        as: 'rule',
        attributes: ['id', 'name', 'ruleType', 'action']
      },
      {
        model: sequelize.models.User,
        as: 'moderator',
        attributes: ['id', 'username', 'firstName', 'lastName']
      },
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

ContentModerationLog.getUserModerationHistory = function(userId, options = {}) {
  const { limit = 50, offset = 0, severity, action, startDate, endDate } = options;
  
  const where = { authorId: userId };
  
  if (severity) {
    where.severity = severity;
  }
  
  if (action) {
    where.action = action;
  }
  
  if (startDate && endDate) {
    where.createdAt = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  return this.findAndCountAll({
    where,
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.ContentModerationRule,
        as: 'rule',
        attributes: ['id', 'name', 'ruleType']
      },
      {
        model: sequelize.models.User,
        as: 'moderator',
        attributes: ['id', 'username']
      }
    ]
  });
};

ContentModerationLog.getModerationStats = function(startDate, endDate) {
  return sequelize.query(`
    SELECT 
      action,
      moderation_type,
      severity,
      COUNT(*) as count,
      COUNT(CASE WHEN confidence IS NOT NULL THEN 1 END) as ai_decisions,
      AVG(confidence) as avg_confidence,
      COUNT(CASE WHEN status = 'appealed' THEN 1 END) as appeals,
      COUNT(CASE WHEN status = 'overturned' THEN 1 END) as overturned
    FROM content_moderation_logs 
    WHERE created_at BETWEEN :startDate AND :endDate
    GROUP BY action, moderation_type, severity
    ORDER BY count DESC
  `, {
    replacements: { startDate, endDate },
    type: sequelize.QueryTypes.SELECT
  });
};

ContentModerationLog.getModeratorPerformance = function(startDate, endDate) {
  return sequelize.query(`
    SELECT 
      u.username,
      u.id as moderator_id,
      COUNT(*) as total_reviews,
      COUNT(CASE WHEN cml.action = 'approved' THEN 1 END) as approvals,
      COUNT(CASE WHEN cml.action = 'rejected' THEN 1 END) as rejections,
      COUNT(CASE WHEN cml.status = 'appealed' THEN 1 END) as appeals,
      COUNT(CASE WHEN cml.status = 'overturned' THEN 1 END) as overturned,
      ROUND(
        COUNT(CASE WHEN cml.status = 'overturned' THEN 1 END)::numeric / 
        NULLIF(COUNT(*), 0) * 100, 2
      ) as overturn_rate
    FROM content_moderation_logs cml
    JOIN users u ON u.id = cml.moderator_id
    WHERE cml.moderation_type = 'manual'
      AND cml.created_at BETWEEN :startDate AND :endDate
    GROUP BY u.id, u.username
    ORDER BY total_reviews DESC
  `, {
    replacements: { startDate, endDate },
    type: sequelize.QueryTypes.SELECT
  });
};

ContentModerationLog.getContentTypeStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      content_type,
      COUNT(*) as total_moderations,
      COUNT(CASE WHEN action = 'approved' THEN 1 END) as approved,
      COUNT(CASE WHEN action = 'rejected' THEN 1 END) as rejected,
      COUNT(CASE WHEN action = 'flagged' THEN 1 END) as flagged,
      ROUND(AVG(confidence), 2) as avg_ai_confidence
    FROM content_moderation_logs 
    WHERE created_at >= :startDate
    GROUP BY content_type
    ORDER BY total_moderations DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

ContentModerationLog.getAppealableContent = function(userId) {
  return this.findAll({
    where: {
      authorId: userId,
      action: {
        [sequelize.Sequelize.Op.in]: ['rejected', 'flagged', 'quarantined']
      },
      status: 'processed',
      appealedAt: null
    },
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: sequelize.models.ContentModerationRule,
        as: 'rule',
        attributes: ['id', 'name', 'description']
      }
    ]
  });
};

ContentModerationLog.getPendingAppeals = function(options = {}) {
  const { limit = 20, offset = 0 } = options;
  
  return this.findAndCountAll({
    where: {
      status: 'appealed'
    },
    order: [['appealedAt', 'ASC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName']
      },
      {
        model: sequelize.models.ContentModerationRule,
        as: 'rule',
        attributes: ['id', 'name', 'ruleType']
      }
    ]
  });
};

ContentModerationLog.bulkReview = async function(logIds, action, reason, reviewerId) {
  const transaction = await sequelize.transaction();
  
  try {
    const logs = await this.findAll({
      where: {
        id: {
          [sequelize.Sequelize.Op.in]: logIds
        },
        status: 'appealed'
      },
      transaction
    });
    
    const results = [];
    for (const log of logs) {
      await log.review(action, reason, reviewerId);
      results.push(log);
    }
    
    await transaction.commit();
    return results;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

module.exports = ContentModerationLog;
