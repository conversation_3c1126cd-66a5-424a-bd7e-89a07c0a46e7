const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ContentModerationRule = sequelize.define('ContentModerationRule', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '规则名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '规则描述'
  },
  ruleType: {
    type: DataTypes.ENUM(
      'keyword',        // 关键词过滤
      'regex',          // 正则表达式
      'ai_sentiment',   // AI情感分析
      'ai_toxicity',    // AI毒性检测
      'ai_spam',        // AI垃圾内容检测
      'ai_nsfw',        // AI不当内容检测
      'image_analysis', // 图片分析
      'link_analysis',  // 链接分析
      'length_check',   // 长度检查
      'custom'          // 自定义规则
    ),
    allowNull: false,
    comment: '规则类型'
  },
  targetType: {
    type: DataTypes.ENUM(
      'article',
      'comment',
      'message',
      'user_profile',
      'image',
      'all'
    ),
    allowNull: false,
    defaultValue: 'all',
    comment: '目标类型'
  },
  ruleConfig: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: '规则配置'
  },
  action: {
    type: DataTypes.ENUM(
      'flag',           // 标记
      'auto_reject',    // 自动拒绝
      'require_review', // 需要人工审核
      'auto_approve',   // 自动通过
      'quarantine',     // 隔离
      'warn_user'       // 警告用户
    ),
    allowNull: false,
    defaultValue: 'flag',
    comment: '触发动作'
  },
  severity: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    allowNull: false,
    defaultValue: 'medium',
    comment: '严重程度'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用'
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 100,
    comment: '优先级（数字越小优先级越高）'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '创建者ID'
  },
  lastModifiedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '最后修改者ID'
  },
  triggerCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '触发次数'
  },
  lastTriggered: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后触发时间'
  }
}, {
  tableName: 'content_moderation_rules',
  timestamps: true,
  indexes: [
    {
      fields: ['ruleType']
    },
    {
      fields: ['targetType']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['severity']
    },
    {
      fields: ['createdBy']
    },
    {
      fields: ['isActive', 'priority'],
      name: 'content_moderation_rules_active_priority_idx'
    }
  ]
});

// Instance methods
ContentModerationRule.prototype.incrementTriggerCount = function() {
  return this.update({
    triggerCount: this.triggerCount + 1,
    lastTriggered: new Date()
  });
};

ContentModerationRule.prototype.updateConfig = function(newConfig, userId) {
  return this.update({
    ruleConfig: { ...this.ruleConfig, ...newConfig },
    lastModifiedBy: userId
  });
};

// Static methods
ContentModerationRule.getActiveRules = function(targetType = null) {
  const where = { isActive: true };
  
  if (targetType) {
    where.targetType = {
      [sequelize.Sequelize.Op.in]: [targetType, 'all']
    };
  }
  
  return this.findAll({
    where,
    order: [['priority', 'ASC'], ['createdAt', 'ASC']],
    include: [
      {
        model: sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username']
      }
    ]
  });
};

ContentModerationRule.createKeywordRule = function(name, keywords, action, targetType, userId) {
  return this.create({
    name,
    description: `Keyword filtering rule for: ${keywords.join(', ')}`,
    ruleType: 'keyword',
    targetType,
    ruleConfig: {
      keywords: keywords,
      caseSensitive: false,
      wholeWordOnly: false
    },
    action,
    createdBy: userId
  });
};

ContentModerationRule.createRegexRule = function(name, pattern, action, targetType, userId) {
  return this.create({
    name,
    description: `Regex pattern rule: ${pattern}`,
    ruleType: 'regex',
    targetType,
    ruleConfig: {
      pattern: pattern,
      flags: 'gi'
    },
    action,
    createdBy: userId
  });
};

ContentModerationRule.createAIRule = function(name, aiType, threshold, action, targetType, userId) {
  const aiTypes = {
    sentiment: 'AI sentiment analysis',
    toxicity: 'AI toxicity detection',
    spam: 'AI spam detection',
    nsfw: 'AI NSFW content detection'
  };
  
  return this.create({
    name,
    description: aiTypes[aiType] || 'AI-based content analysis',
    ruleType: `ai_${aiType}`,
    targetType,
    ruleConfig: {
      threshold: threshold,
      model: 'default',
      confidence: 0.8
    },
    action,
    createdBy: userId
  });
};

ContentModerationRule.getRuleStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      rule_type,
      COUNT(*) as rule_count,
      SUM(trigger_count) as total_triggers,
      AVG(trigger_count) as avg_triggers,
      COUNT(CASE WHEN is_active = true THEN 1 END) as active_rules
    FROM content_moderation_rules 
    WHERE created_at >= :startDate
    GROUP BY rule_type
    ORDER BY total_triggers DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

ContentModerationRule.getTopTriggeredRules = function(limit = 10, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    where: {
      lastTriggered: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    order: [['triggerCount', 'DESC']],
    limit,
    include: [
      {
        model: sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username']
      }
    ]
  });
};

ContentModerationRule.bulkUpdateStatus = function(ruleIds, isActive, userId) {
  return this.update(
    { 
      isActive,
      lastModifiedBy: userId
    },
    {
      where: {
        id: {
          [sequelize.Sequelize.Op.in]: ruleIds
        }
      }
    }
  );
};

ContentModerationRule.exportRules = function(ruleType = null) {
  const where = {};
  if (ruleType) {
    where.ruleType = ruleType;
  }
  
  return this.findAll({
    where,
    attributes: [
      'name', 'description', 'ruleType', 'targetType', 
      'ruleConfig', 'action', 'severity', 'priority'
    ],
    order: [['priority', 'ASC']]
  });
};

ContentModerationRule.importRules = async function(rulesData, userId) {
  const transaction = await sequelize.transaction();
  
  try {
    const importedRules = [];
    
    for (const ruleData of rulesData) {
      const rule = await this.create({
        ...ruleData,
        createdBy: userId,
        isActive: false // 导入的规则默认不激活
      }, { transaction });
      
      importedRules.push(rule);
    }
    
    await transaction.commit();
    return importedRules;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

module.exports = ContentModerationRule;
