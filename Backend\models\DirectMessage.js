const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const DirectMessage = sequelize.define('DirectMessage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  conversationId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '对话ID，用于标识两个用户之间的对话'
  },
  senderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '发送者ID'
  },
  receiverId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '接收者ID'
  },
  messageType: {
    type: DataTypes.ENUM(
      'text',      // 文本消息
      'image',     // 图片消息
      'file',      // 文件消息
      'link',      // 链接消息
      'system'     // 系统消息
    ),
    allowNull: false,
    defaultValue: 'text',
    comment: '消息类型'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '消息内容'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '消息元数据（如文件信息、链接预览等）'
  },
  isRead: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已读'
  },
  readAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '阅读时间'
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已删除'
  },
  deletedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '删除时间'
  },
  deletedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '删除者ID'
  },
  replyToId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'direct_messages',
      key: 'id'
    },
    comment: '回复的消息ID'
  },
  editedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '编辑时间'
  },
  originalContent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '原始内容（编辑前）'
  }
}, {
  tableName: 'direct_messages',
  timestamps: true,
  indexes: [
    {
      fields: ['conversationId']
    },
    {
      fields: ['senderId']
    },
    {
      fields: ['receiverId']
    },
    {
      fields: ['isRead']
    },
    {
      fields: ['isDeleted']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['conversationId', 'createdAt'],
      name: 'direct_messages_conversation_time_idx'
    },
    {
      fields: ['receiverId', 'isRead'],
      name: 'direct_messages_receiver_read_idx'
    }
  ]
});

// Instance methods
DirectMessage.prototype.markAsRead = function() {
  return this.update({
    isRead: true,
    readAt: new Date()
  });
};

DirectMessage.prototype.softDelete = function(deletedBy) {
  return this.update({
    isDeleted: true,
    deletedAt: new Date(),
    deletedBy: deletedBy
  });
};

DirectMessage.prototype.editMessage = function(newContent) {
  return this.update({
    originalContent: this.content,
    content: newContent,
    editedAt: new Date()
  });
};

// Static methods
DirectMessage.generateConversationId = function(userId1, userId2) {
  // 确保对话ID的一致性，较小的用户ID在前
  const sortedIds = [userId1, userId2].sort((a, b) => a - b);
  return `conv_${sortedIds[0]}_${sortedIds[1]}`;
};

DirectMessage.getConversation = function(userId1, userId2, options = {}) {
  const conversationId = this.generateConversationId(userId1, userId2);
  
  return this.findAll({
    where: {
      conversationId,
      isDeleted: false
    },
    order: [['createdAt', 'ASC']],
    include: [
      {
        model: sequelize.models.User,
        as: 'sender',
        attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
      },
      {
        model: sequelize.models.User,
        as: 'receiver',
        attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
      },
      {
        model: DirectMessage,
        as: 'replyTo',
        attributes: ['id', 'content', 'senderId'],
        include: [
          {
            model: sequelize.models.User,
            as: 'sender',
            attributes: ['id', 'username']
          }
        ]
      }
    ],
    ...options
  });
};

DirectMessage.getConversationList = function(userId, options = {}) {
  const { limit = 20, offset = 0 } = options;
  
  return sequelize.query(`
    WITH latest_messages AS (
      SELECT DISTINCT ON (conversation_id) 
        conversation_id,
        id,
        sender_id,
        receiver_id,
        content,
        message_type,
        is_read,
        created_at,
        CASE 
          WHEN sender_id = :userId THEN receiver_id 
          ELSE sender_id 
        END as other_user_id
      FROM direct_messages 
      WHERE (sender_id = :userId OR receiver_id = :userId) 
        AND is_deleted = false
      ORDER BY conversation_id, created_at DESC
    )
    SELECT 
      lm.*,
      u.username,
      u."firstName",
      u."lastName",
      u.avatar,
      (SELECT COUNT(*) FROM direct_messages dm 
       WHERE dm.conversation_id = lm.conversation_id 
         AND dm.receiver_id = :userId 
         AND dm.is_read = false 
         AND dm.is_deleted = false) as unread_count
    FROM latest_messages lm
    JOIN users u ON u.id = lm.other_user_id
    ORDER BY lm.created_at DESC
    LIMIT :limit OFFSET :offset
  `, {
    replacements: { userId, limit, offset },
    type: sequelize.QueryTypes.SELECT
  });
};

DirectMessage.getUnreadCount = function(userId) {
  return this.count({
    where: {
      receiverId: userId,
      isRead: false,
      isDeleted: false
    }
  });
};

DirectMessage.markConversationAsRead = function(conversationId, userId) {
  return this.update(
    {
      isRead: true,
      readAt: new Date()
    },
    {
      where: {
        conversationId,
        receiverId: userId,
        isRead: false,
        isDeleted: false
      }
    }
  );
};

DirectMessage.searchMessages = function(userId, query, options = {}) {
  const { limit = 50, offset = 0 } = options;
  
  return this.findAll({
    where: {
      [sequelize.Sequelize.Op.or]: [
        { senderId: userId },
        { receiverId: userId }
      ],
      content: {
        [sequelize.Sequelize.Op.iLike]: `%${query}%`
      },
      isDeleted: false
    },
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'sender',
        attributes: ['id', 'username', 'firstName', 'lastName']
      },
      {
        model: sequelize.models.User,
        as: 'receiver',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

DirectMessage.getMessageStats = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as sent_count,
      COUNT(CASE WHEN sender_id = :userId THEN 1 END) as messages_sent,
      COUNT(CASE WHEN receiver_id = :userId THEN 1 END) as messages_received
    FROM direct_messages 
    WHERE (sender_id = :userId OR receiver_id = :userId)
      AND created_at >= :startDate
      AND is_deleted = false
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `, {
    replacements: { userId, startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

DirectMessage.bulkMarkAsRead = function(messageIds, userId) {
  return this.update(
    {
      isRead: true,
      readAt: new Date()
    },
    {
      where: {
        id: {
          [sequelize.Sequelize.Op.in]: messageIds
        },
        receiverId: userId,
        isDeleted: false
      }
    }
  );
};

DirectMessage.deleteConversation = function(conversationId, userId) {
  return this.update(
    {
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: userId
    },
    {
      where: {
        conversationId,
        [sequelize.Sequelize.Op.or]: [
          { senderId: userId },
          { receiverId: userId }
        ]
      }
    }
  );
};

module.exports = DirectMessage;
