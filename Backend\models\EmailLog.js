const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const EmailLog = sequelize.define('EmailLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '关联的用户ID（如果适用）'
  },
  recipient: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    },
    comment: '收件人邮箱地址'
  },
  sender: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '发件人邮箱地址'
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '邮件主题'
  },
  template: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '使用的邮件模板名称'
  },
  templateData: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '模板渲染数据'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',    // 待发送
      'sent',       // 已发送
      'delivered',  // 已送达
      'opened',     // 已打开
      'clicked',    // 已点击
      'bounced',    // 退回
      'failed',     // 发送失败
      'spam',       // 标记为垃圾邮件
      'unsubscribed' // 取消订阅
    ),
    allowNull: false,
    defaultValue: 'pending',
    comment: '邮件状态'
  },
  provider: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '邮件服务提供商（smtp, sendgrid等）'
  },
  messageId: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    comment: '邮件服务商返回的消息ID'
  },
  providerResponse: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '邮件服务商的响应数据'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '错误信息（如果发送失败）'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '重试次数'
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    comment: '最大重试次数'
  },
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '计划发送时间'
  },
  sentAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '实际发送时间'
  },
  deliveredAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '送达时间'
  },
  openedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '首次打开时间'
  },
  clickedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '首次点击时间'
  },
  bouncedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '退回时间'
  },
  bounceReason: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '退回原因'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal',
    comment: '邮件优先级'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: '邮件标签'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外的元数据'
  },
  ipAddress: {
    type: DataTypes.INET,
    allowNull: true,
    comment: '发送请求的IP地址'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '发送请求的用户代理'
  }
}, {
  tableName: 'email_logs',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['recipient']
    },
    {
      fields: ['status']
    },
    {
      fields: ['template']
    },
    {
      fields: ['messageId'],
      unique: true
    },
    {
      fields: ['sentAt']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['status', 'scheduledAt'],
      name: 'email_logs_status_scheduled_idx'
    },
    {
      fields: ['provider', 'status'],
      name: 'email_logs_provider_status_idx'
    }
  ]
});

// Instance methods
EmailLog.prototype.markAsSent = function(messageId, providerResponse = null) {
  return this.update({
    status: 'sent',
    messageId: messageId,
    sentAt: new Date(),
    providerResponse: providerResponse
  });
};

EmailLog.prototype.markAsDelivered = function(deliveredAt = null) {
  return this.update({
    status: 'delivered',
    deliveredAt: deliveredAt || new Date()
  });
};

EmailLog.prototype.markAsOpened = function(openedAt = null) {
  return this.update({
    status: 'opened',
    openedAt: openedAt || new Date()
  });
};

EmailLog.prototype.markAsClicked = function(clickedAt = null) {
  return this.update({
    status: 'clicked',
    clickedAt: clickedAt || new Date()
  });
};

EmailLog.prototype.markAsBounced = function(bounceReason = null, bouncedAt = null) {
  return this.update({
    status: 'bounced',
    bounceReason: bounceReason,
    bouncedAt: bouncedAt || new Date()
  });
};

EmailLog.prototype.markAsFailed = function(errorMessage, canRetry = true) {
  const updates = {
    status: 'failed',
    errorMessage: errorMessage
  };
  
  if (canRetry && this.retryCount < this.maxRetries) {
    updates.retryCount = this.retryCount + 1;
    updates.status = 'pending'; // 重新标记为待发送以便重试
  }
  
  return this.update(updates);
};

// Static methods
EmailLog.findPendingEmails = function(limit = 100) {
  return this.findAll({
    where: {
      status: 'pending',
      retryCount: {
        [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('maxRetries')
      }
    },
    order: [
      ['priority', 'DESC'],
      ['scheduledAt', 'ASC'],
      ['createdAt', 'ASC']
    ],
    limit: limit
  });
};

EmailLog.getStatsByTemplate = function(startDate, endDate) {
  return this.findAll({
    attributes: [
      'template',
      [sequelize.fn('COUNT', '*'), 'total'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'sent')), 'sent'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'delivered')), 'delivered'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'opened')), 'opened'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'clicked')), 'clicked'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'bounced')), 'bounced'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'failed')), 'failed']
    ],
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      }
    },
    group: ['template'],
    raw: true
  });
};

EmailLog.getDeliveryStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    attributes: [
      [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
      [sequelize.fn('COUNT', '*'), 'total'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'sent')), 'sent'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'delivered')), 'delivered'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('status'), 'failed')), 'failed']
    ],
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: [sequelize.fn('DATE', sequelize.col('createdAt'))],
    order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC']],
    raw: true
  });
};

module.exports = EmailLog;
