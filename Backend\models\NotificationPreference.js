const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const NotificationPreference = sequelize.define('NotificationPreference', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID'
  },
  // 通知类型偏好设置
  preferences: {
    type: DataTypes.JSONB,
    defaultValue: {
      like: {
        web: true,
        email: false,
        push: true
      },
      comment: {
        web: true,
        email: true,
        push: true
      },
      follow: {
        web: true,
        email: true,
        push: true
      },
      message: {
        web: true,
        email: true,
        push: true
      },
      article_published: {
        web: true,
        email: false,
        push: false
      },
      article_approved: {
        web: true,
        email: true,
        push: true
      },
      article_rejected: {
        web: true,
        email: true,
        push: false
      },
      system: {
        web: true,
        email: true,
        push: true
      },
      promotion: {
        web: false,
        email: false,
        push: false
      },
      reminder: {
        web: true,
        email: false,
        push: true
      },
      security: {
        web: true,
        email: true,
        push: true
      },
      newsletter: {
        web: false,
        email: true,
        push: false
      }
    },
    comment: '各类型通知的渠道偏好设置'
  },
  // 全局设置
  globalSettings: {
    type: DataTypes.JSONB,
    defaultValue: {
      enableWeb: true,
      enableEmail: true,
      enablePush: true,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
        timezone: 'Asia/Shanghai'
      },
      frequency: {
        email: 'immediate', // immediate, hourly, daily, weekly
        push: 'immediate'
      },
      language: 'zh-CN'
    },
    comment: '全局通知设置'
  },
  // 邮件设置
  emailSettings: {
    type: DataTypes.JSONB,
    defaultValue: {
      verified: false,
      unsubscribeToken: null,
      bounced: false,
      lastBounceAt: null
    },
    comment: '邮件通知相关设置'
  },
  // 推送设置
  pushSettings: {
    type: DataTypes.JSONB,
    defaultValue: {
      enabled: false,
      subscriptions: []
    },
    comment: '推送通知相关设置'
  },
  lastUpdated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '最后更新时间'
  }
}, {
  tableName: 'notification_preferences',
  timestamps: true,
  indexes: [
    {
      fields: ['userId'],
      unique: true
    }
  ]
});

// 实例方法
NotificationPreference.prototype.isChannelEnabled = function(notificationType, channel) {
  const typePrefs = this.preferences[notificationType];
  if (!typePrefs) return false;
  
  const globalEnabled = this.globalSettings[`enable${channel.charAt(0).toUpperCase() + channel.slice(1)}`];
  return globalEnabled && typePrefs[channel];
};

NotificationPreference.prototype.isInQuietHours = function() {
  const { quietHours } = this.globalSettings;
  if (!quietHours.enabled) return false;
  
  const now = new Date();
  const currentTime = now.toLocaleTimeString('en-US', { 
    hour12: false, 
    timeZone: quietHours.timezone 
  }).substring(0, 5);
  
  return currentTime >= quietHours.start || currentTime <= quietHours.end;
};

NotificationPreference.prototype.updatePreference = function(notificationType, channel, enabled) {
  if (!this.preferences[notificationType]) {
    this.preferences[notificationType] = {};
  }
  this.preferences[notificationType][channel] = enabled;
  this.lastUpdated = new Date();
  this.changed('preferences', true);
  return this.save();
};

NotificationPreference.prototype.updateGlobalSetting = function(setting, value) {
  this.globalSettings[setting] = value;
  this.lastUpdated = new Date();
  this.changed('globalSettings', true);
  return this.save();
};

// 静态方法
NotificationPreference.getOrCreate = async function(userId) {
  const [preference, created] = await this.findOrCreate({
    where: { userId },
    defaults: { userId }
  });
  return preference;
};

NotificationPreference.getUserPreference = async function(userId) {
  return await this.findOne({
    where: { userId }
  });
};

NotificationPreference.bulkUpdatePreferences = async function(userId, preferences) {
  const preference = await this.getOrCreate(userId);
  
  Object.keys(preferences).forEach(type => {
    if (preference.preferences[type]) {
      Object.assign(preference.preferences[type], preferences[type]);
    } else {
      preference.preferences[type] = preferences[type];
    }
  });
  
  preference.lastUpdated = new Date();
  preference.changed('preferences', true);
  return await preference.save();
};

NotificationPreference.getEnabledChannels = async function(userId, notificationType) {
  const preference = await this.getUserPreference(userId);
  if (!preference) return ['web']; // 默认只启用web通知
  
  const channels = [];
  const typePrefs = preference.preferences[notificationType];
  
  if (!typePrefs) return ['web'];
  
  ['web', 'email', 'push'].forEach(channel => {
    if (preference.isChannelEnabled(notificationType, channel)) {
      channels.push(channel);
    }
  });
  
  return channels.length > 0 ? channels : ['web'];
};

module.exports = NotificationPreference;
