const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Permission = sequelize.define('Permission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: 'Permission name (e.g., "create_article", "delete_user")'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Human-readable description of the permission'
  },
  resource: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Resource type (e.g., "article", "user", "comment")'
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Action type (e.g., "create", "read", "update", "delete")'
  },
  scope: {
    type: DataTypes.ENUM('global', 'own', 'department', 'custom'),
    allowNull: false,
    defaultValue: 'global',
    comment: 'Permission scope'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this permission is active'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional permission metadata'
  }
}, {
  tableName: 'permissions',
  timestamps: true,
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['resource']
    },
    {
      fields: ['action']
    },
    {
      fields: ['resource', 'action']
    },
    {
      fields: ['isActive']
    }
  ]
});

// Class methods for permission management
Permission.createDefaultPermissions = async function() {
  const defaultPermissions = [
    // Article permissions
    { name: 'article:create', description: 'Create articles', resource: 'article', action: 'create', scope: 'own' },
    { name: 'article:read', description: 'Read articles', resource: 'article', action: 'read', scope: 'global' },
    { name: 'article:update', description: 'Update articles', resource: 'article', action: 'update', scope: 'own' },
    { name: 'article:delete', description: 'Delete articles', resource: 'article', action: 'delete', scope: 'own' },
    { name: 'article:publish', description: 'Publish articles', resource: 'article', action: 'publish', scope: 'own' },
    { name: 'article:moderate', description: 'Moderate articles', resource: 'article', action: 'moderate', scope: 'global' },
    
    // Draft permissions
    { name: 'draft:create', description: 'Create drafts', resource: 'draft', action: 'create', scope: 'own' },
    { name: 'draft:read', description: 'Read drafts', resource: 'draft', action: 'read', scope: 'own' },
    { name: 'draft:update', description: 'Update drafts', resource: 'draft', action: 'update', scope: 'own' },
    { name: 'draft:delete', description: 'Delete drafts', resource: 'draft', action: 'delete', scope: 'own' },
    
    // Comment permissions
    { name: 'comment:create', description: 'Create comments', resource: 'comment', action: 'create', scope: 'global' },
    { name: 'comment:read', description: 'Read comments', resource: 'comment', action: 'read', scope: 'global' },
    { name: 'comment:update', description: 'Update comments', resource: 'comment', action: 'update', scope: 'own' },
    { name: 'comment:delete', description: 'Delete comments', resource: 'comment', action: 'delete', scope: 'own' },
    { name: 'comment:moderate', description: 'Moderate comments', resource: 'comment', action: 'moderate', scope: 'global' },
    
    // User permissions
    { name: 'user:read', description: 'Read user profiles', resource: 'user', action: 'read', scope: 'global' },
    { name: 'user:update', description: 'Update user profiles', resource: 'user', action: 'update', scope: 'own' },
    { name: 'user:delete', description: 'Delete user accounts', resource: 'user', action: 'delete', scope: 'own' },
    { name: 'user:manage', description: 'Manage user accounts', resource: 'user', action: 'manage', scope: 'global' },
    { name: 'user:ban', description: 'Ban user accounts', resource: 'user', action: 'ban', scope: 'global' },
    
    // Media permissions
    { name: 'media:upload', description: 'Upload media files', resource: 'media', action: 'upload', scope: 'own' },
    { name: 'media:delete', description: 'Delete media files', resource: 'media', action: 'delete', scope: 'own' },
    { name: 'media:manage', description: 'Manage all media files', resource: 'media', action: 'manage', scope: 'global' },
    
    // Admin permissions
    { name: 'admin:dashboard', description: 'Access admin dashboard', resource: 'admin', action: 'dashboard', scope: 'global' },
    { name: 'admin:settings', description: 'Manage system settings', resource: 'admin', action: 'settings', scope: 'global' },
    { name: 'admin:analytics', description: 'View analytics', resource: 'admin', action: 'analytics', scope: 'global' },
    { name: 'admin:logs', description: 'View system logs', resource: 'admin', action: 'logs', scope: 'global' },
    
    // Review permissions
    { name: 'review:create', description: 'Create content reviews', resource: 'review', action: 'create', scope: 'global' },
    { name: 'review:approve', description: 'Approve content', resource: 'review', action: 'approve', scope: 'global' },
    { name: 'review:reject', description: 'Reject content', resource: 'review', action: 'reject', scope: 'global' }
  ];

  for (const permission of defaultPermissions) {
    await this.findOrCreate({
      where: { name: permission.name },
      defaults: permission
    });
  }
};

Permission.getByResource = async function(resource) {
  return await this.findAll({
    where: { 
      resource,
      isActive: true 
    },
    order: [['action', 'ASC']]
  });
};

Permission.getByAction = async function(action) {
  return await this.findAll({
    where: { 
      action,
      isActive: true 
    },
    order: [['resource', 'ASC']]
  });
};

module.exports = Permission;
