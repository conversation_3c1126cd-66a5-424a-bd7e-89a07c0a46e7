const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const PushSubscription = sequelize.define('PushSubscription', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID'
  },
  endpoint: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '推送端点URL'
  },
  p256dh: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'P256DH密钥'
  },
  auth: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '认证密钥'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理字符串'
  },
  deviceInfo: {
    type: DataTypes.JSONB,
    defaultValue: {},
    comment: '设备信息'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  lastUsed: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '最后使用时间'
  },
  failureCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '推送失败次数'
  },
  lastFailureAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后失败时间'
  },
  lastFailureReason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '最后失败原因'
  }
}, {
  tableName: 'push_subscriptions',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['endpoint'],
      unique: true
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['lastUsed']
    },
    {
      fields: ['userId', 'isActive']
    }
  ]
});

// 实例方法
PushSubscription.prototype.getSubscriptionObject = function() {
  return {
    endpoint: this.endpoint,
    keys: {
      p256dh: this.p256dh,
      auth: this.auth
    }
  };
};

PushSubscription.prototype.markAsUsed = function() {
  this.lastUsed = new Date();
  this.failureCount = 0;
  this.lastFailureAt = null;
  this.lastFailureReason = null;
  return this.save();
};

PushSubscription.prototype.markAsFailed = function(reason) {
  this.failureCount += 1;
  this.lastFailureAt = new Date();
  this.lastFailureReason = reason;
  
  // 如果连续失败超过5次，则标记为不活跃
  if (this.failureCount >= 5) {
    this.isActive = false;
  }
  
  return this.save();
};

PushSubscription.prototype.reactivate = function() {
  this.isActive = true;
  this.failureCount = 0;
  this.lastFailureAt = null;
  this.lastFailureReason = null;
  this.lastUsed = new Date();
  return this.save();
};

// 静态方法
PushSubscription.findOrCreateSubscription = async function(userId, subscriptionData) {
  const { endpoint, keys } = subscriptionData;
  
  // 首先尝试查找现有订阅
  let subscription = await this.findOne({
    where: { endpoint }
  });
  
  if (subscription) {
    // 如果找到现有订阅，更新用户ID和密钥（可能用户重新登录）
    subscription.userId = userId;
    subscription.p256dh = keys.p256dh;
    subscription.auth = keys.auth;
    subscription.isActive = true;
    subscription.lastUsed = new Date();
    await subscription.save();
    return subscription;
  }
  
  // 创建新订阅
  return await this.create({
    userId,
    endpoint,
    p256dh: keys.p256dh,
    auth: keys.auth
  });
};

PushSubscription.getUserActiveSubscriptions = async function(userId) {
  return await this.findAll({
    where: {
      userId,
      isActive: true
    },
    order: [['lastUsed', 'DESC']]
  });
};

PushSubscription.deactivateSubscription = async function(endpoint) {
  return await this.update(
    { isActive: false },
    { where: { endpoint } }
  );
};

PushSubscription.cleanupInactiveSubscriptions = async function(daysInactive = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysInactive);
  
  return await this.destroy({
    where: {
      isActive: false,
      lastUsed: {
        [sequelize.Sequelize.Op.lt]: cutoffDate
      }
    }
  });
};

PushSubscription.getSubscriptionStats = async function() {
  const total = await this.count();
  const active = await this.count({ where: { isActive: true } });
  const inactive = total - active;
  
  const recentlyUsed = await this.count({
    where: {
      isActive: true,
      lastUsed: {
        [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7天内
      }
    }
  });
  
  return {
    total,
    active,
    inactive,
    recentlyUsed
  };
};

module.exports = PushSubscription;
