const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ReadingStats = sequelize.define('ReadingStats', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  articleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'articles',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true, // Allow anonymous reading
    references: {
      model: 'users',
      key: 'id'
    }
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '会话ID，用于跟踪匿名用户'
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '开始阅读时间'
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '结束阅读时间'
  },
  readingDuration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '实际阅读时长（秒）'
  },
  totalTimeOnPage: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '页面总停留时间（秒）'
  },
  scrollDepth: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0,
    comment: '最大滚动深度百分比（0-100）'
  },
  readingProgress: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0,
    comment: '阅读进度百分比（0-100）'
  },
  isCompleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否完整阅读'
  },
  exitType: {
    type: DataTypes.ENUM('completed', 'bounced', 'navigated', 'closed'),
    allowNull: true,
    comment: '退出类型'
  },
  deviceType: {
    type: DataTypes.ENUM('desktop', 'mobile', 'tablet'),
    allowNull: true
  },
  referrer: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '来源页面'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  location: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '地理位置信息'
  },
  engagementScore: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: '参与度评分（0-100）'
  },
  interactions: {
    type: DataTypes.JSONB,
    defaultValue: {},
    comment: '交互行为记录 {likes: 0, shares: 0, comments: 0}'
  }
}, {
  tableName: 'reading_stats',
  timestamps: true,
  indexes: [
    {
      fields: ['articleId']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['sessionId']
    },
    {
      fields: ['startTime']
    },
    {
      fields: ['isCompleted']
    },
    {
      fields: ['articleId', 'userId']
    },
    {
      fields: ['articleId', 'isCompleted']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['deviceType']
    },
    {
      fields: ['engagementScore']
    }
  ]
});

// 实例方法
ReadingStats.prototype.calculateEngagementScore = function() {
  let score = 0;
  
  // 基于阅读进度（40%权重）
  score += this.readingProgress * 0.4;
  
  // 基于阅读时长与预期时长的比例（30%权重）
  const expectedDuration = 300; // 假设5分钟为标准阅读时间
  const durationScore = Math.min(this.readingDuration / expectedDuration * 100, 100);
  score += durationScore * 0.3;
  
  // 基于交互行为（30%权重）
  const interactions = this.interactions || {};
  const interactionScore = (interactions.likes || 0) * 20 + 
                          (interactions.shares || 0) * 30 + 
                          (interactions.comments || 0) * 25;
  score += Math.min(interactionScore, 30);
  
  this.engagementScore = Math.round(score * 100) / 100;
  return this.engagementScore;
};

ReadingStats.prototype.getDurationInMinutes = function() {
  return Math.round(this.readingDuration / 60 * 100) / 100;
};

// 类方法
ReadingStats.getArticleStats = async function(articleId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const stats = await this.findAll({
    attributes: [
      [sequelize.fn('COUNT', '*'), 'totalReads'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('isCompleted'), true)), 'completedReads'],
      [sequelize.fn('AVG', sequelize.col('readingDuration')), 'avgDuration'],
      [sequelize.fn('AVG', sequelize.col('scrollDepth')), 'avgScrollDepth'],
      [sequelize.fn('AVG', sequelize.col('readingProgress')), 'avgProgress'],
      [sequelize.fn('AVG', sequelize.col('engagementScore')), 'avgEngagement']
    ],
    where: {
      articleId,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    }
  });
  
  return stats[0];
};

ReadingStats.getTopArticles = async function(days = 7, limit = 10, orderBy = 'reads') {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  let orderField;
  switch (orderBy) {
    case 'engagement':
      orderField = sequelize.fn('AVG', sequelize.col('engagementScore'));
      break;
    case 'completion':
      orderField = sequelize.literal('completion_rate');
      break;
    case 'duration':
      orderField = sequelize.fn('AVG', sequelize.col('readingDuration'));
      break;
    default:
      orderField = sequelize.fn('COUNT', '*');
  }
  
  return await this.findAll({
    attributes: [
      'articleId',
      [sequelize.fn('COUNT', '*'), 'totalReads'],
      [sequelize.fn('COUNT', sequelize.where(sequelize.col('isCompleted'), true)), 'completedReads'],
      [sequelize.literal('ROUND(COUNT(CASE WHEN "isCompleted" = true THEN 1 END) * 100.0 / COUNT(*), 2)'), 'completion_rate'],
      [sequelize.fn('AVG', sequelize.col('readingDuration')), 'avgDuration'],
      [sequelize.fn('AVG', sequelize.col('engagementScore')), 'avgEngagement']
    ],
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: ['articleId'],
    order: [[orderField, 'DESC']],
    limit
  });
};

module.exports = ReadingStats;
