const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Report = sequelize.define('Report', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '报表名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '报表描述'
  },
  type: {
    type: DataTypes.ENUM(
      'dashboard',      // 仪表板
      'analytics',      // 分析报表
      'performance',    // 性能报表
      'user_behavior',  // 用户行为报表
      'content',        // 内容报表
      'financial',      // 财务报表
      'custom'          // 自定义报表
    ),
    allowNull: false,
    comment: '报表类型'
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '报表分类'
  },
  config: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: '报表配置'
  },
  query: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'SQL查询语句'
  },
  dataSource: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '数据源配置'
  },
  visualization: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '可视化配置'
  },
  filters: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '过滤器配置'
  },
  schedule: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '定时生成配置'
  },
  recipients: {
    type: DataTypes.ARRAY(DataTypes.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: '报表接收者用户ID列表'
  },
  isPublic: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否公开'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '创建者ID'
  },
  lastGeneratedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后生成时间'
  },
  generationCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '生成次数'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外元数据'
  }
}, {
  tableName: 'reports',
  timestamps: true,
  indexes: [
    {
      fields: ['type']
    },
    {
      fields: ['category']
    },
    {
      fields: ['createdBy']
    },
    {
      fields: ['isPublic']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['lastGeneratedAt']
    },
    {
      fields: ['type', 'isActive'],
      name: 'reports_type_active_idx'
    }
  ]
});

// Instance methods
Report.prototype.generateReport = async function(params = {}) {
  try {
    const startTime = Date.now();
    
    // 构建查询参数
    const queryParams = {
      ...this.config.defaultParams,
      ...params
    };
    
    let data;
    
    if (this.query) {
      // 执行自定义SQL查询
      data = await sequelize.query(this.query, {
        replacements: queryParams,
        type: sequelize.QueryTypes.SELECT
      });
    } else {
      // 根据报表类型生成数据
      data = await this.generateDataByType(queryParams);
    }
    
    const generationTime = Date.now() - startTime;
    
    // 更新生成统计
    await this.update({
      lastGeneratedAt: new Date(),
      generationCount: this.generationCount + 1
    });
    
    return {
      reportId: this.id,
      name: this.name,
      type: this.type,
      data,
      generatedAt: new Date(),
      generationTime,
      params: queryParams
    };
  } catch (error) {
    throw new Error(`Report generation failed: ${error.message}`);
  }
};

Report.prototype.generateDataByType = async function(params) {
  const Analytics = require('./Analytics');
  const { startDate, endDate, groupBy = 'day' } = params;
  
  switch (this.type) {
    case 'analytics':
      return await this.generateAnalyticsData(params);
      
    case 'user_behavior':
      return await this.generateUserBehaviorData(params);
      
    case 'content':
      return await this.generateContentData(params);
      
    case 'performance':
      return await this.generatePerformanceData(params);
      
    default:
      throw new Error(`Unsupported report type: ${this.type}`);
  }
};

Report.prototype.generateAnalyticsData = async function(params) {
  const Analytics = require('./Analytics');
  const { startDate, endDate, eventType = 'page_view', groupBy = 'day' } = params;
  
  return await Analytics.getEventCounts(eventType, startDate, endDate, groupBy);
};

Report.prototype.generateUserBehaviorData = async function(params) {
  const Analytics = require('./Analytics');
  const { userId, days = 30 } = params;
  
  if (userId) {
    return await Analytics.getUserBehaviorAnalysis(userId, days);
  } else {
    // 返回聚合的用户行为数据
    return await sequelize.query(`
      SELECT 
        event_type,
        COUNT(*) as total_events,
        COUNT(DISTINCT user_id) as unique_users,
        AVG(value) as avg_value
      FROM analytics 
      WHERE timestamp >= NOW() - INTERVAL ':days days'
      GROUP BY event_type
      ORDER BY total_events DESC
    `, {
      replacements: { days },
      type: sequelize.QueryTypes.SELECT
    });
  }
};

Report.prototype.generateContentData = async function(params) {
  const Analytics = require('./Analytics');
  const { targetType = 'article', days = 30, limit = 20 } = params;
  
  return await Analytics.getContentPerformance(targetType, days, limit);
};

Report.prototype.generatePerformanceData = async function(params) {
  const Analytics = require('./Analytics');
  const { days = 7 } = params;
  
  return await Analytics.getPerformanceMetrics(days);
};

// Static methods
Report.createReport = function(reportData) {
  return this.create({
    name: reportData.name,
    description: reportData.description,
    type: reportData.type,
    category: reportData.category,
    config: reportData.config || {},
    query: reportData.query,
    dataSource: reportData.dataSource || {},
    visualization: reportData.visualization || {},
    filters: reportData.filters || {},
    schedule: reportData.schedule,
    recipients: reportData.recipients || [],
    isPublic: reportData.isPublic || false,
    createdBy: reportData.createdBy
  });
};

Report.getPublicReports = function(type = null, limit = 20, offset = 0) {
  const where = { isPublic: true, isActive: true };
  if (type) where.type = type;
  
  return this.findAndCountAll({
    where,
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

Report.getUserReports = function(userId, type = null, limit = 20, offset = 0) {
  const where = { 
    [sequelize.Sequelize.Op.or]: [
      { createdBy: userId },
      { recipients: { [sequelize.Sequelize.Op.contains]: [userId] } },
      { isPublic: true }
    ],
    isActive: true
  };
  
  if (type) where.type = type;
  
  return this.findAndCountAll({
    where,
    order: [['lastGeneratedAt', 'DESC'], ['createdAt', 'DESC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

Report.getScheduledReports = function() {
  return this.findAll({
    where: {
      schedule: {
        [sequelize.Sequelize.Op.ne]: null
      },
      isActive: true
    },
    order: [['lastGeneratedAt', 'ASC']]
  });
};

Report.generateBulkReports = async function(reportIds, params = {}) {
  const reports = await this.findAll({
    where: {
      id: {
        [sequelize.Sequelize.Op.in]: reportIds
      },
      isActive: true
    }
  });
  
  const results = [];
  
  for (const report of reports) {
    try {
      const result = await report.generateReport(params);
      results.push({ success: true, ...result });
    } catch (error) {
      results.push({
        success: false,
        reportId: report.id,
        error: error.message
      });
    }
  }
  
  return results;
};

Report.getReportStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      type,
      COUNT(*) as total_reports,
      COUNT(CASE WHEN is_active = true THEN 1 END) as active_reports,
      SUM(generation_count) as total_generations,
      AVG(generation_count) as avg_generations_per_report,
      COUNT(CASE WHEN last_generated_at >= :startDate THEN 1 END) as recently_generated
    FROM reports 
    GROUP BY type
    ORDER BY total_reports DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

Report.searchReports = function(query, userId = null, limit = 20, offset = 0) {
  const where = {
    [sequelize.Sequelize.Op.and]: [
      {
        [sequelize.Sequelize.Op.or]: [
          {
            name: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          },
          {
            description: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          },
          {
            category: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          }
        ]
      },
      {
        isActive: true
      }
    ]
  };
  
  if (userId) {
    where[sequelize.Sequelize.Op.and].push({
      [sequelize.Sequelize.Op.or]: [
        { createdBy: userId },
        { recipients: { [sequelize.Sequelize.Op.contains]: [userId] } },
        { isPublic: true }
      ]
    });
  } else {
    where[sequelize.Sequelize.Op.and].push({ isPublic: true });
  }
  
  return this.findAndCountAll({
    where,
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

module.exports = Report;
