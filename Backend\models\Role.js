const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: 'Role name (e.g., "admin", "moderator", "author")'
  },
  displayName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Human-readable role name'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Role description'
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Role hierarchy level (higher = more permissions)'
  },
  isSystem: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this is a system role (cannot be deleted)'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this role is active'
  },
  color: {
    type: DataTypes.STRING(7),
    allowNull: true,
    defaultValue: '#6366f1',
    comment: 'Role color for UI display'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional role metadata'
  }
}, {
  tableName: 'roles',
  timestamps: true,
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['level']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['isSystem']
    }
  ]
});

// Instance methods
Role.prototype.hasPermission = async function(permissionName) {
  const RolePermission = sequelize.models.RolePermission;
  const Permission = sequelize.models.Permission;
  
  const rolePermission = await RolePermission.findOne({
    where: { roleId: this.id },
    include: [{
      model: Permission,
      where: { 
        name: permissionName,
        isActive: true 
      }
    }]
  });
  
  return !!rolePermission;
};

Role.prototype.getPermissions = async function() {
  const RolePermission = sequelize.models.RolePermission;
  const Permission = sequelize.models.Permission;
  
  const rolePermissions = await RolePermission.findAll({
    where: { roleId: this.id },
    include: [{
      model: Permission,
      where: { isActive: true }
    }]
  });
  
  return rolePermissions.map(rp => rp.Permission);
};

Role.prototype.addPermission = async function(permissionId) {
  const RolePermission = sequelize.models.RolePermission;
  
  return await RolePermission.findOrCreate({
    where: {
      roleId: this.id,
      permissionId: permissionId
    }
  });
};

Role.prototype.removePermission = async function(permissionId) {
  const RolePermission = sequelize.models.RolePermission;
  
  return await RolePermission.destroy({
    where: {
      roleId: this.id,
      permissionId: permissionId
    }
  });
};

// Class methods
Role.createDefaultRoles = async function() {
  const Permission = sequelize.models.Permission;
  
  const defaultRoles = [
    {
      name: 'super_admin',
      displayName: 'Super Administrator',
      description: 'Full system access with all permissions',
      level: 1000,
      isSystem: true,
      color: '#dc2626'
    },
    {
      name: 'admin',
      displayName: 'Administrator',
      description: 'Administrative access with most permissions',
      level: 900,
      isSystem: true,
      color: '#ea580c'
    },
    {
      name: 'moderator',
      displayName: 'Moderator',
      description: 'Content moderation and user management',
      level: 500,
      isSystem: true,
      color: '#d97706'
    },
    {
      name: 'author',
      displayName: 'Author',
      description: 'Can create and manage own content',
      level: 200,
      isSystem: true,
      color: '#059669'
    },
    {
      name: 'user',
      displayName: 'User',
      description: 'Basic user with limited permissions',
      level: 100,
      isSystem: true,
      color: '#6366f1'
    }
  ];

  for (const roleData of defaultRoles) {
    const [role] = await this.findOrCreate({
      where: { name: roleData.name },
      defaults: roleData
    });

    // Assign permissions based on role
    if (roleData.name === 'super_admin') {
      // Super admin gets all permissions
      const allPermissions = await Permission.findAll({ where: { isActive: true } });
      for (const permission of allPermissions) {
        await role.addPermission(permission.id);
      }
    } else if (roleData.name === 'admin') {
      // Admin gets most permissions except super admin specific ones
      const adminPermissions = await Permission.findAll({
        where: {
          isActive: true,
          name: {
            [sequelize.Sequelize.Op.notLike]: 'admin:logs'
          }
        }
      });
      for (const permission of adminPermissions) {
        await role.addPermission(permission.id);
      }
    } else if (roleData.name === 'moderator') {
      // Moderator gets content moderation permissions
      const moderatorPermissions = await Permission.findAll({
        where: {
          isActive: true,
          [sequelize.Sequelize.Op.or]: [
            { name: { [sequelize.Sequelize.Op.like]: '%:moderate' } },
            { name: { [sequelize.Sequelize.Op.like]: '%:read' } },
            { name: { [sequelize.Sequelize.Op.like]: 'review:%' } },
            { name: 'user:ban' }
          ]
        }
      });
      for (const permission of moderatorPermissions) {
        await role.addPermission(permission.id);
      }
    } else if (roleData.name === 'author') {
      // Author gets content creation permissions
      const authorPermissions = await Permission.findAll({
        where: {
          isActive: true,
          [sequelize.Sequelize.Op.or]: [
            { resource: 'article', scope: 'own' },
            { resource: 'draft' },
            { resource: 'media', scope: 'own' },
            { name: 'comment:create' },
            { name: 'comment:read' },
            { name: 'user:read' },
            { name: 'user:update' }
          ]
        }
      });
      for (const permission of authorPermissions) {
        await role.addPermission(permission.id);
      }
    } else if (roleData.name === 'user') {
      // User gets basic permissions
      const userPermissions = await Permission.findAll({
        where: {
          isActive: true,
          name: {
            [sequelize.Sequelize.Op.in]: [
              'article:read',
              'comment:create',
              'comment:read',
              'comment:update',
              'comment:delete',
              'user:read',
              'user:update',
              'user:delete'
            ]
          }
        }
      });
      for (const permission of userPermissions) {
        await role.addPermission(permission.id);
      }
    }
  }
};

Role.getByLevel = async function(minLevel = 0) {
  return await this.findAll({
    where: {
      level: { [sequelize.Sequelize.Op.gte]: minLevel },
      isActive: true
    },
    order: [['level', 'DESC']]
  });
};

Role.getHierarchy = async function() {
  return await this.findAll({
    where: { isActive: true },
    order: [['level', 'DESC']],
    include: [{
      model: sequelize.models.Permission,
      through: { attributes: [] },
      where: { isActive: true },
      required: false
    }]
  });
};

module.exports = Role;
