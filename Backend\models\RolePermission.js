const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const RolePermission = sequelize.define('RolePermission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  roleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'roles',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  permissionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'permissions',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  grantedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When this permission was granted to the role'
  },
  grantedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who granted this permission'
  },
  conditions: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional conditions for this permission grant'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this permission grant is active'
  }
}, {
  tableName: 'role_permissions',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['roleId', 'permissionId']
    },
    {
      fields: ['roleId']
    },
    {
      fields: ['permissionId']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['grantedAt']
    }
  ]
});

// Instance methods
RolePermission.prototype.activate = async function() {
  this.isActive = true;
  return await this.save();
};

RolePermission.prototype.deactivate = async function() {
  this.isActive = false;
  return await this.save();
};

// Class methods
RolePermission.getActiveByRole = async function(roleId) {
  const Permission = sequelize.models.Permission;
  
  return await this.findAll({
    where: {
      roleId,
      isActive: true
    },
    include: [{
      model: Permission,
      where: { isActive: true }
    }],
    order: [['grantedAt', 'ASC']]
  });
};

RolePermission.getActiveByPermission = async function(permissionId) {
  const Role = sequelize.models.Role;
  
  return await this.findAll({
    where: {
      permissionId,
      isActive: true
    },
    include: [{
      model: Role,
      where: { isActive: true }
    }],
    order: [['grantedAt', 'ASC']]
  });
};

RolePermission.grantPermission = async function(roleId, permissionId, grantedBy = null, conditions = {}) {
  const [rolePermission, created] = await this.findOrCreate({
    where: {
      roleId,
      permissionId
    },
    defaults: {
      grantedBy,
      conditions,
      isActive: true
    }
  });

  if (!created && !rolePermission.isActive) {
    rolePermission.isActive = true;
    rolePermission.grantedBy = grantedBy;
    rolePermission.conditions = conditions;
    rolePermission.grantedAt = new Date();
    await rolePermission.save();
  }

  return rolePermission;
};

RolePermission.revokePermission = async function(roleId, permissionId) {
  return await this.update(
    { isActive: false },
    {
      where: {
        roleId,
        permissionId,
        isActive: true
      }
    }
  );
};

RolePermission.bulkGrantPermissions = async function(roleId, permissionIds, grantedBy = null) {
  const results = [];
  
  for (const permissionId of permissionIds) {
    const result = await this.grantPermission(roleId, permissionId, grantedBy);
    results.push(result);
  }
  
  return results;
};

RolePermission.bulkRevokePermissions = async function(roleId, permissionIds) {
  return await this.update(
    { isActive: false },
    {
      where: {
        roleId,
        permissionId: permissionIds,
        isActive: true
      }
    }
  );
};

RolePermission.syncRolePermissions = async function(roleId, permissionIds, grantedBy = null) {
  // First, deactivate all current permissions for this role
  await this.update(
    { isActive: false },
    {
      where: {
        roleId,
        isActive: true
      }
    }
  );

  // Then, grant the new permissions
  return await this.bulkGrantPermissions(roleId, permissionIds, grantedBy);
};

module.exports = RolePermission;
