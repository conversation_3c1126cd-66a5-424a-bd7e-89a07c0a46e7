const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ScheduledPost = sequelize.define('ScheduledPost', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  articleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'articles',
      key: 'id'
    },
    comment: '关联的文章ID'
  },
  versionId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'article_versions',
      key: 'id'
    },
    comment: '指定发布的版本ID'
  },
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '计划发布时间'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',    // 等待发布
      'processing', // 正在处理
      'published',  // 已发布
      'failed',     // 发布失败
      'cancelled'   // 已取消
    ),
    allowNull: false,
    defaultValue: 'pending',
    comment: '调度状态'
  },
  publishType: {
    type: DataTypes.ENUM(
      'immediate',  // 立即发布
      'scheduled',  // 定时发布
      'recurring'   // 循环发布
    ),
    allowNull: false,
    defaultValue: 'scheduled',
    comment: '发布类型'
  },
  recurringPattern: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '循环发布模式配置'
  },
  publishSettings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '发布设置'
  },
  socialMediaSettings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '社交媒体发布设置'
  },
  authorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '作者ID'
  },
  scheduledBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '调度创建者ID'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '实际发布时间'
  },
  publishedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '实际发布者ID'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '错误信息'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '重试次数'
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    comment: '最大重试次数'
  },
  nextRetryAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '下次重试时间'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal',
    comment: '发布优先级'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: '调度标签'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外元数据'
  }
}, {
  tableName: 'scheduled_posts',
  timestamps: true,
  indexes: [
    {
      fields: ['articleId']
    },
    {
      fields: ['versionId']
    },
    {
      fields: ['scheduledAt']
    },
    {
      fields: ['status']
    },
    {
      fields: ['publishType']
    },
    {
      fields: ['authorId']
    },
    {
      fields: ['scheduledBy']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['status', 'scheduledAt'],
      name: 'scheduled_posts_status_time_idx'
    },
    {
      fields: ['status', 'nextRetryAt'],
      name: 'scheduled_posts_retry_idx'
    }
  ]
});

// Instance methods
ScheduledPost.prototype.canExecute = function() {
  const now = new Date();
  return this.status === 'pending' && 
         this.scheduledAt <= now &&
         this.retryCount < this.maxRetries;
};

ScheduledPost.prototype.markAsProcessing = function() {
  return this.update({
    status: 'processing',
    updatedAt: new Date()
  });
};

ScheduledPost.prototype.markAsPublished = function(publishedBy = null) {
  return this.update({
    status: 'published',
    publishedAt: new Date(),
    publishedBy: publishedBy,
    updatedAt: new Date()
  });
};

ScheduledPost.prototype.markAsFailed = function(errorMessage, canRetry = true) {
  const updates = {
    status: 'failed',
    errorMessage: errorMessage,
    updatedAt: new Date()
  };
  
  if (canRetry && this.retryCount < this.maxRetries) {
    updates.retryCount = this.retryCount + 1;
    updates.status = 'pending'; // 重新标记为待发布以便重试
    
    // 计算下次重试时间（指数退避）
    const retryDelay = Math.pow(2, this.retryCount) * 60 * 1000; // 分钟转毫秒
    updates.nextRetryAt = new Date(Date.now() + retryDelay);
  }
  
  return this.update(updates);
};

ScheduledPost.prototype.cancel = function() {
  return this.update({
    status: 'cancelled',
    updatedAt: new Date()
  });
};

ScheduledPost.prototype.reschedule = function(newScheduledAt) {
  return this.update({
    scheduledAt: newScheduledAt,
    status: 'pending',
    retryCount: 0,
    nextRetryAt: null,
    errorMessage: null,
    updatedAt: new Date()
  });
};

// Static methods
ScheduledPost.getPendingPosts = function(limit = 50) {
  const now = new Date();
  
  return this.findAll({
    where: {
      status: 'pending',
      scheduledAt: {
        [sequelize.Sequelize.Op.lte]: now
      },
      retryCount: {
        [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('maxRetries')
      }
    },
    order: [
      ['priority', 'DESC'],
      ['scheduledAt', 'ASC']
    ],
    limit: limit,
    include: [
      {
        model: sequelize.models.Article,
        as: 'article'
      },
      {
        model: sequelize.models.ArticleVersion,
        as: 'version'
      },
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }
    ]
  });
};

ScheduledPost.getRetryablePosts = function(limit = 20) {
  const now = new Date();
  
  return this.findAll({
    where: {
      status: 'pending',
      nextRetryAt: {
        [sequelize.Sequelize.Op.lte]: now
      },
      retryCount: {
        [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('maxRetries')
      }
    },
    order: [
      ['priority', 'DESC'],
      ['nextRetryAt', 'ASC']
    ],
    limit: limit
  });
};

ScheduledPost.getScheduleStats = function(startDate, endDate) {
  return this.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', '*'), 'count'],
      [sequelize.fn('DATE', sequelize.col('scheduledAt')), 'date']
    ],
    where: {
      scheduledAt: {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      }
    },
    group: ['status', sequelize.fn('DATE', sequelize.col('scheduledAt'))],
    order: [[sequelize.fn('DATE', sequelize.col('scheduledAt')), 'ASC']],
    raw: true
  });
};

ScheduledPost.createSchedule = async function(scheduleData) {
  const transaction = await sequelize.transaction();
  
  try {
    // 验证文章存在
    const article = await sequelize.models.Article.findByPk(scheduleData.articleId, { transaction });
    if (!article) {
      throw new Error('Article not found');
    }
    
    // 如果指定了版本，验证版本存在
    if (scheduleData.versionId) {
      const version = await sequelize.models.ArticleVersion.findByPk(scheduleData.versionId, { transaction });
      if (!version || version.articleId !== scheduleData.articleId) {
        throw new Error('Version not found or does not belong to the article');
      }
    }
    
    // 创建调度
    const schedule = await this.create(scheduleData, { transaction });
    
    await transaction.commit();
    return schedule;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

ScheduledPost.bulkReschedule = async function(scheduleIds, newScheduledAt) {
  return this.update(
    {
      scheduledAt: newScheduledAt,
      status: 'pending',
      retryCount: 0,
      nextRetryAt: null,
      errorMessage: null,
      updatedAt: new Date()
    },
    {
      where: {
        id: {
          [sequelize.Sequelize.Op.in]: scheduleIds
        },
        status: {
          [sequelize.Sequelize.Op.in]: ['pending', 'failed']
        }
      }
    }
  );
};

module.exports = ScheduledPost;
