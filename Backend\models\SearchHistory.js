const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SearchHistory = sequelize.define('SearchHistory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID（可为空，支持匿名搜索）'
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '会话ID（用于匿名用户）'
  },
  query: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '搜索查询'
  },
  normalizedQuery: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '标准化后的查询（去除停用词、词干提取等）'
  },
  searchType: {
    type: DataTypes.ENUM(
      'fulltext',      // 全文搜索
      'keyword',       // 关键词搜索
      'tag',           // 标签搜索
      'user',          // 用户搜索
      'advanced'       // 高级搜索
    ),
    allowNull: false,
    defaultValue: 'fulltext',
    comment: '搜索类型'
  },
  filters: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '搜索过滤条件'
  },
  resultCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '搜索结果数量'
  },
  clickedResults: {
    type: DataTypes.ARRAY(DataTypes.INTEGER),
    allowNull: true,
    defaultValue: [],
    comment: '用户点击的结果ID列表'
  },
  responseTime: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '搜索响应时间（毫秒）'
  },
  ipAddress: {
    type: DataTypes.INET,
    allowNull: true,
    comment: 'IP地址'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理'
  },
  referer: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '来源页面'
  },
  language: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'zh',
    comment: '搜索语言'
  },
  location: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '地理位置信息'
  },
  device: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '设备信息'
  },
  isSuccessful: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '搜索是否成功'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '错误信息（如果搜索失败）'
  }
}, {
  tableName: 'search_histories',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['sessionId']
    },
    {
      fields: ['query']
    },
    {
      fields: ['normalizedQuery']
    },
    {
      fields: ['searchType']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['isSuccessful']
    },
    {
      fields: ['userId', 'createdAt'],
      name: 'search_histories_user_time_idx'
    },
    {
      fields: ['query', 'createdAt'],
      name: 'search_histories_query_time_idx'
    }
  ]
});

// Instance methods
SearchHistory.prototype.addClickedResult = function(resultId) {
  const clickedResults = [...(this.clickedResults || [])];
  if (!clickedResults.includes(resultId)) {
    clickedResults.push(resultId);
    return this.update({ clickedResults });
  }
  return Promise.resolve(this);
};

// Static methods
SearchHistory.logSearch = function(data) {
  return this.create({
    userId: data.userId || null,
    sessionId: data.sessionId || null,
    query: data.query,
    normalizedQuery: data.normalizedQuery || null,
    searchType: data.searchType || 'fulltext',
    filters: data.filters || {},
    resultCount: data.resultCount || 0,
    responseTime: data.responseTime || null,
    ipAddress: data.ipAddress || null,
    userAgent: data.userAgent || null,
    referer: data.referer || null,
    language: data.language || 'zh',
    location: data.location || null,
    device: data.device || null,
    isSuccessful: data.isSuccessful !== false,
    errorMessage: data.errorMessage || null
  });
};

SearchHistory.getUserSearchHistory = function(userId, options = {}) {
  const { limit = 50, offset = 0, searchType } = options;
  
  const where = { userId, isSuccessful: true };
  if (searchType) where.searchType = searchType;
  
  return this.findAndCountAll({
    where,
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    attributes: ['id', 'query', 'searchType', 'resultCount', 'createdAt']
  });
};

SearchHistory.getPopularQueries = function(options = {}) {
  const { 
    limit = 20, 
    timeframe = '7d',
    searchType,
    minCount = 2
  } = options;
  
  const timeMap = {
    '1d': 1,
    '7d': 7,
    '30d': 30,
    '90d': 90
  };
  
  const days = timeMap[timeframe] || 7;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  let whereClause = `created_at >= '${startDate.toISOString()}' AND is_successful = true`;
  if (searchType) {
    whereClause += ` AND search_type = '${searchType}'`;
  }
  
  return sequelize.query(`
    SELECT 
      normalized_query as query,
      COUNT(*) as search_count,
      AVG(result_count) as avg_results,
      AVG(response_time) as avg_response_time,
      COUNT(DISTINCT user_id) as unique_users
    FROM search_histories 
    WHERE ${whereClause}
      AND normalized_query IS NOT NULL
      AND normalized_query != ''
    GROUP BY normalized_query
    HAVING COUNT(*) >= :minCount
    ORDER BY search_count DESC, avg_results DESC
    LIMIT :limit
  `, {
    replacements: { minCount, limit },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.getSearchTrends = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as total_searches,
      COUNT(DISTINCT user_id) as unique_users,
      COUNT(DISTINCT session_id) as unique_sessions,
      AVG(result_count) as avg_results,
      AVG(response_time) as avg_response_time,
      COUNT(CASE WHEN result_count = 0 THEN 1 END) as zero_result_searches
    FROM search_histories 
    WHERE created_at >= :startDate
      AND is_successful = true
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `, {
    replacements: { startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.getFailedSearches = function(options = {}) {
  const { limit = 50, offset = 0, days = 7 } = options;
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAndCountAll({
    where: {
      isSuccessful: false,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    attributes: ['id', 'query', 'searchType', 'errorMessage', 'createdAt']
  });
};

SearchHistory.getZeroResultQueries = function(options = {}) {
  const { limit = 50, days = 7 } = options;
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      normalized_query as query,
      COUNT(*) as search_count,
      MAX(created_at) as last_searched
    FROM search_histories 
    WHERE created_at >= :startDate
      AND is_successful = true
      AND result_count = 0
      AND normalized_query IS NOT NULL
      AND normalized_query != ''
    GROUP BY normalized_query
    ORDER BY search_count DESC, last_searched DESC
    LIMIT :limit
  `, {
    replacements: { startDate, limit },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.getUserSearchPatterns = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return sequelize.query(`
    SELECT 
      search_type,
      COUNT(*) as search_count,
      AVG(result_count) as avg_results,
      COUNT(DISTINCT DATE(created_at)) as active_days,
      EXTRACT(HOUR FROM created_at) as hour,
      COUNT(*) as hourly_count
    FROM search_histories 
    WHERE user_id = :userId
      AND created_at >= :startDate
      AND is_successful = true
    GROUP BY search_type, EXTRACT(HOUR FROM created_at)
    ORDER BY search_count DESC
  `, {
    replacements: { userId, startDate },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.getSearchSuggestions = function(partialQuery, userId = null, limit = 10) {
  let whereClause = `normalized_query ILIKE :pattern AND is_successful = true AND result_count > 0`;
  const replacements = { 
    pattern: `${partialQuery}%`,
    limit 
  };
  
  if (userId) {
    whereClause += ` AND user_id = :userId`;
    replacements.userId = userId;
  }
  
  return sequelize.query(`
    SELECT 
      normalized_query as suggestion,
      COUNT(*) as frequency,
      MAX(created_at) as last_used
    FROM search_histories 
    WHERE ${whereClause}
    GROUP BY normalized_query
    ORDER BY frequency DESC, last_used DESC
    LIMIT :limit
  `, {
    replacements,
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.getRelatedQueries = function(query, limit = 10) {
  return sequelize.query(`
    WITH user_sessions AS (
      SELECT DISTINCT user_id, session_id
      FROM search_histories 
      WHERE normalized_query = :query
        AND is_successful = true
        AND created_at >= NOW() - INTERVAL '30 days'
    )
    SELECT 
      sh.normalized_query as related_query,
      COUNT(*) as co_occurrence,
      AVG(sh.result_count) as avg_results
    FROM search_histories sh
    INNER JOIN user_sessions us ON (
      (sh.user_id = us.user_id AND sh.user_id IS NOT NULL) OR
      (sh.session_id = us.session_id AND sh.session_id IS NOT NULL)
    )
    WHERE sh.normalized_query != :query
      AND sh.is_successful = true
      AND sh.created_at >= NOW() - INTERVAL '30 days'
    GROUP BY sh.normalized_query
    ORDER BY co_occurrence DESC, avg_results DESC
    LIMIT :limit
  `, {
    replacements: { query, limit },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchHistory.cleanupOldHistory = function(days = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return this.destroy({
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.lt]: cutoffDate
      }
    }
  });
};

SearchHistory.getSearchAnalytics = function(startDate, endDate) {
  return sequelize.query(`
    SELECT 
      COUNT(*) as total_searches,
      COUNT(DISTINCT user_id) as unique_users,
      COUNT(DISTINCT session_id) as unique_sessions,
      AVG(result_count) as avg_results_per_search,
      AVG(response_time) as avg_response_time,
      COUNT(CASE WHEN result_count = 0 THEN 1 END) as zero_result_count,
      COUNT(CASE WHEN is_successful = false THEN 1 END) as failed_searches,
      search_type,
      COUNT(*) as type_count
    FROM search_histories 
    WHERE created_at BETWEEN :startDate AND :endDate
    GROUP BY search_type
    ORDER BY type_count DESC
  `, {
    replacements: { startDate, endDate },
    type: sequelize.QueryTypes.SELECT
  });
};

module.exports = SearchHistory;
