const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SearchIndex = sequelize.define('SearchIndex', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  contentType: {
    type: DataTypes.ENUM(
      'article',
      'user',
      'comment',
      'tag'
    ),
    allowNull: false,
    comment: '内容类型'
  },
  contentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '内容ID'
  },
  title: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '标题'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '内容'
  },
  searchVector: {
    type: DataTypes.TSVECTOR,
    allowNull: true,
    comment: 'PostgreSQL全文搜索向量'
  },
  keywords: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: '关键词'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
    comment: '标签'
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '分类'
  },
  authorId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '作者ID'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发布时间'
  },
  popularity: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0.0,
    comment: '热度分数'
  },
  qualityScore: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0.0,
    comment: '质量分数'
  },
  relevanceScore: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0.0,
    comment: '相关性分数'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  likeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '点赞次数'
  },
  commentCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '评论次数'
  },
  shareCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '分享次数'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否活跃'
  },
  language: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'zh',
    comment: '语言'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外元数据'
  },
  lastIndexedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '最后索引时间'
  }
}, {
  tableName: 'search_indexes',
  timestamps: true,
  indexes: [
    {
      fields: ['contentType', 'contentId'],
      unique: true,
      name: 'search_indexes_content_unique_idx'
    },
    {
      fields: ['contentType']
    },
    {
      fields: ['authorId']
    },
    {
      fields: ['category']
    },
    {
      fields: ['publishedAt']
    },
    {
      fields: ['popularity']
    },
    {
      fields: ['qualityScore']
    },
    {
      fields: ['relevanceScore']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['language']
    },
    {
      fields: ['lastIndexedAt']
    },
    {
      name: 'search_indexes_search_vector_idx',
      fields: ['searchVector'],
      using: 'gin'
    },
    {
      name: 'search_indexes_keywords_idx',
      fields: ['keywords'],
      using: 'gin'
    },
    {
      name: 'search_indexes_tags_idx',
      fields: ['tags'],
      using: 'gin'
    }
  ]
});

// Instance methods
SearchIndex.prototype.updatePopularity = function() {
  // 计算热度分数：基于浏览、点赞、评论、分享的加权平均
  const weights = {
    view: 1,
    like: 3,
    comment: 5,
    share: 7
  };
  
  const totalInteractions = 
    this.viewCount * weights.view +
    this.likeCount * weights.like +
    this.commentCount * weights.comment +
    this.shareCount * weights.share;
  
  // 时间衰减因子
  const daysSincePublished = this.publishedAt ? 
    (Date.now() - new Date(this.publishedAt).getTime()) / (1000 * 60 * 60 * 24) : 0;
  const timeDecay = Math.exp(-daysSincePublished / 30); // 30天半衰期
  
  const popularity = totalInteractions * timeDecay;
  
  return this.update({ popularity });
};

SearchIndex.prototype.updateQualityScore = function(score) {
  return this.update({ qualityScore: score });
};

SearchIndex.prototype.updateRelevanceScore = function(score) {
  return this.update({ relevanceScore: score });
};

// Static methods
SearchIndex.createOrUpdateIndex = async function(contentType, contentId, data) {
  const [index, created] = await this.findOrCreate({
    where: { contentType, contentId },
    defaults: {
      ...data,
      lastIndexedAt: new Date()
    }
  });
  
  if (!created) {
    await index.update({
      ...data,
      lastIndexedAt: new Date()
    });
  }
  
  return index;
};

SearchIndex.fullTextSearch = function(query, options = {}) {
  const {
    contentType,
    category,
    authorId,
    language = 'zh',
    limit = 20,
    offset = 0,
    sortBy = 'relevance',
    sortOrder = 'DESC'
  } = options;
  
  const where = {
    isActive: true,
    language
  };
  
  if (contentType) where.contentType = contentType;
  if (category) where.category = category;
  if (authorId) where.authorId = authorId;
  
  // 构建排序条件
  let order;
  switch (sortBy) {
    case 'popularity':
      order = [['popularity', sortOrder]];
      break;
    case 'quality':
      order = [['qualityScore', sortOrder]];
      break;
    case 'date':
      order = [['publishedAt', sortOrder]];
      break;
    case 'relevance':
    default:
      order = [['relevanceScore', sortOrder], ['popularity', 'DESC']];
  }
  
  return this.findAndCountAll({
    where: {
      ...where,
      [sequelize.Sequelize.Op.and]: [
        sequelize.literal(`search_vector @@ plainto_tsquery('${language}', '${query}')`)
      ]
    },
    order,
    limit,
    offset,
    include: [
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
      }
    ]
  });
};

SearchIndex.searchByKeywords = function(keywords, options = {}) {
  const {
    contentType,
    limit = 20,
    offset = 0
  } = options;
  
  const where = {
    isActive: true,
    keywords: {
      [sequelize.Sequelize.Op.overlap]: keywords
    }
  };
  
  if (contentType) where.contentType = contentType;
  
  return this.findAndCountAll({
    where,
    order: [['popularity', 'DESC'], ['qualityScore', 'DESC']],
    limit,
    offset
  });
};

SearchIndex.searchByTags = function(tags, options = {}) {
  const {
    contentType,
    limit = 20,
    offset = 0
  } = options;
  
  const where = {
    isActive: true,
    tags: {
      [sequelize.Sequelize.Op.overlap]: tags
    }
  };
  
  if (contentType) where.contentType = contentType;
  
  return this.findAndCountAll({
    where,
    order: [['popularity', 'DESC']],
    limit,
    offset
  });
};

SearchIndex.getPopularContent = function(contentType = null, timeframe = '7d', limit = 20) {
  const timeMap = {
    '1d': 1,
    '7d': 7,
    '30d': 30,
    '90d': 90
  };
  
  const days = timeMap[timeframe] || 7;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const where = {
    isActive: true,
    publishedAt: {
      [sequelize.Sequelize.Op.gte]: startDate
    }
  };
  
  if (contentType) where.contentType = contentType;
  
  return this.findAll({
    where,
    order: [['popularity', 'DESC'], ['qualityScore', 'DESC']],
    limit
  });
};

SearchIndex.getTrendingContent = function(contentType = null, limit = 20) {
  // 趋势内容：最近24小时内热度增长最快的内容
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  const where = {
    isActive: true,
    publishedAt: {
      [sequelize.Sequelize.Op.gte]: yesterday
    }
  };
  
  if (contentType) where.contentType = contentType;
  
  return this.findAll({
    where,
    order: [
      [sequelize.literal('(popularity * quality_score)'), 'DESC'],
      ['createdAt', 'DESC']
    ],
    limit
  });
};

SearchIndex.getSimilarContent = function(contentId, contentType, limit = 10) {
  return sequelize.query(`
    SELECT si.*, 
           similarity(si.search_vector::text, ref.search_vector::text) as similarity_score
    FROM search_indexes si
    CROSS JOIN (
      SELECT search_vector 
      FROM search_indexes 
      WHERE content_id = :contentId AND content_type = :contentType
    ) ref
    WHERE si.content_id != :contentId 
      AND si.content_type = :contentType
      AND si.is_active = true
    ORDER BY similarity_score DESC
    LIMIT :limit
  `, {
    replacements: { contentId, contentType, limit },
    type: sequelize.QueryTypes.SELECT
  });
};

SearchIndex.updateBulkStats = async function(updates) {
  const transaction = await sequelize.transaction();
  
  try {
    for (const update of updates) {
      await this.update(
        {
          viewCount: update.viewCount,
          likeCount: update.likeCount,
          commentCount: update.commentCount,
          shareCount: update.shareCount
        },
        {
          where: {
            contentType: update.contentType,
            contentId: update.contentId
          },
          transaction
        }
      );
    }
    
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

SearchIndex.rebuildIndex = async function(contentType = null) {
  const where = {};
  if (contentType) where.contentType = contentType;
  
  const indexes = await this.findAll({ where });
  
  for (const index of indexes) {
    await index.update({
      lastIndexedAt: new Date()
    });
    
    // 重新计算热度分数
    await index.updatePopularity();
  }
  
  return indexes.length;
};

module.exports = SearchIndex;
