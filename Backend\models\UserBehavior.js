const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserBehavior = sequelize.define('UserBehavior', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true, // Allow anonymous tracking
    references: {
      model: 'users',
      key: 'id'
    }
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '会话ID，用于跟踪匿名用户'
  },
  eventType: {
    type: DataTypes.ENUM(
      'page_view',      // 页面浏览
      'article_view',   // 文章查看
      'article_read',   // 文章阅读完成
      'click',          // 点击事件
      'scroll',         // 滚动事件
      'search',         // 搜索事件
      'like',           // 点赞
      'share',          // 分享
      'comment',        // 评论
      'follow',         // 关注
      'login',          // 登录
      'logout',         // 登出
      'register'        // 注册
    ),
    allowNull: false
  },
  targetType: {
    type: DataTypes.ENUM('article', 'user', 'page', 'search', 'comment'),
    allowNull: true
  },
  targetId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '目标对象ID（文章ID、用户ID等）'
  },
  url: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '访问的URL'
  },
  referrer: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '来源页面'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理信息'
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'IP地址'
  },
  deviceType: {
    type: DataTypes.ENUM('desktop', 'mobile', 'tablet'),
    allowNull: true
  },
  browserName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  osName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  screenResolution: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '屏幕分辨率，格式：1920x1080'
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '持续时间（秒），用于页面停留时间等'
  },
  scrollDepth: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: '滚动深度百分比（0-100）'
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {},
    comment: '额外的元数据信息'
  },
  location: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '地理位置信息 {country, city, lat, lng}'
  }
}, {
  tableName: 'user_behaviors',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['sessionId']
    },
    {
      fields: ['eventType']
    },
    {
      fields: ['targetType', 'targetId']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['userId', 'eventType']
    },
    {
      fields: ['sessionId', 'eventType']
    },
    {
      fields: ['eventType', 'createdAt']
    },
    {
      fields: ['deviceType']
    },
    {
      fields: ['ipAddress']
    }
  ]
});

// 实例方法
UserBehavior.prototype.getDurationInMinutes = function() {
  return this.duration ? Math.round(this.duration / 60 * 100) / 100 : 0;
};

// 类方法
UserBehavior.getUserActivity = async function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return await this.findAll({
    where: {
      userId,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    order: [['createdAt', 'DESC']]
  });
};

UserBehavior.getPopularContent = async function(days = 7, limit = 10) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return await this.findAll({
    attributes: [
      'targetId',
      [sequelize.fn('COUNT', '*'), 'viewCount'],
      [sequelize.fn('AVG', sequelize.col('duration')), 'avgDuration']
    ],
    where: {
      eventType: 'article_view',
      targetType: 'article',
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: ['targetId'],
    order: [[sequelize.literal('viewCount'), 'DESC']],
    limit
  });
};

module.exports = UserBehavior;
