const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserInteraction = sequelize.define('UserInteraction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID'
  },
  targetType: {
    type: DataTypes.ENUM(
      'article',    // 文章
      'comment',    // 评论
      'user'        // 用户
    ),
    allowNull: false,
    comment: '目标类型'
  },
  targetId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '目标ID'
  },
  interactionType: {
    type: DataTypes.ENUM(
      'like',       // 点赞
      'dislike',    // 踩
      'favorite',   // 收藏
      'bookmark',   // 书签
      'share',      // 分享
      'follow',     // 关注
      'block',      // 屏蔽
      'report'      // 举报
    ),
    allowNull: false,
    comment: '交互类型'
  },
  value: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '交互值（1为正向，-1为负向，0为取消）'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '额外元数据'
  },
  ipAddress: {
    type: DataTypes.INET,
    allowNull: true,
    comment: 'IP地址'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否活跃（用于软删除）'
  }
}, {
  tableName: 'user_interactions',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['targetType', 'targetId']
    },
    {
      fields: ['interactionType']
    },
    {
      fields: ['userId', 'targetType', 'targetId', 'interactionType'],
      unique: true,
      name: 'user_interactions_unique_idx'
    },
    {
      fields: ['targetType', 'targetId', 'interactionType'],
      name: 'user_interactions_target_type_idx'
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['isActive']
    }
  ]
});

// Instance methods
UserInteraction.prototype.toggle = function() {
  return this.update({
    value: this.value === 1 ? 0 : 1,
    isActive: this.value !== 1
  });
};

UserInteraction.prototype.remove = function() {
  return this.update({
    value: 0,
    isActive: false
  });
};

// Static methods
UserInteraction.addInteraction = async function(userId, targetType, targetId, interactionType, metadata = {}) {
  const transaction = await sequelize.transaction();
  
  try {
    // 检查是否已存在相同的交互
    const existing = await this.findOne({
      where: {
        userId,
        targetType,
        targetId,
        interactionType
      },
      transaction
    });

    if (existing) {
      // 如果已存在，切换状态
      await existing.update({
        value: existing.value === 1 ? 0 : 1,
        isActive: existing.value !== 1,
        metadata: { ...existing.metadata, ...metadata }
      }, { transaction });
      
      await transaction.commit();
      return { interaction: existing, action: existing.value === 1 ? 'removed' : 'added' };
    } else {
      // 创建新的交互
      const interaction = await this.create({
        userId,
        targetType,
        targetId,
        interactionType,
        value: 1,
        metadata,
        isActive: true
      }, { transaction });
      
      await transaction.commit();
      return { interaction, action: 'added' };
    }
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

UserInteraction.removeInteraction = function(userId, targetType, targetId, interactionType) {
  return this.update(
    {
      value: 0,
      isActive: false
    },
    {
      where: {
        userId,
        targetType,
        targetId,
        interactionType
      }
    }
  );
};

UserInteraction.getInteractionCounts = function(targetType, targetId) {
  return this.findAll({
    attributes: [
      'interactionType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('value')), 'total']
    ],
    where: {
      targetType,
      targetId,
      isActive: true
    },
    group: ['interactionType'],
    raw: true
  });
};

UserInteraction.getUserInteractions = function(userId, targetType, targetIds) {
  return this.findAll({
    where: {
      userId,
      targetType,
      targetId: {
        [sequelize.Sequelize.Op.in]: targetIds
      },
      isActive: true,
      value: 1
    }
  });
};

UserInteraction.getPopularContent = function(targetType, timeframe = '7d', limit = 20) {
  const timeMap = {
    '1d': 1,
    '7d': 7,
    '30d': 30,
    '90d': 90
  };
  
  const days = timeMap[timeframe] || 7;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    attributes: [
      'targetId',
      [sequelize.fn('COUNT', sequelize.col('id')), 'totalInteractions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN interaction_type = 'like' THEN 1 END")), 'likes'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN interaction_type = 'favorite' THEN 1 END")), 'favorites'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN interaction_type = 'share' THEN 1 END")), 'shares']
    ],
    where: {
      targetType,
      isActive: true,
      value: 1,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: ['targetId'],
    order: [[sequelize.literal('totalInteractions'), 'DESC']],
    limit,
    raw: true
  });
};

UserInteraction.getUserActivityStats = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.findAll({
    attributes: [
      'interactionType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('DATE', sequelize.col('createdAt')), 'date']
    ],
    where: {
      userId,
      isActive: true,
      value: 1,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: ['interactionType', sequelize.fn('DATE', sequelize.col('createdAt'))],
    order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'DESC']],
    raw: true
  });
};

UserInteraction.getRecommendations = async function(userId, targetType = 'article', limit = 10) {
  // 基于用户行为的协同过滤推荐
  const userInteractions = await this.findAll({
    attributes: ['targetId'],
    where: {
      userId,
      targetType,
      interactionType: {
        [sequelize.Sequelize.Op.in]: ['like', 'favorite']
      },
      isActive: true,
      value: 1
    }
  });
  
  const userTargetIds = userInteractions.map(i => i.targetId);
  
  if (userTargetIds.length === 0) {
    return [];
  }
  
  // 找到与当前用户有相似兴趣的其他用户
  const similarUsers = await this.findAll({
    attributes: [
      'userId',
      [sequelize.fn('COUNT', sequelize.col('id')), 'commonInteractions']
    ],
    where: {
      targetType,
      targetId: {
        [sequelize.Sequelize.Op.in]: userTargetIds
      },
      userId: {
        [sequelize.Sequelize.Op.ne]: userId
      },
      interactionType: {
        [sequelize.Sequelize.Op.in]: ['like', 'favorite']
      },
      isActive: true,
      value: 1
    },
    group: ['userId'],
    having: sequelize.literal('COUNT(id) >= 2'), // 至少有2个共同兴趣
    order: [[sequelize.literal('commonInteractions'), 'DESC']],
    limit: 50,
    raw: true
  });
  
  if (similarUsers.length === 0) {
    return [];
  }
  
  const similarUserIds = similarUsers.map(u => u.userId);
  
  // 获取相似用户喜欢但当前用户未交互的内容
  const recommendations = await this.findAll({
    attributes: [
      'targetId',
      [sequelize.fn('COUNT', sequelize.col('id')), 'score']
    ],
    where: {
      userId: {
        [sequelize.Sequelize.Op.in]: similarUserIds
      },
      targetType,
      targetId: {
        [sequelize.Sequelize.Op.notIn]: userTargetIds
      },
      interactionType: {
        [sequelize.Sequelize.Op.in]: ['like', 'favorite']
      },
      isActive: true,
      value: 1
    },
    group: ['targetId'],
    order: [[sequelize.literal('score'), 'DESC']],
    limit,
    raw: true
  });
  
  return recommendations;
};

UserInteraction.getTrendingContent = function(targetType, hours = 24, limit = 20) {
  const startDate = new Date();
  startDate.setHours(startDate.getHours() - hours);
  
  return this.findAll({
    attributes: [
      'targetId',
      [sequelize.fn('COUNT', sequelize.col('id')), 'interactions'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN interaction_type = 'like' THEN 1 END")), 'likes'],
      [sequelize.fn('COUNT', sequelize.literal("CASE WHEN interaction_type = 'share' THEN 1 END")), 'shares'],
      // 计算趋势分数：最近的交互权重更高
      [sequelize.literal(`
        SUM(
          CASE 
            WHEN created_at > NOW() - INTERVAL '1 hour' THEN 3
            WHEN created_at > NOW() - INTERVAL '6 hours' THEN 2
            ELSE 1
          END
        )
      `), 'trendScore']
    ],
    where: {
      targetType,
      isActive: true,
      value: 1,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate
      }
    },
    group: ['targetId'],
    having: sequelize.literal('COUNT(id) >= 3'), // 至少3个交互才算趋势
    order: [[sequelize.literal('trendScore'), 'DESC']],
    limit,
    raw: true
  });
};

UserInteraction.bulkAddInteractions = async function(interactions) {
  const transaction = await sequelize.transaction();
  
  try {
    const results = [];
    
    for (const interaction of interactions) {
      const result = await this.addInteraction(
        interaction.userId,
        interaction.targetType,
        interaction.targetId,
        interaction.interactionType,
        interaction.metadata || {},
        { transaction }
      );
      results.push(result);
    }
    
    await transaction.commit();
    return results;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

module.exports = UserInteraction;
