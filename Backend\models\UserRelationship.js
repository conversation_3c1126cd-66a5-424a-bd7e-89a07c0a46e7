const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserRelationship = sequelize.define('UserRelationship', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  followerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '关注者ID'
  },
  followingId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '被关注者ID'
  },
  relationshipType: {
    type: DataTypes.ENUM(
      'follow',     // 关注
      'block',      // 屏蔽
      'mute',       // 静音
      'close_friend' // 密友
    ),
    allowNull: false,
    defaultValue: 'follow',
    comment: '关系类型'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',    // 待确认（私密账户）
      'accepted',   // 已接受
      'rejected',   // 已拒绝
      'cancelled'   // 已取消
    ),
    allowNull: false,
    defaultValue: 'accepted',
    comment: '关系状态'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否活跃'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: '关系元数据'
  },
  notificationSettings: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {
      posts: true,
      comments: true,
      likes: false,
      mentions: true
    },
    comment: '通知设置'
  }
}, {
  tableName: 'user_relationships',
  timestamps: true,
  indexes: [
    {
      fields: ['followerId']
    },
    {
      fields: ['followingId']
    },
    {
      fields: ['relationshipType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['followerId', 'followingId'],
      unique: true,
      name: 'user_relationships_unique_idx'
    },
    {
      fields: ['followingId', 'relationshipType', 'status'],
      name: 'user_relationships_following_type_status_idx'
    },
    {
      fields: ['createdAt']
    }
  ]
});

// Instance methods
UserRelationship.prototype.accept = function() {
  return this.update({ status: 'accepted' });
};

UserRelationship.prototype.reject = function() {
  return this.update({ status: 'rejected' });
};

UserRelationship.prototype.cancel = function() {
  return this.update({ 
    status: 'cancelled',
    isActive: false 
  });
};

UserRelationship.prototype.updateNotificationSettings = function(settings) {
  return this.update({
    notificationSettings: {
      ...this.notificationSettings,
      ...settings
    }
  });
};

// Static methods
UserRelationship.follow = async function(followerId, followingId, isPrivateAccount = false) {
  if (followerId === followingId) {
    throw new Error('Cannot follow yourself');
  }

  const transaction = await sequelize.transaction();
  
  try {
    // 检查是否已存在关系
    const existing = await this.findOne({
      where: { followerId, followingId },
      transaction
    });

    if (existing) {
      if (existing.relationshipType === 'follow' && existing.isActive) {
        throw new Error('Already following this user');
      }
      
      // 重新激活关注关系
      await existing.update({
        relationshipType: 'follow',
        status: isPrivateAccount ? 'pending' : 'accepted',
        isActive: true
      }, { transaction });
      
      await transaction.commit();
      return existing;
    }

    // 创建新的关注关系
    const relationship = await this.create({
      followerId,
      followingId,
      relationshipType: 'follow',
      status: isPrivateAccount ? 'pending' : 'accepted',
      isActive: true
    }, { transaction });

    await transaction.commit();
    return relationship;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

UserRelationship.unfollow = function(followerId, followingId) {
  return this.update(
    { isActive: false },
    {
      where: {
        followerId,
        followingId,
        relationshipType: 'follow'
      }
    }
  );
};

UserRelationship.block = async function(blockerId, blockedId) {
  if (blockerId === blockedId) {
    throw new Error('Cannot block yourself');
  }

  const transaction = await sequelize.transaction();
  
  try {
    // 移除现有的关注关系
    await this.update(
      { isActive: false },
      {
        where: {
          [sequelize.Sequelize.Op.or]: [
            { followerId: blockerId, followingId: blockedId },
            { followerId: blockedId, followingId: blockerId }
          ]
        },
        transaction
      }
    );

    // 创建或更新屏蔽关系
    const [relationship] = await this.findOrCreate({
      where: { followerId: blockerId, followingId: blockedId },
      defaults: {
        relationshipType: 'block',
        status: 'accepted',
        isActive: true
      },
      transaction
    });

    if (!relationship.isActive || relationship.relationshipType !== 'block') {
      await relationship.update({
        relationshipType: 'block',
        status: 'accepted',
        isActive: true
      }, { transaction });
    }

    await transaction.commit();
    return relationship;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

UserRelationship.unblock = function(blockerId, blockedId) {
  return this.update(
    { isActive: false },
    {
      where: {
        followerId: blockerId,
        followingId: blockedId,
        relationshipType: 'block'
      }
    }
  );
};

UserRelationship.getFollowers = function(userId, options = {}) {
  const { limit = 20, offset = 0, includeUser = true } = options;
  
  const queryOptions = {
    where: {
      followingId: userId,
      relationshipType: 'follow',
      status: 'accepted',
      isActive: true
    },
    order: [['createdAt', 'DESC']],
    limit,
    offset
  };

  if (includeUser) {
    queryOptions.include = [
      {
        model: sequelize.models.User,
        as: 'follower',
        attributes: ['id', 'username', 'firstName', 'lastName', 'avatar', 'bio']
      }
    ];
  }

  return this.findAndCountAll(queryOptions);
};

UserRelationship.getFollowing = function(userId, options = {}) {
  const { limit = 20, offset = 0, includeUser = true } = options;
  
  const queryOptions = {
    where: {
      followerId: userId,
      relationshipType: 'follow',
      status: 'accepted',
      isActive: true
    },
    order: [['createdAt', 'DESC']],
    limit,
    offset
  };

  if (includeUser) {
    queryOptions.include = [
      {
        model: sequelize.models.User,
        as: 'following',
        attributes: ['id', 'username', 'firstName', 'lastName', 'avatar', 'bio']
      }
    ];
  }

  return this.findAndCountAll(queryOptions);
};

UserRelationship.getRelationshipStatus = function(followerId, followingId) {
  return this.findOne({
    where: { followerId, followingId },
    attributes: ['relationshipType', 'status', 'isActive']
  });
};

UserRelationship.getMutualFollowers = async function(userId1, userId2) {
  const user1Followers = await this.findAll({
    where: {
      followingId: userId1,
      relationshipType: 'follow',
      status: 'accepted',
      isActive: true
    },
    attributes: ['followerId']
  });

  const user2Followers = await this.findAll({
    where: {
      followingId: userId2,
      relationshipType: 'follow',
      status: 'accepted',
      isActive: true
    },
    attributes: ['followerId']
  });

  const user1FollowerIds = user1Followers.map(f => f.followerId);
  const user2FollowerIds = user2Followers.map(f => f.followerId);
  
  const mutualFollowerIds = user1FollowerIds.filter(id => user2FollowerIds.includes(id));
  
  if (mutualFollowerIds.length === 0) {
    return [];
  }

  return sequelize.models.User.findAll({
    where: {
      id: {
        [sequelize.Sequelize.Op.in]: mutualFollowerIds
      }
    },
    attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
  });
};

UserRelationship.getRecommendations = async function(userId, limit = 10) {
  // 基于共同关注的推荐算法
  const userFollowing = await this.findAll({
    where: {
      followerId: userId,
      relationshipType: 'follow',
      status: 'accepted',
      isActive: true
    },
    attributes: ['followingId']
  });

  const followingIds = userFollowing.map(f => f.followingId);
  
  if (followingIds.length === 0) {
    // 如果用户没有关注任何人，返回热门用户
    return this.getPopularUsers(limit);
  }

  // 找到被用户关注的人也关注的用户
  const recommendations = await sequelize.query(`
    WITH user_following AS (
      SELECT following_id FROM user_relationships 
      WHERE follower_id = :userId 
        AND relationship_type = 'follow' 
        AND status = 'accepted' 
        AND is_active = true
    ),
    potential_follows AS (
      SELECT 
        ur.following_id as recommended_user_id,
        COUNT(*) as mutual_connections,
        COUNT(DISTINCT ur.follower_id) as connection_strength
      FROM user_relationships ur
      WHERE ur.follower_id IN (SELECT following_id FROM user_following)
        AND ur.relationship_type = 'follow'
        AND ur.status = 'accepted'
        AND ur.is_active = true
        AND ur.following_id != :userId
        AND ur.following_id NOT IN (SELECT following_id FROM user_following)
        AND ur.following_id NOT IN (
          SELECT following_id FROM user_relationships 
          WHERE follower_id = :userId AND relationship_type = 'block' AND is_active = true
        )
      GROUP BY ur.following_id
      HAVING COUNT(*) >= 2
    )
    SELECT 
      pf.*,
      u.username,
      u."firstName",
      u."lastName",
      u.avatar,
      u.bio,
      (SELECT COUNT(*) FROM user_relationships WHERE following_id = pf.recommended_user_id AND relationship_type = 'follow' AND status = 'accepted' AND is_active = true) as follower_count
    FROM potential_follows pf
    JOIN users u ON u.id = pf.recommended_user_id
    ORDER BY pf.mutual_connections DESC, follower_count DESC
    LIMIT :limit
  `, {
    replacements: { userId, limit },
    type: sequelize.QueryTypes.SELECT
  });

  return recommendations;
};

UserRelationship.getPopularUsers = function(limit = 10) {
  return sequelize.query(`
    SELECT 
      u.id,
      u.username,
      u."firstName",
      u."lastName",
      u.avatar,
      u.bio,
      COUNT(ur.follower_id) as follower_count,
      (SELECT COUNT(*) FROM articles WHERE author_id = u.id AND published = true) as article_count
    FROM users u
    LEFT JOIN user_relationships ur ON ur.following_id = u.id 
      AND ur.relationship_type = 'follow' 
      AND ur.status = 'accepted' 
      AND ur.is_active = true
    WHERE u.role IN ('user', 'author', 'editor')
      AND u."emailVerified" = true
    GROUP BY u.id
    HAVING COUNT(ur.follower_id) > 0
    ORDER BY follower_count DESC, article_count DESC
    LIMIT :limit
  `, {
    replacements: { limit },
    type: sequelize.QueryTypes.SELECT
  });
};

UserRelationship.getNetworkStats = function(userId) {
  return sequelize.query(`
    SELECT 
      (SELECT COUNT(*) FROM user_relationships WHERE follower_id = :userId AND relationship_type = 'follow' AND status = 'accepted' AND is_active = true) as following_count,
      (SELECT COUNT(*) FROM user_relationships WHERE following_id = :userId AND relationship_type = 'follow' AND status = 'accepted' AND is_active = true) as follower_count,
      (SELECT COUNT(*) FROM user_relationships WHERE follower_id = :userId AND relationship_type = 'block' AND is_active = true) as blocked_count,
      (SELECT COUNT(*) FROM user_relationships WHERE following_id = :userId AND status = 'pending' AND is_active = true) as pending_requests
  `, {
    replacements: { userId },
    type: sequelize.QueryTypes.SELECT
  }).then(results => results[0]);
};

UserRelationship.searchUsers = function(userId, query, limit = 20) {
  return sequelize.query(`
    SELECT 
      u.id,
      u.username,
      u."firstName",
      u."lastName",
      u.avatar,
      u.bio,
      (SELECT COUNT(*) FROM user_relationships WHERE following_id = u.id AND relationship_type = 'follow' AND status = 'accepted' AND is_active = true) as follower_count,
      CASE 
        WHEN ur.relationship_type = 'follow' AND ur.status = 'accepted' AND ur.is_active = true THEN 'following'
        WHEN ur.relationship_type = 'follow' AND ur.status = 'pending' AND ur.is_active = true THEN 'pending'
        WHEN ur.relationship_type = 'block' AND ur.is_active = true THEN 'blocked'
        ELSE 'none'
      END as relationship_status
    FROM users u
    LEFT JOIN user_relationships ur ON ur.following_id = u.id AND ur.follower_id = :userId
    WHERE u.id != :userId
      AND (
        u.username ILIKE :query 
        OR u."firstName" ILIKE :query 
        OR u."lastName" ILIKE :query
        OR CONCAT(u."firstName", ' ', u."lastName") ILIKE :query
      )
      AND u."emailVerified" = true
    ORDER BY 
      CASE WHEN ur.relationship_type = 'follow' AND ur.status = 'accepted' THEN 1 ELSE 2 END,
      follower_count DESC,
      u.username
    LIMIT :limit
  `, {
    replacements: { 
      userId, 
      query: `%${query}%`,
      limit 
    },
    type: sequelize.QueryTypes.SELECT
  });
};

module.exports = UserRelationship;
