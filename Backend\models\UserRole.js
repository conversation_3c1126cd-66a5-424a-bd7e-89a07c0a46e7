const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserRole = sequelize.define('UserRole', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  roleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'roles',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  assignedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When this role was assigned to the user'
  },
  assignedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who assigned this role'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this role assignment expires (null = never expires)'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this role assignment is active'
  },
  isPrimary: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this is the user\'s primary role'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata for this role assignment'
  }
}, {
  tableName: 'user_roles',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['userId', 'roleId']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['roleId']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['isPrimary']
    },
    {
      fields: ['expiresAt']
    },
    {
      fields: ['assignedAt']
    }
  ]
});

// Instance methods
UserRole.prototype.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

UserRole.prototype.activate = async function() {
  this.isActive = true;
  return await this.save();
};

UserRole.prototype.deactivate = async function() {
  this.isActive = false;
  return await this.save();
};

UserRole.prototype.setPrimary = async function() {
  // First, unset any existing primary role for this user
  await UserRole.update(
    { isPrimary: false },
    {
      where: {
        userId: this.userId,
        isPrimary: true
      }
    }
  );

  // Then set this role as primary
  this.isPrimary = true;
  return await this.save();
};

UserRole.prototype.extend = async function(newExpiryDate) {
  this.expiresAt = newExpiryDate;
  return await this.save();
};

// Class methods
UserRole.getActiveByUser = async function(userId) {
  const Role = sequelize.models.Role;
  
  return await this.findAll({
    where: {
      userId,
      isActive: true,
      [sequelize.Sequelize.Op.or]: [
        { expiresAt: null },
        { expiresAt: { [sequelize.Sequelize.Op.gt]: new Date() } }
      ]
    },
    include: [{
      model: Role,
      where: { isActive: true }
    }],
    order: [['isPrimary', 'DESC'], ['assignedAt', 'ASC']]
  });
};

UserRole.getPrimaryRole = async function(userId) {
  const Role = sequelize.models.Role;
  
  return await this.findOne({
    where: {
      userId,
      isPrimary: true,
      isActive: true,
      [sequelize.Sequelize.Op.or]: [
        { expiresAt: null },
        { expiresAt: { [sequelize.Sequelize.Op.gt]: new Date() } }
      ]
    },
    include: [{
      model: Role,
      where: { isActive: true }
    }]
  });
};

UserRole.getUsersByRole = async function(roleId, includeExpired = false) {
  const User = sequelize.models.User;
  
  const whereClause = {
    roleId,
    isActive: true
  };

  if (!includeExpired) {
    whereClause[sequelize.Sequelize.Op.or] = [
      { expiresAt: null },
      { expiresAt: { [sequelize.Sequelize.Op.gt]: new Date() } }
    ];
  }
  
  return await this.findAll({
    where: whereClause,
    include: [{
      model: User,
      where: { isActive: true }
    }],
    order: [['assignedAt', 'DESC']]
  });
};

UserRole.assignRole = async function(userId, roleId, assignedBy = null, expiresAt = null, isPrimary = false) {
  const [userRole, created] = await this.findOrCreate({
    where: {
      userId,
      roleId
    },
    defaults: {
      assignedBy,
      expiresAt,
      isPrimary,
      isActive: true
    }
  });

  if (!created) {
    // Update existing assignment
    userRole.isActive = true;
    userRole.assignedBy = assignedBy;
    userRole.expiresAt = expiresAt;
    userRole.assignedAt = new Date();
    
    if (isPrimary) {
      await userRole.setPrimary();
    } else {
      userRole.isPrimary = isPrimary;
    }
    
    await userRole.save();
  } else if (isPrimary) {
    await userRole.setPrimary();
  }

  return userRole;
};

UserRole.revokeRole = async function(userId, roleId) {
  return await this.update(
    { isActive: false },
    {
      where: {
        userId,
        roleId,
        isActive: true
      }
    }
  );
};

UserRole.bulkAssignRoles = async function(userId, roleIds, assignedBy = null) {
  const results = [];
  
  for (const roleId of roleIds) {
    const result = await this.assignRole(userId, roleId, assignedBy);
    results.push(result);
  }
  
  return results;
};

UserRole.bulkRevokeRoles = async function(userId, roleIds) {
  return await this.update(
    { isActive: false },
    {
      where: {
        userId,
        roleId: roleIds,
        isActive: true
      }
    }
  );
};

UserRole.syncUserRoles = async function(userId, roleIds, assignedBy = null) {
  // First, deactivate all current roles for this user
  await this.update(
    { isActive: false, isPrimary: false },
    {
      where: {
        userId,
        isActive: true
      }
    }
  );

  // Then, assign the new roles
  const results = await this.bulkAssignRoles(userId, roleIds, assignedBy);
  
  // Set the first role as primary if no primary role exists
  if (results.length > 0) {
    await results[0].setPrimary();
  }
  
  return results;
};

UserRole.cleanupExpiredRoles = async function() {
  return await this.update(
    { isActive: false },
    {
      where: {
        isActive: true,
        expiresAt: {
          [sequelize.Sequelize.Op.lt]: new Date()
        }
      }
    }
  );
};

module.exports = UserRole;
