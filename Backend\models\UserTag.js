const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserTag = sequelize.define('UserTag', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  tagId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'tags',
      key: 'id'
    }
  },
  interestLevel: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium'
  },
  source: {
    type: DataTypes.ENUM('manual', 'auto', 'imported'),
    defaultValue: 'manual'
  }
}, {
  tableName: 'user_tags',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['userId', 'tagId']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['tagId']
    },
    {
      fields: ['interestLevel']
    }
  ]
});

module.exports = UserTag;
