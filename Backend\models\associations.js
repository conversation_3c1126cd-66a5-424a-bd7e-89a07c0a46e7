const User = require('./User');
const Article = require('./Article');
const Comment = require('./Comment');
const Follow = require('./Follow');
const Message = require('./Message');
const Tag = require('./Tag');
const UserTag = require('./UserTag');
const Activity = require('./Activity');
const Share = require('./Share');
const Draft = require('./Draft');
const MediaFile = require('./MediaFile');
const ContentReview = require('./ContentReview');
const ReviewRule = require('./ReviewRule');
const UserBehavior = require('./UserBehavior');
const ReadingStats = require('./ReadingStats');
const SearchLog = require('./SearchLog');
const UserProfile = require('./UserProfile');
const Notification = require('./Notification');
const NotificationPreference = require('./NotificationPreference');
const PushSubscription = require('./PushSubscription');
const EmailLog = require('./EmailLog');
const ArticleVersion = require('./ArticleVersion');
const ScheduledPost = require('./ScheduledPost');
const DirectMessage = require('./DirectMessage');
const UserInteraction = require('./UserInteraction');
const UserRelationship = require('./UserRelationship');

// Permission system models
const Permission = require('./Permission');
const Role = require('./Role');
const RolePermission = require('./RolePermission');
const UserRole = require('./UserRole');

// User associations
// User.hasMany(Article, { foreignKey: 'authorId', as: 'articles' }); // Commented out - no authorId in articles table
User.hasMany(Comment, { foreignKey: 'authorId', as: 'comments' });
User.hasMany(Activity, { foreignKey: 'userId', as: 'activities' });
User.hasMany(Share, { foreignKey: 'userId', as: 'shares' });

// Follow associations
User.belongsToMany(User, {
  through: Follow,
  as: 'following',
  foreignKey: 'followerId',
  otherKey: 'followingId'
});

User.belongsToMany(User, {
  through: Follow,
  as: 'followers',
  foreignKey: 'followingId',
  otherKey: 'followerId'
});

Follow.belongsTo(User, { as: 'follower', foreignKey: 'followerId' });
Follow.belongsTo(User, { as: 'following', foreignKey: 'followingId' });

// Message associations
User.hasMany(Message, { foreignKey: 'senderId', as: 'sentMessages' });
User.hasMany(Message, { foreignKey: 'receiverId', as: 'receivedMessages' });
Message.belongsTo(User, { as: 'sender', foreignKey: 'senderId' });
Message.belongsTo(User, { as: 'receiver', foreignKey: 'receiverId' });

// Tag associations
User.belongsToMany(Tag, {
  through: UserTag,
  as: 'tags',
  foreignKey: 'userId',
  otherKey: 'tagId'
});

Tag.belongsToMany(User, {
  through: UserTag,
  as: 'users',
  foreignKey: 'tagId',
  otherKey: 'userId'
});

UserTag.belongsTo(User, { foreignKey: 'userId' });
UserTag.belongsTo(Tag, { foreignKey: 'tagId' });

// Article associations
// Article.belongsTo(User, { foreignKey: 'authorId', as: 'authorUser' }); // Commented out - no authorId in articles table
Article.hasMany(Comment, { foreignKey: 'articleId', as: 'comments' });
Article.hasMany(Share, { foreignKey: 'articleId', as: 'shares' });

// Comment associations
Comment.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
Comment.belongsTo(User, { foreignKey: 'authorId', as: 'authorUser' });

// Activity associations
Activity.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Share associations
Share.belongsTo(User, { foreignKey: 'userId', as: 'user' });
Share.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });

// Draft associations
User.hasMany(Draft, { foreignKey: 'authorId', as: 'drafts' });
Draft.belongsTo(User, { foreignKey: 'authorId', as: 'author' });

Draft.belongsTo(Draft, { foreignKey: 'parentDraftId', as: 'parentDraft' });
Draft.hasMany(Draft, { foreignKey: 'parentDraftId', as: 'versions' });

Draft.belongsTo(Article, { foreignKey: 'publishedArticleId', as: 'publishedArticle' });
Article.hasOne(Draft, { foreignKey: 'publishedArticleId', as: 'sourceDraft' });

// MediaFile associations
User.hasMany(MediaFile, { foreignKey: 'uploaderId', as: 'uploadedFiles' });
MediaFile.belongsTo(User, { foreignKey: 'uploaderId', as: 'uploader' });

// ContentReview associations
User.hasMany(ContentReview, { foreignKey: 'submitterId', as: 'submittedReviews' });
User.hasMany(ContentReview, { foreignKey: 'reviewerId', as: 'reviewedContent' });
ContentReview.belongsTo(User, { foreignKey: 'submitterId', as: 'submitter' });
ContentReview.belongsTo(User, { foreignKey: 'reviewerId', as: 'reviewer' });

// ReviewRule associations
User.hasMany(ReviewRule, { foreignKey: 'createdBy', as: 'createdRules' });
User.hasMany(ReviewRule, { foreignKey: 'updatedBy', as: 'updatedRules' });
ReviewRule.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });
ReviewRule.belongsTo(User, { foreignKey: 'updatedBy', as: 'updater' });

// Analytics associations
User.hasMany(UserBehavior, { foreignKey: 'userId', as: 'behaviors' });
User.hasMany(ReadingStats, { foreignKey: 'userId', as: 'readingStats' });
User.hasMany(SearchLog, { foreignKey: 'userId', as: 'searchLogs' });
User.hasOne(UserProfile, { foreignKey: 'userId', as: 'profile' });

UserBehavior.belongsTo(User, { foreignKey: 'userId', as: 'user' });
ReadingStats.belongsTo(User, { foreignKey: 'userId', as: 'user' });
ReadingStats.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
SearchLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });
UserProfile.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Article.hasMany(ReadingStats, { foreignKey: 'articleId', as: 'readingStats' });

// Notification associations
User.hasMany(Notification, { foreignKey: 'userId', as: 'notifications' });
Notification.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasOne(NotificationPreference, { foreignKey: 'userId', as: 'notificationPreference' });
NotificationPreference.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasMany(PushSubscription, { foreignKey: 'userId', as: 'pushSubscriptions' });
PushSubscription.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Email log associations
User.hasMany(EmailLog, { foreignKey: 'userId', as: 'emailLogs' });
EmailLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Article version associations
Article.hasMany(ArticleVersion, { foreignKey: 'articleId', as: 'versions' });
ArticleVersion.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
ArticleVersion.belongsTo(User, { foreignKey: 'authorId', as: 'author' });
ArticleVersion.belongsTo(User, { foreignKey: 'editorId', as: 'editor' });

// Scheduled post associations
Article.hasMany(ScheduledPost, { foreignKey: 'articleId', as: 'scheduledPosts' });
ScheduledPost.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
ScheduledPost.belongsTo(ArticleVersion, { foreignKey: 'versionId', as: 'version' });
ScheduledPost.belongsTo(User, { foreignKey: 'authorId', as: 'author' });
ScheduledPost.belongsTo(User, { foreignKey: 'scheduledBy', as: 'scheduledByUser' });
ScheduledPost.belongsTo(User, { foreignKey: 'publishedBy', as: 'publishedByUser' });

// Direct message associations
DirectMessage.belongsTo(User, { foreignKey: 'senderId', as: 'sender' });
DirectMessage.belongsTo(User, { foreignKey: 'receiverId', as: 'receiver' });
DirectMessage.belongsTo(DirectMessage, { foreignKey: 'replyToId', as: 'replyTo' });
DirectMessage.hasMany(DirectMessage, { foreignKey: 'replyToId', as: 'replies' });

// User interaction associations
UserInteraction.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// User relationship associations
UserRelationship.belongsTo(User, { foreignKey: 'followerId', as: 'follower' });
UserRelationship.belongsTo(User, { foreignKey: 'followingId', as: 'following' });

module.exports = {
  User,
  Article,
  Comment,
  Follow,
  Message,
  Tag,
  UserTag,
  Activity,
  Share,
  Draft,
  MediaFile,
  ContentReview,
  ReviewRule,
  UserBehavior,
  ReadingStats,
  SearchLog,
  UserProfile,
  Notification,
  NotificationPreference,
  PushSubscription,
  EmailLog,
  ArticleVersion,
  ScheduledPost,
  DirectMessage,
  UserInteraction,
  UserRelationship,

  // Permission system models
  Permission,
  Role,
  RolePermission,
  UserRole
};

// Permission system associations
// Role and Permission many-to-many relationship
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: 'roleId',
  otherKey: 'permissionId',
  as: 'permissions'
});

Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: 'permissionId',
  otherKey: 'roleId',
  as: 'roles'
});

// User and Role many-to-many relationship
User.belongsToMany(Role, {
  through: UserRole,
  foreignKey: 'userId',
  otherKey: 'roleId',
  as: 'roles'
});

Role.belongsToMany(User, {
  through: UserRole,
  foreignKey: 'roleId',
  otherKey: 'userId',
  as: 'users'
});

// Direct associations for easier querying
RolePermission.belongsTo(Role, { foreignKey: 'roleId' });
RolePermission.belongsTo(Permission, { foreignKey: 'permissionId' });
RolePermission.belongsTo(User, { foreignKey: 'grantedBy', as: 'grantor' });

UserRole.belongsTo(User, { foreignKey: 'userId' });
UserRole.belongsTo(Role, { foreignKey: 'roleId' });
UserRole.belongsTo(User, { foreignKey: 'assignedBy', as: 'assignor' });

Role.hasMany(RolePermission, { foreignKey: 'roleId' });
Permission.hasMany(RolePermission, { foreignKey: 'permissionId' });
User.hasMany(UserRole, { foreignKey: 'userId' });
Role.hasMany(UserRole, { foreignKey: 'roleId' });
