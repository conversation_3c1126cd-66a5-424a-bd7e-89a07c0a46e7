{"name": "newzora-backend", "version": "1.0.0", "description": "Backend API for Newzora", "main": "server-launcher.js", "scripts": {"start": "node server-launcher.js", "dev": "nodemon server-launcher.js", "stable": "node server-launcher.js", "legacy": "node legacy/server.js", "seed": "node scripts/seed.js", "init-db": "node scripts/initDatabase.js", "init-supabase": "node scripts/init-supabase.js", "user-mgmt": "node scripts/supabase-user-management.js", "health-check": "node scripts/api-health-check.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration", "test:api": "node utils/apiTester.js", "test:api:dev": "nodemon utils/apiTester.js", "health": "node -e \"console.log('Server configuration check...'); require('./stable-server'); console.log('✅ Server configuration loaded successfully')\"", "verify": "node scripts/verify-setup.js", "network-test": "node scripts/simple-auth-test.js"}, "keywords": ["content", "hub", "api", "express"], "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.5", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "colors": "^1.4.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "handlebars": "^4.7.8", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "mjml": "^4.15.3", "mjml-handlebars": "^0.3.2", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.4", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.16.3", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "web-push": "^3.6.7", "winston": "^3.17.0"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.0.1", "supertest": "^7.1.3"}}