const request = require('supertest')
const express = require('express')
const { sequelize } = require('../../config/database')
const User = require('../../models/User')
const userRoutes = require('../users')

// Create test app
const app = express()
app.use(express.json())
app.use('/api/users', userRoutes)

describe('User Routes', () => {
  beforeAll(async () => {
    // Ensure database is connected and synced
    await sequelize.authenticate()
    await sequelize.sync({ force: true })
  })

  afterAll(async () => {
    await sequelize.close()
  })

  beforeEach(async () => {
    // Clear users table before each test
    await User.destroy({ where: {}, truncate: true })
  })

  describe('POST /api/users/register', () => {
    const validUserData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!'
    }

    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(201)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('注册成功')
      })

      // Verify user was created in database
      const user = await User.findOne({ where: { email: validUserData.email } })
      expect(user).toBeTruthy()
      expect(user.username).toBe(validUserData.username)
      expect(user.email).toBe(validUserData.email)
    })

    it('should reject registration with invalid email', async () => {
      const invalidData = { ...validUserData, email: 'invalid-email' }
      
      const response = await request(app)
        .post('/api/users/register')
        .send(invalidData)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('valid email')
      })
    })

    it('should reject registration with weak password', async () => {
      const weakPasswordData = { ...validUserData, password: '123' }
      
      const response = await request(app)
        .post('/api/users/register')
        .send(weakPasswordData)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Password must')
      })
    })

    it('should reject duplicate email registration', async () => {
      // Create first user
      await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(201)

      // Try to register with same email
      const response = await request(app)
        .post('/api/users/register')
        .send(validUserData)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Email already registered')
      })
    })

    it('should reject registration with missing required fields', async () => {
      const incompleteData = { username: 'testuser' }
      
      const response = await request(app)
        .post('/api/users/register')
        .send(incompleteData)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false
      })
    })
  })

  describe('POST /api/users/login', () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!'
    }

    beforeEach(async () => {
      // Create a test user before each login test
      await request(app)
        .post('/api/users/register')
        .send(userData)
    })

    it('should login successfully with email', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: userData.password
        })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('登录成功'),
        user: {
          id: expect.any(Number),
          username: userData.username,
          email: userData.email,
          role: 'user'
        },
        token: expect.any(String)
      })

      // Verify JWT token format
      expect(response.body.token).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/)
    })

    it('should login successfully with username', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.username,
          password: userData.password
        })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        user: {
          username: userData.username,
          email: userData.email
        },
        token: expect.any(String)
      })
    })

    it('should reject login with wrong password', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('密码错误')
      })
    })

    it('should reject login with non-existent user', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          identifier: '<EMAIL>',
          password: userData.password
        })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('用户不存在')
      })
    })

    it('should reject login with missing credentials', async () => {
      const response = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email
          // missing password
        })
        .expect(400)

      expect(response.body).toMatchObject({
        success: false
      })
    })
  })

  describe('GET /api/users/profile', () => {
    let authToken
    let userId

    beforeEach(async () => {
      // Register and login to get auth token
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Password123!'
      }

      await request(app)
        .post('/api/users/register')
        .send(userData)

      const loginResponse = await request(app)
        .post('/api/users/login')
        .send({
          identifier: userData.email,
          password: userData.password
        })

      authToken = loginResponse.body.token
      userId = loginResponse.body.user.id
    })

    it('should get user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        user: {
          id: userId,
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user'
        }
      })

      // Should not include sensitive data
      expect(response.body.user.password).toBeUndefined()
    })

    it('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Access token required')
      })
    })

    it('should reject request with invalid token', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Invalid token')
      })
    })
  })
})
