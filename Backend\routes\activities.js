const express = require('express');
const router = express.Router();
const { User, Activity, Article, Follow } = require('../models/associations');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// Create activity (internal use)
const createActivity = async (userId, activityType, targetType = null, targetId = null, metadata = {}, isPublic = true) => {
  try {
    return await Activity.create({
      userId,
      activityType,
      targetType,
      targetId,
      metadata,
      isPublic
    });
  } catch (error) {
    console.error('Error creating activity:', error);
  }
};

// Get user's timeline (activities from followed users)
router.get('/timeline', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Get users that current user follows
    const followedUsers = await Follow.findAll({
      where: { 
        followerId: userId,
        status: 'active'
      },
      attributes: ['followingId']
    });

    const followedUserIds = followedUsers.map(f => f.followingId);
    followedUserIds.push(userId); // Include own activities

    // Get activities from followed users
    const { count, rows: activities } = await Activity.findAndCountAll({
      where: {
        userId: { [Op.in]: followedUserIds },
        isPublic: true
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'avatar']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    // Enrich activities with target data
    const enrichedActivities = await Promise.all(
      activities.map(async (activity) => {
        const activityData = activity.toJSON();
        
        // Add target data based on target type
        if (activity.targetType === 'article' && activity.targetId) {
          const article = await Article.findByPk(activity.targetId, {
            attributes: ['id', 'title', 'image', 'category']
          });
          activityData.targetData = article;
        } else if (activity.targetType === 'user' && activity.targetId) {
          const user = await User.findByPk(activity.targetId, {
            attributes: ['id', 'username', 'avatar']
          });
          activityData.targetData = user;
        }
        
        return activityData;
      })
    );

    res.json({
      success: true,
      data: {
        activities: enrichedActivities,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching timeline:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get user's own activities
router.get('/user/:userId?', async (req, res) => {
  try {
    const userId = req.params.userId || (req.user ? req.user.id : null);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    if (!userId) {
      return res.status(400).json({ 
        success: false, 
        message: 'User ID is required' 
      });
    }

    const { count, rows: activities } = await Activity.findAndCountAll({
      where: {
        userId: parseInt(userId),
        isPublic: true
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'avatar']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    // Enrich activities with target data
    const enrichedActivities = await Promise.all(
      activities.map(async (activity) => {
        const activityData = activity.toJSON();
        
        if (activity.targetType === 'article' && activity.targetId) {
          const article = await Article.findByPk(activity.targetId, {
            attributes: ['id', 'title', 'image', 'category']
          });
          activityData.targetData = article;
        } else if (activity.targetType === 'user' && activity.targetId) {
          const user = await User.findByPk(activity.targetId, {
            attributes: ['id', 'username', 'avatar']
          });
          activityData.targetData = user;
        }
        
        return activityData;
      })
    );

    res.json({
      success: true,
      data: {
        activities: enrichedActivities,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching user activities:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Record article like activity
router.post('/article-like', authenticateToken, async (req, res) => {
  try {
    const { articleId } = req.body;
    const userId = req.user.id;

    if (!articleId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Article ID is required' 
      });
    }

    // Check if article exists
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({ 
        success: false, 
        message: 'Article not found' 
      });
    }

    // Create activity
    const activity = await createActivity(
      userId,
      'article_liked',
      'article',
      articleId,
      { articleTitle: article.title }
    );

    res.status(201).json({
      success: true,
      message: 'Activity recorded',
      data: activity
    });
  } catch (error) {
    console.error('Error recording article like activity:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Record user follow activity
router.post('/user-follow', authenticateToken, async (req, res) => {
  try {
    const { followedUserId } = req.body;
    const userId = req.user.id;

    if (!followedUserId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Followed user ID is required' 
      });
    }

    // Check if user exists
    const followedUser = await User.findByPk(followedUserId);
    if (!followedUser) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Create activity
    const activity = await createActivity(
      userId,
      'user_followed',
      'user',
      followedUserId,
      { followedUsername: followedUser.username }
    );

    res.status(201).json({
      success: true,
      message: 'Activity recorded',
      data: activity
    });
  } catch (error) {
    console.error('Error recording user follow activity:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get activity statistics
router.get('/stats/:userId?', async (req, res) => {
  try {
    const userId = req.params.userId || (req.user ? req.user.id : null);

    if (!userId) {
      return res.status(400).json({ 
        success: false, 
        message: 'User ID is required' 
      });
    }

    // Get activity counts by type
    const activityStats = await Activity.findAll({
      where: { userId: parseInt(userId) },
      attributes: [
        'activityType',
        [Activity.sequelize.fn('COUNT', Activity.sequelize.col('id')), 'count']
      ],
      group: ['activityType'],
      raw: true
    });

    // Get total activity count
    const totalActivities = await Activity.count({
      where: { userId: parseInt(userId) }
    });

    res.json({
      success: true,
      data: {
        totalActivities,
        activityBreakdown: activityStats.reduce((acc, stat) => {
          acc[stat.activityType] = parseInt(stat.count);
          return acc;
        }, {})
      }
    });
  } catch (error) {
    console.error('Error fetching activity stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Export the createActivity function for use in other routes
module.exports = router;
module.exports.createActivity = createActivity;
