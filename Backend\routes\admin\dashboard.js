const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../../middleware/auth');
const { User, Article, Comment, Role, Permission } = require('../../models');
const { Op } = require('sequelize');

// Get dashboard statistics
router.get('/stats', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    // Get current date for today's stats
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // User statistics
    const [totalUsers, activeUsers, newUsersToday, verifiedUsers] = await Promise.all([
      User.count(),
      User.count({ where: { isActive: true } }),
      User.count({ 
        where: { 
          createdAt: {
            [Op.gte]: today,
            [Op.lt]: tomorrow
          }
        }
      }),
      User.count({ where: { emailVerified: true } })
    ]);

    // Content statistics
    const [totalArticles, draftArticles, totalComments, pendingReview] = await Promise.all([
      Article.count({ where: { status: 'published' } }),
      Article.count({ where: { status: 'draft' } }),
      Comment.count(),
      Article.count({ where: { status: 'pending' } })
    ]);

    // System statistics
    const serverUptime = process.uptime();
    const uptimeHours = Math.floor(serverUptime / 3600);
    const uptimeMinutes = Math.floor((serverUptime % 3600) / 60);
    const uptimeString = `${uptimeHours}h ${uptimeMinutes}m`;

    // Mock email statistics (will be replaced with real data)
    const emailsSent = 0; // This should come from email service
    const activeConnections = 0; // This should come from socket service

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        newToday: newUsersToday,
        verified: verifiedUsers
      },
      content: {
        articles: totalArticles,
        drafts: draftArticles,
        comments: totalComments,
        pendingReview: pendingReview
      },
      system: {
        emailsSent: emailsSent,
        activeConnections: activeConnections,
        serverUptime: uptimeString,
        databaseStatus: 'connected'
      }
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
});

// Get recent activity
router.get('/activity', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    // Get recent users
    const recentUsers = await User.findAll({
      order: [['createdAt', 'DESC']],
      limit: 5,
      attributes: ['id', 'username', 'email', 'createdAt']
    });

    // Get recent articles
    const recentArticles = await Article.findAll({
      order: [['createdAt', 'DESC']],
      limit: 5,
      attributes: ['id', 'title', 'status', 'createdAt'],
      include: [{
        model: User,
        as: 'author',
        attributes: ['username']
      }]
    });

    // Get recent comments
    const recentComments = await Comment.findAll({
      order: [['createdAt', 'DESC']],
      limit: 5,
      attributes: ['id', 'content', 'createdAt'],
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['username']
        },
        {
          model: Article,
          attributes: ['title']
        }
      ]
    });

    const activity = {
      recentUsers,
      recentArticles,
      recentComments
    };

    res.json({
      success: true,
      data: activity
    });
  } catch (error) {
    console.error('Dashboard activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent activity',
      error: error.message
    });
  }
});

// Get system health
router.get('/health', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { sequelize } = require('../../models');
    
    // Check database connection
    let databaseStatus = 'connected';
    let databaseLatency = 0;
    
    try {
      const startTime = Date.now();
      await sequelize.authenticate();
      databaseLatency = Date.now() - startTime;
    } catch (error) {
      databaseStatus = 'disconnected';
    }

    // Check memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024)
    };

    // Get CPU usage (simplified)
    const cpuUsage = process.cpuUsage();

    const health = {
      status: databaseStatus === 'connected' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: {
        status: databaseStatus,
        latency: databaseLatency
      },
      memory: memoryUsageMB,
      cpu: cpuUsage,
      uptime: process.uptime(),
      version: process.version,
      environment: process.env.NODE_ENV || 'development'
    };

    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    console.error('System health check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check system health',
      error: error.message
    });
  }
});

// Get user growth analytics
router.get('/analytics/users', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get daily user registrations
    const dailyRegistrations = await User.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        createdAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate
        }
      },
      group: [sequelize.fn('DATE', sequelize.col('createdAt'))],
      order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC']]
    });

    // Get user role distribution
    const roleDistribution = await User.findAll({
      attributes: [
        'role',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['role']
    });

    const analytics = {
      dailyRegistrations,
      roleDistribution,
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        days: days
      }
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('User analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user analytics',
      error: error.message
    });
  }
});

// Get content analytics
router.get('/analytics/content', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get daily article publications
    const dailyArticles = await Article.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('publishedAt')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        publishedAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate
        },
        status: 'published'
      },
      group: [sequelize.fn('DATE', sequelize.col('publishedAt'))],
      order: [[sequelize.fn('DATE', sequelize.col('publishedAt')), 'ASC']]
    });

    // Get article status distribution
    const statusDistribution = await Article.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status']
    });

    // Get top authors
    const topAuthors = await Article.findAll({
      attributes: [
        'authorId',
        [sequelize.fn('COUNT', sequelize.col('Article.id')), 'articleCount']
      ],
      include: [{
        model: User,
        as: 'author',
        attributes: ['username', 'email']
      }],
      where: {
        status: 'published'
      },
      group: ['authorId', 'author.id'],
      order: [[sequelize.fn('COUNT', sequelize.col('Article.id')), 'DESC']],
      limit: 10
    });

    const analytics = {
      dailyArticles,
      statusDistribution,
      topAuthors,
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        days: days
      }
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Content analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch content analytics',
      error: error.message
    });
  }
});

module.exports = router;
