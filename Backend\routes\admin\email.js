const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const { authenticateToken, requireRole } = require('../../middleware/auth');
const modernEmailService = require('../../services/ModernEmailService');
const { EMAIL_CONFIG } = require('../../config/email');

// Get all email templates
router.get('/templates', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const templatesDir = path.join(__dirname, '..', '..', 'templates', 'email');
    const files = await fs.readdir(templatesDir);
    
    const templates = [];
    
    for (const file of files) {
      if (file.endsWith('.mjml')) {
        const templateName = file.replace('.mjml', '');
        const filePath = path.join(templatesDir, file);
        const stats = await fs.stat(filePath);
        const content = await fs.readFile(filePath, 'utf8');
        
        // Extract title and preview from MJML content
        const titleMatch = content.match(/<mj-title>(.*?)<\/mj-title>/);
        const previewMatch = content.match(/<mj-preview>(.*?)<\/mj-preview>/);
        
        const emailType = Object.entries(EMAIL_CONFIG.EMAIL_TYPES).find(
          ([key, config]) => config.template === templateName
        );
        
        templates.push({
          id: templateName,
          name: titleMatch ? titleMatch[1] : templateName,
          subject: emailType ? emailType[1].subject : 'No subject configured',
          template: templateName,
          description: previewMatch ? previewMatch[1] : 'No description available',
          isActive: !!emailType,
          lastModified: stats.mtime.toISOString(),
          usage: 0, // This would come from a database in a real implementation
          size: stats.size
        });
      }
    }
    
    res.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email templates',
      error: error.message
    });
  }
});

// Get email statistics
router.get('/stats', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    // Get email service stats
    const serviceStats = modernEmailService.getStats();
    
    // Mock statistics - in a real implementation, these would come from a database
    const stats = {
      totalSent: 1250,
      successRate: 98.5,
      templates: serviceStats.templatesLoaded,
      recentActivity: [
        {
          id: '1',
          type: 'Welcome Email',
          recipient: '<EMAIL>',
          status: 'sent',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          type: 'Email Verification',
          recipient: '<EMAIL>',
          status: 'sent',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          id: '3',
          type: 'Password Reset',
          recipient: '<EMAIL>',
          status: 'failed',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        },
        {
          id: '4',
          type: 'Notification',
          recipient: '<EMAIL>',
          status: 'sent',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString()
        },
        {
          id: '5',
          type: 'Weekly Digest',
          recipient: '<EMAIL>',
          status: 'pending',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
        }
      ],
      provider: serviceStats.provider,
      rateLimitEntries: serviceStats.rateLimitEntries
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching email statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email statistics',
      error: error.message
    });
  }
});

// Get specific email template
router.get('/templates/:templateName', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { templateName } = req.params;
    const templatePath = path.join(__dirname, '..', '..', 'templates', 'email', `${templateName}.mjml`);
    
    try {
      const content = await fs.readFile(templatePath, 'utf8');
      const stats = await fs.stat(templatePath);
      
      // Extract metadata from MJML content
      const titleMatch = content.match(/<mj-title>(.*?)<\/mj-title>/);
      const previewMatch = content.match(/<mj-preview>(.*?)<\/mj-preview>/);
      
      const emailType = Object.entries(EMAIL_CONFIG.EMAIL_TYPES).find(
        ([key, config]) => config.template === templateName
      );
      
      res.json({
        success: true,
        data: {
          id: templateName,
          name: titleMatch ? titleMatch[1] : templateName,
          subject: emailType ? emailType[1].subject : 'No subject configured',
          template: templateName,
          content: content,
          description: previewMatch ? previewMatch[1] : 'No description available',
          isActive: !!emailType,
          lastModified: stats.mtime.toISOString(),
          size: stats.size
        }
      });
    } catch (fileError) {
      if (fileError.code === 'ENOENT') {
        return res.status(404).json({
          success: false,
          message: 'Template not found'
        });
      }
      throw fileError;
    }
  } catch (error) {
    console.error('Error fetching email template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email template',
      error: error.message
    });
  }
});

// Update email template
router.put('/templates/:templateName', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { templateName } = req.params;
    const { content, subject, description } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Template content is required'
      });
    }
    
    const templatePath = path.join(__dirname, '..', '..', 'templates', 'email', `${templateName}.mjml`);
    
    // Backup existing template
    try {
      const existingContent = await fs.readFile(templatePath, 'utf8');
      const backupPath = path.join(path.dirname(templatePath), `${templateName}.backup.${Date.now()}.mjml`);
      await fs.writeFile(backupPath, existingContent);
    } catch (backupError) {
      console.warn('Could not create backup:', backupError.message);
    }
    
    // Write new content
    await fs.writeFile(templatePath, content);
    
    // Clear template cache
    modernEmailService.templateCache.delete(templateName);
    
    res.json({
      success: true,
      message: 'Template updated successfully',
      data: {
        templateName,
        lastModified: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating email template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update email template',
      error: error.message
    });
  }
});

// Test email template
router.post('/test', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { email, template } = req.body;
    
    if (!email || !template) {
      return res.status(400).json({
        success: false,
        message: 'Email address and template name are required'
      });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address format'
      });
    }
    
    // Test data for template rendering
    const testData = {
      user: {
        firstName: 'Test User',
        username: 'testuser',
        email: email
      },
      verificationUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/verify-email?token=test-token`,
      resetUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/reset-password?token=test-token`,
      loginUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/login`,
      profileUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/profile/testuser`,
      notification: {
        title: 'Test Notification',
        message: 'This is a test notification message',
        actionUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/notifications`
      }
    };
    
    // Send test email
    const result = await modernEmailService.sendEmail({
      to: email,
      subject: `[TEST] ${template} Template`,
      template: template,
      data: testData
    });
    
    res.json({
      success: true,
      message: 'Test email sent successfully',
      data: {
        messageId: result.messageId,
        provider: result.provider,
        recipient: email,
        template: template
      }
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email',
      error: error.message
    });
  }
});

// Get email service configuration
router.get('/config', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const config = {
      provider: EMAIL_CONFIG.CURRENT_PROVIDER,
      defaultFrom: EMAIL_CONFIG.DEFAULT_FROM,
      rateLimits: EMAIL_CONFIG.RATE_LIMITS,
      emailTypes: Object.keys(EMAIL_CONFIG.EMAIL_TYPES),
      templatesLoaded: modernEmailService.templateCache.size,
      baseUrl: EMAIL_CONFIG.TEMPLATES.BASE_URL
    };
    
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Error fetching email configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email configuration',
      error: error.message
    });
  }
});

// Email delivery status tracking
router.get('/delivery-status', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { page = 1, limit = 20, status, template, recipient } = req.query;
    
    // Mock delivery status data - in a real implementation, this would come from a database
    const mockDeliveryData = [
      {
        id: '1',
        recipient: '<EMAIL>',
        template: 'welcome',
        subject: 'Welcome to Newzora!',
        status: 'delivered',
        sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        deliveredAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000).toISOString(),
        provider: 'sendgrid',
        messageId: 'msg_123456'
      },
      {
        id: '2',
        recipient: '<EMAIL>',
        template: 'email-verification',
        subject: 'Verify Your Email - Newzora',
        status: 'failed',
        sentAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        error: 'Invalid recipient address',
        provider: 'sendgrid',
        messageId: 'msg_123457'
      }
    ];
    
    // Apply filters
    let filteredData = mockDeliveryData;
    if (status) {
      filteredData = filteredData.filter(item => item.status === status);
    }
    if (template) {
      filteredData = filteredData.filter(item => item.template === template);
    }
    if (recipient) {
      filteredData = filteredData.filter(item => 
        item.recipient.toLowerCase().includes(recipient.toLowerCase())
      );
    }
    
    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedData = filteredData.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        deliveries: paginatedData,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredData.length,
          pages: Math.ceil(filteredData.length / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching delivery status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch delivery status',
      error: error.message
    });
  }
});

module.exports = router;
