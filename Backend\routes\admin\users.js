const express = require('express');
const router = express.Router();
const { User, Role, UserRole, Permission } = require('../../models');
const { authenticateToken, requireRole, requirePermission } = require('../../middleware/auth');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');

// Get all users with filtering and pagination
router.get('/', authenticateToken, requirePermission('user:manage'), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isString(),
  query('status').optional().isIn(['active', 'inactive', 'banned']),
  query('search').optional().isLength({ min: 1, max: 100 }),
  query('sortBy').optional().isIn(['username', 'email', 'createdAt', 'lastLoginAt']),
  query('sortOrder').optional().isIn(['ASC', 'DESC'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      role,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const whereClause = {};
    
    if (status === 'active') whereClause.isActive = true;
    if (status === 'inactive') whereClause.isActive = false;
    if (status === 'banned') whereClause.isBanned = true;
    
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const includeOptions = [
      {
        model: Role,
        as: 'roles',
        through: { 
          attributes: ['assignedAt', 'isActive', 'isPrimary'],
          where: { isActive: true }
        },
        required: false
      }
    ];

    // Filter by role if specified
    if (role) {
      includeOptions[0].where = { name: role };
      includeOptions[0].required = true;
    }

    const users = await User.findAndCountAll({
      where: whereClause,
      include: includeOptions,
      order: [[sortBy, sortOrder]],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      data: users.rows,
      pagination: {
        total: users.count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(users.count / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

// Get user by ID
router.get('/:id', authenticateToken, requirePermission('user:manage'), async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id, {
      include: [
        {
          model: Role,
          as: 'roles',
          through: { 
            attributes: ['assignedAt', 'isActive', 'isPrimary', 'expiresAt'],
            where: { isActive: true }
          },
          include: [{
            model: Permission,
            as: 'permissions',
            through: { 
              attributes: [],
              where: { isActive: true }
            },
            required: false
          }],
          required: false
        }
      ],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: error.message
    });
  }
});

// Update user
router.put('/:id', authenticateToken, requirePermission('user:manage'), [
  body('username').optional().isString().isLength({ min: 3, max: 50 }),
  body('email').optional().isEmail(),
  body('firstName').optional().isString().isLength({ max: 50 }),
  body('lastName').optional().isString().isLength({ max: 50 }),
  body('isActive').optional().isBoolean(),
  body('isBanned').optional().isBoolean(),
  body('banReason').optional().isString().isLength({ max: 500 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent self-modification of critical fields
    if (user.id === req.user.id) {
      const { isActive, isBanned } = req.body;
      if (isActive === false || isBanned === true) {
        return res.status(403).json({
          success: false,
          message: 'Cannot deactivate or ban yourself'
        });
      }
    }

    const {
      username,
      email,
      firstName,
      lastName,
      isActive,
      isBanned,
      banReason
    } = req.body;

    // Check for unique constraints
    if (username && username !== user.username) {
      const existingUser = await User.findOne({ where: { username } });
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'Username already exists'
        });
      }
    }

    if (email && email !== user.email) {
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'Email already exists'
        });
      }
    }

    await user.update({
      ...(username && { username }),
      ...(email && { email }),
      ...(firstName !== undefined && { firstName }),
      ...(lastName !== undefined && { lastName }),
      ...(isActive !== undefined && { isActive }),
      ...(isBanned !== undefined && { isBanned }),
      ...(banReason !== undefined && { banReason })
    });

    // Fetch updated user with roles
    const updatedUser = await User.findByPk(user.id, {
      include: [{
        model: Role,
        as: 'roles',
        through: { 
          attributes: ['assignedAt', 'isActive', 'isPrimary'],
          where: { isActive: true }
        },
        required: false
      }],
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: error.message
    });
  }
});

// Ban user
router.post('/:id/ban', authenticateToken, requirePermission('user:ban'), [
  body('reason').isString().isLength({ min: 1, max: 500 }),
  body('duration').optional().isInt({ min: 1 }) // Duration in days
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.id === req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Cannot ban yourself'
      });
    }

    const { reason, duration } = req.body;
    
    let banExpiresAt = null;
    if (duration) {
      banExpiresAt = new Date();
      banExpiresAt.setDate(banExpiresAt.getDate() + duration);
    }

    await user.update({
      isBanned: true,
      banReason: reason,
      banExpiresAt,
      bannedBy: req.user.id,
      bannedAt: new Date()
    });

    res.json({
      success: true,
      message: 'User banned successfully'
    });
  } catch (error) {
    console.error('Error banning user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to ban user',
      error: error.message
    });
  }
});

// Unban user
router.post('/:id/unban', authenticateToken, requirePermission('user:ban'), async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    await user.update({
      isBanned: false,
      banReason: null,
      banExpiresAt: null,
      bannedBy: null,
      bannedAt: null
    });

    res.json({
      success: true,
      message: 'User unbanned successfully'
    });
  } catch (error) {
    console.error('Error unbanning user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unban user',
      error: error.message
    });
  }
});

// Get user roles
router.get('/:id/roles', authenticateToken, requirePermission('user:manage'), async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userRoles = await UserRole.getActiveByUser(user.id);

    res.json({
      success: true,
      data: userRoles
    });
  } catch (error) {
    console.error('Error fetching user roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user roles',
      error: error.message
    });
  }
});

// Assign role to user
router.post('/:id/roles', authenticateToken, requirePermission('user:manage'), [
  body('roleId').isInt(),
  body('isPrimary').optional().isBoolean(),
  body('expiresAt').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const { roleId, isPrimary = false, expiresAt } = req.body;

    const role = await Role.findByPk(roleId);
    if (!role || !role.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Role not found or inactive'
      });
    }

    // Check if user can assign this role level
    const userRoles = await req.user.getRoles();
    const maxUserLevel = Math.max(...userRoles.map(r => r.level));

    if (role.level >= maxUserLevel) {
      return res.status(403).json({
        success: false,
        message: 'Cannot assign role with level equal to or higher than your own'
      });
    }

    const userRole = await UserRole.assignRole(
      user.id,
      roleId,
      req.user.id,
      expiresAt ? new Date(expiresAt) : null,
      isPrimary
    );

    res.status(201).json({
      success: true,
      data: userRole,
      message: 'Role assigned successfully'
    });
  } catch (error) {
    console.error('Error assigning role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign role',
      error: error.message
    });
  }
});

// Remove role from user
router.delete('/:id/roles/:roleId', authenticateToken, requirePermission('user:manage'), async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const role = await Role.findByPk(req.params.roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if user can remove this role level
    const userRoles = await req.user.getRoles();
    const maxUserLevel = Math.max(...userRoles.map(r => r.level));

    if (role.level >= maxUserLevel) {
      return res.status(403).json({
        success: false,
        message: 'Cannot remove role with level equal to or higher than your own'
      });
    }

    await UserRole.revokeRole(user.id, role.id);

    res.json({
      success: true,
      message: 'Role removed successfully'
    });
  } catch (error) {
    console.error('Error removing role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove role',
      error: error.message
    });
  }
});

// Sync user roles
router.put('/:id/roles', authenticateToken, requirePermission('user:manage'), [
  body('roleIds').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const { roleIds } = req.body;

    // Validate all roles exist and user can assign them
    const roles = await Role.findAll({
      where: {
        id: roleIds,
        isActive: true
      }
    });

    if (roles.length !== roleIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more roles not found or inactive'
      });
    }

    const userRoles = await req.user.getRoles();
    const maxUserLevel = Math.max(...userRoles.map(r => r.level));

    const invalidRoles = roles.filter(role => role.level >= maxUserLevel);
    if (invalidRoles.length > 0) {
      return res.status(403).json({
        success: false,
        message: 'Cannot assign roles with level equal to or higher than your own'
      });
    }

    // Sync roles
    await UserRole.syncUserRoles(user.id, roleIds, req.user.id);

    // Fetch updated user roles
    const updatedUserRoles = await UserRole.getActiveByUser(user.id);

    res.json({
      success: true,
      data: updatedUserRoles,
      message: 'User roles updated successfully'
    });
  } catch (error) {
    console.error('Error syncing user roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync user roles',
      error: error.message
    });
  }
});

module.exports = router;
