/**
 * Newzora AI内容审核 API 路由
 * 提供AI驱动的内容安全审核服务
 */

const express = require('express');
const router = express.Router();
const aiContentModerationService = require('../services/AIContentModerationService');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { logger } = require('../config/logger');

// 审核单个内容
router.post('/moderate', authenticateToken, async (req, res) => {
  try {
    const {
      contentType = 'text',
      contentId,
      content,
      options = {}
    } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required for moderation'
      });
    }

    const authorId = req.user.id;
    const moderationResult = await aiContentModerationService.moderateContent(
      contentType,
      contentId || `temp_${Date.now()}`,
      content,
      authorId,
      options
    );

    res.json({
      success: true,
      data: {
        ...moderationResult,
        contentId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Content moderation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to moderate content',
      error: error.message
    });
  }
});

// 批量审核内容
router.post('/moderate-batch', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const { contents } = req.body;

    if (!Array.isArray(contents) || contents.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Contents array is required'
      });
    }

    if (contents.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 100 contents per batch'
      });
    }

    const results = [];
    const authorId = req.user.id;

    for (const item of contents) {
      try {
        const result = await aiContentModerationService.moderateContent(
          item.contentType || 'text',
          item.contentId || `batch_${Date.now()}_${results.length}`,
          item.content,
          authorId,
          item.options || {}
        );

        results.push({
          contentId: item.contentId,
          ...result
        });
      } catch (error) {
        results.push({
          contentId: item.contentId,
          approved: false,
          error: error.message,
          flags: [{
            ruleName: 'Processing Error',
            severity: 'high',
            confidence: 0.0
          }]
        });
      }
    }

    res.json({
      success: true,
      data: {
        results,
        total: contents.length,
        approved: results.filter(r => r.approved).length,
        flagged: results.filter(r => !r.approved).length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Batch moderation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to moderate batch content',
      error: error.message
    });
  }
});

// 检测敏感词
router.post('/detect-sensitive-words', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    const result = await aiContentModerationService.detectSensitiveWords(content);

    res.json({
      success: true,
      data: {
        ...result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Sensitive words detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect sensitive words',
      error: error.message
    });
  }
});

// 毒性检测
router.post('/detect-toxicity', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    const result = await aiContentModerationService.detectToxicity(content);

    res.json({
      success: true,
      data: {
        ...result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Toxicity detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect toxicity',
      error: error.message
    });
  }
});

// 语义分析
router.post('/semantic-analysis', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    const result = await aiContentModerationService.performSemanticAnalysis(content);

    res.json({
      success: true,
      data: {
        ...result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Semantic analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform semantic analysis',
      error: error.message
    });
  }
});

// 图像内容检测
router.post('/detect-image-content', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    const result = await aiContentModerationService.detectImageContent(content);

    res.json({
      success: true,
      data: {
        ...result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Image content detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect image content',
      error: error.message
    });
  }
});

// URL安全检测
router.post('/detect-malicious-urls', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    const result = await aiContentModerationService.detectMaliciousUrls(content);

    res.json({
      success: true,
      data: {
        ...result,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Malicious URL detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect malicious URLs',
      error: error.message
    });
  }
});

// 获取审核统计（管理员功能）
router.get('/stats', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      endDate = new Date().toISOString(),
      contentType = null
    } = req.query;

    // 这里应该从数据库获取统计数据
    // 简化版本返回模拟数据
    const stats = {
      totalModerated: 1250,
      approved: 1100,
      flagged: 150,
      blocked: 50,
      categories: {
        violence: 25,
        hate: 15,
        spam: 40,
        nsfw: 30,
        political: 10,
        other: 30
      },
      accuracy: 0.94,
      falsePositives: 8,
      falseNegatives: 3,
      period: {
        startDate,
        endDate
      }
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Moderation stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get moderation stats',
      error: error.message
    });
  }
});

// 更新敏感词库（管理员功能）
router.post('/update-sensitive-words', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { category, words, action = 'add' } = req.body;

    if (!category || !Array.isArray(words)) {
      return res.status(400).json({
        success: false,
        message: 'Category and words array are required'
      });
    }

    // 这里应该更新敏感词库
    // 简化版本只记录日志
    logger.info(`Admin ${req.user.id} ${action} sensitive words in category ${category}:`, words);

    res.json({
      success: true,
      message: `Successfully ${action}ed ${words.length} words in category ${category}`,
      data: {
        category,
        words,
        action,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Update sensitive words error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update sensitive words',
      error: error.message
    });
  }
});

// 测试AI审核系统
router.get('/test', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const testContents = [
      'This is a normal message.',
      'I hate you so much!',
      'Check out this amazing product at http://malware.com',
      'This contains some violence and blood.',
      'Political discussion about democracy and freedom.'
    ];

    const results = [];

    for (let i = 0; i < testContents.length; i++) {
      const result = await aiContentModerationService.moderateContent(
        'text',
        `test_${i}`,
        testContents[i],
        req.user.id,
        { test: true }
      );

      results.push({
        content: testContents[i],
        ...result
      });
    }

    res.json({
      success: true,
      message: 'AI moderation system test completed',
      data: {
        results,
        summary: {
          total: results.length,
          approved: results.filter(r => r.approved).length,
          flagged: results.filter(r => !r.approved).length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('AI moderation test error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test AI moderation system',
      error: error.message
    });
  }
});

module.exports = router;
