const express = require('express');
const router = express.Router();
const Article = require('../models/Article');
const { Op } = require('sequelize');
const { authenticateToken } = require('../middleware/supabaseAuth');



// Get all articles with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      search,
      featured,
      sort = 'createdAt'
    } = req.query;

    // Build where conditions
    const whereConditions = {
      published: true
    };

    // Filter by category
    if (category && category !== 'all' && category !== 'Trending') {
      whereConditions.category = category;
    }

    // Filter by featured
    if (featured === 'true') {
      whereConditions.featured = true;
    }

    // Search functionality
    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = sort.startsWith('-')
      ? [[sort.substring(1), 'DESC']]
      : [[sort, 'ASC']];

    // Query articles with pagination
    const { count, rows: articles } = await Article.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: order
    });

    res.json({
      success: true,
      data: {
        articles: articles,
        totalPages: Math.ceil(count / limit),
        currentPage: parseInt(page),
        total: count
      }
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get single article by ID (简化版本用于测试)
router.get('/:id', async (req, res) => {
  try {
    const articleId = req.params.id;

    // 模拟文章数据 - 保持ID为字符串格式
    const mockArticle = {
      id: articleId, // 保持原始ID格式，不转换为数字
      title: `Sample Article ${articleId}`,
      content: `<h2>Introduction</h2><p>This is a sample article with ID ${articleId}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p><h2>Main Content</h2><p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p><p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p><h2>Conclusion</h2><p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>`,
      excerpt: `This is a sample article excerpt for article ${articleId}...`,
      author: {
        id: 1,
        name: 'John Doe',
        username: 'johndoe',
        avatar: `https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff&size=64`,
        bio: 'Tech writer and developer'
      },
      category: 'Technology',
      tags: ['tech', 'development', 'programming'],
      publishedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      readTime: 5,
      image: `https://picsum.photos/800/400?random=${articleId}`,
      likes: Math.floor(Math.random() * 200) + 50,
      comments: Math.floor(Math.random() * 50) + 10,
      shares: Math.floor(Math.random() * 30) + 5,
      views: Math.floor(Math.random() * 1000) + 100,
      published: true
    };

    console.log(`Serving article ${articleId}`);
    res.json({
      success: true,
      data: mockArticle
    });
  } catch (error) {
    console.error('Error fetching article:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get recommended articles
router.get('/recommended/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const limit = parseInt(req.query.limit) || 4;

    const articles = await Article.findAll({
      where: {
        category: category,
        published: true
      },
      order: [['views', 'DESC']],
      limit: limit
    });

    res.json({
      success: true,
      data: articles
    });
  } catch (error) {
    console.error('Error fetching recommended articles:', error);
    res.status(500).json({ message: error.message });
  }
});

// Create new article (authenticated users)
router.post('/', authenticateToken, async (req, res) => {
  try {
    // 确保必填字段有值
    const title = req.body.title || '未命名文章';
    const content = req.body.content || '';
    const description = req.body.description || (content ? content.substring(0, 200) + '...' : '暂无描述');
    const author = req.user?.email || req.body.author || 'Anonymous';

    const article = await Article.create({
      title,
      description,
      content,
      category: req.body.category || 'General',
      image: req.body.image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop',
      author,
      readTime: req.body.readTime || Math.ceil(content.length / 1000) || 5,
      tags: req.body.tags || [],
      featured: req.body.featured || false,
      published: req.body.published !== false
    }, {
      // Explicitly specify which fields to return to avoid authorId issue
      returning: ['id', 'title', 'description', 'content', 'category', 'image', 'author', 'readTime', 'tags', 'views', 'likes', 'featured', 'published', 'createdAt', 'updatedAt']
    });

    res.status(201).json({
      success: true,
      data: article
    });
  } catch (error) {
    console.error('Error creating article:', error);
    res.status(500).json({ message: 'Something went wrong!' });
  }
});

// Like an article
router.post('/:id/like', async (req, res) => {
  try {
    const article = await Article.findByPk(req.params.id);

    if (!article) {
      return res.status(404).json({ message: 'Article not found' });
    }

    await article.increment('likes');

    res.json({
      success: true,
      data: { likes: article.likes + 1 }
    });
  } catch (error) {
    console.error('Error liking article:', error);
    res.status(500).json({ message: error.message });
  }
});

// Unlike an article
router.delete('/:id/like', async (req, res) => {
  try {
    const article = await Article.findByPk(req.params.id);

    if (!article) {
      return res.status(404).json({ message: 'Article not found' });
    }

    if (article.likes > 0) {
      await article.decrement('likes');
    }

    res.json({
      success: true,
      data: { likes: Math.max(0, article.likes - 1) }
    });
  } catch (error) {
    console.error('Error unliking article:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
