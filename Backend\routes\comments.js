const express = require('express');
const router = express.Router();
const Comment = require('../models/Comment');
const { Op } = require('sequelize');
const commentInteractionService = require('../services/CommentInteractionService');
const { authenticateToken } = require('../middleware/auth');
const { body, validationResult, query } = require('express-validator');

// Get comments for an article (简化版本用于测试)
router.get('/article/:articleId', async (req, res) => {
  try {
    const { articleId } = req.params;

    // 模拟评论数据
    const mockComments = [
      {
        id: 1,
        content: 'Great article! Very informative and well-written.',
        author: {
          id: 1,
          username: 'alice_reader',
          name: '<PERSON>',
          avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=10b981&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        likes: 12,
        replies: []
      },
      {
        id: 2,
        content: 'I have a different perspective on this topic. What about the environmental impact?',
        author: {
          id: 2,
          username: 'eco_warrior',
          name: 'Bob Green',
          avatar: 'https://ui-avatars.com/api/?name=Bob+Green&background=ef4444&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        likes: 8,
        replies: [
          {
            id: 3,
            content: 'That\'s a valid point! Thanks for bringing it up.',
            author: {
              id: 1,
              username: 'alice_reader',
              name: 'Alice Johnson',
              avatar: 'https://ui-avatars.com/api/?name=Alice+Johnson&background=10b981&color=fff&size=40'
            },
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
            updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
            likes: 3
          }
        ]
      },
      {
        id: 4,
        content: 'Could you provide more examples in the next article?',
        author: {
          id: 3,
          username: 'curious_dev',
          name: 'Charlie Smith',
          avatar: 'https://ui-avatars.com/api/?name=Charlie+Smith&background=8b5cf6&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        likes: 5,
        replies: []
      }
    ];

    console.log(`Serving comments for article ${articleId}`);
    res.json(mockComments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add a new comment (简化版本用于测试)
router.post('/', async (req, res) => {
  try {
    const { articleId, author, content, parentId } = req.body;

    // Validate required fields
    if (!articleId || !author || !content) {
      return res.status(400).json({
        message: 'Article ID, author, and content are required'
      });
    }

    // 模拟创建评论
    const newComment = {
      id: Date.now(), // 简单的ID生成
      content,
      author,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      likes: 0,
      replies: []
    };

    console.log(`Created new comment for article ${articleId}:`, newComment);
    res.status(201).json(newComment);
  } catch (error) {
    console.error('Error creating comment:', error);
    res.status(400).json({ message: error.message });
  }
});

// Like a comment
router.post('/:id/like', async (req, res) => {
  try {
    const comment = await Comment.findByPk(req.params.id);
    
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    await comment.increment('likes');
    
    res.json({ likes: comment.likes + 1 });
  } catch (error) {
    console.error('Error liking comment:', error);
    res.status(500).json({ message: error.message });
  }
});

// Delete a comment (soft delete by setting content to deleted message)
router.delete('/:id', async (req, res) => {
  try {
    const comment = await Comment.findByPk(req.params.id);
    
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Instead of deleting, we'll mark it as deleted
    await comment.update({
      content: '[This comment has been deleted]',
      author: '[Deleted User]'
    });
    
    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ message: error.message });
  }
});

// ==================== 增强的评论交互功能 ====================

// 添加评论交互（点赞、点踩等）
router.post('/:commentId/interact', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { type } = req.body;
    const userId = req.user.id;

    if (!['like', 'dislike', 'love', 'laugh', 'angry', 'sad', 'wow'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid interaction type'
      });
    }

    const result = await commentInteractionService.addInteraction(commentId, userId, type);

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: {
          action: result.action,
          interactionType: result.interactionType,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }

  } catch (error) {
    console.error('Add interaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add interaction',
      error: error.message
    });
  }
});

// 举报评论
router.post('/:commentId/report', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { type, reason, additionalInfo } = req.body;
    const userId = req.user.id;

    const validReportTypes = ['spam', 'harassment', 'hate_speech', 'misinformation', 'inappropriate', 'copyright', 'violence', 'other'];

    if (!validReportTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid report type'
      });
    }

    const result = await commentInteractionService.reportComment(commentId, userId, {
      type,
      reason,
      additionalInfo
    });

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: {
          report: result.report,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }

  } catch (error) {
    console.error('Report comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to report comment',
      error: error.message
    });
  }
});

// 获取增强的评论列表
router.get('/enhanced/:targetType/:targetId', async (req, res) => {
  try {
    const { targetType, targetId } = req.params;
    const {
      page = 1,
      limit = 20,
      sort = 'newest',
      includeReplies = true,
      maxDepth = 3
    } = req.query;

    const userId = req.user?.id || null;

    const result = await commentInteractionService.getComments(targetType, targetId, {
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      includeReplies: includeReplies === 'true',
      maxDepth: parseInt(maxDepth),
      userId
    });

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }

  } catch (error) {
    console.error('Get enhanced comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get comments',
      error: error.message
    });
  }
});

// 创建增强评论
router.post('/enhanced', authenticateToken, async (req, res) => {
  try {
    const {
      targetType,
      targetId,
      content,
      parentId,
      mentions = [],
      attachments = []
    } = req.body;

    if (!targetType || !targetId || !content) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    if (content.length > 5000) {
      return res.status(400).json({
        success: false,
        message: 'Content too long (max 5000 characters)'
      });
    }

    const result = await commentInteractionService.createComment({
      userId: req.user.id,
      targetType,
      targetId,
      content,
      parentId,
      mentions,
      attachments
    });

    if (result.success) {
      res.status(201).json({
        success: true,
        message: result.message,
        data: {
          comment: result.comment,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }

  } catch (error) {
    console.error('Create enhanced comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create comment',
      error: error.message
    });
  }
});

// 获取评论统计
router.get('/stats/:targetType/:targetId', async (req, res) => {
  try {
    const { targetType, targetId } = req.params;

    const result = await commentInteractionService.getCommentStats(targetType, targetId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          targetType,
          targetId,
          stats: result.stats,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }

  } catch (error) {
    console.error('Get comment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get comment stats',
      error: error.message
    });
  }
});

// 获取交互类型
router.get('/interaction-types', (req, res) => {
  try {
    const interactionTypes = commentInteractionService.getInteractionTypes();

    res.json({
      success: true,
      data: {
        interactionTypes,
        totalTypes: Object.keys(interactionTypes).length
      }
    });
  } catch (error) {
    console.error('Get interaction types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get interaction types',
      error: error.message
    });
  }
});

// 获取举报类型
router.get('/report-types', (req, res) => {
  try {
    const reportTypes = commentInteractionService.getReportTypes();

    res.json({
      success: true,
      data: {
        reportTypes,
        totalTypes: Object.keys(reportTypes).length
      }
    });
  } catch (error) {
    console.error('Get report types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get report types',
      error: error.message
    });
  }
});

module.exports = router;
