const express = require('express');
const router = express.Router();
const Comment = require('../models/Comment');
const { Op } = require('sequelize');

// Get comments for an article (简化版本用于测试)
router.get('/article/:articleId', async (req, res) => {
  try {
    const { articleId } = req.params;

    // 模拟评论数据
    const mockComments = [
      {
        id: 1,
        content: 'Great article! Very informative and well-written.',
        author: {
          id: 1,
          username: 'alice_reader',
          name: '<PERSON>',
          avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=10b981&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        likes: 12,
        replies: []
      },
      {
        id: 2,
        content: 'I have a different perspective on this topic. What about the environmental impact?',
        author: {
          id: 2,
          username: 'eco_warrior',
          name: '<PERSON>',
          avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=ef4444&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        likes: 8,
        replies: [
          {
            id: 3,
            content: 'That\'s a valid point! Thanks for bringing it up.',
            author: {
              id: 1,
              username: 'alice_reader',
              name: 'Alice Johnson',
              avatar: 'https://ui-avatars.com/api/?name=Alice+Johnson&background=10b981&color=fff&size=40'
            },
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
            updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
            likes: 3
          }
        ]
      },
      {
        id: 4,
        content: 'Could you provide more examples in the next article?',
        author: {
          id: 3,
          username: 'curious_dev',
          name: 'Charlie Smith',
          avatar: 'https://ui-avatars.com/api/?name=Charlie+Smith&background=8b5cf6&color=fff&size=40'
        },
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        likes: 5,
        replies: []
      }
    ];

    console.log(`Serving comments for article ${articleId}`);
    res.json(mockComments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add a new comment (简化版本用于测试)
router.post('/', async (req, res) => {
  try {
    const { articleId, author, content, parentId } = req.body;

    // Validate required fields
    if (!articleId || !author || !content) {
      return res.status(400).json({
        message: 'Article ID, author, and content are required'
      });
    }

    // 模拟创建评论
    const newComment = {
      id: Date.now(), // 简单的ID生成
      content,
      author,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      likes: 0,
      replies: []
    };

    console.log(`Created new comment for article ${articleId}:`, newComment);
    res.status(201).json(newComment);
  } catch (error) {
    console.error('Error creating comment:', error);
    res.status(400).json({ message: error.message });
  }
});

// Like a comment
router.post('/:id/like', async (req, res) => {
  try {
    const comment = await Comment.findByPk(req.params.id);
    
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    await comment.increment('likes');
    
    res.json({ likes: comment.likes + 1 });
  } catch (error) {
    console.error('Error liking comment:', error);
    res.status(500).json({ message: error.message });
  }
});

// Delete a comment (soft delete by setting content to deleted message)
router.delete('/:id', async (req, res) => {
  try {
    const comment = await Comment.findByPk(req.params.id);
    
    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Instead of deleting, we'll mark it as deleted
    await comment.update({
      content: '[This comment has been deleted]',
      author: '[Deleted User]'
    });
    
    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
