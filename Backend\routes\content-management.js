const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../middleware/auth');
const ArticleVersion = require('../models/ArticleVersion');
const ScheduledPost = require('../models/ScheduledPost');
const Article = require('../models/Article');
const User = require('../models/User');
const { sequelize } = require('../config/database');

// 获取文章版本历史
router.get('/articles/:articleId/versions', authenticateToken, async (req, res) => {
  try {
    const { articleId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    // 检查用户权限
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }
    
    // 检查用户是否有权限查看版本
    if (article.authorId !== req.user.id && !['admin', 'super_admin', 'editor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const versions = await ArticleVersion.findAndCountAll({
      where: { articleId },
      order: [['version', 'DESC']],
      limit: parseInt(limit),
      offset: offset,
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'firstName', 'lastName']
        },
        {
          model: User,
          as: 'editor',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });
    
    res.json({
      success: true,
      data: {
        versions: versions.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: versions.count,
          pages: Math.ceil(versions.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching article versions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch article versions',
      error: error.message
    });
  }
});

// 创建新版本
router.post('/articles/:articleId/versions', authenticateToken, async (req, res) => {
  try {
    const { articleId } = req.params;
    const versionData = req.body;
    
    // 检查文章存在性和权限
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }
    
    if (article.authorId !== req.user.id && !['admin', 'super_admin', 'editor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const newVersion = await ArticleVersion.createVersion(
      parseInt(articleId),
      {
        ...versionData,
        authorId: article.authorId,
        changeType: versionData.changeType || 'updated',
        changeDescription: versionData.changeDescription || 'Manual version creation'
      },
      req.user.id
    );
    
    res.status(201).json({
      success: true,
      message: 'Version created successfully',
      data: newVersion
    });
  } catch (error) {
    console.error('Error creating article version:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create article version',
      error: error.message
    });
  }
});

// 激活指定版本
router.post('/articles/:articleId/versions/:versionId/activate', authenticateToken, async (req, res) => {
  try {
    const { articleId, versionId } = req.params;
    
    // 检查权限
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }
    
    if (article.authorId !== req.user.id && !['admin', 'super_admin', 'editor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const version = await ArticleVersion.findOne({
      where: { id: versionId, articleId }
    });
    
    if (!version) {
      return res.status(404).json({
        success: false,
        message: 'Version not found'
      });
    }
    
    await version.activate();
    
    // 更新主文章内容
    await article.update({
      title: version.title,
      description: version.description,
      content: version.content,
      category: version.category,
      featuredImage: version.featuredImage
    });
    
    res.json({
      success: true,
      message: 'Version activated successfully',
      data: version
    });
  } catch (error) {
    console.error('Error activating version:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to activate version',
      error: error.message
    });
  }
});

// 比较两个版本
router.get('/articles/:articleId/versions/:version1Id/compare/:version2Id', authenticateToken, async (req, res) => {
  try {
    const { articleId, version1Id, version2Id } = req.params;
    
    // 检查权限
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }
    
    if (article.authorId !== req.user.id && !['admin', 'super_admin', 'editor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const comparison = await ArticleVersion.compareVersions(version1Id, version2Id);
    
    res.json({
      success: true,
      data: comparison
    });
  } catch (error) {
    console.error('Error comparing versions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to compare versions',
      error: error.message
    });
  }
});

// 获取调度发布列表
router.get('/scheduled-posts', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      authorId, 
      startDate, 
      endDate 
    } = req.query;
    
    const where = {};
    
    // 权限过滤
    if (!['admin', 'super_admin'].includes(req.user.role)) {
      where.authorId = req.user.id;
    } else if (authorId) {
      where.authorId = authorId;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (startDate && endDate) {
      where.scheduledAt = {
        [sequelize.Sequelize.Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const scheduledPosts = await ScheduledPost.findAndCountAll({
      where,
      order: [['scheduledAt', 'DESC']],
      limit: parseInt(limit),
      offset: offset,
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'description']
        },
        {
          model: ArticleVersion,
          as: 'version',
          attributes: ['id', 'version', 'title']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'firstName', 'lastName']
        },
        {
          model: User,
          as: 'scheduledByUser',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });
    
    res.json({
      success: true,
      data: {
        scheduledPosts: scheduledPosts.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: scheduledPosts.count,
          pages: Math.ceil(scheduledPosts.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching scheduled posts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch scheduled posts',
      error: error.message
    });
  }
});

// 创建调度发布
router.post('/scheduled-posts', authenticateToken, async (req, res) => {
  try {
    const {
      articleId,
      versionId,
      scheduledAt,
      publishType = 'scheduled',
      recurringPattern,
      publishSettings = {},
      socialMediaSettings = {},
      priority = 'normal'
    } = req.body;
    
    // 验证必需字段
    if (!articleId || !scheduledAt) {
      return res.status(400).json({
        success: false,
        message: 'Article ID and scheduled time are required'
      });
    }
    
    // 检查文章权限
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }
    
    if (article.authorId !== req.user.id && !['admin', 'super_admin', 'editor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const scheduleData = {
      articleId: parseInt(articleId),
      versionId: versionId ? parseInt(versionId) : null,
      scheduledAt: new Date(scheduledAt),
      publishType,
      recurringPattern,
      publishSettings,
      socialMediaSettings,
      authorId: article.authorId,
      scheduledBy: req.user.id,
      priority
    };
    
    const scheduledPost = await ScheduledPost.createSchedule(scheduleData);
    
    res.status(201).json({
      success: true,
      message: 'Post scheduled successfully',
      data: scheduledPost
    });
  } catch (error) {
    console.error('Error creating scheduled post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to schedule post',
      error: error.message
    });
  }
});

// 更新调度发布
router.put('/scheduled-posts/:scheduleId', authenticateToken, async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const updateData = req.body;
    
    const scheduledPost = await ScheduledPost.findByPk(scheduleId);
    if (!scheduledPost) {
      return res.status(404).json({
        success: false,
        message: 'Scheduled post not found'
      });
    }
    
    // 检查权限
    if (scheduledPost.scheduledBy !== req.user.id && !['admin', 'super_admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    // 只允许更新待发布或失败的调度
    if (!['pending', 'failed'].includes(scheduledPost.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot update scheduled post in current status'
      });
    }
    
    await scheduledPost.update(updateData);
    
    res.json({
      success: true,
      message: 'Scheduled post updated successfully',
      data: scheduledPost
    });
  } catch (error) {
    console.error('Error updating scheduled post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update scheduled post',
      error: error.message
    });
  }
});

// 取消调度发布
router.delete('/scheduled-posts/:scheduleId', authenticateToken, async (req, res) => {
  try {
    const { scheduleId } = req.params;
    
    const scheduledPost = await ScheduledPost.findByPk(scheduleId);
    if (!scheduledPost) {
      return res.status(404).json({
        success: false,
        message: 'Scheduled post not found'
      });
    }
    
    // 检查权限
    if (scheduledPost.scheduledBy !== req.user.id && !['admin', 'super_admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    await scheduledPost.cancel();
    
    res.json({
      success: true,
      message: 'Scheduled post cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling scheduled post:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel scheduled post',
      error: error.message
    });
  }
});

// 获取调度统计
router.get('/scheduled-posts/stats', authenticateToken, requireRole(['admin', 'super_admin', 'editor']), async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    const stats = await ScheduledPost.getScheduleStats(start, end);
    
    // 汇总统计
    const summary = {
      total: 0,
      pending: 0,
      published: 0,
      failed: 0,
      cancelled: 0
    };
    
    stats.forEach(stat => {
      summary.total += parseInt(stat.count);
      summary[stat.status] += parseInt(stat.count);
    });
    
    res.json({
      success: true,
      data: {
        summary,
        dailyStats: stats,
        dateRange: { start, end }
      }
    });
  } catch (error) {
    console.error('Error fetching schedule stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch schedule statistics',
      error: error.message
    });
  }
});

module.exports = router;
