const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');
const modernEmailService = require('../services/ModernEmailService');
const { User } = require('../models');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../config/security');

// Send verification email
router.post('/send-verification', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.emailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }

    const result = await modernEmailService.sendVerificationEmail(user);
    
    res.json({
      success: true,
      message: 'Verification email sent successfully',
      data: {
        messageId: result.messageId,
        previewUrl: result.previewUrl // For development
      }
    });
  } catch (error) {
    console.error('Send verification email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send verification email',
      error: error.message
    });
  }
});

// Verify email with token
router.post('/verify-email', [
  body('token').isString().notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token',
        errors: errors.array()
      });
    }

    const { token } = req.body;

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(400).json({
          success: false,
          message: 'Verification link has expired',
          error: { code: 'TOKEN_EXPIRED' }
        });
      }
      return res.status(400).json({
        success: false,
        message: 'Invalid verification token'
      });
    }

    if (decoded.type !== 'email_verification') {
      return res.status(400).json({
        success: false,
        message: 'Invalid token type'
      });
    }

    // Find and update user
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.emailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }

    // Update user verification status
    await user.update({
      emailVerified: true,
      emailVerifiedAt: new Date()
    });

    // Send welcome email
    try {
      await modernEmailService.sendWelcomeEmail(user);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Don't fail the verification if welcome email fails
    }

    res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Email verification failed',
      error: error.message
    });
  }
});

// Resend verification email
router.post('/resend-verification', [
  body('email').isEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address',
        errors: errors.array()
      });
    }

    const { email } = req.body;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      // Don't reveal if user exists for security
      return res.json({
        success: true,
        message: 'If an account with this email exists, a verification email has been sent'
      });
    }

    if (user.emailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }

    const result = await modernEmailService.sendVerificationEmail(user);
    
    res.json({
      success: true,
      message: 'Verification email sent successfully',
      data: {
        messageId: result.messageId,
        previewUrl: result.previewUrl // For development
      }
    });
  } catch (error) {
    console.error('Resend verification email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send verification email',
      error: error.message
    });
  }
});

// Send password reset email
router.post('/send-password-reset', [
  body('email').isEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address',
        errors: errors.array()
      });
    }

    const { email } = req.body;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      // Don't reveal if user exists for security
      return res.json({
        success: true,
        message: 'If an account with this email exists, a password reset email has been sent'
      });
    }

    const result = await modernEmailService.sendPasswordResetEmail(user);
    
    res.json({
      success: true,
      message: 'Password reset email sent successfully',
      data: {
        messageId: result.messageId,
        previewUrl: result.previewUrl // For development
      }
    });
  } catch (error) {
    console.error('Send password reset email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send password reset email',
      error: error.message
    });
  }
});

// Reset password with token
router.post('/reset-password', [
  body('token').isString().notEmpty(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        errors: errors.array()
      });
    }

    const { token, password } = req.body;

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(400).json({
          success: false,
          message: 'Password reset link has expired',
          error: { code: 'TOKEN_EXPIRED' }
        });
      }
      return res.status(400).json({
        success: false,
        message: 'Invalid reset token'
      });
    }

    if (decoded.type !== 'password_reset') {
      return res.status(400).json({
        success: false,
        message: 'Invalid token type'
      });
    }

    // Find user and update password
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Hash and update password
    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 12);
    
    await user.update({
      password: hashedPassword,
      passwordResetAt: new Date()
    });

    res.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Password reset failed',
      error: error.message
    });
  }
});

// Send notification email (admin only)
router.post('/send-notification', authenticateToken, requireRole(['admin', 'super_admin']), [
  body('userId').isInt(),
  body('notification').isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        errors: errors.array()
      });
    }

    const { userId, notification } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const result = await modernEmailService.sendNotificationEmail(user, notification);
    
    res.json({
      success: true,
      message: 'Notification email sent successfully',
      data: {
        messageId: result.messageId,
        previewUrl: result.previewUrl // For development
      }
    });
  } catch (error) {
    console.error('Send notification email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send notification email',
      error: error.message
    });
  }
});

// Get email service statistics (admin only)
router.get('/stats', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const stats = modernEmailService.getStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get email stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email statistics',
      error: error.message
    });
  }
});

// Test email service (admin only)
router.post('/test', authenticateToken, requireRole(['admin', 'super_admin']), [
  body('email').isEmail(),
  body('template').isString().notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input data',
        errors: errors.array()
      });
    }

    const { email, template } = req.body;

    const testData = {
      user: {
        firstName: 'Test User',
        username: 'testuser',
        email: email
      },
      verificationUrl: 'https://example.com/verify',
      resetUrl: 'https://example.com/reset',
      loginUrl: 'https://example.com/login',
      profileUrl: 'https://example.com/profile'
    };

    const result = await modernEmailService.sendEmail({
      to: email,
      subject: `Test Email - ${template}`,
      template: template,
      data: testData
    });
    
    res.json({
      success: true,
      message: 'Test email sent successfully',
      data: {
        messageId: result.messageId,
        previewUrl: result.previewUrl // For development
      }
    });
  } catch (error) {
    console.error('Test email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email',
      error: error.message
    });
  }
});

module.exports = router;
