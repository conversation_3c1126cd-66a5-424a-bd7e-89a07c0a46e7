const express = require('express');
const router = express.Router();
const { User, Follow } = require('../models/associations');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// Follow a user
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { followingId } = req.body;
    const followerId = req.user.id;

    // Validate input
    if (!followingId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Following user ID is required' 
      });
    }

    // Check if user exists
    const userToFollow = await User.findByPk(followingId);
    if (!userToFollow) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Prevent self-following
    if (followerId === followingId) {
      return res.status(400).json({ 
        success: false, 
        message: 'You cannot follow yourself' 
      });
    }

    // Check if already following
    const existingFollow = await Follow.findOne({
      where: { followerId, followingId }
    });

    if (existingFollow) {
      return res.status(400).json({ 
        success: false, 
        message: 'You are already following this user' 
      });
    }

    // Create follow relationship
    const follow = await Follow.create({
      followerId,
      followingId,
      status: 'active'
    });

    res.status(201).json({
      success: true,
      message: 'Successfully followed user',
      data: follow
    });
  } catch (error) {
    console.error('Error following user:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Unfollow a user
router.delete('/:followingId', authenticateToken, async (req, res) => {
  try {
    const { followingId } = req.params;
    const followerId = req.user.id;

    const follow = await Follow.findOne({
      where: { followerId, followingId: parseInt(followingId) }
    });

    if (!follow) {
      return res.status(404).json({ 
        success: false, 
        message: 'Follow relationship not found' 
      });
    }

    await follow.destroy();

    res.json({
      success: true,
      message: 'Successfully unfollowed user'
    });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get user's followers
router.get('/:userId/followers', async (req, res) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const { count, rows: followers } = await Follow.findAndCountAll({
      where: { 
        followingId: parseInt(userId),
        status: 'active'
      },
      include: [{
        model: User,
        as: 'follower',
        attributes: ['id', 'username', 'avatar', 'createdAt']
      }],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        followers: followers.map(f => f.follower),
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching followers:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get user's following
router.get('/:userId/following', async (req, res) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const { count, rows: following } = await Follow.findAndCountAll({
      where: { 
        followerId: parseInt(userId),
        status: 'active'
      },
      include: [{
        model: User,
        as: 'following',
        attributes: ['id', 'username', 'avatar', 'createdAt']
      }],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        following: following.map(f => f.following),
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching following:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Check if user is following another user
router.get('/check/:followingId', authenticateToken, async (req, res) => {
  try {
    const { followingId } = req.params;
    const followerId = req.user.id;

    const follow = await Follow.findOne({
      where: { 
        followerId, 
        followingId: parseInt(followingId),
        status: 'active'
      }
    });

    res.json({
      success: true,
      data: {
        isFollowing: !!follow,
        followedAt: follow ? follow.createdAt : null
      }
    });
  } catch (error) {
    console.error('Error checking follow status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get follow statistics for a user
router.get('/:userId/stats', async (req, res) => {
  try {
    const { userId } = req.params;

    const [followersCount, followingCount] = await Promise.all([
      Follow.count({
        where: { 
          followingId: parseInt(userId),
          status: 'active'
        }
      }),
      Follow.count({
        where: { 
          followerId: parseInt(userId),
          status: 'active'
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        followersCount,
        followingCount
      }
    });
  } catch (error) {
    console.error('Error fetching follow stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

module.exports = router;
