/**
 * Newzora 国际化 API 路由
 * 提供多语言支持和翻译服务
 */

const express = require('express');
const router = express.Router();
const internationalizationService = require('../services/InternationalizationService');
const { logger } = require('../config/logger');

// 检测用户语言
router.get('/detect-language', (req, res) => {
  try {
    const acceptLanguage = req.headers['accept-language'];
    const userAgent = req.headers['user-agent'] || '';
    
    const detectedLanguage = internationalizationService.detectUserLanguage(acceptLanguage, userAgent);
    const languageInfo = internationalizationService.supportedLanguages[detectedLanguage];
    
    res.json({
      success: true,
      data: {
        detectedLanguage,
        languageInfo,
        supportedLanguages: internationalizationService.getSupportedLanguages(),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Language detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect language',
      error: error.message
    });
  }
});

// 获取支持的语言列表
router.get('/languages', (req, res) => {
  try {
    const languages = internationalizationService.getSupportedLanguages();
    
    res.json({
      success: true,
      data: {
        languages,
        total: languages.length,
        rtlLanguages: internationalizationService.getRTLLanguages(),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Get languages error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get supported languages',
      error: error.message
    });
  }
});

// 获取翻译
router.get('/translations/:language', (req, res) => {
  try {
    const { language } = req.params;
    const { keys } = req.query;
    
    if (!internationalizationService.supportedLanguages[language]) {
      return res.status(400).json({
        success: false,
        message: 'Unsupported language'
      });
    }
    
    let translations;
    
    if (keys) {
      // 获取特定键的翻译
      const keyArray = Array.isArray(keys) ? keys : keys.split(',');
      translations = {};
      
      keyArray.forEach(key => {
        translations[key] = internationalizationService.getTranslation(key, language);
      });
    } else {
      // 获取所有翻译
      translations = internationalizationService.getAllTranslations(language);
    }
    
    res.json({
      success: true,
      data: {
        language,
        translations,
        isRTL: internationalizationService.isRTLLanguage(language),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Get translations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get translations',
      error: error.message
    });
  }
});

// 翻译文本
router.post('/translate', async (req, res) => {
  try {
    const {
      text,
      targetLanguage,
      sourceLanguage = 'auto'
    } = req.body;
    
    if (!text || !targetLanguage) {
      return res.status(400).json({
        success: false,
        message: 'Text and target language are required'
      });
    }
    
    if (!internationalizationService.supportedLanguages[targetLanguage]) {
      return res.status(400).json({
        success: false,
        message: 'Unsupported target language'
      });
    }
    
    const translatedText = await internationalizationService.translateText(
      text,
      targetLanguage,
      sourceLanguage
    );
    
    res.json({
      success: true,
      data: {
        originalText: text,
        translatedText,
        sourceLanguage,
        targetLanguage,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Text translation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to translate text',
      error: error.message
    });
  }
});

// 批量翻译
router.post('/translate-batch', async (req, res) => {
  try {
    const {
      texts,
      targetLanguage,
      sourceLanguage = 'auto'
    } = req.body;
    
    if (!Array.isArray(texts) || texts.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Texts array is required'
      });
    }
    
    if (texts.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 100 texts per batch'
      });
    }
    
    if (!targetLanguage || !internationalizationService.supportedLanguages[targetLanguage]) {
      return res.status(400).json({
        success: false,
        message: 'Valid target language is required'
      });
    }
    
    const translations = [];
    
    for (const text of texts) {
      try {
        const translatedText = await internationalizationService.translateText(
          text,
          targetLanguage,
          sourceLanguage
        );
        
        translations.push({
          originalText: text,
          translatedText,
          success: true
        });
      } catch (error) {
        translations.push({
          originalText: text,
          translatedText: text,
          success: false,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      data: {
        translations,
        total: texts.length,
        successful: translations.filter(t => t.success).length,
        failed: translations.filter(t => !t.success).length,
        sourceLanguage,
        targetLanguage,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Batch translation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to translate batch texts',
      error: error.message
    });
  }
});

// 检测文本语言
router.post('/detect-text-language', async (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Text is required'
      });
    }
    
    const detectedLanguage = await internationalizationService.detectTextLanguage(text);
    const languageInfo = internationalizationService.supportedLanguages[detectedLanguage];
    
    res.json({
      success: true,
      data: {
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        detectedLanguage,
        languageInfo: languageInfo || { name: 'Unknown', nativeName: 'Unknown' },
        confidence: detectedLanguage !== 'unknown' ? 0.8 : 0.1,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Text language detection error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect text language',
      error: error.message
    });
  }
});

// 格式化日期时间
router.post('/format-datetime', (req, res) => {
  try {
    const {
      date,
      language = 'en',
      options = {}
    } = req.body;
    
    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date is required'
      });
    }
    
    if (!internationalizationService.supportedLanguages[language]) {
      return res.status(400).json({
        success: false,
        message: 'Unsupported language'
      });
    }
    
    const formattedDate = internationalizationService.formatDateTime(date, language, options);
    
    res.json({
      success: true,
      data: {
        originalDate: date,
        formattedDate,
        language,
        locale: internationalizationService.getLocaleFromLanguage(language),
        options,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Date formatting error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to format date',
      error: error.message
    });
  }
});

// 获取语言统计信息
router.get('/stats', (req, res) => {
  try {
    const languages = internationalizationService.getSupportedLanguages();
    const rtlLanguages = internationalizationService.getRTLLanguages();
    
    const stats = {
      totalLanguages: languages.length,
      rtlLanguages: rtlLanguages.length,
      ltrLanguages: languages.length - rtlLanguages.length,
      languagesByRegion: {
        europe: languages.filter(l => ['en', 'es', 'fr', 'de', 'it', 'nl', 'sv', 'da', 'no', 'fi', 'pl', 'ru'].includes(l.code)).length,
        asia: languages.filter(l => ['zh', 'zh-TW', 'ja', 'ko', 'hi', 'th', 'vi', 'id', 'ms', 'tl'].includes(l.code)).length,
        middleEast: languages.filter(l => ['ar', 'tr'].includes(l.code)).length,
        americas: languages.filter(l => ['en', 'es', 'pt'].includes(l.code)).length
      },
      mostCommonLanguages: [
        'en', 'zh', 'es', 'hi', 'ar', 'pt', 'ru', 'ja', 'de', 'fr'
      ].filter(code => languages.some(l => l.code === code))
    };
    
    res.json({
      success: true,
      data: {
        stats,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Get i18n stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get internationalization stats',
      error: error.message
    });
  }
});

// 清理翻译缓存（管理员功能）
router.post('/clear-cache', (req, res) => {
  try {
    // 这里应该检查管理员权限
    // 简化版本跳过权限检查
    
    internationalizationService.clearTranslationCache();
    
    res.json({
      success: true,
      message: 'Translation cache cleared successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Clear cache error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear translation cache',
      error: error.message
    });
  }
});

module.exports = router;
