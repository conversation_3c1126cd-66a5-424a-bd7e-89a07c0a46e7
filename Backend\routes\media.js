const express = require('express');
const router = express.Router();
const { MediaFile, User } = require('../models/associations');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { upload, processUploadedFile, getFileType } = require('../middleware/upload');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs').promises;
const mediaProcessingService = require('../services/MediaProcessingService');

// 上传单个文件
router.post('/upload', authenticateToken, upload.single('file'), processUploadedFile, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const { file, fileProcessResult = {}, fileType } = req;
    const { folder = 'uploads', description, altText, tags = [] } = req.body;

    // 解析标签
    let parsedTags = [];
    try {
      parsedTags = typeof tags === 'string' ? JSON.parse(tags) : tags;
    } catch (error) {
      parsedTags = [];
    }

    // 创建媒体文件记录
    const mediaFile = await MediaFile.create({
      filename: file.filename,
      originalName: file.originalname,
      mimeType: file.mimetype,
      fileType,
      size: file.size,
      path: file.path,
      url: `/uploads/${fileType}/${file.filename}`,
      thumbnailUrl: fileProcessResult.thumbnailPath ? 
        `/uploads/${fileType}/${path.basename(fileProcessResult.thumbnailPath)}` : null,
      duration: fileProcessResult.duration || null,
      dimensions: fileProcessResult.dimensions || null,
      uploaderId: req.user.id,
      folder,
      description,
      altText,
      tags: parsedTags,
      status: 'ready',
      processingInfo: fileProcessResult
    });

    res.status(201).json({
      success: true,
      data: { mediaFile },
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    
    // 清理上传的文件
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Error cleaning up file:', unlinkError);
      }
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Internal server error'
    });
  }
});

// 批量上传文件
router.post('/upload/batch', authenticateToken, upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const { folder = 'uploads' } = req.body;
    const uploadedFiles = [];
    const errors = [];

    for (const file of req.files) {
      try {
        const fileType = getFileType(file.mimetype);
        
        const mediaFile = await MediaFile.create({
          filename: file.filename,
          originalName: file.originalname,
          mimeType: file.mimetype,
          fileType,
          size: file.size,
          path: file.path,
          url: `/uploads/${fileType}/${file.filename}`,
          uploaderId: req.user.id,
          folder,
          status: 'ready'
        });

        uploadedFiles.push(mediaFile);
      } catch (error) {
        errors.push({
          filename: file.originalname,
          error: error.message
        });
        
        // 清理失败的文件
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('Error cleaning up file:', unlinkError);
        }
      }
    }

    res.status(201).json({
      success: true,
      data: { 
        uploadedFiles,
        errors: errors.length > 0 ? errors : undefined
      },
      message: `${uploadedFiles.length} files uploaded successfully${errors.length > 0 ? `, ${errors.length} failed` : ''}`
    });
  } catch (error) {
    console.error('Error in batch upload:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取用户文件列表
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('fileType').optional().isIn(['image', 'video', 'audio', 'document']),
  query('folder').optional().isLength({ min: 1, max: 100 }),
  query('search').optional().isLength({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      fileType,
      folder,
      search,
      orderBy = 'createdAt',
      orderDirection = 'DESC'
    } = req.query;

    const whereClause = {
      uploaderId: req.user.id,
      isDeleted: false,
      status: 'ready'
    };

    if (fileType) {
      whereClause.fileType = fileType;
    }

    if (folder) {
      whereClause.folder = folder;
    }

    if (search) {
      whereClause[Op.or] = [
        { originalName: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { tags: { [Op.contains]: [search] } }
      ];
    }

    const { count, rows: files } = await MediaFile.findAndCountAll({
      where: whereClause,
      order: [[orderBy, orderDirection]],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'avatar']
        }
      ]
    });

    res.json({
      success: true,
      data: {
        files,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching media files:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取单个文件详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id,
        isDeleted: false
      },
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'avatar']
        }
      ]
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    // 增加查看次数
    await mediaFile.incrementView();

    res.json({
      success: true,
      data: { mediaFile }
    });
  } catch (error) {
    console.error('Error fetching media file:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 更新文件信息
router.put('/:id', authenticateToken, [
  body('description').optional().isLength({ max: 1000 }),
  body('altText').optional().isLength({ max: 255 }),
  body('tags').optional().isArray({ max: 10 }),
  body('folder').optional().isLength({ min: 1, max: 100 }),
  body('isPublic').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id,
        isDeleted: false
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    // 更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        mediaFile[key] = updateData[key];
      }
    });

    await mediaFile.save();

    res.json({
      success: true,
      data: { mediaFile },
      message: 'Media file updated successfully'
    });
  } catch (error) {
    console.error('Error updating media file:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 删除文件
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id,
        isDeleted: false
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    // 软删除
    mediaFile.isDeleted = true;
    mediaFile.deletedAt = new Date();
    mediaFile.status = 'deleted';
    await mediaFile.save();

    // TODO: 可以选择立即删除物理文件或定期清理
    // try {
    //   await fs.unlink(mediaFile.path);
    //   if (mediaFile.thumbnailUrl) {
    //     const thumbnailPath = path.join(__dirname, '../uploads', mediaFile.thumbnailUrl);
    //     await fs.unlink(thumbnailPath);
    //   }
    // } catch (error) {
    //   console.error('Error deleting physical file:', error);
    // }

    res.json({
      success: true,
      message: 'Media file deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting media file:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 批量删除文件
router.delete('/batch/delete', authenticateToken, [
  body('fileIds').isArray({ min: 1, max: 50 }),
  body('fileIds.*').isInt({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { fileIds } = req.body;

    const deletedCount = await MediaFile.update(
      {
        isDeleted: true,
        deletedAt: new Date(),
        status: 'deleted'
      },
      {
        where: {
          id: { [Op.in]: fileIds },
          uploaderId: req.user.id,
          isDeleted: false
        }
      }
    );

    res.json({
      success: true,
      data: { deletedCount: deletedCount[0] },
      message: `${deletedCount[0]} files deleted successfully`
    });
  } catch (error) {
    console.error('Error batch deleting files:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取文件夹列表
router.get('/folders/list', authenticateToken, async (req, res) => {
  try {
    const folders = await MediaFile.findAll({
      where: {
        uploaderId: req.user.id,
        isDeleted: false
      },
      attributes: [
        'folder',
        [MediaFile.sequelize.fn('COUNT', '*'), 'fileCount'],
        [MediaFile.sequelize.fn('SUM', MediaFile.sequelize.col('size')), 'totalSize']
      ],
      group: ['folder'],
      order: [['folder', 'ASC']]
    });

    res.json({
      success: true,
      data: { folders }
    });
  } catch (error) {
    console.error('Error fetching folders:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取文件统计信息
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // 按文件类型统计
    const typeStats = await MediaFile.findAll({
      where: {
        uploaderId: userId,
        isDeleted: false,
        status: 'ready'
      },
      attributes: [
        'fileType',
        [MediaFile.sequelize.fn('COUNT', '*'), 'count'],
        [MediaFile.sequelize.fn('SUM', MediaFile.sequelize.col('size')), 'totalSize']
      ],
      group: ['fileType'],
      raw: true
    });

    // 总统计
    const totalStats = await MediaFile.findOne({
      where: {
        uploaderId: userId,
        isDeleted: false,
        status: 'ready'
      },
      attributes: [
        [MediaFile.sequelize.fn('COUNT', '*'), 'totalFiles'],
        [MediaFile.sequelize.fn('SUM', MediaFile.sequelize.col('size')), 'totalSize'],
        [MediaFile.sequelize.fn('SUM', MediaFile.sequelize.col('viewCount')), 'totalViews'],
        [MediaFile.sequelize.fn('SUM', MediaFile.sequelize.col('downloadCount')), 'totalDownloads']
      ],
      raw: true
    });

    // 最近上传统计
    const recentUploads = await MediaFile.count({
      where: {
        uploaderId: userId,
        isDeleted: false,
        status: 'ready',
        createdAt: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
        }
      }
    });

    res.json({
      success: true,
      data: {
        typeStats,
        totalStats,
        recentUploads
      }
    });
  } catch (error) {
    console.error('Error fetching media stats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 下载文件
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        isDeleted: false,
        status: 'ready'
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    // 检查权限（如果文件不是公开的，需要是上传者）
    if (!mediaFile.isPublic && mediaFile.uploaderId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // 增加下载次数
    await mediaFile.incrementDownload();

    // 发送文件
    res.download(mediaFile.path, mediaFile.originalName, (err) => {
      if (err) {
        console.error('Error downloading file:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: 'Error downloading file'
          });
        }
      }
    });
  } catch (error) {
    console.error('Error in download:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取公开文件（用于富文本编辑器等）
router.get('/public/browse', [
  query('fileType').optional().isIn(['image', 'video', 'audio']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const {
      fileType = 'image',
      page = 1,
      limit = 20
    } = req.query;

    const { count, rows: files } = await MediaFile.findAndCountAll({
      where: {
        fileType,
        isPublic: true,
        isDeleted: false,
        status: 'ready'
      },
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      attributes: ['id', 'originalName', 'url', 'thumbnailUrl', 'dimensions', 'size', 'createdAt']
    });

    res.json({
      success: true,
      data: {
        files,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching public files:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// ==================== 音视频处理功能 ====================

// 转换媒体文件
router.post('/convert/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      outputFormat,
      quality = 'medium',
      options = {}
    } = req.body;

    // 获取媒体文件信息
    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id // 确保用户只能处理自己的文件
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    const inputPath = mediaFile.path;
    const outputDir = path.join(path.dirname(inputPath), 'converted');
    await fs.mkdir(outputDir, { recursive: true });

    const outputFilename = `${path.parse(mediaFile.filename).name}_${quality}.${outputFormat}`;
    const outputPath = path.join(outputDir, outputFilename);

    let conversionResult;

    if (['mp4', 'webm', 'mov'].includes(outputFormat)) {
      // 视频转换
      conversionResult = await mediaProcessingService.convertVideo(inputPath, outputPath, {
        quality,
        format: outputFormat,
        ...options
      });
    } else if (['mp3', 'aac', 'ogg', 'wav'].includes(outputFormat)) {
      // 音频转换
      conversionResult = await mediaProcessingService.convertAudio(inputPath, outputPath, {
        quality,
        format: outputFormat,
        ...options
      });
    } else {
      return res.status(400).json({
        success: false,
        message: `Unsupported output format: ${outputFormat}`
      });
    }

    // 创建转换后的文件记录
    const convertedFile = await MediaFile.create({
      filename: outputFilename,
      originalName: `${mediaFile.originalName}_converted.${outputFormat}`,
      mimeType: outputFormat === 'mp4' ? 'video/mp4' :
                outputFormat === 'webm' ? 'video/webm' :
                outputFormat === 'mp3' ? 'audio/mp3' :
                outputFormat === 'aac' ? 'audio/aac' : 'application/octet-stream',
      fileType: ['mp4', 'webm', 'mov'].includes(outputFormat) ? 'video' : 'audio',
      size: (await fs.stat(outputPath)).size,
      path: outputPath,
      url: `/uploads/converted/${outputFilename}`,
      uploaderId: req.user.id,
      parentId: mediaFile.id,
      description: `Converted from ${mediaFile.originalName}`,
      status: 'ready',
      metadata: {
        convertedFrom: mediaFile.id,
        originalFormat: path.extname(mediaFile.filename).slice(1),
        conversionOptions: { quality, outputFormat, ...options }
      }
    });

    res.json({
      success: true,
      message: 'Media conversion completed',
      data: {
        originalFile: mediaFile,
        convertedFile,
        conversionResult
      }
    });

  } catch (error) {
    console.error('Media conversion error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to convert media',
      error: error.message
    });
  }
});

// 压缩媒体文件
router.post('/compress/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { compressionLevel = 'medium' } = req.body;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found'
      });
    }

    const inputPath = mediaFile.path;
    const outputDir = path.join(path.dirname(inputPath), 'compressed');
    await fs.mkdir(outputDir, { recursive: true });

    const outputFilename = `${path.parse(mediaFile.filename).name}_compressed_${compressionLevel}${path.extname(mediaFile.filename)}`;
    const outputPath = path.join(outputDir, outputFilename);

    const compressionResult = await mediaProcessingService.compressMedia(
      inputPath,
      outputPath,
      mediaFile.fileType,
      compressionLevel
    );

    // 创建压缩后的文件记录
    const compressedFile = await MediaFile.create({
      filename: outputFilename,
      originalName: `${mediaFile.originalName}_compressed`,
      mimeType: mediaFile.mimeType,
      fileType: mediaFile.fileType,
      size: (await fs.stat(outputPath)).size,
      path: outputPath,
      url: `/uploads/compressed/${outputFilename}`,
      uploaderId: req.user.id,
      parentId: mediaFile.id,
      description: `Compressed version of ${mediaFile.originalName}`,
      status: 'ready',
      metadata: {
        compressedFrom: mediaFile.id,
        compressionLevel,
        originalSize: mediaFile.size,
        compressionRatio: ((mediaFile.size - (await fs.stat(outputPath)).size) / mediaFile.size * 100).toFixed(2) + '%'
      }
    });

    res.json({
      success: true,
      message: 'Media compression completed',
      data: {
        originalFile: mediaFile,
        compressedFile,
        compressionResult,
        sizeReduction: compressedFile.metadata.compressionRatio
      }
    });

  } catch (error) {
    console.error('Media compression error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to compress media',
      error: error.message
    });
  }
});

// 提取音频
router.post('/extract-audio/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      format = 'mp3',
      quality = 'medium'
    } = req.body;

    const mediaFile = await MediaFile.findOne({
      where: {
        id,
        uploaderId: req.user.id,
        fileType: 'video' // 只能从视频文件提取音频
      }
    });

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Video file not found'
      });
    }

    const inputPath = mediaFile.path;
    const outputDir = path.join(path.dirname(inputPath), 'extracted');
    await fs.mkdir(outputDir, { recursive: true });

    const outputFilename = `${path.parse(mediaFile.filename).name}_audio.${format}`;
    const outputPath = path.join(outputDir, outputFilename);

    const extractionResult = await mediaProcessingService.extractAudio(inputPath, outputPath, {
      format,
      quality
    });

    // 创建提取的音频文件记录
    const audioFile = await MediaFile.create({
      filename: outputFilename,
      originalName: `${mediaFile.originalName}_audio.${format}`,
      mimeType: format === 'mp3' ? 'audio/mp3' :
                format === 'aac' ? 'audio/aac' :
                format === 'wav' ? 'audio/wav' : 'audio/mpeg',
      fileType: 'audio',
      size: (await fs.stat(outputPath)).size,
      path: outputPath,
      url: `/uploads/extracted/${outputFilename}`,
      uploaderId: req.user.id,
      parentId: mediaFile.id,
      description: `Audio extracted from ${mediaFile.originalName}`,
      status: 'ready',
      metadata: {
        extractedFrom: mediaFile.id,
        extractionFormat: format,
        extractionQuality: quality
      }
    });

    res.json({
      success: true,
      message: 'Audio extraction completed',
      data: {
        originalFile: mediaFile,
        audioFile,
        extractionResult
      }
    });

  } catch (error) {
    console.error('Audio extraction error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to extract audio',
      error: error.message
    });
  }
});

module.exports = router;
