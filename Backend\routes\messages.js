const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const DirectMessage = require('../models/DirectMessage');
const User = require('../models/User');
const UserRelationship = require('../models/UserRelationship');
const { Op } = require('sequelize');

// 获取对话列表
router.get('/conversations', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const conversations = await DirectMessage.getConversationList(req.user.id, {
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        conversations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: conversations.length === parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch conversations',
      error: error.message
    });
  }
});

// 获取特定对话的消息
router.get('/conversations/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    // 验证目标用户存在
    const targetUser = await User.findByPk(userId, {
      attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // 检查是否被屏蔽
    const relationship = await UserRelationship.getRelationshipStatus(userId, req.user.id);
    if (relationship && relationship.relationshipType === 'block' && relationship.isActive) {
      return res.status(403).json({
        success: false,
        message: 'You are blocked by this user'
      });
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const messages = await DirectMessage.getConversation(req.user.id, parseInt(userId), {
      limit: parseInt(limit),
      offset: offset,
      order: [['createdAt', 'DESC']]
    });

    // 标记消息为已读
    const conversationId = DirectMessage.generateConversationId(req.user.id, parseInt(userId));
    await DirectMessage.markConversationAsRead(conversationId, req.user.id);

    res.json({
      success: true,
      data: {
        messages: messages.reverse(), // 反转以显示最新消息在底部
        targetUser,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: messages.length === parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch conversation',
      error: error.message
    });
  }
});

// 发送消息
router.post('/send', authenticateToken, async (req, res) => {
  try {
    const { receiverId, content, messageType = 'text', metadata = {}, replyToId } = req.body;

    if (!receiverId || !content) {
      return res.status(400).json({
        success: false,
        message: 'Receiver ID and content are required'
      });
    }

    // 验证接收者存在
    const receiver = await User.findByPk(receiverId);
    if (!receiver) {
      return res.status(404).json({
        success: false,
        message: 'Receiver not found'
      });
    }

    // 检查是否被屏蔽
    const relationship = await UserRelationship.getRelationshipStatus(receiverId, req.user.id);
    if (relationship && relationship.relationshipType === 'block' && relationship.isActive) {
      return res.status(403).json({
        success: false,
        message: 'You are blocked by this user'
      });
    }

    // 检查是否屏蔽了对方
    const myRelationship = await UserRelationship.getRelationshipStatus(req.user.id, receiverId);
    if (myRelationship && myRelationship.relationshipType === 'block' && myRelationship.isActive) {
      return res.status(403).json({
        success: false,
        message: 'You have blocked this user'
      });
    }

    const conversationId = DirectMessage.generateConversationId(req.user.id, receiverId);

    const message = await DirectMessage.create({
      conversationId,
      senderId: req.user.id,
      receiverId: parseInt(receiverId),
      messageType,
      content,
      metadata,
      replyToId: replyToId ? parseInt(replyToId) : null
    });

    // 获取完整的消息信息（包含关联数据）
    const fullMessage = await DirectMessage.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
        },
        {
          model: DirectMessage,
          as: 'replyTo',
          attributes: ['id', 'content', 'senderId'],
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['id', 'username']
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: fullMessage
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message',
      error: error.message
    });
  }
});

// 标记消息为已读
router.post('/mark-read', authenticateToken, async (req, res) => {
  try {
    const { messageIds, conversationUserId } = req.body;

    if (messageIds && Array.isArray(messageIds)) {
      // 标记特定消息为已读
      await DirectMessage.bulkMarkAsRead(messageIds, req.user.id);
    } else if (conversationUserId) {
      // 标记整个对话为已读
      const conversationId = DirectMessage.generateConversationId(req.user.id, conversationUserId);
      await DirectMessage.markConversationAsRead(conversationId, req.user.id);
    } else {
      return res.status(400).json({
        success: false,
        message: 'Either messageIds or conversationUserId is required'
      });
    }

    res.json({
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
});

// 获取未读消息数量
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const unreadCount = await DirectMessage.getUnreadCount(req.user.id);

    res.json({
      success: true,
      data: { unreadCount }
    });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch unread count',
      error: error.message
    });
  }
});

// 删除消息
router.delete('/messages/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;

    const message = await DirectMessage.findByPk(messageId);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // 只有发送者可以删除消息
    if (message.senderId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own messages'
      });
    }

    await message.softDelete(req.user.id);

    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete message',
      error: error.message
    });
  }
});

module.exports = router;
