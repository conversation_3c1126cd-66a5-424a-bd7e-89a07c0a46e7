const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../middleware/auth');
const { sequelize } = require('../config/database');
const Article = require('../models/Article');
const User = require('../models/User');
const ReviewRule = require('../models/ReviewRule');
const ContentModerationRule = require('../models/ContentModerationRule');
const ContentModerationLog = require('../models/ContentModerationLog');
const aiContentModerationService = require('../services/AIContentModerationService');

// 获取审核统计
router.get('/stats', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 获取基础统计
    const [totalPending] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE status = 'pending_review'
    `);

    const [totalReviewed] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE status IN ('approved', 'rejected') 
      AND "reviewedAt" IS NOT NULL
    `);

    const [approvedToday] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE status = 'approved' 
      AND "reviewedAt" >= :today 
      AND "reviewedAt" < :tomorrow
    `, {
      replacements: { today, tomorrow }
    });

    const [rejectedToday] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE status = 'rejected' 
      AND "reviewedAt" >= :today 
      AND "reviewedAt" < :tomorrow
    `, {
      replacements: { today, tomorrow }
    });

    // 获取当前用户今日审核数量
    const [myReviewsToday] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE "reviewedBy" = :userId 
      AND "reviewedAt" >= :today 
      AND "reviewedAt" < :tomorrow
    `, {
      replacements: { userId: req.user.id, today, tomorrow }
    });

    // 计算平均审核时间（分钟）
    const [avgReviewTime] = await sequelize.query(`
      SELECT AVG(EXTRACT(EPOCH FROM ("reviewedAt" - "createdAt"))/60) as avg_minutes
      FROM articles 
      WHERE "reviewedAt" IS NOT NULL 
      AND "createdAt" >= :weekAgo
    `, {
      replacements: { weekAgo: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });

    const stats = {
      totalPending: parseInt(totalPending[0].count),
      totalReviewed: parseInt(totalReviewed[0].count),
      approvedToday: parseInt(approvedToday[0].count),
      rejectedToday: parseInt(rejectedToday[0].count),
      myReviewsToday: parseInt(myReviewsToday[0].count),
      averageReviewTime: Math.round(parseFloat(avgReviewTime[0].avg_minutes) || 0)
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching moderation stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch moderation statistics',
      error: error.message
    });
  }
});

// 获取待审核队列
router.get('/queue', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      priority, 
      category, 
      status = 'pending_review',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const where = {};
    
    if (status !== 'all') {
      where.status = status;
    }
    
    if (priority && priority !== 'all') {
      where.priority = priority;
    }
    
    if (category && category !== 'all') {
      where.category = category;
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const articles = await Article.findAndCountAll({
      where,
      order: [[sortBy, sortOrder.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset,
      include: [
        {
          model: User,
          as: 'authorUser',
          attributes: ['id', 'username', 'firstName', 'lastName']
        },
        {
          model: User,
          as: 'reviewedByUser',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });

    // 转换为前端需要的格式
    const reviews = articles.rows.map(article => ({
      id: article.id,
      articleId: article.id,
      title: article.title,
      description: article.description,
      content: article.content,
      author: {
        id: article.authorUser.id,
        username: article.authorUser.username,
        firstName: article.authorUser.firstName,
        lastName: article.authorUser.lastName
      },
      submittedAt: article.createdAt,
      priority: article.priority || 'normal',
      category: article.category,
      wordCount: article.content ? article.content.replace(/<[^>]*>/g, '').split(/\s+/).length : 0,
      flags: article.flags || [],
      aiScore: article.aiScore,
      aiReasons: article.aiReasons,
      status: article.status,
      assignedTo: article.reviewedByUser ? {
        id: article.reviewedByUser.id,
        username: article.reviewedByUser.username
      } : null
    }));

    res.json({
      success: true,
      data: {
        reviews,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: articles.count,
          pages: Math.ceil(articles.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching moderation queue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch moderation queue',
      error: error.message
    });
  }
});

// 获取待审核内容详情
router.get('/pending', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const articles = await Article.findAll({
      where: { status: 'pending_review' },
      order: [['priority', 'DESC'], ['createdAt', 'ASC']],
      limit: parseInt(limit),
      include: [
        {
          model: User,
          as: 'authorUser',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });

    const reviews = articles.map(article => ({
      id: article.id,
      articleId: article.id,
      title: article.title,
      author: {
        id: article.authorUser.id,
        username: article.authorUser.username,
        firstName: article.authorUser.firstName,
        lastName: article.authorUser.lastName
      },
      submittedAt: article.createdAt,
      priority: article.priority || 'normal',
      category: article.category,
      wordCount: article.content ? article.content.replace(/<[^>]*>/g, '').split(/\s+/).length : 0,
      flags: article.flags || [],
      aiScore: article.aiScore
    }));

    res.json({
      success: true,
      data: { reviews }
    });
  } catch (error) {
    console.error('Error fetching pending reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending reviews',
      error: error.message
    });
  }
});

// 获取最近活动
router.get('/activity', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const activities = await Article.findAll({
      where: {
        status: {
          [sequelize.Sequelize.Op.in]: ['approved', 'rejected']
        },
        reviewedAt: {
          [sequelize.Sequelize.Op.not]: null
        }
      },
      order: [['reviewedAt', 'DESC']],
      limit: parseInt(limit),
      include: [
        {
          model: User,
          as: 'reviewedByUser',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });

    const recentActivity = activities.map(article => ({
      id: article.id,
      type: article.status === 'approved' ? 'approved' : 'rejected',
      articleTitle: article.title,
      moderator: article.reviewedByUser ? 
        (article.reviewedByUser.firstName && article.reviewedByUser.lastName 
          ? `${article.reviewedByUser.firstName} ${article.reviewedByUser.lastName}`
          : article.reviewedByUser.username) : 'Unknown',
      timestamp: article.reviewedAt,
      reason: article.reviewNotes
    }));

    res.json({
      success: true,
      data: { activities: recentActivity }
    });
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent activity',
      error: error.message
    });
  }
});

// 审核文章 - 批准
router.post('/reviews/:reviewId/approve', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { notes } = req.body;
    
    const article = await Article.findByPk(reviewId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }

    await article.update({
      status: 'approved',
      published: true,
      publishedAt: new Date(),
      reviewedBy: req.user.id,
      reviewedAt: new Date(),
      reviewNotes: notes
    });

    res.json({
      success: true,
      message: 'Article approved successfully'
    });
  } catch (error) {
    console.error('Error approving article:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve article',
      error: error.message
    });
  }
});

// 审核文章 - 拒绝
router.post('/reviews/:reviewId/reject', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reason, notes } = req.body;
    
    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const article = await Article.findByPk(reviewId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }

    await article.update({
      status: 'rejected',
      published: false,
      reviewedBy: req.user.id,
      reviewedAt: new Date(),
      reviewNotes: notes,
      rejectionReason: reason
    });

    res.json({
      success: true,
      message: 'Article rejected successfully'
    });
  } catch (error) {
    console.error('Error rejecting article:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject article',
      error: error.message
    });
  }
});

// 分配审核员
router.post('/reviews/:reviewId/assign', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { moderatorId } = req.body;
    
    const article = await Article.findByPk(reviewId);
    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'Article not found'
      });
    }

    // 验证审核员存在且有权限
    const moderator = await User.findByPk(moderatorId);
    if (!moderator || !['moderator', 'admin', 'super_admin'].includes(moderator.role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid moderator'
      });
    }

    await article.update({
      reviewedBy: moderatorId,
      status: 'in_review'
    });

    res.json({
      success: true,
      message: 'Article assigned successfully'
    });
  } catch (error) {
    console.error('Error assigning article:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign article',
      error: error.message
    });
  }
});

// 批量操作
router.post('/bulk/:action', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { action } = req.params;
    const { reviewIds, reason, notes } = req.body;
    
    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Review IDs are required'
      });
    }

    const updateData = {
      reviewedBy: req.user.id,
      reviewedAt: new Date()
    };

    switch (action) {
      case 'approve':
        updateData.status = 'approved';
        updateData.published = true;
        updateData.publishedAt = new Date();
        if (notes) updateData.reviewNotes = notes;
        break;
        
      case 'reject':
        if (!reason) {
          return res.status(400).json({
            success: false,
            message: 'Rejection reason is required for bulk reject'
          });
        }
        updateData.status = 'rejected';
        updateData.published = false;
        updateData.rejectionReason = reason;
        if (notes) updateData.reviewNotes = notes;
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid bulk action'
        });
    }

    const [updatedCount] = await Article.update(updateData, {
      where: {
        id: {
          [sequelize.Sequelize.Op.in]: reviewIds
        }
      }
    });

    res.json({
      success: true,
      message: `Bulk ${action} completed successfully`,
      data: { updatedCount }
    });
  } catch (error) {
    console.error(`Error in bulk ${req.params.action}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to perform bulk ${req.params.action}`,
      error: error.message
    });
  }
});

// AI内容审核相关API

// 创建审核规则
router.post('/rules', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { name, description, ruleType, targetType, ruleConfig, action, severity, priority } = req.body;

    if (!name || !ruleType || !targetType || !action) {
      return res.status(400).json({
        success: false,
        message: 'Name, rule type, target type, and action are required'
      });
    }

    const rule = await ContentModerationRule.create({
      name,
      description,
      ruleType,
      targetType,
      ruleConfig: ruleConfig || {},
      action,
      severity: severity || 'medium',
      priority: priority || 100,
      createdBy: req.user.id
    });

    res.status(201).json({
      success: true,
      message: 'Moderation rule created successfully',
      data: rule
    });
  } catch (error) {
    console.error('Error creating moderation rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create moderation rule',
      error: error.message
    });
  }
});

// 获取审核规则列表
router.get('/rules', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { ruleType, targetType, isActive, page = 1, limit = 20 } = req.query;

    const where = {};
    if (ruleType) where.ruleType = ruleType;
    if (targetType) where.targetType = targetType;
    if (isActive !== undefined) where.isActive = isActive === 'true';

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const rules = await ContentModerationRule.findAndCountAll({
      where,
      order: [['priority', 'ASC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: offset,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });

    res.json({
      success: true,
      data: {
        rules: rules.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: rules.count,
          pages: Math.ceil(rules.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching moderation rules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch moderation rules',
      error: error.message
    });
  }
});

// 更新审核规则
router.put('/rules/:ruleId', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { ruleId } = req.params;
    const updateData = req.body;

    const rule = await ContentModerationRule.findByPk(ruleId);
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Moderation rule not found'
      });
    }

    await rule.update({
      ...updateData,
      lastModifiedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Moderation rule updated successfully',
      data: rule
    });
  } catch (error) {
    console.error('Error updating moderation rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update moderation rule',
      error: error.message
    });
  }
});

// 删除审核规则
router.delete('/rules/:ruleId', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { ruleId } = req.params;

    const rule = await ContentModerationRule.findByPk(ruleId);
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Moderation rule not found'
      });
    }

    await rule.destroy();

    res.json({
      success: true,
      message: 'Moderation rule deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting moderation rule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete moderation rule',
      error: error.message
    });
  }
});

// 手动触发AI审核
router.post('/ai-moderate', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const { contentType, contentId, content, authorId } = req.body;

    if (!contentType || !contentId || !content || !authorId) {
      return res.status(400).json({
        success: false,
        message: 'Content type, ID, content, and author ID are required'
      });
    }

    const result = await aiContentModerationService.moderateContent(
      contentType,
      parseInt(contentId),
      content,
      parseInt(authorId)
    );

    res.json({
      success: true,
      message: 'AI moderation completed',
      data: result
    });
  } catch (error) {
    console.error('Error in AI moderation:', error);
    res.status(500).json({
      success: false,
      message: 'AI moderation failed',
      error: error.message
    });
  }
});

// 获取审核日志
router.get('/logs', authenticateToken, requireRole(['moderator', 'admin', 'super_admin']), async (req, res) => {
  try {
    const {
      contentType,
      authorId,
      moderationType,
      action,
      severity,
      page = 1,
      limit = 20,
      startDate,
      endDate
    } = req.query;

    const where = {};
    if (contentType) where.contentType = contentType;
    if (authorId) where.authorId = parseInt(authorId);
    if (moderationType) where.moderationType = moderationType;
    if (action) where.action = action;
    if (severity) where.severity = severity;

    if (startDate && endDate) {
      where.createdAt = {
        [sequelize.Sequelize.Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const logs = await ContentModerationLog.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: offset,
      include: [
        {
          model: ContentModerationRule,
          as: 'rule',
          attributes: ['id', 'name', 'ruleType', 'action']
        },
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'firstName', 'lastName']
        },
        {
          model: User,
          as: 'moderator',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });

    res.json({
      success: true,
      data: {
        logs: logs.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: logs.count,
          pages: Math.ceil(logs.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching moderation logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch moderation logs',
      error: error.message
    });
  }
});

module.exports = router;
