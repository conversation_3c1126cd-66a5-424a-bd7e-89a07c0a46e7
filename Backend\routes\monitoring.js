/**
 * OneNews 监控和健康检查路由
 * 提供系统状态、性能指标、日志查询等监控功能
 */

const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const fs = require('fs').promises;
const path = require('path');

const { sequelize } = require('../config/database');
const { logger, performanceLogger } = require('../config/logger');
const { getApiUsageStats } = require('../middleware/logging');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

/**
 * 基础健康检查端点
 * GET /api/monitoring/health
 */
router.get('/health', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // 检查数据库连接
    await sequelize.authenticate();
    const dbResponseTime = Date.now() - startTime;
    
    // 检查必需的环境变量
    const requiredEnvVars = ['JWT_SECRET', 'DB_HOST', 'DB_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    // 获取系统信息
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      
      // 数据库状态
      database: {
        status: 'connected',
        responseTime: `${dbResponseTime}ms`
      },
      
      // 系统资源
      system: {
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024), // MB
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
          external: Math.round(memUsage.external / 1024 / 1024) // MB
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        }
      }
    };
    
    // 添加警告信息
    if (missingVars.length > 0) {
      healthStatus.warnings = [`Missing environment variables: ${missingVars.join(', ')}`];
    }
    
    if (dbResponseTime > 1000) {
      healthStatus.warnings = healthStatus.warnings || [];
      healthStatus.warnings.push(`Slow database response: ${dbResponseTime}ms`);
    }
    
    if (memUsage.heapUsed / memUsage.heapTotal > 0.9) {
      healthStatus.warnings = healthStatus.warnings || [];
      healthStatus.warnings.push('High memory usage detected');
    }
    
    res.status(200).json(healthStatus);
    
  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected'
    });
  }
});

/**
 * 详细系统状态
 * GET /api/monitoring/status
 */
router.get('/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // 获取数据库统计
    const dbStats = await getDatabaseStats();
    
    // 获取 API 使用统计
    const apiStats = getApiUsageStats();
    
    // 获取系统指标
    const systemMetrics = await getSystemMetrics();
    
    // 获取错误统计
    const errorStats = await getErrorStats();
    
    const status = {
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      environment: process.env.NODE_ENV || 'development',
      
      database: dbStats,
      api: apiStats,
      system: systemMetrics,
      errors: errorStats
    };
    
    res.json(status);
    
  } catch (error) {
    logger.error('Status check failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to get system status',
      error: error.message
    });
  }
});

/**
 * 性能指标
 * GET /api/monitoring/metrics
 */
router.get('/metrics', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { timeRange = '1h' } = req.query;
    
    // 获取性能日志
    const metrics = await getPerformanceMetrics(timeRange);
    
    res.json({
      success: true,
      data: metrics,
      timeRange
    });
    
  } catch (error) {
    logger.error('Metrics retrieval failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to get performance metrics',
      error: error.message
    });
  }
});

/**
 * 日志查询
 * GET /api/monitoring/logs
 */
router.get('/logs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { 
      level = 'info', 
      limit = 100, 
      offset = 0,
      startDate,
      endDate 
    } = req.query;
    
    const logs = await getLogs({
      level,
      limit: parseInt(limit),
      offset: parseInt(offset),
      startDate,
      endDate
    });
    
    res.json({
      success: true,
      data: logs,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
    
  } catch (error) {
    logger.error('Log retrieval failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve logs',
      error: error.message
    });
  }
});

/**
 * 系统警报
 * GET /api/monitoring/alerts
 */
router.get('/alerts', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const alerts = await getSystemAlerts();
    
    res.json({
      success: true,
      data: alerts
    });
    
  } catch (error) {
    logger.error('Alert retrieval failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to get system alerts',
      error: error.message
    });
  }
});

/**
 * 清理日志
 * POST /api/monitoring/cleanup-logs
 */
router.post('/cleanup-logs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { olderThan = 30 } = req.body; // 默认清理30天前的日志
    
    const result = await cleanupOldLogs(olderThan);
    
    logger.info('Log cleanup completed', { 
      deletedFiles: result.deletedFiles,
      olderThan: olderThan 
    });
    
    res.json({
      success: true,
      message: 'Log cleanup completed',
      data: result
    });
    
  } catch (error) {
    logger.error('Log cleanup failed', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup logs',
      error: error.message
    });
  }
});

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 获取数据库统计信息
 */
async function getDatabaseStats() {
  try {
    // 获取连接池状态
    const poolStatus = {
      total: sequelize.connectionManager.pool?.size || 0,
      used: sequelize.connectionManager.pool?.used || 0,
      waiting: sequelize.connectionManager.pool?.pending || 0
    };
    
    // 获取数据库大小
    const [sizeResult] = await sequelize.query(
      "SELECT pg_size_pretty(pg_database_size(current_database())) as size"
    );
    
    // 获取表统计
    const [tableStats] = await sequelize.query(`
      SELECT 
        schemaname,
        relname as tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples
      FROM pg_stat_user_tables
      ORDER BY n_live_tup DESC
      LIMIT 10
    `);
    
    return {
      connectionPool: poolStatus,
      size: sizeResult[0]?.size || 'Unknown',
      tableStats: tableStats || []
    };
    
  } catch (error) {
    logger.error('Database stats error', { error: error.message });
    return {
      connectionPool: { total: 0, used: 0, waiting: 0 },
      size: 'Unknown',
      tableStats: []
    };
  }
}

/**
 * 获取系统指标
 */
async function getSystemMetrics() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024) // MB
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    uptime: Math.round(process.uptime()),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch
  };
}

/**
 * 获取错误统计
 */
async function getErrorStats() {
  try {
    const logDir = path.join(__dirname, '../logs');
    const errorLogPath = path.join(logDir, 'error.log');

    // 读取错误日志文件
    const errorLogContent = await fs.readFile(errorLogPath, 'utf8');
    const errorLines = errorLogContent.split('\n').filter(line => line.trim());

    // 统计最近24小时的错误
    const last24Hours = Date.now() - (24 * 60 * 60 * 1000);
    const recentErrors = errorLines.filter(line => {
      try {
        const logEntry = JSON.parse(line);
        return new Date(logEntry.timestamp).getTime() > last24Hours;
      } catch {
        return false;
      }
    });

    return {
      total: errorLines.length,
      last24Hours: recentErrors.length,
      errorRate: recentErrors.length / 24 // 每小时平均错误数
    };

  } catch (error) {
    return {
      total: 0,
      last24Hours: 0,
      errorRate: 0
    };
  }
}

/**
 * 获取性能指标
 */
async function getPerformanceMetrics(timeRange) {
  try {
    const logDir = path.join(__dirname, '../logs');
    const perfLogPath = path.join(logDir, 'performance.log');

    // 计算时间范围
    let timeRangeMs;
    switch (timeRange) {
      case '1h': timeRangeMs = 60 * 60 * 1000; break;
      case '6h': timeRangeMs = 6 * 60 * 60 * 1000; break;
      case '24h': timeRangeMs = 24 * 60 * 60 * 1000; break;
      case '7d': timeRangeMs = 7 * 24 * 60 * 60 * 1000; break;
      default: timeRangeMs = 60 * 60 * 1000;
    }

    const startTime = Date.now() - timeRangeMs;

    // 读取性能日志
    const perfLogContent = await fs.readFile(perfLogPath, 'utf8');
    const perfLines = perfLogContent.split('\n').filter(line => line.trim());

    // 过滤时间范围内的日志
    const recentMetrics = perfLines
      .map(line => {
        try {
          return JSON.parse(line);
        } catch {
          return null;
        }
      })
      .filter(entry => entry && new Date(entry.timestamp).getTime() > startTime);

    // 分析性能数据
    const requestDurations = recentMetrics
      .filter(entry => entry.metric === 'request_duration')
      .map(entry => entry.value);

    const memoryChanges = recentMetrics
      .filter(entry => entry.metric === 'memory_change')
      .map(entry => entry.value);

    return {
      requestPerformance: {
        count: requestDurations.length,
        avgDuration: requestDurations.length > 0 ?
          Math.round(requestDurations.reduce((a, b) => a + b, 0) / requestDurations.length) : 0,
        maxDuration: requestDurations.length > 0 ? Math.max(...requestDurations) : 0,
        minDuration: requestDurations.length > 0 ? Math.min(...requestDurations) : 0
      },
      memoryUsage: {
        changes: memoryChanges.length,
        avgChange: memoryChanges.length > 0 ?
          Math.round(memoryChanges.reduce((a, b) => a + b, 0) / memoryChanges.length) : 0,
        maxChange: memoryChanges.length > 0 ? Math.max(...memoryChanges) : 0
      }
    };

  } catch (error) {
    logger.error('Performance metrics error', { error: error.message });
    return {
      requestPerformance: { count: 0, avgDuration: 0, maxDuration: 0, minDuration: 0 },
      memoryUsage: { changes: 0, avgChange: 0, maxChange: 0 }
    };
  }
}

/**
 * 获取日志
 */
async function getLogs({ level, limit, offset, startDate, endDate }) {
  try {
    const logDir = path.join(__dirname, '../logs');
    let logFile;

    // 根据级别选择日志文件
    switch (level) {
      case 'error': logFile = 'error.log'; break;
      case 'warn': logFile = 'warn.log'; break;
      case 'http': logFile = 'access.log'; break;
      default: logFile = 'combined.log';
    }

    const logPath = path.join(logDir, logFile);
    const logContent = await fs.readFile(logPath, 'utf8');
    const logLines = logContent.split('\n').filter(line => line.trim());

    // 解析日志条目
    let logs = logLines
      .map(line => {
        try {
          return JSON.parse(line);
        } catch {
          return null;
        }
      })
      .filter(entry => entry);

    // 时间过滤
    if (startDate) {
      const start = new Date(startDate).getTime();
      logs = logs.filter(entry => new Date(entry.timestamp).getTime() >= start);
    }

    if (endDate) {
      const end = new Date(endDate).getTime();
      logs = logs.filter(entry => new Date(entry.timestamp).getTime() <= end);
    }

    // 排序（最新的在前）
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // 分页
    const paginatedLogs = logs.slice(offset, offset + limit);

    return {
      logs: paginatedLogs,
      total: logs.length
    };

  } catch (error) {
    logger.error('Log retrieval error', { error: error.message });
    return {
      logs: [],
      total: 0
    };
  }
}

/**
 * 获取系统警报
 */
async function getSystemAlerts() {
  const alerts = [];

  try {
    // 检查内存使用
    const memUsage = process.memoryUsage();
    const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    if (memoryUsagePercent > 90) {
      alerts.push({
        type: 'warning',
        category: 'memory',
        message: `High memory usage: ${Math.round(memoryUsagePercent)}%`,
        timestamp: new Date().toISOString()
      });
    }

    // 检查数据库连接
    try {
      const startTime = Date.now();
      await sequelize.authenticate();
      const dbResponseTime = Date.now() - startTime;

      if (dbResponseTime > 2000) {
        alerts.push({
          type: 'warning',
          category: 'database',
          message: `Slow database response: ${dbResponseTime}ms`,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      alerts.push({
        type: 'error',
        category: 'database',
        message: `Database connection failed: ${error.message}`,
        timestamp: new Date().toISOString()
      });
    }

    // 检查磁盘空间（如果可用）
    // 这里可以添加磁盘空间检查逻辑

    // 检查错误率
    const errorStats = await getErrorStats();
    if (errorStats.errorRate > 10) { // 每小时超过10个错误
      alerts.push({
        type: 'warning',
        category: 'errors',
        message: `High error rate: ${errorStats.errorRate} errors/hour`,
        timestamp: new Date().toISOString()
      });
    }

    return alerts;

  } catch (error) {
    logger.error('System alerts error', { error: error.message });
    return [{
      type: 'error',
      category: 'system',
      message: `Failed to check system alerts: ${error.message}`,
      timestamp: new Date().toISOString()
    }];
  }
}

/**
 * 清理旧日志
 */
async function cleanupOldLogs(olderThanDays) {
  const logDir = path.join(__dirname, '../logs');
  const maxAge = olderThanDays * 24 * 60 * 60 * 1000; // 转换为毫秒
  const now = Date.now();

  let deletedFiles = 0;
  let totalSize = 0;

  try {
    const files = await fs.readdir(logDir);

    for (const file of files) {
      const filePath = path.join(logDir, file);
      const stats = await fs.stat(filePath);

      if (now - stats.mtime.getTime() > maxAge) {
        totalSize += stats.size;
        await fs.unlink(filePath);
        deletedFiles++;
        logger.info('Deleted old log file', { file, size: stats.size });
      }
    }

    return {
      deletedFiles,
      totalSize: Math.round(totalSize / 1024 / 1024), // MB
      message: `Deleted ${deletedFiles} files, freed ${Math.round(totalSize / 1024 / 1024)}MB`
    };

  } catch (error) {
    logger.error('Log cleanup error', { error: error.message });
    throw error;
  }
}

module.exports = router;
