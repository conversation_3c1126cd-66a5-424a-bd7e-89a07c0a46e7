const express = require('express');
const router = express.Router();
const { Permission, Role, RolePermission } = require('../models');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');

// Get all permissions with filtering
router.get('/', authenticateToken, requireRole(['admin', 'super_admin']), [
  query('resource').optional().isString(),
  query('action').optional().isString(),
  query('scope').optional().isIn(['global', 'own', 'department', 'custom']),
  query('search').optional().isLength({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { resource, action, scope, search } = req.query;
    
    const whereClause = { isActive: true };
    
    if (resource) whereClause.resource = resource;
    if (action) whereClause.action = action;
    if (scope) whereClause.scope = scope;
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const permissions = await Permission.findAll({
      where: whereClause,
      order: [['resource', 'ASC'], ['action', 'ASC']],
      include: [{
        model: Role,
        as: 'roles',
        through: { 
          attributes: ['grantedAt', 'isActive'],
          where: { isActive: true }
        },
        required: false
      }]
    });

    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error('Error fetching permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions',
      error: error.message
    });
  }
});

// Get permission by ID
router.get('/:id', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const permission = await Permission.findByPk(req.params.id, {
      include: [{
        model: Role,
        as: 'roles',
        through: { 
          attributes: ['grantedAt', 'isActive'],
          where: { isActive: true }
        },
        required: false
      }]
    });

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    res.json({
      success: true,
      data: permission
    });
  } catch (error) {
    console.error('Error fetching permission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permission',
      error: error.message
    });
  }
});

// Create new permission
router.post('/', authenticateToken, requireRole(['super_admin']), [
  body('name').isString().isLength({ min: 1, max: 100 }),
  body('description').optional().isString().isLength({ max: 255 }),
  body('resource').isString().isLength({ min: 1, max: 50 }),
  body('action').isString().isLength({ min: 1, max: 50 }),
  body('scope').optional().isIn(['global', 'own', 'department', 'custom'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, description, resource, action, scope = 'global', metadata = {} } = req.body;

    // Check if permission already exists
    const existingPermission = await Permission.findOne({ where: { name } });
    if (existingPermission) {
      return res.status(409).json({
        success: false,
        message: 'Permission with this name already exists'
      });
    }

    const permission = await Permission.create({
      name,
      description,
      resource,
      action,
      scope,
      metadata
    });

    res.status(201).json({
      success: true,
      data: permission,
      message: 'Permission created successfully'
    });
  } catch (error) {
    console.error('Error creating permission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create permission',
      error: error.message
    });
  }
});

// Update permission
router.put('/:id', authenticateToken, requireRole(['super_admin']), [
  body('name').optional().isString().isLength({ min: 1, max: 100 }),
  body('description').optional().isString().isLength({ max: 255 }),
  body('resource').optional().isString().isLength({ min: 1, max: 50 }),
  body('action').optional().isString().isLength({ min: 1, max: 50 }),
  body('scope').optional().isIn(['global', 'own', 'department', 'custom']),
  body('isActive').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const permission = await Permission.findByPk(req.params.id);
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    const { name, description, resource, action, scope, isActive, metadata } = req.body;

    // Check if new name conflicts with existing permission
    if (name && name !== permission.name) {
      const existingPermission = await Permission.findOne({ where: { name } });
      if (existingPermission) {
        return res.status(409).json({
          success: false,
          message: 'Permission with this name already exists'
        });
      }
    }

    await permission.update({
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(resource && { resource }),
      ...(action && { action }),
      ...(scope && { scope }),
      ...(isActive !== undefined && { isActive }),
      ...(metadata && { metadata })
    });

    res.json({
      success: true,
      data: permission,
      message: 'Permission updated successfully'
    });
  } catch (error) {
    console.error('Error updating permission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update permission',
      error: error.message
    });
  }
});

// Delete permission (soft delete)
router.delete('/:id', authenticateToken, requireRole(['super_admin']), async (req, res) => {
  try {
    const permission = await Permission.findByPk(req.params.id);
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    await permission.update({ isActive: false });

    res.json({
      success: true,
      message: 'Permission deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting permission:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete permission',
      error: error.message
    });
  }
});

// Get permissions by resource
router.get('/resource/:resource', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const permissions = await Permission.getByResource(req.params.resource);
    
    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error('Error fetching permissions by resource:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions',
      error: error.message
    });
  }
});

// Get permissions by action
router.get('/action/:action', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const permissions = await Permission.getByAction(req.params.action);
    
    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error('Error fetching permissions by action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions',
      error: error.message
    });
  }
});

// Initialize default permissions
router.post('/initialize', authenticateToken, requireRole(['super_admin']), async (req, res) => {
  try {
    await Permission.createDefaultPermissions();
    
    res.json({
      success: true,
      message: 'Default permissions initialized successfully'
    });
  } catch (error) {
    console.error('Error initializing permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize permissions',
      error: error.message
    });
  }
});

module.exports = router;
