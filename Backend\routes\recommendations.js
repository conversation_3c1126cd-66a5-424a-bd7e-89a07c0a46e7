/**
 * Newzora 推荐系统 API 路由
 * 提供智能内容推荐服务
 */

const express = require('express');
const router = express.Router();
const recommendationEngine = require('../services/RecommendationEngine');
const { authenticateToken } = require('../middleware/auth');
const { logger } = require('../config/logger');

// 获取个性化推荐
router.get('/personalized', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      algorithm = 'hybrid',
      contentType = 'article',
      limit = 20,
      excludeViewed = true,
      includeReasons = true,
      context = {}
    } = req.query;

    // 解析上下文信息
    const contextData = {
      timeOfDay: req.headers['x-time-of-day'] || undefined,
      deviceType: req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'desktop',
      location: req.headers['x-user-location'] || null,
      ...context
    };

    const recommendations = await recommendationEngine.getRecommendations(userId, {
      algorithm,
      contentType,
      limit: parseInt(limit),
      excludeViewed: excludeViewed === 'true',
      includeReasons: includeReasons === 'true',
      context: contextData
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm,
        userId,
        timestamp: new Date().toISOString(),
        context: contextData
      }
    });

  } catch (error) {
    logger.error('Error getting personalized recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations',
      error: error.message
    });
  }
});

// 获取实时推荐
router.get('/realtime', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { contentType = 'article', limit = 10 } = req.query;

    const recommendations = await recommendationEngine.realTimeRecommendation(userId, {
      contentType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'real_time',
        userId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting real-time recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get real-time recommendations',
      error: error.message
    });
  }
});

// 获取上下文感知推荐
router.get('/contextual', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      contentType = 'article',
      limit = 20,
      timeOfDay,
      deviceType,
      location,
      weather
    } = req.query;

    const context = {
      timeOfDay: timeOfDay || req.headers['x-time-of-day'],
      deviceType: deviceType || (req.headers['user-agent']?.includes('Mobile') ? 'mobile' : 'desktop'),
      location: location || req.headers['x-user-location'],
      weather: weather || req.headers['x-weather'],
      dayOfWeek: new Date().getDay()
    };

    const recommendations = await recommendationEngine.contextualRecommendation(userId, {
      contentType,
      limit: parseInt(limit),
      context
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'contextual',
        userId,
        context,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting contextual recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get contextual recommendations',
      error: error.message
    });
  }
});

// 获取深度学习推荐
router.get('/deep-learning', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { contentType = 'article', limit = 20 } = req.query;

    const recommendations = await recommendationEngine.deepLearningRecommendation(userId, {
      contentType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'deep_learning',
        userId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting deep learning recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get deep learning recommendations',
      error: error.message
    });
  }
});

// 多臂老虎机推荐
router.get('/multi-armed-bandit', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { contentType = 'article', limit = 20 } = req.query;

    const recommendations = await recommendationEngine.multiArmedBanditRecommendation(userId, {
      contentType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'multi_armed_bandit',
        userId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting multi-armed bandit recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get multi-armed bandit recommendations',
      error: error.message
    });
  }
});

// 记录推荐反馈
router.post('/feedback', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { contentId, action, algorithm, metadata = {} } = req.body;

    if (!contentId || !action) {
      return res.status(400).json({
        success: false,
        message: 'contentId and action are required'
      });
    }

    await recommendationEngine.recordRecommendationFeedback(
      userId,
      contentId,
      action,
      algorithm || 'unknown'
    );

    res.json({
      success: true,
      message: 'Feedback recorded successfully',
      data: {
        userId,
        contentId,
        action,
        algorithm,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error recording recommendation feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record feedback',
      error: error.message
    });
  }
});

// 获取推荐统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { days = 30 } = req.query;

    const stats = await recommendationEngine.getRecommendationStats(userId, parseInt(days));

    res.json({
      success: true,
      data: {
        stats,
        userId,
        period: `${days} days`,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting recommendation stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendation stats',
      error: error.message
    });
  }
});

// 获取热门推荐（无需认证）
router.get('/popular', async (req, res) => {
  try {
    const { contentType = 'article', limit = 20 } = req.query;

    const recommendations = await recommendationEngine.popularityBased(null, {
      contentType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'popularity',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting popular recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular recommendations',
      error: error.message
    });
  }
});

// 获取趋势推荐（无需认证）
router.get('/trending', async (req, res) => {
  try {
    const { contentType = 'article', limit = 20 } = req.query;

    const recommendations = await recommendationEngine.trendingBased(null, {
      contentType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        recommendations,
        algorithm: 'trending',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error getting trending recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending recommendations',
      error: error.message
    });
  }
});

// 更新推荐权重（管理员功能）
router.put('/weights', authenticateToken, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const { weights } = req.body;

    if (!weights || typeof weights !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Valid weights object is required'
      });
    }

    recommendationEngine.updateWeights(weights);

    res.json({
      success: true,
      message: 'Recommendation weights updated successfully',
      data: {
        newWeights: weights,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error updating recommendation weights:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update weights',
      error: error.message
    });
  }
});

module.exports = router;
