const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../middleware/auth');
const Report = require('../models/Report');

// 获取报表列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      type, 
      category,
      isPublic,
      page = 1, 
      limit = 20 
    } = req.query;

    let reports;
    
    if (isPublic === 'true') {
      reports = await Report.getPublicReports(type, parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
    } else {
      reports = await Report.getUserReports(req.user.id, type, parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
    }

    res.json({
      success: true,
      data: {
        reports: reports.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: reports.count,
          pages: Math.ceil(reports.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reports',
      error: error.message
    });
  }
});

// 获取单个报表
router.get('/:reportId', authenticateToken, async (req, res) => {
  try {
    const { reportId } = req.params;
    
    const report = await Report.findByPk(reportId, {
      include: [
        {
          model: require('../models/User'),
          as: 'creator',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ]
    });
    
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // 检查访问权限
    const hasAccess = report.isPublic || 
                     report.createdBy === req.user.id ||
                     report.recipients.includes(req.user.id) ||
                     ['admin', 'moderator'].includes(req.user.role);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    res.json({
      success: true,
      data: { report }
    });
  } catch (error) {
    console.error('Error fetching report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch report',
      error: error.message
    });
  }
});

// 创建报表
router.post('/', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const reportData = {
      ...req.body,
      createdBy: req.user.id
    };
    
    if (!reportData.name || !reportData.type) {
      return res.status(400).json({
        success: false,
        message: 'Report name and type are required'
      });
    }
    
    const report = await Report.createReport(reportData);
    
    res.status(201).json({
      success: true,
      message: 'Report created successfully',
      data: { report }
    });
  } catch (error) {
    console.error('Error creating report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create report',
      error: error.message
    });
  }
});

// 更新报表
router.put('/:reportId', authenticateToken, async (req, res) => {
  try {
    const { reportId } = req.params;
    
    const report = await Report.findByPk(reportId);
    
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // 检查权限：只有创建者或管理员可以修改
    if (report.createdBy !== req.user.id && !['admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    await report.update(req.body);
    
    res.json({
      success: true,
      message: 'Report updated successfully',
      data: { report }
    });
  } catch (error) {
    console.error('Error updating report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update report',
      error: error.message
    });
  }
});

// 删除报表
router.delete('/:reportId', authenticateToken, async (req, res) => {
  try {
    const { reportId } = req.params;
    
    const report = await Report.findByPk(reportId);
    
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // 检查权限：只有创建者或管理员可以删除
    if (report.createdBy !== req.user.id && !['admin'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    await report.destroy();
    
    res.json({
      success: true,
      message: 'Report deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete report',
      error: error.message
    });
  }
});

// 生成报表
router.post('/:reportId/generate', authenticateToken, async (req, res) => {
  try {
    const { reportId } = req.params;
    const params = req.body;
    
    const report = await Report.findByPk(reportId);
    
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // 检查访问权限
    const hasAccess = report.isPublic || 
                     report.createdBy === req.user.id ||
                     report.recipients.includes(req.user.id) ||
                     ['admin', 'moderator'].includes(req.user.role);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const result = await report.generateReport(params);
    
    res.json({
      success: true,
      message: 'Report generated successfully',
      data: result
    });
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate report',
      error: error.message
    });
  }
});

// 批量生成报表
router.post('/bulk-generate', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const { reportIds, params = {} } = req.body;
    
    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Report IDs array is required'
      });
    }
    
    const results = await Report.generateBulkReports(reportIds, params);
    
    res.json({
      success: true,
      message: `Generated ${results.length} reports`,
      data: { results }
    });
  } catch (error) {
    console.error('Error bulk generating reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk generate reports',
      error: error.message
    });
  }
});

// 获取定时报表
router.get('/scheduled/list', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const scheduledReports = await Report.getScheduledReports();
    
    res.json({
      success: true,
      data: { reports: scheduledReports }
    });
  } catch (error) {
    console.error('Error fetching scheduled reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch scheduled reports',
      error: error.message
    });
  }
});

// 搜索报表
router.get('/search/:query', authenticateToken, async (req, res) => {
  try {
    const { query } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    const results = await Report.searchReports(
      query,
      req.user.id,
      parseInt(limit),
      (parseInt(page) - 1) * parseInt(limit)
    );
    
    res.json({
      success: true,
      data: {
        reports: results.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: results.count,
          pages: Math.ceil(results.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error searching reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search reports',
      error: error.message
    });
  }
});

// 获取报表统计
router.get('/stats/overview', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const stats = await Report.getReportStats(parseInt(days));
    
    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    console.error('Error fetching report stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch report statistics',
      error: error.message
    });
  }
});

module.exports = router;
