const express = require('express');
const router = express.Router();
const { ReviewRule, User } = require('../models/associations');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');

// 获取审核规则列表
router.get('/', authenticateToken, requireRole(['admin']), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('contentType').optional().isIn(['article', 'draft', 'comment', 'media_file', 'user_profile', 'all']),
  query('ruleType').optional().isIn(['keyword', 'pattern', 'ai_model', 'user_behavior', 'content_length', 'custom']),
  query('isActive').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      contentType,
      ruleType,
      isActive
    } = req.query;

    const whereClause = {};
    if (contentType) whereClause.contentType = contentType;
    if (ruleType) whereClause.ruleType = ruleType;
    if (isActive !== undefined) whereClause.isActive = isActive === 'true';

    const { count, rows: rules } = await ReviewRule.findAndCountAll({
      where: whereClause,
      order: [['priority', 'ASC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username'],
          required: false
        }
      ]
    });

    res.json({
      success: true,
      data: {
        rules,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching review rules:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 获取单个规则详情
router.get('/:id', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;

    const rule = await ReviewRule.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username'],
          required: false
        }
      ]
    });

    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Review rule not found'
      });
    }

    res.json({
      success: true,
      data: { rule }
    });
  } catch (error) {
    console.error('Error fetching review rule:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 创建审核规则
router.post('/', authenticateToken, requireRole(['admin']), [
  body('name').notEmpty().isLength({ min: 1, max: 255 }),
  body('description').optional().isLength({ max: 1000 }),
  body('contentType').isIn(['article', 'draft', 'comment', 'media_file', 'user_profile', 'all']),
  body('ruleType').isIn(['keyword', 'pattern', 'ai_model', 'user_behavior', 'content_length', 'custom']),
  body('conditions').isObject(),
  body('action').isIn(['auto_approve', 'auto_reject', 'flag_for_review', 'escalate', 'quarantine']),
  body('severity').isIn(['info', 'warning', 'error', 'critical']),
  body('priority').optional().isInt({ min: 1, max: 1000 }),
  body('tags').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      name,
      description,
      contentType,
      ruleType,
      conditions,
      action,
      severity,
      priority = 100,
      tags = []
    } = req.body;

    // 验证条件配置
    if (!validateRuleConditions(ruleType, conditions)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid rule conditions for the specified rule type'
      });
    }

    const rule = await ReviewRule.create({
      name,
      description,
      contentType,
      ruleType,
      conditions,
      action,
      severity,
      priority,
      tags,
      createdBy: req.user.id,
      isActive: true
    });

    res.status(201).json({
      success: true,
      data: { rule },
      message: 'Review rule created successfully'
    });
  } catch (error) {
    console.error('Error creating review rule:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 更新审核规则
router.put('/:id', authenticateToken, requireRole(['admin']), [
  body('name').optional().isLength({ min: 1, max: 255 }),
  body('description').optional().isLength({ max: 1000 }),
  body('contentType').optional().isIn(['article', 'draft', 'comment', 'media_file', 'user_profile', 'all']),
  body('ruleType').optional().isIn(['keyword', 'pattern', 'ai_model', 'user_behavior', 'content_length', 'custom']),
  body('conditions').optional().isObject(),
  body('action').optional().isIn(['auto_approve', 'auto_reject', 'flag_for_review', 'escalate', 'quarantine']),
  body('severity').optional().isIn(['info', 'warning', 'error', 'critical']),
  body('priority').optional().isInt({ min: 1, max: 1000 }),
  body('isActive').optional().isBoolean(),
  body('tags').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const rule = await ReviewRule.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Review rule not found'
      });
    }

    // 验证条件配置（如果更新了规则类型或条件）
    if (updateData.ruleType || updateData.conditions) {
      const ruleType = updateData.ruleType || rule.ruleType;
      const conditions = updateData.conditions || rule.conditions;
      
      if (!validateRuleConditions(ruleType, conditions)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid rule conditions for the specified rule type'
        });
      }
    }

    // 更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        rule[key] = updateData[key];
      }
    });

    rule.updatedBy = req.user.id;
    await rule.save();

    res.json({
      success: true,
      data: { rule },
      message: 'Review rule updated successfully'
    });
  } catch (error) {
    console.error('Error updating review rule:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 删除审核规则
router.delete('/:id', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;

    const rule = await ReviewRule.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Review rule not found'
      });
    }

    await rule.destroy();

    res.json({
      success: true,
      message: 'Review rule deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting review rule:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 测试规则
router.post('/:id/test', authenticateToken, requireRole(['admin']), [
  body('testContent').notEmpty().isLength({ min: 1, max: 10000 }),
  body('metadata').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { testContent, metadata = {} } = req.body;

    const rule = await ReviewRule.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Review rule not found'
      });
    }

    const isTriggered = rule.checkCondition(testContent, metadata);

    res.json({
      success: true,
      data: {
        isTriggered,
        rule: {
          id: rule.id,
          name: rule.name,
          action: rule.action,
          severity: rule.severity
        }
      },
      message: `Rule ${isTriggered ? 'triggered' : 'not triggered'}`
    });
  } catch (error) {
    console.error('Error testing rule:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// 验证规则条件的辅助函数
function validateRuleConditions(ruleType, conditions) {
  switch (ruleType) {
    case 'keyword':
      return conditions.keywords && Array.isArray(conditions.keywords) && conditions.keywords.length > 0;
    case 'pattern':
      return conditions.patterns && Array.isArray(conditions.patterns) && conditions.patterns.length > 0;
    case 'content_length':
      return (conditions.minLength !== undefined || conditions.maxLength !== undefined) &&
             (conditions.minLength === undefined || typeof conditions.minLength === 'number') &&
             (conditions.maxLength === undefined || typeof conditions.maxLength === 'number');
    case 'user_behavior':
      return typeof conditions === 'object' && Object.keys(conditions).length > 0;
    default:
      return true; // 对于自定义规则或AI模型，允许任何条件
  }
}

module.exports = router;
