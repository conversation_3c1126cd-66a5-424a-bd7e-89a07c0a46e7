const express = require('express');
const router = express.Router();
const { Role, Permission, RolePermission, User, UserRole } = require('../models');
const { authenticateToken, requireRole, requirePermission } = require('../middleware/auth');
const { body, validationResult, query } = require('express-validator');
const { Op } = require('sequelize');

// Get all roles with filtering
router.get('/', authenticateToken, requireRole(['admin', 'super_admin']), [
  query('level').optional().isInt({ min: 0 }),
  query('search').optional().isLength({ min: 1, max: 100 }),
  query('includePermissions').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { level, search, includePermissions = false } = req.query;
    
    const whereClause = { isActive: true };
    
    if (level) whereClause.level = { [Op.gte]: parseInt(level) };
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { displayName: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const includeOptions = [];
    
    if (includePermissions === 'true') {
      includeOptions.push({
        model: Permission,
        as: 'permissions',
        through: { 
          attributes: ['grantedAt', 'isActive'],
          where: { isActive: true }
        },
        required: false
      });
    }

    const roles = await Role.findAll({
      where: whereClause,
      order: [['level', 'DESC'], ['name', 'ASC']],
      include: includeOptions
    });

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles',
      error: error.message
    });
  }
});

// Get role by ID
router.get('/:id', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const role = await Role.findByPk(req.params.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { 
            attributes: ['grantedAt', 'isActive'],
            where: { isActive: true }
          },
          required: false
        },
        {
          model: User,
          as: 'users',
          through: { 
            attributes: ['assignedAt', 'isActive', 'isPrimary'],
            where: { isActive: true }
          },
          attributes: ['id', 'username', 'email', 'avatar'],
          required: false
        }
      ]
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('Error fetching role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role',
      error: error.message
    });
  }
});

// Create new role
router.post('/', authenticateToken, requireRole(['super_admin']), [
  body('name').isString().isLength({ min: 1, max: 50 }).matches(/^[a-z_]+$/),
  body('displayName').isString().isLength({ min: 1, max: 100 }),
  body('description').optional().isString().isLength({ max: 500 }),
  body('level').isInt({ min: 0, max: 999 }),
  body('color').optional().isString().matches(/^#[0-9A-Fa-f]{6}$/),
  body('permissionIds').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { 
      name, 
      displayName, 
      description, 
      level, 
      color = '#6366f1', 
      metadata = {},
      permissionIds = []
    } = req.body;

    // Check if role already exists
    const existingRole = await Role.findOne({ where: { name } });
    if (existingRole) {
      return res.status(409).json({
        success: false,
        message: 'Role with this name already exists'
      });
    }

    // Check if user has permission to create role at this level
    const userRoles = await req.user.getRoles();
    const maxUserLevel = Math.max(...userRoles.map(r => r.level));
    
    if (level >= maxUserLevel) {
      return res.status(403).json({
        success: false,
        message: 'Cannot create role with level equal to or higher than your own'
      });
    }

    const role = await Role.create({
      name,
      displayName,
      description,
      level,
      color,
      metadata
    });

    // Assign permissions if provided
    if (permissionIds.length > 0) {
      await RolePermission.bulkGrantPermissions(role.id, permissionIds, req.user.id);
    }

    // Fetch the created role with permissions
    const createdRole = await Role.findByPk(role.id, {
      include: [{
        model: Permission,
        as: 'permissions',
        through: { 
          attributes: ['grantedAt', 'isActive'],
          where: { isActive: true }
        },
        required: false
      }]
    });

    res.status(201).json({
      success: true,
      data: createdRole,
      message: 'Role created successfully'
    });
  } catch (error) {
    console.error('Error creating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role',
      error: error.message
    });
  }
});

// Update role
router.put('/:id', authenticateToken, requireRole(['super_admin']), [
  body('name').optional().isString().isLength({ min: 1, max: 50 }).matches(/^[a-z_]+$/),
  body('displayName').optional().isString().isLength({ min: 1, max: 100 }),
  body('description').optional().isString().isLength({ max: 500 }),
  body('level').optional().isInt({ min: 0, max: 999 }),
  body('color').optional().isString().matches(/^#[0-9A-Fa-f]{6}$/),
  body('isActive').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const role = await Role.findByPk(req.params.id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if role is system role
    if (role.isSystem && req.body.name && req.body.name !== role.name) {
      return res.status(403).json({
        success: false,
        message: 'Cannot change name of system role'
      });
    }

    const { name, displayName, description, level, color, isActive, metadata } = req.body;

    // Check if new name conflicts with existing role
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({ where: { name } });
      if (existingRole) {
        return res.status(409).json({
          success: false,
          message: 'Role with this name already exists'
        });
      }
    }

    // Check level permissions
    if (level !== undefined) {
      const userRoles = await req.user.getRoles();
      const maxUserLevel = Math.max(...userRoles.map(r => r.level));
      
      if (level >= maxUserLevel || role.level >= maxUserLevel) {
        return res.status(403).json({
          success: false,
          message: 'Cannot modify role with level equal to or higher than your own'
        });
      }
    }

    await role.update({
      ...(name && { name }),
      ...(displayName && { displayName }),
      ...(description !== undefined && { description }),
      ...(level !== undefined && { level }),
      ...(color && { color }),
      ...(isActive !== undefined && { isActive }),
      ...(metadata && { metadata })
    });

    res.json({
      success: true,
      data: role,
      message: 'Role updated successfully'
    });
  } catch (error) {
    console.error('Error updating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role',
      error: error.message
    });
  }
});

// Delete role (soft delete)
router.delete('/:id', authenticateToken, requireRole(['super_admin']), async (req, res) => {
  try {
    const role = await Role.findByPk(req.params.id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    if (role.isSystem) {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete system role'
      });
    }

    // Check if role has active users
    const activeUsers = await UserRole.count({
      where: {
        roleId: role.id,
        isActive: true
      }
    });

    if (activeUsers > 0) {
      return res.status(409).json({
        success: false,
        message: `Cannot delete role with ${activeUsers} active users. Please reassign users first.`
      });
    }

    await role.update({ isActive: false });

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role',
      error: error.message
    });
  }
});

// Get role permissions
router.get('/:id/permissions', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const role = await Role.findByPk(req.params.id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    const permissions = await role.getPermissions();

    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role permissions',
      error: error.message
    });
  }
});

// Update role permissions
router.put('/:id/permissions', authenticateToken, requireRole(['super_admin']), [
  body('permissionIds').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const role = await Role.findByPk(req.params.id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    const { permissionIds } = req.body;

    // Sync role permissions
    await RolePermission.syncRolePermissions(role.id, permissionIds, req.user.id);

    // Fetch updated permissions
    const updatedPermissions = await role.getPermissions();

    res.json({
      success: true,
      data: updatedPermissions,
      message: 'Role permissions updated successfully'
    });
  } catch (error) {
    console.error('Error updating role permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role permissions',
      error: error.message
    });
  }
});

// Initialize default roles
router.post('/initialize', authenticateToken, requireRole(['super_admin']), async (req, res) => {
  try {
    await Role.createDefaultRoles();

    res.json({
      success: true,
      message: 'Default roles initialized successfully'
    });
  } catch (error) {
    console.error('Error initializing roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize roles',
      error: error.message
    });
  }
});

module.exports = router;
