const express = require('express');
const router = express.Router();
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const searchService = require('../services/SearchService');
const recommendationEngine = require('../services/RecommendationEngine');
const SearchHistory = require('../models/SearchHistory');
const SearchIndex = require('../models/SearchIndex');

// 主要搜索接口
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      q: query,
      type: searchType = 'fulltext',
      contentType,
      category,
      authorId,
      tags,
      sortBy = 'relevance',
      sortOrder = 'DESC',
      page = 1,
      limit = 20,
      language = 'zh',
      highlights = 'true',
      facets = 'false'
    } = req.query;

    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const filters = {};
    if (category) filters.category = category;
    if (authorId) filters.authorId = parseInt(authorId);
    if (tags) filters.tags = tags.split(',').map(tag => tag.trim());

    const options = {
      userId: req.user?.id,
      sessionId: req.sessionID,
      searchType,
      contentType,
      filters,
      sortBy,
      sortOrder,
      page: parseInt(page),
      limit: parseInt(limit),
      language,
      includeHighlights: highlights === 'true',
      includeFacets: facets === 'true'
    };

    const results = await searchService.search(query, options);

    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed',
      error: error.message
    });
  }
});

// 搜索建议
router.get('/suggestions', optionalAuth, async (req, res) => {
  try {
    const { q: query, limit = 10 } = req.query;

    if (!query || query.length < 2) {
      return res.json({
        success: true,
        data: { suggestions: [] }
      });
    }

    const suggestions = await SearchHistory.getSearchSuggestions(
      query,
      req.user?.id,
      parseInt(limit)
    );

    res.json({
      success: true,
      data: { suggestions }
    });
  } catch (error) {
    console.error('Error fetching search suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch search suggestions',
      error: error.message
    });
  }
});

// 热门搜索
router.get('/popular', async (req, res) => {
  try {
    const { 
      timeframe = '7d', 
      searchType,
      limit = 20 
    } = req.query;

    const popularQueries = await SearchHistory.getPopularQueries({
      timeframe,
      searchType,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: { queries: popularQueries }
    });
  } catch (error) {
    console.error('Error fetching popular searches:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch popular searches',
      error: error.message
    });
  }
});

// 搜索历史
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 50, 
      searchType 
    } = req.query;

    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    };

    if (searchType) options.searchType = searchType;

    const result = await SearchHistory.getUserSearchHistory(req.user.id, options);

    res.json({
      success: true,
      data: {
        history: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.count,
          pages: Math.ceil(result.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching search history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch search history',
      error: error.message
    });
  }
});

// 清除搜索历史
router.delete('/history', authenticateToken, async (req, res) => {
  try {
    await SearchHistory.destroy({
      where: { userId: req.user.id }
    });

    res.json({
      success: true,
      message: 'Search history cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing search history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear search history',
      error: error.message
    });
  }
});

// 相关搜索
router.get('/related', async (req, res) => {
  try {
    const { q: query, limit = 10 } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Query is required'
      });
    }

    const relatedQueries = await SearchHistory.getRelatedQueries(
      query,
      parseInt(limit)
    );

    res.json({
      success: true,
      data: { queries: relatedQueries }
    });
  } catch (error) {
    console.error('Error fetching related searches:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch related searches',
      error: error.message
    });
  }
});

// 推荐内容
router.get('/recommendations', authenticateToken, async (req, res) => {
  try {
    const {
      algorithm = 'hybrid',
      contentType = 'article',
      limit = 20,
      excludeViewed = 'true',
      includeReasons = 'false'
    } = req.query;

    const options = {
      algorithm,
      contentType,
      limit: parseInt(limit),
      excludeViewed: excludeViewed === 'true',
      includeReasons: includeReasons === 'true'
    };

    const recommendations = await recommendationEngine.getRecommendations(
      req.user.id,
      options
    );

    res.json({
      success: true,
      data: { recommendations }
    });
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recommendations',
      error: error.message
    });
  }
});

// 热门内容
router.get('/popular-content', async (req, res) => {
  try {
    const {
      contentType = 'article',
      timeframe = '7d',
      limit = 20
    } = req.query;

    const popularContent = await SearchIndex.getPopularContent(
      contentType,
      timeframe,
      parseInt(limit)
    );

    res.json({
      success: true,
      data: { content: popularContent }
    });
  } catch (error) {
    console.error('Error fetching popular content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch popular content',
      error: error.message
    });
  }
});

// 趋势内容
router.get('/trending', async (req, res) => {
  try {
    const {
      contentType = 'article',
      limit = 20
    } = req.query;

    const trendingContent = await SearchIndex.getTrendingContent(
      contentType,
      parseInt(limit)
    );

    res.json({
      success: true,
      data: { content: trendingContent }
    });
  } catch (error) {
    console.error('Error fetching trending content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trending content',
      error: error.message
    });
  }
});

// 相似内容
router.get('/similar/:contentType/:contentId', async (req, res) => {
  try {
    const { contentType, contentId } = req.params;
    const { limit = 10 } = req.query;

    const similarContent = await SearchIndex.getSimilarContent(
      parseInt(contentId),
      contentType,
      parseInt(limit)
    );

    res.json({
      success: true,
      data: { content: similarContent }
    });
  } catch (error) {
    console.error('Error fetching similar content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch similar content',
      error: error.message
    });
  }
});

// 搜索统计（管理员）
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const { 
      startDate, 
      endDate,
      days = 30 
    } = req.query;

    let start, end;
    
    if (startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      end = new Date();
      start = new Date();
      start.setDate(start.getDate() - parseInt(days));
    }

    const stats = await searchService.getSearchStats(start, end);

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    console.error('Error fetching search stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch search statistics',
      error: error.message
    });
  }
});

// 搜索趋势
router.get('/trends', async (req, res) => {
  try {
    const { days = 30 } = req.query;

    const trends = await SearchHistory.getSearchTrends(parseInt(days));

    res.json({
      success: true,
      data: { trends }
    });
  } catch (error) {
    console.error('Error fetching search trends:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch search trends',
      error: error.message
    });
  }
});

module.exports = router;
