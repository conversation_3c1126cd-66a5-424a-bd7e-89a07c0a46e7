const express = require('express');
const router = express.Router();
const { User, Article, Share } = require('../models/associations');
const { authenticateToken } = require('../middleware/auth');
const { createActivity } = require('./activities');
const { Op } = require('sequelize');

// Record a share
router.post('/', async (req, res) => {
  try {
    const { articleId, platform, referrer } = req.body;
    const userId = req.user ? req.user.id : null;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    if (!articleId || !platform) {
      return res.status(400).json({ 
        success: false, 
        message: 'Article ID and platform are required' 
      });
    }

    // Check if article exists
    const article = await Article.findByPk(articleId);
    if (!article) {
      return res.status(404).json({ 
        success: false, 
        message: 'Article not found' 
      });
    }

    // Validate platform
    const validPlatforms = [
      'facebook', 'twitter', 'linkedin', 'whatsapp', 
      'telegram', 'email', 'copy_link', 'other'
    ];
    
    if (!validPlatforms.includes(platform)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid platform' 
      });
    }

    // Create share record
    const share = await Share.create({
      userId,
      articleId,
      platform,
      ipAddress,
      userAgent,
      referrer
    });

    // Create activity if user is logged in
    if (userId) {
      await createActivity(
        userId,
        'article_shared',
        'article',
        articleId,
        { 
          platform,
          articleTitle: article.title 
        }
      );
    }

    res.status(201).json({
      success: true,
      message: 'Share recorded successfully',
      data: share
    });
  } catch (error) {
    console.error('Error recording share:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get share statistics for an article
router.get('/article/:articleId/stats', async (req, res) => {
  try {
    const { articleId } = req.params;

    // Get total shares
    const totalShares = await Share.count({
      where: { articleId: parseInt(articleId) }
    });

    // Get shares by platform
    const sharesByPlatform = await Share.findAll({
      where: { articleId: parseInt(articleId) },
      attributes: [
        'platform',
        [Share.sequelize.fn('COUNT', Share.sequelize.col('id')), 'count']
      ],
      group: ['platform'],
      raw: true
    });

    // Get recent shares (last 7 days)
    const recentShares = await Share.count({
      where: {
        articleId: parseInt(articleId),
        createdAt: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalShares,
        recentShares,
        sharesByPlatform: sharesByPlatform.reduce((acc, share) => {
          acc[share.platform] = parseInt(share.count);
          return acc;
        }, {})
      }
    });
  } catch (error) {
    console.error('Error fetching share stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get user's sharing history
router.get('/user/:userId?', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.userId || req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Only allow users to see their own sharing history
    if (parseInt(userId) !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied' 
      });
    }

    const { count, rows: shares } = await Share.findAndCountAll({
      where: { userId: parseInt(userId) },
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'image', 'category']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    res.json({
      success: true,
      data: {
        shares,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching user shares:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get trending articles by shares
router.get('/trending', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const limit = parseInt(req.query.limit) || 10;

    const trendingArticles = await Share.findAll({
      where: {
        createdAt: {
          [Op.gte]: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
        }
      },
      attributes: [
        'articleId',
        [Share.sequelize.fn('COUNT', Share.sequelize.col('Share.id')), 'shareCount']
      ],
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'description', 'image', 'category', 'author', 'createdAt']
        }
      ],
      group: ['articleId', 'article.id'],
      order: [[Share.sequelize.fn('COUNT', Share.sequelize.col('Share.id')), 'DESC']],
      limit,
      raw: false
    });

    res.json({
      success: true,
      data: {
        articles: trendingArticles.map(item => ({
          ...item.article.toJSON(),
          shareCount: parseInt(item.dataValues.shareCount)
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching trending articles:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get platform statistics
router.get('/platforms/stats', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;

    const platformStats = await Share.findAll({
      where: {
        createdAt: {
          [Op.gte]: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
        }
      },
      attributes: [
        'platform',
        [Share.sequelize.fn('COUNT', Share.sequelize.col('id')), 'count']
      ],
      group: ['platform'],
      order: [[Share.sequelize.fn('COUNT', Share.sequelize.col('id')), 'DESC']],
      raw: true
    });

    const totalShares = platformStats.reduce((sum, stat) => sum + parseInt(stat.count), 0);

    res.json({
      success: true,
      data: {
        totalShares,
        platforms: platformStats.map(stat => ({
          platform: stat.platform,
          count: parseInt(stat.count),
          percentage: totalShares > 0 ? ((parseInt(stat.count) / totalShares) * 100).toFixed(2) : 0
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching platform stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

// Get share analytics (admin only)
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Admin access required' 
      });
    }

    const days = parseInt(req.query.days) || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Daily share counts
    const dailyShares = await Share.findAll({
      where: {
        createdAt: { [Op.gte]: startDate }
      },
      attributes: [
        [Share.sequelize.fn('DATE', Share.sequelize.col('createdAt')), 'date'],
        [Share.sequelize.fn('COUNT', Share.sequelize.col('id')), 'count']
      ],
      group: [Share.sequelize.fn('DATE', Share.sequelize.col('createdAt'))],
      order: [[Share.sequelize.fn('DATE', Share.sequelize.col('createdAt')), 'ASC']],
      raw: true
    });

    // Top shared articles
    const topArticles = await Share.findAll({
      where: {
        createdAt: { [Op.gte]: startDate }
      },
      attributes: [
        'articleId',
        [Share.sequelize.fn('COUNT', Share.sequelize.col('Share.id')), 'shareCount']
      ],
      include: [
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'category', 'author']
        }
      ],
      group: ['articleId', 'article.id'],
      order: [[Share.sequelize.fn('COUNT', Share.sequelize.col('Share.id')), 'DESC']],
      limit: 10,
      raw: false
    });

    res.json({
      success: true,
      data: {
        dailyShares,
        topArticles: topArticles.map(item => ({
          ...item.article.toJSON(),
          shareCount: parseInt(item.dataValues.shareCount)
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching share analytics:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
});

module.exports = router;
