const express = require('express');
const router = express.Router();
const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const TwitterStrategy = require('passport-twitter').Strategy;
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { logger } = require('../config/logger');

// 配置Passport策略
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: "/api/auth/google/callback"
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      // 查找现有用户
      let user = await User.findOne({
        where: {
          [User.sequelize.Sequelize.Op.or]: [
            { googleId: profile.id },
            { email: profile.emails[0].value }
          ]
        }
      });

      if (user) {
        // 更新Google ID如果没有
        if (!user.googleId) {
          await user.update({ googleId: profile.id });
        }
        return done(null, user);
      }

      // 创建新用户
      user = await User.create({
        googleId: profile.id,
        username: profile.emails[0].value.split('@')[0] + '_' + Date.now(),
        email: profile.emails[0].value,
        firstName: profile.name.givenName,
        lastName: profile.name.familyName,
        avatar: profile.photos[0]?.value,
        isEmailVerified: true,
        isActive: true,
        role: 'user'
      });

      logger.info(`New user created via Google: ${user.email}`);
      return done(null, user);
    } catch (error) {
      logger.error('Google OAuth error:', error);
      return done(error, null);
    }
  }));
}

if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
  passport.use(new FacebookStrategy({
    clientID: process.env.FACEBOOK_APP_ID,
    clientSecret: process.env.FACEBOOK_APP_SECRET,
    callbackURL: "/api/auth/facebook/callback",
    profileFields: ['id', 'emails', 'name', 'picture']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      let user = await User.findOne({
        where: {
          [User.sequelize.Sequelize.Op.or]: [
            { facebookId: profile.id },
            { email: profile.emails[0].value }
          ]
        }
      });

      if (user) {
        if (!user.facebookId) {
          await user.update({ facebookId: profile.id });
        }
        return done(null, user);
      }

      user = await User.create({
        facebookId: profile.id,
        username: profile.emails[0].value.split('@')[0] + '_' + Date.now(),
        email: profile.emails[0].value,
        firstName: profile.name.givenName,
        lastName: profile.name.familyName,
        avatar: profile.photos[0]?.value,
        isEmailVerified: true,
        isActive: true,
        role: 'user'
      });

      logger.info(`New user created via Facebook: ${user.email}`);
      return done(null, user);
    } catch (error) {
      logger.error('Facebook OAuth error:', error);
      return done(error, null);
    }
  }));
}

if (process.env.TWITTER_CONSUMER_KEY && process.env.TWITTER_CONSUMER_SECRET) {
  passport.use(new TwitterStrategy({
    consumerKey: process.env.TWITTER_CONSUMER_KEY,
    consumerSecret: process.env.TWITTER_CONSUMER_SECRET,
    callbackURL: "/api/auth/twitter/callback",
    includeEmail: true
  }, async (token, tokenSecret, profile, done) => {
    try {
      let user = await User.findOne({
        where: {
          [User.sequelize.Sequelize.Op.or]: [
            { twitterId: profile.id },
            { email: profile.emails[0]?.value }
          ]
        }
      });

      if (user) {
        if (!user.twitterId) {
          await user.update({ twitterId: profile.id });
        }
        return done(null, user);
      }

      user = await User.create({
        twitterId: profile.id,
        username: profile.username + '_' + Date.now(),
        email: profile.emails[0]?.value || `${profile.username}@twitter.local`,
        firstName: profile.displayName.split(' ')[0] || '',
        lastName: profile.displayName.split(' ').slice(1).join(' ') || '',
        avatar: profile.photos[0]?.value,
        isEmailVerified: !!profile.emails[0]?.value,
        isActive: true,
        role: 'user'
      });

      logger.info(`New user created via Twitter: ${user.username}`);
      return done(null, user);
    } catch (error) {
      logger.error('Twitter OAuth error:', error);
      return done(error, null);
    }
  }));
}

// Passport序列化
passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// 生成JWT Token
const generateToken = (userId, expiresIn = '7d') => {
  return jwt.sign(
    { 
      userId,
      iat: Math.floor(Date.now() / 1000),
      type: 'access'
    },
    process.env.JWT_SECRET,
    { 
      expiresIn,
      issuer: 'newzora',
      audience: 'newzora-users'
    }
  );
};

// Google OAuth路由
router.get('/google', 
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

router.get('/google/callback',
  passport.authenticate('google', { session: false }),
  (req, res) => {
    try {
      const token = generateToken(req.user.id);
      const redirectUrl = `${process.env.FRONTEND_URL}/auth/callback?token=${token}&provider=google`;
      res.redirect(redirectUrl);
    } catch (error) {
      logger.error('Google callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
    }
  }
);

// Facebook OAuth路由
router.get('/facebook',
  passport.authenticate('facebook', { scope: ['email'] })
);

router.get('/facebook/callback',
  passport.authenticate('facebook', { session: false }),
  (req, res) => {
    try {
      const token = generateToken(req.user.id);
      const redirectUrl = `${process.env.FRONTEND_URL}/auth/callback?token=${token}&provider=facebook`;
      res.redirect(redirectUrl);
    } catch (error) {
      logger.error('Facebook callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
    }
  }
);

// Twitter OAuth路由
router.get('/twitter',
  passport.authenticate('twitter')
);

router.get('/twitter/callback',
  passport.authenticate('twitter', { session: false }),
  (req, res) => {
    try {
      const token = generateToken(req.user.id);
      const redirectUrl = `${process.env.FRONTEND_URL}/auth/callback?token=${token}&provider=twitter`;
      res.redirect(redirectUrl);
    } catch (error) {
      logger.error('Twitter callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
    }
  }
);

// 绑定社交账号
router.post('/link/:provider', async (req, res) => {
  try {
    const { provider } = req.params;
    const { socialId, email } = req.body;
    const { authorization } = req.headers;

    if (!authorization) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const token = authorization.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    // 检查社交账号是否已被其他用户绑定
    const existingUser = await User.findOne({
      where: { [`${provider}Id`]: socialId }
    });

    if (existingUser && existingUser.id !== user.id) {
      return res.status(409).json({
        success: false,
        message: 'This social account is already linked to another user'
      });
    }

    // 绑定社交账号
    await user.update({ [`${provider}Id`]: socialId });

    logger.info(`${provider} account linked for user: ${user.username}`);

    res.json({
      success: true,
      message: `${provider} account linked successfully`
    });

  } catch (error) {
    logger.error('Link social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to link social account'
    });
  }
});

// 解绑社交账号
router.delete('/unlink/:provider', async (req, res) => {
  try {
    const { provider } = req.params;
    const { authorization } = req.headers;

    if (!authorization) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const token = authorization.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    // 检查用户是否设置了密码（防止无法登录）
    if (!user.password && !user.googleId && !user.facebookId && !user.twitterId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot unlink the only authentication method. Please set a password first.'
      });
    }

    // 解绑社交账号
    await user.update({ [`${provider}Id`]: null });

    logger.info(`${provider} account unlinked for user: ${user.username}`);

    res.json({
      success: true,
      message: `${provider} account unlinked successfully`
    });

  } catch (error) {
    logger.error('Unlink social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlink social account'
    });
  }
});

module.exports = router;
