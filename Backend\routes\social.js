const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const UserInteraction = require('../models/UserInteraction');
const UserRelationship = require('../models/UserRelationship');
const User = require('../models/User');
const Article = require('../models/Article');

// 点赞/取消点赞
router.post('/like', authenticateToken, async (req, res) => {
  try {
    const { targetType, targetId } = req.body;
    
    if (!targetType || !targetId) {
      return res.status(400).json({
        success: false,
        message: 'Target type and ID are required'
      });
    }
    
    const result = await UserInteraction.addInteraction(
      req.user.id,
      targetType,
      parseInt(targetId),
      'like',
      { ip: req.ip, userAgent: req.get('User-Agent') }
    );
    
    res.json({
      success: true,
      message: `${result.action === 'added' ? 'Liked' : 'Unliked'} successfully`,
      data: {
        action: result.action,
        interaction: result.interaction
      }
    });
  } catch (error) {
    console.error('Error handling like:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process like',
      error: error.message
    });
  }
});

// 收藏/取消收藏
router.post('/favorite', authenticateToken, async (req, res) => {
  try {
    const { targetType, targetId } = req.body;
    
    if (!targetType || !targetId) {
      return res.status(400).json({
        success: false,
        message: 'Target type and ID are required'
      });
    }
    
    const result = await UserInteraction.addInteraction(
      req.user.id,
      targetType,
      parseInt(targetId),
      'favorite',
      { ip: req.ip, userAgent: req.get('User-Agent') }
    );
    
    res.json({
      success: true,
      message: `${result.action === 'added' ? 'Added to favorites' : 'Removed from favorites'}`,
      data: {
        action: result.action,
        interaction: result.interaction
      }
    });
  } catch (error) {
    console.error('Error handling favorite:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process favorite',
      error: error.message
    });
  }
});

// 分享
router.post('/share', authenticateToken, async (req, res) => {
  try {
    const { targetType, targetId, platform } = req.body;
    
    if (!targetType || !targetId) {
      return res.status(400).json({
        success: false,
        message: 'Target type and ID are required'
      });
    }
    
    const result = await UserInteraction.addInteraction(
      req.user.id,
      targetType,
      parseInt(targetId),
      'share',
      { 
        platform: platform || 'unknown',
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      }
    );
    
    res.json({
      success: true,
      message: 'Shared successfully',
      data: result.interaction
    });
  } catch (error) {
    console.error('Error handling share:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process share',
      error: error.message
    });
  }
});

// 获取内容的交互统计
router.get('/interactions/:targetType/:targetId', async (req, res) => {
  try {
    const { targetType, targetId } = req.params;
    
    const interactions = await UserInteraction.getInteractionCounts(targetType, parseInt(targetId));
    
    // 转换为更友好的格式
    const stats = {
      likes: 0,
      favorites: 0,
      shares: 0,
      dislikes: 0
    };
    
    interactions.forEach(interaction => {
      stats[interaction.interactionType + 's'] = parseInt(interaction.total);
    });
    
    // 如果用户已登录，获取用户的交互状态
    let userInteractions = {};
    if (req.user) {
      const userInteractionsList = await UserInteraction.getUserInteractions(
        req.user.id,
        targetType,
        [parseInt(targetId)]
      );
      
      userInteractionsList.forEach(interaction => {
        userInteractions[interaction.interactionType] = true;
      });
    }
    
    res.json({
      success: true,
      data: {
        stats,
        userInteractions
      }
    });
  } catch (error) {
    console.error('Error fetching interactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interactions',
      error: error.message
    });
  }
});

// 关注用户
router.post('/follow', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    // 检查目标用户是否存在
    const targetUser = await User.findByPk(userId);
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // 检查是否是私密账户
    const isPrivateAccount = targetUser.isPrivate || false;
    
    const relationship = await UserRelationship.follow(req.user.id, parseInt(userId), isPrivateAccount);
    
    res.json({
      success: true,
      message: isPrivateAccount ? 'Follow request sent' : 'Followed successfully',
      data: {
        relationship,
        isPending: relationship.status === 'pending'
      }
    });
  } catch (error) {
    console.error('Error following user:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to follow user',
      error: error.message
    });
  }
});

// 取消关注用户
router.post('/unfollow', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    await UserRelationship.unfollow(req.user.id, parseInt(userId));
    
    res.json({
      success: true,
      message: 'Unfollowed successfully'
    });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unfollow user',
      error: error.message
    });
  }
});

// 屏蔽用户
router.post('/block', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    const relationship = await UserRelationship.block(req.user.id, parseInt(userId));
    
    res.json({
      success: true,
      message: 'User blocked successfully',
      data: relationship
    });
  } catch (error) {
    console.error('Error blocking user:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to block user',
      error: error.message
    });
  }
});

// 取消屏蔽用户
router.post('/unblock', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    await UserRelationship.unblock(req.user.id, parseInt(userId));
    
    res.json({
      success: true,
      message: 'User unblocked successfully'
    });
  } catch (error) {
    console.error('Error unblocking user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unblock user',
      error: error.message
    });
  }
});

// 获取关注者列表
router.get('/followers/:userId?', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.userId ? parseInt(req.params.userId) : req.user.id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const result = await UserRelationship.getFollowers(userId, {
      limit: parseInt(limit),
      offset: offset
    });
    
    res.json({
      success: true,
      data: {
        followers: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.count,
          pages: Math.ceil(result.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching followers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch followers',
      error: error.message
    });
  }
});

// 获取关注列表
router.get('/following/:userId?', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.userId ? parseInt(req.params.userId) : req.user.id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const result = await UserRelationship.getFollowing(userId, {
      limit: parseInt(limit),
      offset: offset
    });
    
    res.json({
      success: true,
      data: {
        following: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.count,
          pages: Math.ceil(result.count / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching following:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch following',
      error: error.message
    });
  }
});

// 获取用户推荐
router.get('/recommendations/users', authenticateToken, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const recommendations = await UserRelationship.getRecommendations(req.user.id, parseInt(limit));
    
    res.json({
      success: true,
      data: { recommendations }
    });
  } catch (error) {
    console.error('Error fetching user recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user recommendations',
      error: error.message
    });
  }
});

// 获取内容推荐
router.get('/recommendations/content', authenticateToken, async (req, res) => {
  try {
    const { targetType = 'article', limit = 10 } = req.query;
    
    const recommendations = await UserInteraction.getRecommendations(
      req.user.id,
      targetType,
      parseInt(limit)
    );
    
    // 如果是文章推荐，获取文章详情
    if (targetType === 'article' && recommendations.length > 0) {
      const articleIds = recommendations.map(r => r.targetId);
      const articles = await Article.findAll({
        where: {
          id: articleIds,
          published: true
        },
        include: [
          {
            model: User,
            as: 'authorUser',
            attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
          }
        ]
      });
      
      // 合并推荐分数和文章信息
      const enrichedRecommendations = recommendations.map(rec => {
        const article = articles.find(a => a.id === rec.targetId);
        return {
          ...rec,
          article
        };
      }).filter(rec => rec.article); // 过滤掉找不到的文章
      
      res.json({
        success: true,
        data: { recommendations: enrichedRecommendations }
      });
    } else {
      res.json({
        success: true,
        data: { recommendations }
      });
    }
  } catch (error) {
    console.error('Error fetching content recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch content recommendations',
      error: error.message
    });
  }
});

// 获取热门内容
router.get('/trending/:targetType?', async (req, res) => {
  try {
    const targetType = req.params.targetType || 'article';
    const { timeframe = '24h', limit = 20 } = req.query;
    
    const trending = await UserInteraction.getTrendingContent(
      targetType,
      parseInt(timeframe.replace('h', '')),
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: { trending }
    });
  } catch (error) {
    console.error('Error fetching trending content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trending content',
      error: error.message
    });
  }
});

// 搜索用户
router.get('/search/users', authenticateToken, async (req, res) => {
  try {
    const { query, limit = 20 } = req.query;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }
    
    const users = await UserRelationship.searchUsers(req.user.id, query, parseInt(limit));
    
    res.json({
      success: true,
      data: { users }
    });
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search users',
      error: error.message
    });
  }
});

// 获取网络统计
router.get('/stats/:userId?', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.userId ? parseInt(req.params.userId) : req.user.id;
    
    const stats = await UserRelationship.getNetworkStats(userId);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching network stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch network statistics',
      error: error.message
    });
  }
});

module.exports = router;
