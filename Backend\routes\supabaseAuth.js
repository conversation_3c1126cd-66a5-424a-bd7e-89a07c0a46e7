const express = require('express');
const jwt = require('jsonwebtoken');
const authService = require('../services/authService');
const { authenticateToken, refreshSession, verifyEmailToken } = require('../middleware/supabaseAuth');

const router = express.Router();

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, displayName } = req.body;

    // 验证必填字段
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username, email, and password are required'
      });
    }

    // 验证用户名格式
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      return res.status(400).json({
        success: false,
        message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
      });
    }

    // 验证密码强度
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid email address'
      });
    }

    // 检查用户名是否可用
    const isUsernameAvailable = await authService.checkUsernameAvailability(username);
    if (!isUsernameAvailable) {
      return res.status(400).json({
        success: false,
        message: 'Username is already taken'
      });
    }

    // 注册用户
    const result = await authService.register({
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password,
      displayName: displayName || username
    });

    res.status(201).json({
      success: true,
      message: result.needsEmailConfirmation 
        ? 'Registration successful! Please check your email to confirm your account.'
        : 'Registration successful!',
      user: result.user,
      session: result.session,
      needsEmailConfirmation: result.needsEmailConfirmation
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    let message = 'Registration failed';
    if (error.message === 'User already registered') {
      message = 'Email is already registered';
    } else if (error.message === 'Username already taken') {
      message = 'Username is already taken';
    } else if (error.message.includes('email')) {
      message = 'Invalid email address';
    }

    res.status(400).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    console.log('🔍 Login request body:', req.body);
    console.log('🔍 Login request headers:', req.headers);

    const { email, password } = req.body;

    // 验证必填字段
    if (!email || !password) {
      console.log('❌ Missing email or password:', { email: !!email, password: !!password });
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // 登录用户
    const result = await authService.login(email.toLowerCase(), password);

    // 生成JWT token
    const token = jwt.sign(
      {
        id: result.user.id,
        email: result.user.email,
        role: result.user.user_metadata?.role || 'user'
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: result.user,
      session: result.session
    });

  } catch (error) {
    console.error('Login error:', error);
    
    let message = 'Invalid email or password';
    if (error.message === 'Email not confirmed') {
      message = 'Please confirm your email address before signing in';
    } else if (error.message === 'Invalid login credentials') {
      message = 'Invalid email or password';
    }

    res.status(401).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const userProfile = await authService.getUser(req.user.id);

    res.json({
      success: true,
      user: userProfile
    });
  } catch (error) {
    console.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user information'
    });
  }
});

// 更新用户资料
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { display_name, bio, avatar_url } = req.body;
    const userId = req.user.id;

    // 构建更新数据
    const updateData = {};
    if (display_name !== undefined) updateData.display_name = display_name;
    if (bio !== undefined) updateData.bio = bio;
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url;

    const updatedUser = await authService.updateProfile(userId, updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile'
    });
  }
});

// 修改密码
router.put('/change-password', authenticateToken, async (req, res) => {
  try {
    const { newPassword } = req.body;

    // 验证新密码
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    await authService.changePassword(newPassword);

    res.json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password'
    });
  }
});

// 发送密码重置邮件
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    await authService.resetPassword(email.toLowerCase());

    res.json({
      success: true,
      message: 'Password reset email sent successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send reset email'
    });
  }
});

// 用户登出
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    await authService.logout();

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed'
    });
  }
});

// 刷新会话
router.post('/refresh', refreshSession);

// 验证邮箱
router.get('/verify-email', verifyEmailToken);

// 检查用户名可用性
router.get('/check-username/:username', async (req, res) => {
  try {
    const { username } = req.params;
    
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid username format'
      });
    }

    const isAvailable = await authService.checkUsernameAvailability(username.toLowerCase());

    res.json({
      success: true,
      available: isAvailable,
      message: isAvailable ? 'Username is available' : 'Username is already taken'
    });

  } catch (error) {
    console.error('Check username error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check username availability'
    });
  }
});

// 检查邮箱可用性
router.get('/check-email/:email', async (req, res) => {
  try {
    const { email } = req.params;

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    const isAvailable = await authService.checkEmailAvailability(email.toLowerCase());

    res.json({
      success: true,
      available: isAvailable,
      message: isAvailable ? 'Email is available' : 'Email is already registered'
    });

  } catch (error) {
    console.error('Check email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check email availability'
    });
  }
});

// 搜索用户
router.get('/search', async (req, res) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || q.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters'
      });
    }

    const users = await authService.searchUsers(q, parseInt(limit));

    res.json({
      success: true,
      users
    });

  } catch (error) {
    console.error('Search users error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed'
    });
  }
});

module.exports = router;
