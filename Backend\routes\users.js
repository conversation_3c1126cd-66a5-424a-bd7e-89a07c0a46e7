const express = require('express');
const router = express.Router();
const {
  authenticateToken,
  requireAdmin
} = require('../middleware/supabaseAuth');

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    
    // 适配Supabase用户对象结构
    const userProfile = {
      id: user.id,
      email: user.email,
      role: user.role || user.user_metadata?.role || 'user',
      username: user.user_metadata?.username || user.email?.split('@')[0],
      displayName: user.user_metadata?.display_name || user.user_metadata?.username || user.email?.split('@')[0],
      avatar: user.user_metadata?.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.email?.split('@')[0] || 'User')}&background=6366f1&color=fff&size=128`,
      isEmailVerified: user.email_confirmed_at ? true : (user.user_metadata?.email_verified || false),
      preferences: user.user_metadata?.preferences || {},
      createdAt: user.created_at,
      lastLogin: user.last_sign_in_at
    };

    res.json({
      success: true,
      user: userProfile
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user profile'
    });
  }
});

module.exports = router;
