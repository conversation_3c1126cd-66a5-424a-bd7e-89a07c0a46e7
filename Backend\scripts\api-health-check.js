#!/usr/bin/env node

/**
 * API 健康检查脚本
 * 检测所有API端点的连接状态和功能正常性
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const axios = require('axios');
const colors = require('colors');

class APIHealthChecker {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'http://localhost:5000/api';
    this.testResults = [];
    this.authToken = null;
    this.testUser = {
      email: '<EMAIL>',
      password: 'Demo123456!'
    };
    this.adminUser = {
      email: '<EMAIL>',
      password: 'Admin123456!'
    };
    this.adminToken = null;
  }

  // 记录测试结果
  logResult(endpoint, method, status, message, responseTime = 0) {
    const result = {
      endpoint,
      method,
      status,
      message,
      responseTime,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
    const timeStr = responseTime > 0 ? ` (${responseTime}ms)` : '';
    console.log(`${status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'} ${method.padEnd(6)} ${endpoint.padEnd(40)} ${message}`[statusColor] + timeStr.gray);
  }

  // 执行HTTP请求
  async makeRequest(method, endpoint, data = null, headers = {}) {
    const startTime = Date.now();
    try {
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        timeout: 10000
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      const responseTime = Date.now() - startTime;
      return { success: true, data: response.data, status: response.status, responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        'Unknown error';
      return {
        success: false,
        error: errorMessage,
        status: error.response?.status || 0,
        responseTime
      };
    }
  }

  // 测试基础健康检查
  async testHealthEndpoints() {
    console.log('\n🏥 Testing Health Check Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 基础健康检查
    const health = await this.makeRequest('GET', '/monitoring/health');
    if (health.success) {
      this.logResult('/monitoring/health', 'GET', 'PASS', 'Server is healthy', health.responseTime);
    } else {
      this.logResult('/monitoring/health', 'GET', 'FAIL', `Health check failed: ${health.error}`, health.responseTime);
    }

    // 先尝试管理员登录以获取系统状态
    if (!this.adminToken) {
      const adminLogin = await this.makeRequest('POST', '/auth/login', this.adminUser);
      if (adminLogin.success && adminLogin.data.token) {
        this.adminToken = adminLogin.data.token;
        console.log('✅ Admin login successful for monitoring status'.gray);
      } else {
        console.log(`❌ Admin login failed: ${adminLogin.error}`.gray);
      }
    }

    // 系统状态（需要管理员权限）
    if (this.adminToken) {
      const status = await this.makeRequest('GET', '/monitoring/status', null, {
        'Authorization': `Bearer ${this.adminToken}`
      });
      if (status.success) {
        this.logResult('/monitoring/status', 'GET', 'PASS', 'System status retrieved', status.responseTime);
      } else {
        this.logResult('/monitoring/status', 'GET', 'FAIL', `Status check failed: ${status.error}`, status.responseTime);
      }
    } else {
      this.logResult('/monitoring/status', 'GET', 'SKIP', 'Skipped - Admin login failed');
    }
  }

  // 测试认证相关API
  async testAuthEndpoints() {
    console.log('\n🔐 Testing Authentication Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 测试登录
    const login = await this.makeRequest('POST', '/auth/login', this.testUser);
    if (login.success && login.data.token) {
      this.authToken = login.data.token;
      this.logResult('/auth/login', 'POST', 'PASS', 'Login successful', login.responseTime);
    } else {
      this.logResult('/auth/login', 'POST', 'FAIL', `Login failed: ${login.error}`, login.responseTime);
      return; // 如果登录失败，跳过需要认证的测试
    }

    // 测试获取用户信息
    const me = await this.makeRequest('GET', '/auth/me', null, { 
      'Authorization': `Bearer ${this.authToken}` 
    });
    if (me.success) {
      this.logResult('/auth/me', 'GET', 'PASS', 'User info retrieved', me.responseTime);
    } else {
      this.logResult('/auth/me', 'GET', 'FAIL', `Get user info failed: ${me.error}`, me.responseTime);
    }

    // 测试用户名检查
    const checkUsername = await this.makeRequest('GET', '/auth/check-username/testuser123');
    if (checkUsername.success) {
      this.logResult('/auth/check-username/:username', 'GET', 'PASS', 'Username check working', checkUsername.responseTime);
    } else {
      this.logResult('/auth/check-username/:username', 'GET', 'FAIL', `Username check failed: ${checkUsername.error}`, checkUsername.responseTime);
    }

    // 测试邮箱检查
    const checkEmail = await this.makeRequest('GET', '/auth/check-email/<EMAIL>');
    if (checkEmail.success) {
      this.logResult('/auth/check-email/:email', 'GET', 'PASS', 'Email check working', checkEmail.responseTime);
    } else {
      this.logResult('/auth/check-email/:email', 'GET', 'FAIL', `Email check failed: ${checkEmail.error}`, checkEmail.responseTime);
    }
  }

  // 测试文章相关API
  async testArticleEndpoints() {
    console.log('\n📰 Testing Article Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 获取文章列表
    const articles = await this.makeRequest('GET', '/articles');
    if (articles.success) {
      this.logResult('/articles', 'GET', 'PASS', `Retrieved ${articles.data.articles?.length || 0} articles`, articles.responseTime);
    } else {
      this.logResult('/articles', 'GET', 'FAIL', `Get articles failed: ${articles.error}`, articles.responseTime);
    }

    // 获取特色文章
    const featured = await this.makeRequest('GET', '/articles?featured=true&limit=6');
    if (featured.success) {
      this.logResult('/articles?featured=true', 'GET', 'PASS', `Retrieved ${featured.data.articles?.length || 0} featured articles`, featured.responseTime);
    } else {
      this.logResult('/articles?featured=true', 'GET', 'FAIL', `Get featured articles failed: ${featured.error}`, featured.responseTime);
    }

    // 获取分类文章（使用数据库中实际存在的分类）
    const categories = ['Technology', 'Lifestyle', 'Food'];
    for (const category of categories) {
      const categoryArticles = await this.makeRequest('GET', `/articles?category=${category}&limit=3`);
      if (categoryArticles.success) {
        this.logResult(`/articles?category=${category}`, 'GET', 'PASS', `Retrieved ${categoryArticles.data.articles?.length || 0} ${category} articles`, categoryArticles.responseTime);
      } else {
        this.logResult(`/articles?category=${category}`, 'GET', 'WARN', `Category ${category} articles failed: ${categoryArticles.error}`, categoryArticles.responseTime);
      }
    }

    // 搜索文章
    const search = await this.makeRequest('GET', '/articles/search?q=test');
    if (search.success) {
      this.logResult('/articles/search', 'GET', 'PASS', `Search returned ${search.data.articles?.length || 0} results`, search.responseTime);
    } else {
      this.logResult('/articles/search', 'GET', 'WARN', `Article search failed: ${search.error}`, search.responseTime);
    }
  }

  // 测试通知相关API
  async testNotificationEndpoints() {
    console.log('\n🔔 Testing Notification Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 获取通知
    const notifications = await this.makeRequest('GET', '/notifications');
    if (notifications.success) {
      this.logResult('/notifications', 'GET', 'PASS', `Retrieved ${notifications.data.length || 0} notifications`, notifications.responseTime);
    } else {
      this.logResult('/notifications', 'GET', 'FAIL', `Get notifications failed: ${notifications.error}`, notifications.responseTime);
    }

    // 获取未读通知数量
    const unreadCount = await this.makeRequest('GET', '/notifications/unread-count');
    if (unreadCount.success) {
      this.logResult('/notifications/unread-count', 'GET', 'PASS', `Unread count: ${unreadCount.data.count || 0}`, unreadCount.responseTime);
    } else {
      this.logResult('/notifications/unread-count', 'GET', 'WARN', `Get unread count failed: ${unreadCount.error}`, unreadCount.responseTime);
    }
  }

  // 测试用户相关API
  async testUserEndpoints() {
    console.log('\n👤 Testing User Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    if (!this.authToken) {
      this.logResult('/users/*', 'ALL', 'SKIP', 'Skipped - No auth token available');
      return;
    }

    // 获取用户资料
    const profile = await this.makeRequest('GET', '/users/profile', null, {
      'Authorization': `Bearer ${this.authToken}`
    });
    if (profile.success) {
      this.logResult('/users/profile', 'GET', 'PASS', 'User profile retrieved', profile.responseTime);
    } else {
      this.logResult('/users/profile', 'GET', 'FAIL', `Get profile failed: ${profile.error}`, profile.responseTime);
    }
  }

  // 测试标签相关API
  async testTagEndpoints() {
    console.log('\n🏷️ Testing Tag Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 获取标签列表
    const tags = await this.makeRequest('GET', '/tags');
    if (tags.success) {
      this.logResult('/tags', 'GET', 'PASS', `Retrieved ${tags.data.length || 0} tags`, tags.responseTime);
    } else {
      this.logResult('/tags', 'GET', 'WARN', `Get tags failed: ${tags.error}`, tags.responseTime);
    }

    // 获取热门标签
    const popularTags = await this.makeRequest('GET', '/tags/popular');
    if (popularTags.success) {
      this.logResult('/tags/popular', 'GET', 'PASS', `Retrieved ${popularTags.data.length || 0} popular tags`, popularTags.responseTime);
    } else {
      this.logResult('/tags/popular', 'GET', 'WARN', `Get popular tags failed: ${popularTags.error}`, popularTags.responseTime);
    }
  }

  // 测试评论相关API
  async testCommentEndpoints() {
    console.log('\n💬 Testing Comment Endpoints'.cyan.bold);
    console.log('='.repeat(60).gray);

    // 获取评论（需要文章ID，使用示例ID）
    const comments = await this.makeRequest('GET', '/comments/article/1');
    if (comments.success) {
      this.logResult('/comments/article/:id', 'GET', 'PASS', `Retrieved ${comments.data.length || 0} comments`, comments.responseTime);
    } else {
      this.logResult('/comments/article/:id', 'GET', 'WARN', `Get comments failed: ${comments.error}`, comments.responseTime);
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 Test Results Summary'.cyan.bold);
    console.log('='.repeat(60).gray);

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const warned = this.testResults.filter(r => r.status === 'WARN').length;
    const skipped = this.testResults.filter(r => r.status === 'SKIP').length;
    const total = this.testResults.length;

    console.log(`Total Tests: ${total}`.white);
    console.log(`✅ Passed: ${passed}`.green);
    console.log(`❌ Failed: ${failed}`.red);
    console.log(`⚠️ Warnings: ${warned}`.yellow);
    console.log(`⏭️ Skipped: ${skipped}`.gray);

    const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
    console.log(`\n🎯 Success Rate: ${successRate}%`.cyan);

    // 显示失败的测试
    const failedTests = this.testResults.filter(r => r.status === 'FAIL');
    if (failedTests.length > 0) {
      console.log('\n❌ Failed Tests:'.red.bold);
      failedTests.forEach(test => {
        console.log(`   ${test.method} ${test.endpoint}: ${test.message}`.red);
      });
    }

    // 显示警告的测试
    const warnedTests = this.testResults.filter(r => r.status === 'WARN');
    if (warnedTests.length > 0) {
      console.log('\n⚠️ Warning Tests:'.yellow.bold);
      warnedTests.forEach(test => {
        console.log(`   ${test.method} ${test.endpoint}: ${test.message}`.yellow);
      });
    }

    return {
      total,
      passed,
      failed,
      warned,
      skipped,
      successRate: parseFloat(successRate),
      failedTests,
      warnedTests
    };
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 Starting API Health Check...'.cyan.bold);
    console.log(`🌐 Base URL: ${this.baseURL}`.gray);
    console.log(`⏰ Started at: ${new Date().toLocaleString()}`.gray);

    try {
      await this.testHealthEndpoints();
      await this.testAuthEndpoints();
      await this.testArticleEndpoints();
      await this.testNotificationEndpoints();
      await this.testUserEndpoints();
      await this.testTagEndpoints();
      await this.testCommentEndpoints();

      const report = this.generateReport();
      
      console.log('\n🏁 API Health Check Completed!'.cyan.bold);
      
      // 返回退出码
      process.exit(report.failed > 0 ? 1 : 0);
      
    } catch (error) {
      console.error('\n💥 Health check failed with error:'.red.bold);
      console.error(error.message.red);
      process.exit(1);
    }
  }
}

// 运行健康检查
if (require.main === module) {
  const checker = new APIHealthChecker();
  checker.runAllTests();
}

module.exports = APIHealthChecker;
