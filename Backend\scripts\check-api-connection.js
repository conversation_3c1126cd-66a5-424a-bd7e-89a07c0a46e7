#!/usr/bin/env node

/**
 * API服务器连接检查脚本
 * 检查后端和前端服务器的连接状态
 */

const axios = require('axios');
const net = require('net');

console.log('🔍 Newzora API服务器连接检查');
console.log('============================\n');

// 检查端口是否开放
function checkPort(port, host = 'localhost') {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    
    socket.setTimeout(3000);
    
    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });
    
    socket.on('error', () => {
      resolve(false);
    });
    
    socket.connect(port, host);
  });
}

// 测试API端点
async function testApiEndpoint(url, method = 'GET', data = null, description = '') {
  try {
    console.log(`📡 测试: ${method} ${url}`);
    if (description) console.log(`   描述: ${description}`);
    
    const config = {
      method,
      url,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    console.log(`✅ 连接成功!`);
    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${response.headers['content-type']}`);
    
    // 检查是否是JSON响应
    const contentType = response.headers['content-type'];
    if (contentType && contentType.includes('application/json')) {
      console.log(`✅ 正确返回JSON格式`);
      
      // 显示响应数据摘要
      if (response.data) {
        if (response.data.status) {
          console.log(`   服务器状态: ${response.data.status}`);
        }
        if (response.data.success !== undefined) {
          console.log(`   请求成功: ${response.data.success}`);
        }
        if (response.data.message) {
          console.log(`   消息: ${response.data.message}`);
        }
        if (response.data.database) {
          console.log(`   数据库: ${response.data.database.status || response.data.database}`);
        }
        if (response.data.uptime) {
          console.log(`   运行时间: ${Math.round(response.data.uptime)}秒`);
        }
      }
    } else {
      console.log(`❌ 返回的不是JSON格式: ${contentType}`);
      console.log(`   响应预览: ${response.data.toString().substring(0, 100)}...`);
    }
    
    return { success: true, status: response.status, data: response.data };
    
  } catch (error) {
    console.log(`❌ 连接失败:`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log(`   错误: 连接被拒绝 - 服务器可能未启动`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`   错误: 连接超时 - 服务器响应慢`);
    } else if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.statusText}`);
      
      const contentType = error.response.headers['content-type'];
      if (contentType && contentType.includes('text/html')) {
        console.log(`   ❌ 返回HTML页面而不是JSON - 这是问题根源!`);
      }
    } else {
      console.log(`   错误: ${error.message}`);
    }
    
    return { success: false, error: error.message };
  }
}

async function runConnectionCheck() {
  console.log('🚀 开始连接检查...\n');
  
  const results = {
    backendPort: false,
    frontendPort: false,
    backendHealth: false,
    backendAuth: false,
    frontendHealth: false
  };
  
  try {
    // 1. 检查端口状态
    console.log('🚪 1. 检查端口监听状态');
    console.log('========================');
    
    console.log('检查后端端口 5000...');
    results.backendPort = await checkPort(5000);
    if (results.backendPort) {
      console.log('✅ 端口 5000 正在监听');
    } else {
      console.log('❌ 端口 5000 没有监听 - 后端服务器未启动');
    }
    
    console.log('\n检查前端端口 3000...');
    results.frontendPort = await checkPort(3000);
    if (results.frontendPort) {
      console.log('✅ 端口 3000 正在监听');
    } else {
      console.log('❌ 端口 3000 没有监听 - 前端服务器未启动');
    }
    
    // 2. 测试后端API
    console.log('\n🌐 2. 后端API连接测试');
    console.log('=====================');
    
    if (results.backendPort) {
      // 健康检查
      const healthResult = await testApiEndpoint(
        'http://localhost:5000/api/health',
        'GET',
        null,
        '后端健康检查'
      );
      results.backendHealth = healthResult.success;
      
      console.log('');
      
      // 认证端点测试
      const authResult = await testApiEndpoint(
        'http://localhost:5000/api/auth-enhanced/login',
        'POST',
        {
          identifier: '<EMAIL>',
          password: 'Admin123!',
          rememberMe: false
        },
        '认证端点测试'
      );
      results.backendAuth = authResult.success;
      
    } else {
      console.log('⏭️ 跳过API测试 - 后端端口未监听');
    }
    
    // 3. 测试前端服务器
    console.log('\n🎨 3. 前端服务器测试');
    console.log('===================');
    
    if (results.frontendPort) {
      const frontendResult = await testApiEndpoint(
        'http://localhost:3000/health',
        'GET',
        null,
        '前端健康检查'
      );
      results.frontendHealth = frontendResult.success;
    } else {
      console.log('⏭️ 跳过前端测试 - 前端端口未监听');
    }
    
    // 4. 生成诊断报告
    console.log('\n📊 连接检查结果');
    console.log('===============');
    
    console.log(`后端端口 (5000): ${results.backendPort ? '✅ 监听中' : '❌ 未监听'}`);
    console.log(`前端端口 (3000): ${results.frontendPort ? '✅ 监听中' : '❌ 未监听'}`);
    console.log(`后端健康检查: ${results.backendHealth ? '✅ 正常' : '❌ 失败'}`);
    console.log(`后端认证API: ${results.backendAuth ? '✅ 正常' : '❌ 失败'}`);
    console.log(`前端服务器: ${results.frontendHealth ? '✅ 正常' : '❌ 失败'}`);
    
    // 5. 问题诊断和建议
    console.log('\n🔧 问题诊断和建议');
    console.log('=================');
    
    if (!results.backendPort) {
      console.log('❌ 主要问题: 后端服务器未启动');
      console.log('💡 解决方案:');
      console.log('   cd Backend');
      console.log('   node server-launcher.js');
      console.log('   或运行: npm run stable');
    } else if (!results.backendHealth) {
      console.log('❌ 后端服务器启动但API不响应');
      console.log('💡 检查服务器日志查看错误信息');
    } else if (!results.backendAuth) {
      console.log('❌ 认证API有问题');
      console.log('💡 检查认证路由配置和数据库连接');
    } else {
      console.log('✅ 后端服务器工作正常');
    }
    
    if (!results.frontendPort) {
      console.log('⚠️ 前端服务器未启动');
      console.log('💡 解决方案:');
      console.log('   cd Frontend');
      console.log('   node stable-server.js');
      console.log('   或运行: npm run stable');
    } else if (!results.frontendHealth) {
      console.log('⚠️ 前端服务器启动但不响应');
      console.log('💡 检查前端服务器配置');
    } else {
      console.log('✅ 前端服务器工作正常');
    }
    
    // 6. 快速修复建议
    const allGood = Object.values(results).every(r => r);
    
    if (allGood) {
      console.log('\n🎉 所有服务器连接正常!');
      console.log('🌐 可以访问: http://localhost:3000/test-auth');
      console.log('🧪 测试账户: <EMAIL> / Admin123!');
    } else {
      console.log('\n🚀 快速修复步骤:');
      console.log('================');
      console.log('1. 停止所有Node进程:');
      console.log('   taskkill /F /IM node.exe  (Windows)');
      console.log('   或 pkill node  (Linux/Mac)');
      console.log('');
      console.log('2. 启动后端服务器:');
      console.log('   cd Backend && node server-launcher.js');
      console.log('');
      console.log('3. 启动前端服务器 (新终端):');
      console.log('   cd Frontend && node stable-server.js');
      console.log('');
      console.log('4. 等待30秒后重新运行此检查');
    }
    
  } catch (error) {
    console.error('\n💥 检查过程中发生错误:', error.message);
  }
}

// 运行检查
if (require.main === module) {
  runConnectionCheck()
    .then(() => {
      console.log('\n✅ 连接检查完成');
    })
    .catch(error => {
      console.error('\n❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkPort, testApiEndpoint, runConnectionCheck };
