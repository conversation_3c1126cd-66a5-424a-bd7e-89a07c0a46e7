#!/usr/bin/env node

/**
 * 检查数据库数据脚本
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const { Article } = require('../models');

async function checkDatabaseData() {
  try {
    console.log('🔍 Checking database data...\n');

    // 检查文章总数
    const totalArticles = await Article.count();
    console.log(`📊 Total articles: ${totalArticles}`);

    // 检查已发布文章数
    const publishedArticles = await Article.count({ where: { published: true } });
    console.log(`📰 Published articles: ${publishedArticles}`);

    // 检查特色文章数
    const featuredArticles = await Article.count({ where: { featured: true, published: true } });
    console.log(`⭐ Featured articles: ${featuredArticles}`);

    // 检查分类分布
    const categories = await Article.findAll({
      attributes: ['category', [Article.sequelize.fn('COUNT', '*'), 'count']],
      where: { published: true },
      group: ['category'],
      raw: true
    });

    console.log('\n📂 Categories distribution:');
    if (categories.length > 0) {
      categories.forEach(cat => {
        console.log(`   ${cat.category}: ${cat.count} articles`);
      });
    } else {
      console.log('   No categories found');
    }

    // 检查最近的文章
    const recentArticles = await Article.findAll({
      attributes: ['id', 'title', 'category', 'published', 'featured', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    console.log('\n📝 Recent articles:');
    if (recentArticles.length > 0) {
      recentArticles.forEach((article, index) => {
        const status = article.published ? '✅' : '❌';
        const featured = article.featured ? '⭐' : '  ';
        console.log(`   ${index + 1}. ${status}${featured} [${article.category}] ${article.title}`);
      });
    } else {
      console.log('   No articles found');
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
    process.exit(1);
  }
}

checkDatabaseData();
