#!/usr/bin/env node

/**
 * Newzora 综合认证功能测试
 * 测试注册、登录、密码找回等完整流程
 */

const axios = require('axios');
const { testAccounts } = require('./create-test-accounts');

// 配置axios
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('🧪 Newzora 综合认证功能测试');
console.log('============================');

let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

function logTest(testName, success, message = '', details = null) {
  testResults.total++;
  const result = {
    name: testName,
    success,
    message,
    details,
    timestamp: new Date().toISOString()
  };
  
  testResults.details.push(result);
  
  if (success) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
    if (message) console.log(`   ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
}

async function testServerHealth() {
  try {
    const response = await api.get('/health');
    logTest('服务器健康检查', response.status === 200, `状态: ${response.data.status}`);
    return response.data;
  } catch (error) {
    logTest('服务器健康检查', false, error.message);
    return null;
  }
}

async function testUserRegistration() {
  console.log('\n📝 测试用户注册功能');
  console.log('==================');

  // 测试正常注册
  const newUser = {
    username: `testuser_${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User',
    acceptTerms: true
  };

  try {
    const response = await api.post('/auth-enhanced/register', newUser);
    logTest('正常用户注册', response.status === 201 && response.data.success, 
      `用户: ${newUser.username}, 邮箱: ${newUser.email}`);
    
    if (response.data.success) {
      console.log(`   用户ID: ${response.data.data.user.id}`);
      console.log(`   Token: ${response.data.data.tokens.accessToken ? '已生成' : '未生成'}`);
    }
  } catch (error) {
    logTest('正常用户注册', false, error.response?.data?.message || error.message);
  }

  // 测试重复用户名
  try {
    await api.post('/auth-enhanced/register', {
      ...newUser,
      username: 'admin_test', // 已存在的用户名
      email: `unique${Date.now()}@example.com`
    });
    logTest('重复用户名拒绝', false, '应该拒绝重复用户名');
  } catch (error) {
    logTest('重复用户名拒绝', error.response?.status === 400, '正确拒绝重复用户名');
  }

  // 测试重复邮箱
  try {
    await api.post('/auth-enhanced/register', {
      ...newUser,
      username: `unique_${Date.now()}`,
      email: '<EMAIL>' // 已存在的邮箱
    });
    logTest('重复邮箱拒绝', false, '应该拒绝重复邮箱');
  } catch (error) {
    logTest('重复邮箱拒绝', error.response?.status === 400, '正确拒绝重复邮箱');
  }

  // 测试弱密码
  try {
    await api.post('/auth-enhanced/register', {
      ...newUser,
      username: `weakpass_${Date.now()}`,
      email: `weakpass${Date.now()}@example.com`,
      password: '123456' // 弱密码
    });
    logTest('弱密码拒绝', false, '应该拒绝弱密码');
  } catch (error) {
    logTest('弱密码拒绝', error.response?.status === 400, '正确拒绝弱密码');
  }
}

async function testUserLogin() {
  console.log('\n🔐 测试用户登录功能');
  console.log('==================');

  // 测试管理员登录
  try {
    const response = await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'Admin123!',
      rememberMe: false
    });

    const success = response.status === 200 && response.data.success;
    logTest('管理员邮箱登录', success, 
      success ? `用户: ${response.data.data.user.username}, 角色: ${response.data.data.user.role}` : '登录失败');
    
    if (success) {
      // 测试Token验证
      const token = response.data.data.tokens.accessToken;
      try {
        const meResponse = await api.get('/auth-enhanced/me', {
          headers: { Authorization: `Bearer ${token}` }
        });
        logTest('Token验证', meResponse.status === 200, 'Token有效性验证成功');
      } catch (tokenError) {
        logTest('Token验证', false, 'Token验证失败');
      }
    }
  } catch (error) {
    logTest('管理员邮箱登录', false, error.response?.data?.message || error.message);
  }

  // 测试用户名登录
  try {
    const response = await api.post('/auth-enhanced/login', {
      identifier: 'user_test1',
      password: 'User123!',
      rememberMe: true
    });

    const success = response.status === 200 && response.data.success;
    logTest('用户名登录', success, 
      success ? `用户: ${response.data.data.user.username}` : '登录失败');
  } catch (error) {
    logTest('用户名登录', false, error.response?.data?.message || error.message);
  }

  // 测试错误密码
  try {
    await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'WrongPassword123!',
      rememberMe: false
    });
    logTest('错误密码拒绝', false, '应该拒绝错误密码');
  } catch (error) {
    logTest('错误密码拒绝', error.response?.status === 401, '正确拒绝错误密码');
  }

  // 测试不存在用户
  try {
    await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'Password123!',
      rememberMe: false
    });
    logTest('不存在用户拒绝', false, '应该拒绝不存在的用户');
  } catch (error) {
    logTest('不存在用户拒绝', error.response?.status === 401, '正确拒绝不存在用户');
  }

  // 测试未验证用户
  try {
    const response = await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'Unverified123!',
      rememberMe: false
    });
    
    if (response.data.success) {
      logTest('未验证用户登录', true, '允许登录但需要验证提示');
    } else {
      logTest('未验证用户限制', true, '正确限制未验证用户');
    }
  } catch (error) {
    logTest('未验证用户处理', true, '正确处理未验证用户');
  }

  // 测试停用账户
  try {
    await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'Inactive123!',
      rememberMe: false
    });
    logTest('停用账户拒绝', false, '应该拒绝停用账户');
  } catch (error) {
    logTest('停用账户拒绝', error.response?.status === 401, '正确拒绝停用账户');
  }
}

async function testPasswordFeatures() {
  console.log('\n💪 测试密码相关功能');
  console.log('====================');

  // 测试密码强度验证
  const passwordTests = [
    { password: '123', expected: false, name: '弱密码(123)' },
    { password: 'password', expected: false, name: '常见密码' },
    { password: 'Password123', expected: false, name: '缺少特殊字符' },
    { password: 'Password123!', expected: true, name: '强密码' },
    { password: 'VeryStrongPassword123!@#', expected: true, name: '超强密码' }
  ];

  for (const test of passwordTests) {
    try {
      const response = await api.post('/auth-enhanced/check-password-strength', {
        password: test.password
      });

      const isValid = response.data.data.isValid;
      const strength = response.data.data.strength;
      logTest(test.name, isValid === test.expected, 
        `强度: ${strength}, 有效: ${isValid ? '是' : '否'}`);
    } catch (error) {
      logTest(test.name, false, error.message);
    }
  }

  // 测试忘记密码
  try {
    const response = await api.post('/auth-enhanced/forgot-password', {
      email: '<EMAIL>'
    });

    logTest('忘记密码请求', response.status === 200 && response.data.success, 
      '密码重置邮件发送请求');
  } catch (error) {
    logTest('忘记密码请求', false, error.response?.data?.message || error.message);
  }

  // 测试不存在邮箱的忘记密码
  try {
    await api.post('/auth-enhanced/forgot-password', {
      email: '<EMAIL>'
    });
    logTest('不存在邮箱密码重置', false, '应该拒绝不存在的邮箱');
  } catch (error) {
    logTest('不存在邮箱密码重置', error.response?.status === 404, '正确处理不存在邮箱');
  }
}

async function testEmailVerification() {
  console.log('\n📧 测试邮箱验证功能');
  console.log('====================');

  // 测试重发验证邮件
  try {
    const response = await api.post('/auth-enhanced/resend-verification', {
      email: '<EMAIL>'
    });

    logTest('重发验证邮件', response.status === 200 && response.data.success, 
      '验证邮件重发请求');
  } catch (error) {
    logTest('重发验证邮件', false, error.response?.data?.message || error.message);
  }

  // 测试已验证用户重发
  try {
    await api.post('/auth-enhanced/resend-verification', {
      email: '<EMAIL>'
    });
    logTest('已验证用户重发', false, '应该拒绝已验证用户');
  } catch (error) {
    logTest('已验证用户重发', error.response?.status === 400, '正确拒绝已验证用户');
  }
}

async function testTokenManagement() {
  console.log('\n🔑 测试Token管理功能');
  console.log('=====================');

  try {
    // 先登录获取tokens
    const loginResponse = await api.post('/auth-enhanced/login', {
      identifier: '<EMAIL>',
      password: 'User123!'
    });

    if (loginResponse.data.success) {
      const { accessToken, refreshToken } = loginResponse.data.data.tokens;

      // 测试Token刷新
      try {
        const refreshResponse = await api.post('/auth-enhanced/refresh-token', {
          refreshToken
        });

        logTest('Token刷新', refreshResponse.status === 200 && refreshResponse.data.success, 
          '新Token生成成功');
      } catch (error) {
        logTest('Token刷新', false, error.response?.data?.message || error.message);
      }

      // 测试无效Token刷新
      try {
        await api.post('/auth-enhanced/refresh-token', {
          refreshToken: 'invalid_token'
        });
        logTest('无效Token拒绝', false, '应该拒绝无效Token');
      } catch (error) {
        logTest('无效Token拒绝', error.response?.status === 401, '正确拒绝无效Token');
      }
    }
  } catch (error) {
    logTest('Token管理测试', false, '登录失败，无法测试Token管理');
  }
}

async function testRateLimiting() {
  console.log('\n🚦 测试速率限制功能');
  console.log('====================');

  // 快速连续请求测试速率限制
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(
      api.post('/auth-enhanced/login', {
        identifier: '<EMAIL>',
        password: 'wrongpassword'
      }).catch(err => err.response)
    );
  }

  try {
    const responses = await Promise.all(promises);
    const rateLimited = responses.some(response => 
      response?.status === 429 || 
      response?.data?.message?.includes('Too many')
    );
    
    logTest('速率限制', rateLimited, 
      rateLimited ? '速率限制正常工作' : '速率限制未触发（可能阈值较高）');
  } catch (error) {
    logTest('速率限制', false, error.message);
  }
}

async function generateTestReport() {
  console.log('\n📊 测试报告生成');
  console.log('================');

  const report = {
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: ((testResults.passed / testResults.total) * 100).toFixed(1)
    },
    timestamp: new Date().toISOString(),
    details: testResults.details,
    recommendations: []
  };

  // 生成建议
  if (testResults.failed === 0) {
    report.recommendations.push('🎉 所有测试通过！认证系统工作正常。');
  } else {
    report.recommendations.push('⚠️ 部分测试失败，请检查失败的功能。');
  }

  if (testResults.failed > testResults.total * 0.2) {
    report.recommendations.push('🔧 失败率较高，建议检查服务器配置和数据库连接。');
  }

  // 保存报告
  const fs = require('fs');
  const path = require('path');
  
  const reportsDir = path.join(__dirname, '../test-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  const reportFile = path.join(reportsDir, `auth-test-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  console.log(`📄 详细报告已保存: ${reportFile}`);
  return report;
}

async function runComprehensiveTests() {
  console.log('🚀 开始综合认证功能测试...\n');

  try {
    // 1. 服务器健康检查
    const healthData = await testServerHealth();
    if (!healthData) {
      console.log('\n❌ 服务器不可用，测试终止');
      return;
    }

    // 2. 用户注册测试
    await testUserRegistration();

    // 3. 用户登录测试
    await testUserLogin();

    // 4. 密码功能测试
    await testPasswordFeatures();

    // 5. 邮箱验证测试
    await testEmailVerification();

    // 6. Token管理测试
    await testTokenManagement();

    // 7. 速率限制测试
    await testRateLimiting();

    // 8. 生成测试报告
    const report = await generateTestReport();

    // 9. 显示总结
    console.log('\n📈 测试总结');
    console.log('===========');
    console.log(`总测试数: ${report.summary.total}`);
    console.log(`通过: ${report.summary.passed} ✅`);
    console.log(`失败: ${report.summary.failed} ❌`);
    console.log(`成功率: ${report.summary.successRate}%`);

    console.log('\n💡 建议:');
    report.recommendations.forEach(rec => console.log(`   ${rec}`));

    if (report.summary.failed === 0) {
      console.log('\n🎉 恭喜！所有认证功能测试通过！');
      console.log('🌐 可以开始使用前端界面进行手动测试');
      console.log('🔗 测试地址: http://localhost:3000/test-auth');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能');
    }

  } catch (error) {
    console.error('\n💥 测试执行失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  runComprehensiveTests()
    .then(() => {
      console.log('\n✅ 测试执行完成');
      process.exit(testResults.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('\n❌ 测试执行异常:', error);
      process.exit(1);
    });
}

module.exports = { runComprehensiveTests, testResults };
