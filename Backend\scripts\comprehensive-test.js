#!/usr/bin/env node

/**
 * 全面的系统测试脚本
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_BASE = 'http://localhost:3000';

class ComprehensiveTest {
  constructor() {
    this.userToken = null;
    this.adminToken = null;
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}]`;
    
    switch (type) {
      case 'success':
        console.log(`${prefix} ✅ ${message}`.green);
        break;
      case 'error':
        console.log(`${prefix} ❌ ${message}`.red);
        break;
      case 'warning':
        console.log(`${prefix} ⚠️  ${message}`.yellow);
        break;
      case 'info':
      default:
        console.log(`${prefix} ℹ️  ${message}`.blue);
        break;
    }
  }

  async makeRequest(method, url, data = null, token = null) {
    try {
      const config = {
        method,
        url: url.startsWith('http') ? url : `${API_BASE}${url}`,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return { success: true, data: response.data, status: response.status };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        status: error.response?.status || 0
      };
    }
  }

  async testBackendAuth() {
    this.log('🔐 测试后端认证系统', 'info');

    // 测试用户登录
    const loginResult = await this.makeRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'demo123456'
    });

    if (loginResult.success && loginResult.data.token) {
      this.userToken = loginResult.data.token;
      this.log('用户登录成功', 'success');
      this.testResults.push({ test: 'User Login', status: 'PASS' });

      // 测试token验证
      const meResult = await this.makeRequest('GET', '/auth/me', null, this.userToken);
      if (meResult.success) {
        this.log('Token验证成功', 'success');
        this.testResults.push({ test: 'Token Validation', status: 'PASS' });
      } else {
        this.log('Token验证失败', 'error');
        this.testResults.push({ test: 'Token Validation', status: 'FAIL' });
      }

      // 测试用户资料
      const profileResult = await this.makeRequest('GET', '/users/profile', null, this.userToken);
      if (profileResult.success) {
        this.log('用户资料获取成功', 'success');
        this.testResults.push({ test: 'User Profile', status: 'PASS' });
      } else {
        this.log('用户资料获取失败', 'error');
        this.testResults.push({ test: 'User Profile', status: 'FAIL' });
      }
    } else {
      this.log('用户登录失败', 'error');
      this.testResults.push({ test: 'User Login', status: 'FAIL' });
    }

    // 测试管理员登录
    const adminLoginResult = await this.makeRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });

    if (adminLoginResult.success && adminLoginResult.data.token) {
      this.adminToken = adminLoginResult.data.token;
      this.log('管理员登录成功', 'success');
      this.testResults.push({ test: 'Admin Login', status: 'PASS' });
    } else {
      this.log('管理员登录失败', 'error');
      this.testResults.push({ test: 'Admin Login', status: 'FAIL' });
    }
  }

  async testFrontendPages() {
    this.log('🌐 测试前端页面', 'info');

    const pages = [
      { path: '/', name: 'Home Page' },
      { path: '/login', name: 'Login Page' },
      { path: '/create', name: 'Create Page' },
      { path: '/explore', name: 'Explore Page' }
    ];

    for (const page of pages) {
      try {
        const response = await axios.get(`${FRONTEND_BASE}${page.path}`, {
          timeout: 5000,
          validateStatus: (status) => status < 500 // 接受重定向
        });

        if (response.status < 400) {
          this.log(`${page.name} 可访问 (${response.status})`, 'success');
          this.testResults.push({ test: page.name, status: 'PASS' });
        } else {
          this.log(`${page.name} 访问异常 (${response.status})`, 'warning');
          this.testResults.push({ test: page.name, status: 'WARN' });
        }
      } catch (error) {
        this.log(`${page.name} 无法访问: ${error.message}`, 'error');
        this.testResults.push({ test: page.name, status: 'FAIL' });
      }
    }
  }

  async testCreateFunctionality() {
    this.log('✍️ 测试创建功能', 'info');

    if (!this.userToken) {
      this.log('无用户token，跳过创建功能测试', 'warning');
      return;
    }

    // 测试文章创建API（如果存在）
    const createResult = await this.makeRequest('POST', '/articles', {
      title: '测试文章',
      content: '这是一篇测试文章的内容',
      category: 'Technology'
    }, this.userToken);

    if (createResult.success) {
      this.log('文章创建成功', 'success');
      this.testResults.push({ test: 'Article Creation', status: 'PASS' });
    } else {
      this.log(`文章创建失败: ${createResult.error}`, 'error');
      this.testResults.push({ test: 'Article Creation', status: 'FAIL' });
    }
  }

  async testAuthFlow() {
    this.log('🔄 测试完整认证流程', 'info');

    // 模拟前端认证流程
    const steps = [
      '1. 用户访问创建页面',
      '2. 检测未登录状态',
      '3. 重定向到登录页面',
      '4. 用户登录',
      '5. 获取用户信息',
      '6. 重定向回创建页面',
      '7. 验证访问权限'
    ];

    this.log('认证流程步骤:', 'info');
    steps.forEach(step => this.log(`   ${step}`, 'info'));

    // 实际测试流程
    if (this.userToken) {
      this.log('认证流程测试: 用户已登录，可以访问受保护页面', 'success');
      this.testResults.push({ test: 'Auth Flow', status: 'PASS' });
    } else {
      this.log('认证流程测试: 用户未登录，无法完成流程', 'error');
      this.testResults.push({ test: 'Auth Flow', status: 'FAIL' });
    }
  }

  printSummary() {
    this.log('📊 测试结果汇总', 'info');
    console.log('\n' + '='.repeat(60));
    console.log('测试结果汇总'.bold);
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const warned = this.testResults.filter(r => r.status === 'WARN').length;
    const total = this.testResults.length;

    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed}`.green);
    console.log(`失败: ${failed}`.red);
    console.log(`警告: ${warned}`.yellow);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    console.log('\n详细结果:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅ PASS'.green :
                    result.status === 'FAIL' ? '❌ FAIL'.red :
                    '⚠️  WARN'.yellow;
      console.log(`  ${result.test.padEnd(20)} ${status}`);
    });

    console.log('\n' + '='.repeat(60));
  }

  async run() {
    console.log('🚀 开始全面系统测试\n'.bold);

    await this.testBackendAuth();
    await this.testFrontendPages();
    await this.testCreateFunctionality();
    await this.testAuthFlow();

    this.printSummary();
  }
}

// 运行测试
const test = new ComprehensiveTest();
test.run().catch(error => {
  console.error('测试运行失败:', error);
  process.exit(1);
});
