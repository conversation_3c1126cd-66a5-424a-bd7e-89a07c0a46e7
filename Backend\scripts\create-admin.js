console.log('Creating admin user...');

const User = require('../models/User');

async function createAdmin() {
  try {
    // 检查admin用户是否已存在
    const existingAdmin = await User.findByEmailOrUsername('admin');
    if (existingAdmin) {
      console.log('Admin user already exists');
      process.exit(0);
    }
    
    // 创建admin用户
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      isEmailVerified: true,
      isActive: true
    });
    
    console.log('Admin user created successfully:');
    console.log(`ID: ${adminUser.id}`);
    console.log(`Username: ${adminUser.username}`);
    console.log(`Email: ${adminUser.email}`);
    console.log(`Role: ${adminUser.role}`);
    
    // 验证创建
    const verification = await User.findByEmailOrUsername('admin');
    console.log('\nVerification - User found:', verification ? 'YES' : 'NO');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdmin();
