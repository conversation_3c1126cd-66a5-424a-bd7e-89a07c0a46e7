#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function createAIModerationTables() {
  console.log('🤖 Creating AI moderation tables...');
  
  try {
    // Connect to database
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Create content_moderation_rules table
    const createModerationRulesQuery = `
      CREATE TABLE IF NOT EXISTS content_moderation_rules (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        "ruleType" VARCHAR(50) NOT NULL CHECK ("ruleType" IN (
          'keyword', 'regex', 'ai_sentiment', 'ai_toxicity', 
          'ai_spam', 'ai_nsfw', 'image_analysis', 'link_analysis', 
          'length_check', 'custom'
        )),
        "targetType" VARCHAR(50) NOT NULL DEFAULT 'all' CHECK ("targetType" IN (
          'article', 'comment', 'message', 'user_profile', 'image', 'all'
        )),
        "ruleConfig" JSONB NOT NULL DEFAULT '{}',
        action VARCHAR(50) NOT NULL DEFAULT 'flag' CHECK (action IN (
          'flag', 'auto_reject', 'require_review', 'auto_approve', 
          'quarantine', 'warn_user'
        )),
        severity VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (severity IN (
          'low', 'medium', 'high', 'critical'
        )),
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        priority INTEGER NOT NULL DEFAULT 100,
        "createdBy" INTEGER NOT NULL REFERENCES users(id),
        "lastModifiedBy" INTEGER REFERENCES users(id),
        "triggerCount" INTEGER NOT NULL DEFAULT 0,
        "lastTriggered" TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createModerationRulesQuery);
    console.log('✅ content_moderation_rules table created');
    
    // Create content_moderation_logs table
    const createModerationLogsQuery = `
      CREATE TABLE IF NOT EXISTS content_moderation_logs (
        id SERIAL PRIMARY KEY,
        "contentType" VARCHAR(50) NOT NULL CHECK ("contentType" IN (
          'article', 'comment', 'message', 'user_profile', 'image'
        )),
        "contentId" INTEGER NOT NULL,
        "ruleId" INTEGER REFERENCES content_moderation_rules(id),
        "moderationType" VARCHAR(50) NOT NULL DEFAULT 'automatic' CHECK ("moderationType" IN (
          'automatic', 'manual', 'appeal'
        )),
        action VARCHAR(50) NOT NULL CHECK (action IN (
          'approved', 'rejected', 'flagged', 'quarantined', 
          'warning_issued', 'user_notified', 'escalated'
        )),
        reason TEXT,
        details JSONB DEFAULT '{}',
        confidence FLOAT,
        "aiModel" VARCHAR(255),
        "moderatorId" INTEGER REFERENCES users(id),
        "authorId" INTEGER NOT NULL REFERENCES users(id),
        severity VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (severity IN (
          'low', 'medium', 'high', 'critical'
        )),
        status VARCHAR(50) NOT NULL DEFAULT 'processed' CHECK (status IN (
          'pending', 'processed', 'appealed', 'overturned', 'confirmed'
        )),
        "appealedAt" TIMESTAMP,
        "appealReason" TEXT,
        "reviewedAt" TIMESTAMP,
        "reviewedBy" INTEGER REFERENCES users(id),
        "originalContent" TEXT,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createModerationLogsQuery);
    console.log('✅ content_moderation_logs table created');
    
    // Create indexes
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      // Content moderation rules indexes
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_type ON content_moderation_rules("ruleType");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_target ON content_moderation_rules("targetType");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_active ON content_moderation_rules("isActive");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_priority ON content_moderation_rules(priority);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_severity ON content_moderation_rules(severity);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_created_by ON content_moderation_rules("createdBy");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_rules_active_priority ON content_moderation_rules("isActive", priority);',
      
      // Content moderation logs indexes
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_content ON content_moderation_logs("contentType", "contentId");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_rule_id ON content_moderation_logs("ruleId");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_moderation_type ON content_moderation_logs("moderationType");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_action ON content_moderation_logs(action);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_moderator_id ON content_moderation_logs("moderatorId");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_author_id ON content_moderation_logs("authorId");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_severity ON content_moderation_logs(severity);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_status ON content_moderation_logs(status);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_created_at ON content_moderation_logs("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_content_action ON content_moderation_logs("contentType", "contentId", action);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_author_time ON content_moderation_logs("authorId", "createdAt");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // Insert default moderation rules
    console.log('📝 Creating default moderation rules...');
    
    const defaultRules = [
      {
        name: 'Basic Spam Keywords',
        description: 'Detects common spam keywords',
        ruleType: 'keyword',
        targetType: 'all',
        ruleConfig: JSON.stringify({
          keywords: ['spam', 'scam', 'fraud', 'fake', 'click here', 'buy now', 'limited time'],
          caseSensitive: false,
          wholeWordOnly: true
        }),
        action: 'flag',
        severity: 'medium',
        priority: 100
      },
      {
        name: 'Toxic Language Detection',
        description: 'AI-based toxicity detection',
        ruleType: 'ai_toxicity',
        targetType: 'all',
        ruleConfig: JSON.stringify({
          threshold: 0.7,
          model: 'toxicity-v1',
          confidence: 0.8
        }),
        action: 'require_review',
        severity: 'high',
        priority: 50
      },
      {
        name: 'Excessive Length Check',
        description: 'Prevents extremely long content',
        ruleType: 'length_check',
        targetType: 'comment',
        ruleConfig: JSON.stringify({
          maxLength: 5000,
          minLength: 1
        }),
        action: 'flag',
        severity: 'low',
        priority: 200
      },
      {
        name: 'NSFW Content Detection',
        description: 'Detects not-safe-for-work content',
        ruleType: 'ai_nsfw',
        targetType: 'all',
        ruleConfig: JSON.stringify({
          threshold: 0.6,
          model: 'nsfw-v1'
        }),
        action: 'require_review',
        severity: 'high',
        priority: 30
      },
      {
        name: 'Hate Speech Keywords',
        description: 'Detects hate speech and discriminatory language',
        ruleType: 'keyword',
        targetType: 'all',
        ruleConfig: JSON.stringify({
          keywords: ['hate', 'discrimination', 'harassment', 'abuse', 'threat'],
          caseSensitive: false,
          wholeWordOnly: true
        }),
        action: 'auto_reject',
        severity: 'critical',
        priority: 10
      }
    ];
    
    // Get admin user ID (assuming first admin user)
    const adminUser = await sequelize.query(
      'SELECT id FROM users WHERE role = \'admin\' ORDER BY id LIMIT 1',
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const adminUserId = adminUser.length > 0 ? adminUser[0].id : 1;
    
    for (const rule of defaultRules) {
      try {
        await sequelize.query(`
          INSERT INTO content_moderation_rules (
            name, description, "ruleType", "targetType", "ruleConfig", 
            action, severity, priority, "createdBy", "createdAt", "updatedAt"
          ) VALUES (
            :name, :description, :ruleType, :targetType, :ruleConfig,
            :action, :severity, :priority, :createdBy, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `, {
          replacements: {
            ...rule,
            createdBy: adminUserId
          }
        });
        console.log(`✅ Default rule created: ${rule.name}`);
      } catch (error) {
        console.log(`⚠️ Failed to create rule ${rule.name}:`, error.message);
      }
    }
    
    console.log('🎉 AI moderation tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create AI moderation tables:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createAIModerationTables()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createAIModerationTables };
