#!/usr/bin/env node

const { sequelize } = require('../config/database');

async function createAnalyticsTables() {
  console.log('📊 Creating analytics and reporting system tables...');
  
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // Create analytics table
    const createAnalyticsQuery = `
      CREATE TABLE IF NOT EXISTS analytics (
        id SERIAL PRIMARY KEY,
        "eventType" VARCHAR(50) NOT NULL,
        "userId" INTEGER,
        "sessionId" VARCHAR(255),
        "targetType" VARCHAR(50),
        "targetId" INTEGER,
        properties JSONB DEFAULT '{}',
        value FLOAT,
        url TEXT,
        referer TEXT,
        "userAgent" TEXT,
        "ipAddress" INET,
        location JSONB,
        device JSONB,
        browser JSONB,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createAnalyticsQuery);
    console.log('✅ analytics table created');
    
    // Create reports table
    const createReportsQuery = `
      CREATE TABLE IF NOT EXISTS reports (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(50) NOT NULL,
        category VARCHAR(255),
        config JSONB DEFAULT '{}',
        query TEXT,
        "dataSource" JSONB DEFAULT '{}',
        visualization JSONB DEFAULT '{}',
        filters JSONB DEFAULT '{}',
        schedule JSONB,
        recipients INTEGER[],
        "isPublic" BOOLEAN DEFAULT false,
        "isActive" BOOLEAN DEFAULT true,
        "createdBy" INTEGER NOT NULL,
        "lastGeneratedAt" TIMESTAMP,
        "generationCount" INTEGER DEFAULT 0,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createReportsQuery);
    console.log('✅ reports table created');
    
    // Create report_instances table for storing generated report data
    const createReportInstancesQuery = `
      CREATE TABLE IF NOT EXISTS report_instances (
        id SERIAL PRIMARY KEY,
        "reportId" INTEGER NOT NULL,
        data JSONB NOT NULL,
        parameters JSONB DEFAULT '{}',
        "generatedBy" INTEGER,
        "generationTime" INTEGER,
        "expiresAt" TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createReportInstancesQuery);
    console.log('✅ report_instances table created');
    
    // Create analytics_summary table for pre-aggregated data
    const createAnalyticsSummaryQuery = `
      CREATE TABLE IF NOT EXISTS analytics_summary (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL,
        "eventType" VARCHAR(50) NOT NULL,
        "totalEvents" INTEGER DEFAULT 0,
        "uniqueUsers" INTEGER DEFAULT 0,
        "uniqueSessions" INTEGER DEFAULT 0,
        "avgValue" FLOAT,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(date, "eventType")
      );
    `;
    
    await sequelize.query(createAnalyticsSummaryQuery);
    console.log('✅ analytics_summary table created');
    
    // Create basic indexes
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      // Analytics table indexes
      'CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics("eventType");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON analytics("userId");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_session_id ON analytics("sessionId");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_target ON analytics("targetType", "targetId");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON analytics(timestamp);',
      'CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_event_time ON analytics("eventType", timestamp);',
      'CREATE INDEX IF NOT EXISTS idx_analytics_user_time ON analytics("userId", timestamp);',
      
      // Reports table indexes
      'CREATE INDEX IF NOT EXISTS idx_reports_type ON reports(type);',
      'CREATE INDEX IF NOT EXISTS idx_reports_category ON reports(category);',
      'CREATE INDEX IF NOT EXISTS idx_reports_created_by ON reports("createdBy");',
      'CREATE INDEX IF NOT EXISTS idx_reports_is_public ON reports("isPublic");',
      'CREATE INDEX IF NOT EXISTS idx_reports_is_active ON reports("isActive");',
      'CREATE INDEX IF NOT EXISTS idx_reports_last_generated_at ON reports("lastGeneratedAt");',
      
      // Report instances table indexes
      'CREATE INDEX IF NOT EXISTS idx_report_instances_report_id ON report_instances("reportId");',
      'CREATE INDEX IF NOT EXISTS idx_report_instances_generated_by ON report_instances("generatedBy");',
      'CREATE INDEX IF NOT EXISTS idx_report_instances_created_at ON report_instances("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_report_instances_expires_at ON report_instances("expiresAt");',
      
      // Analytics summary table indexes
      'CREATE INDEX IF NOT EXISTS idx_analytics_summary_date ON analytics_summary(date);',
      'CREATE INDEX IF NOT EXISTS idx_analytics_summary_event_type ON analytics_summary("eventType");',
      'CREATE INDEX IF NOT EXISTS idx_analytics_summary_date_event ON analytics_summary(date, "eventType");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // Insert sample analytics data
    console.log('📝 Creating sample analytics data...');
    
    try {
      // Insert sample page view events
      await sequelize.query(`
        INSERT INTO analytics ("eventType", "userId", "sessionId", url, "userAgent", timestamp)
        SELECT 
          'page_view',
          (random() * 10 + 1)::integer,
          'session_' || (random() * 100)::integer,
          '/article/' || (random() * 50 + 1)::integer,
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          CURRENT_TIMESTAMP - INTERVAL '1 day' * random() * 7
        FROM generate_series(1, 100);
      `);
      
      // Insert sample article view events
      await sequelize.query(`
        INSERT INTO analytics ("eventType", "userId", "sessionId", "targetType", "targetId", timestamp)
        SELECT 
          'article_view',
          (random() * 10 + 1)::integer,
          'session_' || (random() * 100)::integer,
          'article',
          (random() * 20 + 1)::integer,
          CURRENT_TIMESTAMP - INTERVAL '1 day' * random() * 7
        FROM generate_series(1, 200);
      `);
      
      console.log('✅ Sample analytics data created');
    } catch (error) {
      console.log('⚠️ Failed to create sample analytics data:', error.message);
    }
    
    // Insert default reports
    console.log('📊 Creating default reports...');
    
    const defaultReports = [
      {
        name: 'Daily Page Views',
        description: 'Daily page view statistics',
        type: 'analytics',
        category: 'Traffic',
        config: JSON.stringify({
          eventType: 'page_view',
          groupBy: 'day',
          defaultParams: {
            days: 30
          }
        })
      },
      {
        name: 'User Behavior Analysis',
        description: 'Analysis of user behavior patterns',
        type: 'user_behavior',
        category: 'Users',
        config: JSON.stringify({
          defaultParams: {
            days: 30
          }
        })
      },
      {
        name: 'Content Performance',
        description: 'Performance metrics for articles',
        type: 'content',
        category: 'Content',
        config: JSON.stringify({
          targetType: 'article',
          defaultParams: {
            days: 30,
            limit: 20
          }
        })
      },
      {
        name: 'System Performance',
        description: 'System performance and error tracking',
        type: 'performance',
        category: 'System',
        config: JSON.stringify({
          defaultParams: {
            days: 7
          }
        })
      }
    ];
    
    // Get admin user ID
    const adminUser = await sequelize.query(
      'SELECT id FROM users WHERE role = \'admin\' ORDER BY id LIMIT 1',
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const adminUserId = adminUser.length > 0 ? adminUser[0].id : 1;
    
    for (const report of defaultReports) {
      try {
        await sequelize.query(`
          INSERT INTO reports (
            name, description, type, category, config, "createdBy", "isPublic", "createdAt", "updatedAt"
          ) VALUES (
            :name, :description, :type, :category, :config, :createdBy, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `, {
          replacements: {
            ...report,
            createdBy: adminUserId
          }
        });
        console.log(`✅ Default report created: ${report.name}`);
      } catch (error) {
        console.log(`⚠️ Failed to create report ${report.name}:`, error.message);
      }
    }
    
    // Create analytics summary for the past week
    console.log('📈 Creating analytics summary data...');
    
    try {
      await sequelize.query(`
        INSERT INTO analytics_summary (date, "eventType", "totalEvents", "uniqueUsers", "uniqueSessions")
        SELECT 
          DATE(timestamp) as date,
          "eventType",
          COUNT(*) as "totalEvents",
          COUNT(DISTINCT "userId") as "uniqueUsers",
          COUNT(DISTINCT "sessionId") as "uniqueSessions"
        FROM analytics 
        WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY DATE(timestamp), "eventType"
        ON CONFLICT (date, "eventType") DO UPDATE SET
          "totalEvents" = EXCLUDED."totalEvents",
          "uniqueUsers" = EXCLUDED."uniqueUsers",
          "uniqueSessions" = EXCLUDED."uniqueSessions",
          "updatedAt" = CURRENT_TIMESTAMP;
      `);
      console.log('✅ Analytics summary data created');
    } catch (error) {
      console.log('⚠️ Failed to create analytics summary:', error.message);
    }
    
    console.log('🎉 Analytics and reporting system tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create analytics tables:', error.message);
    console.error('Error details:', error);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createAnalyticsTables()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createAnalyticsTables };
