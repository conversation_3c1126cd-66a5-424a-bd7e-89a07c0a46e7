#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function createEmailLogsTable() {
  console.log('📧 Creating email_logs table...');
  
  try {
    // Connect to database
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Create email_logs table
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS email_logs (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER REFERENCES users(id) ON DELETE SET NULL,
        recipient VARCHAR(255) NOT NULL,
        sender VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        template VARCHAR(255) NOT NULL,
        "templateData" JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed', 'spam', 'unsubscribed')),
        provider VARCHAR(100) NOT NULL,
        "messageId" VARCHAR(255) UNIQUE,
        "providerResponse" JSONB,
        "errorMessage" TEXT,
        "retryCount" INTEGER DEFAULT 0,
        "maxRetries" INTEGER DEFAULT 3,
        "scheduledAt" TIMESTAMP,
        "sentAt" TIMESTAMP,
        "deliveredAt" TIMESTAMP,
        "openedAt" TIMESTAMP,
        "clickedAt" TIMESTAMP,
        "bouncedAt" TIMESTAMP,
        "bounceReason" VARCHAR(255),
        priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        tags TEXT[],
        metadata JSONB DEFAULT '{}',
        "ipAddress" INET,
        "userAgent" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createTableQuery);
    console.log('✅ email_logs table created successfully');
    
    // Create indexes
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs("userId");',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_template ON email_logs(template);',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_message_id ON email_logs("messageId");',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_sent_at ON email_logs("sentAt");',
      'CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs("createdAt");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    console.log('🎉 Email logs table setup completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create email_logs table:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createEmailLogsTable()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createEmailLogsTable };
