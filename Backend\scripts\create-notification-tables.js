#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function createNotificationTables() {
  console.log('🔔 Creating notification system tables...');
  
  try {
    // Connect to database
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Update notifications table with new fields
    const updateNotificationsQuery = `
      ALTER TABLE notifications 
      ADD COLUMN IF NOT EXISTS "relatedType" VARCHAR(50) CHECK ("relatedType" IN ('article', 'comment', 'user', 'message', 'system')),
      ADD COLUMN IF NOT EXISTS "relatedId" INTEGER,
      ADD COLUMN IF NOT EXISTS "senderId" INTEGER REFERENCES users(id),
      ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
      ADD COLUMN IF NOT EXISTS channels TEXT[] DEFAULT ARRAY['in_app'],
      ADD COLUMN IF NOT EXISTS "isDelivered" BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS "deliveredAt" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "deliveryStatus" JSONB DEFAULT '{}',
      ADD COLUMN IF NOT EXISTS "scheduledAt" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "expiresAt" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "actionUrl" VARCHAR(255),
      ADD COLUMN IF NOT EXISTS "imageUrl" VARCHAR(255),
      ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
    `;
    
    try {
      await sequelize.query(updateNotificationsQuery);
      console.log('✅ notifications table updated with new fields');
    } catch (error) {
      console.log('⚠️ Some notification fields may already exist:', error.message);
    }
    
    // Update notification type enum to include new types
    const updateNotificationTypeQuery = `
      ALTER TYPE notification_type ADD VALUE IF NOT EXISTS 'mention';
      ALTER TYPE notification_type ADD VALUE IF NOT EXISTS 'warning';
      ALTER TYPE notification_type ADD VALUE IF NOT EXISTS 'achievement';
      ALTER TYPE notification_type ADD VALUE IF NOT EXISTS 'update';
    `;
    
    try {
      await sequelize.query(updateNotificationTypeQuery);
      console.log('✅ notification type enum updated');
    } catch (error) {
      console.log('⚠️ Notification type enum update failed:', error.message);
    }
    
    // Create notification_templates table
    const createNotificationTemplatesQuery = `
      CREATE TABLE IF NOT EXISTS notification_templates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        type VARCHAR(50) NOT NULL,
        "titleTemplate" TEXT NOT NULL,
        "messageTemplate" TEXT NOT NULL,
        "emailTemplate" TEXT,
        "pushTemplate" TEXT,
        variables JSONB DEFAULT '{}',
        "isActive" BOOLEAN DEFAULT true,
        "createdBy" INTEGER REFERENCES users(id),
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createNotificationTemplatesQuery);
    console.log('✅ notification_templates table created');
    
    // Create notification_settings table
    const createNotificationSettingsQuery = `
      CREATE TABLE IF NOT EXISTS notification_settings (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "notificationType" VARCHAR(50) NOT NULL,
        "inApp" BOOLEAN DEFAULT true,
        email BOOLEAN DEFAULT true,
        push BOOLEAN DEFAULT true,
        sms BOOLEAN DEFAULT false,
        "quietHoursStart" TIME,
        "quietHoursEnd" TIME,
        timezone VARCHAR(50) DEFAULT 'UTC',
        frequency VARCHAR(20) DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'hourly', 'daily', 'weekly')),
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("userId", "notificationType")
      );
    `;
    
    await sequelize.query(createNotificationSettingsQuery);
    console.log('✅ notification_settings table created');
    
    // Create notification_batches table for bulk operations
    const createNotificationBatchesQuery = `
      CREATE TABLE IF NOT EXISTS notification_batches (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        "targetCriteria" JSONB NOT NULL,
        "notificationData" JSONB NOT NULL,
        status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
        "totalRecipients" INTEGER DEFAULT 0,
        "sentCount" INTEGER DEFAULT 0,
        "failedCount" INTEGER DEFAULT 0,
        "scheduledAt" TIMESTAMP,
        "startedAt" TIMESTAMP,
        "completedAt" TIMESTAMP,
        "createdBy" INTEGER NOT NULL REFERENCES users(id),
        "errorMessage" TEXT,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createNotificationBatchesQuery);
    console.log('✅ notification_batches table created');
    
    // Create indexes
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      // Notifications indexes
      'CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications("userId");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);',
      'CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications("isRead");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_is_delivered ON notifications("isDelivered");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);',
      'CREATE INDEX IF NOT EXISTS idx_notifications_scheduled_at ON notifications("scheduledAt");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications("expiresAt");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_related ON notifications("relatedType", "relatedId");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_sender_id ON notifications("senderId");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON notifications("userId", "isRead");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_user_time ON notifications("userId", "createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_notifications_type_time ON notifications(type, "createdAt");',
      
      // Notification templates indexes
      'CREATE INDEX IF NOT EXISTS idx_notification_templates_name ON notification_templates(name);',
      'CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);',
      'CREATE INDEX IF NOT EXISTS idx_notification_templates_active ON notification_templates("isActive");',
      
      // Notification settings indexes
      'CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings("userId");',
      'CREATE INDEX IF NOT EXISTS idx_notification_settings_type ON notification_settings("notificationType");',
      
      // Notification batches indexes
      'CREATE INDEX IF NOT EXISTS idx_notification_batches_status ON notification_batches(status);',
      'CREATE INDEX IF NOT EXISTS idx_notification_batches_scheduled_at ON notification_batches("scheduledAt");',
      'CREATE INDEX IF NOT EXISTS idx_notification_batches_created_by ON notification_batches("createdBy");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // Insert default notification templates
    console.log('📝 Creating default notification templates...');
    
    const defaultTemplates = [
      {
        name: 'like_notification',
        type: 'like',
        titleTemplate: 'New Like',
        messageTemplate: '{{senderName}} liked your {{contentType}}',
        emailTemplate: 'Hello {{recipientName}}, {{senderName}} liked your {{contentType}}. Check it out!',
        pushTemplate: '{{senderName}} liked your {{contentType}}',
        variables: JSON.stringify(['senderName', 'recipientName', 'contentType'])
      },
      {
        name: 'comment_notification',
        type: 'comment',
        titleTemplate: 'New Comment',
        messageTemplate: '{{senderName}} commented on your {{contentType}}',
        emailTemplate: 'Hello {{recipientName}}, {{senderName}} commented on your {{contentType}}: "{{commentText}}"',
        pushTemplate: '{{senderName}} commented on your {{contentType}}',
        variables: JSON.stringify(['senderName', 'recipientName', 'contentType', 'commentText'])
      },
      {
        name: 'follow_notification',
        type: 'follow',
        titleTemplate: 'New Follower',
        messageTemplate: '{{senderName}} started following you',
        emailTemplate: 'Hello {{recipientName}}, {{senderName}} started following you on Newzora!',
        pushTemplate: '{{senderName}} started following you',
        variables: JSON.stringify(['senderName', 'recipientName'])
      },
      {
        name: 'mention_notification',
        type: 'mention',
        titleTemplate: 'You were mentioned',
        messageTemplate: '{{senderName}} mentioned you in a {{contentType}}',
        emailTemplate: 'Hello {{recipientName}}, {{senderName}} mentioned you in a {{contentType}}.',
        pushTemplate: '{{senderName}} mentioned you',
        variables: JSON.stringify(['senderName', 'recipientName', 'contentType'])
      },
      {
        name: 'system_notification',
        type: 'system',
        titleTemplate: 'System Notification',
        messageTemplate: '{{message}}',
        emailTemplate: 'Hello {{recipientName}}, {{message}}',
        pushTemplate: '{{message}}',
        variables: JSON.stringify(['recipientName', 'message'])
      }
    ];
    
    // Get admin user ID
    const adminUser = await sequelize.query(
      'SELECT id FROM users WHERE role = \'admin\' ORDER BY id LIMIT 1',
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const adminUserId = adminUser.length > 0 ? adminUser[0].id : 1;
    
    for (const template of defaultTemplates) {
      try {
        await sequelize.query(`
          INSERT INTO notification_templates (
            name, type, "titleTemplate", "messageTemplate", "emailTemplate", 
            "pushTemplate", variables, "createdBy", "createdAt", "updatedAt"
          ) VALUES (
            :name, :type, :titleTemplate, :messageTemplate, :emailTemplate,
            :pushTemplate, :variables, :createdBy, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          ) ON CONFLICT (name) DO NOTHING
        `, {
          replacements: {
            ...template,
            createdBy: adminUserId
          }
        });
        console.log(`✅ Default template created: ${template.name}`);
      } catch (error) {
        console.log(`⚠️ Failed to create template ${template.name}:`, error.message);
      }
    }
    
    console.log('🎉 Notification system tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create notification tables:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createNotificationTables()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createNotificationTables };
