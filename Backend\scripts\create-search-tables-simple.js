#!/usr/bin/env node

const { sequelize } = require('../config/database');

async function createSearchTablesSimple() {
  console.log('🔍 Creating search system tables (simplified)...');
  
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // Create search_indexes table (simplified)
    const createSearchIndexesQuery = `
      CREATE TABLE IF NOT EXISTS search_indexes (
        id SERIAL PRIMARY KEY,
        "contentType" VARCHAR(50) NOT NULL,
        "contentId" INTEGER NOT NULL,
        title TEXT,
        content TEXT,
        keywords TEXT[],
        tags TEXT[],
        category VARCHAR(255),
        "authorId" INTEGER,
        "publishedAt" TIMESTAMP,
        popularity FLOAT DEFAULT 0.0,
        "qualityScore" FLOAT DEFAULT 0.0,
        "relevanceScore" FLOAT DEFAULT 0.0,
        "viewCount" INTEGER DEFAULT 0,
        "likeCount" INTEGER DEFAULT 0,
        "commentCount" INTEGER DEFAULT 0,
        "shareCount" INTEGER DEFAULT 0,
        "isActive" BOOLEAN DEFAULT true,
        language VARCHAR(10) DEFAULT 'zh',
        metadata JSONB DEFAULT '{}',
        "lastIndexedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("contentType", "contentId")
      );
    `;
    
    await sequelize.query(createSearchIndexesQuery);
    console.log('✅ search_indexes table created');
    
    // Create search_histories table
    const createSearchHistoriesQuery = `
      CREATE TABLE IF NOT EXISTS search_histories (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER,
        "sessionId" VARCHAR(255),
        query TEXT NOT NULL,
        "normalizedQuery" TEXT,
        "searchType" VARCHAR(50) DEFAULT 'fulltext',
        filters JSONB DEFAULT '{}',
        "resultCount" INTEGER DEFAULT 0,
        "clickedResults" INTEGER[],
        "responseTime" INTEGER,
        "ipAddress" INET,
        "userAgent" TEXT,
        referer TEXT,
        language VARCHAR(10) DEFAULT 'zh',
        location JSONB,
        device JSONB,
        "isSuccessful" BOOLEAN DEFAULT true,
        "errorMessage" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createSearchHistoriesQuery);
    console.log('✅ search_histories table created');
    
    // Create recommendation_logs table
    const createRecommendationLogsQuery = `
      CREATE TABLE IF NOT EXISTS recommendation_logs (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL,
        "contentType" VARCHAR(50) NOT NULL,
        "contentId" INTEGER NOT NULL,
        algorithm VARCHAR(50) NOT NULL,
        score FLOAT NOT NULL,
        reason TEXT,
        clicked BOOLEAN DEFAULT false,
        "clickedAt" TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createRecommendationLogsQuery);
    console.log('✅ recommendation_logs table created');
    
    // Create basic indexes
    console.log('📊 Creating basic indexes...');
    
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_content_type ON search_indexes("contentType");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_author_id ON search_indexes("authorId");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_category ON search_indexes(category);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_popularity ON search_indexes(popularity);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_is_active ON search_indexes("isActive");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_user_id ON search_histories("userId");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_query ON search_histories(query);',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_created_at ON search_histories("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_user_id ON recommendation_logs("userId");',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_content ON recommendation_logs("contentType", "contentId");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // Insert sample data for existing articles
    console.log('📝 Creating search indexes for existing articles...');
    
    try {
      await sequelize.query(`
        INSERT INTO search_indexes (
          "contentType", "contentId", title, content, category, "authorId", 
          "publishedAt", "viewCount", "isActive", language
        )
        SELECT 
          'article' as "contentType",
          a.id as "contentId",
          a.title,
          LEFT(a.content, 1000) as content,
          a.category,
          a."authorId",
          a."publishedAt",
          COALESCE(a.views, 0) as "viewCount",
          a.published as "isActive",
          'zh' as language
        FROM articles a
        WHERE a.published = true
        ON CONFLICT ("contentType", "contentId") DO UPDATE SET
          title = EXCLUDED.title,
          content = EXCLUDED.content,
          category = EXCLUDED.category,
          "authorId" = EXCLUDED."authorId",
          "publishedAt" = EXCLUDED."publishedAt",
          "viewCount" = EXCLUDED."viewCount",
          "isActive" = EXCLUDED."isActive",
          "lastIndexedAt" = CURRENT_TIMESTAMP;
      `);
      console.log('✅ Search indexes created for existing articles');
    } catch (error) {
      console.log('⚠️ Failed to create search indexes for articles:', error.message);
    }
    
    console.log('🎉 Search system tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create search tables:', error.message);
    console.error('Error details:', error);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createSearchTablesSimple()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSearchTablesSimple };
