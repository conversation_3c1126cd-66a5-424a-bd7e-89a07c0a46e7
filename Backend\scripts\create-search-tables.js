#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function createSearchTables() {
  console.log('🔍 Creating search and recommendation system tables...');
  
  try {
    // Connect to database
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Create search_indexes table
    const createSearchIndexesQuery = `
      CREATE TABLE IF NOT EXISTS search_indexes (
        id SERIAL PRIMARY KEY,
        "contentType" VARCHAR(50) NOT NULL CHECK ("contentType" IN ('article', 'user', 'comment', 'tag')),
        "contentId" INTEGER NOT NULL,
        title TEXT,
        content TEXT,
        "searchVector" TSVECTOR,
        keywords TEXT[],
        tags TEXT[],
        category VARCHAR(255),
        "authorId" INTEGER REFERENCES users(id),
        "publishedAt" TIMESTAMP,
        popularity FLOAT DEFAULT 0.0,
        "qualityScore" FLOAT DEFAULT 0.0,
        "relevanceScore" FLOAT DEFAULT 0.0,
        "viewCount" INTEGER DEFAULT 0,
        "likeCount" INTEGER DEFAULT 0,
        "commentCount" INTEGER DEFAULT 0,
        "shareCount" INTEGER DEFAULT 0,
        "isActive" BOOLEAN DEFAULT true,
        language VARCHAR(10) DEFAULT 'zh',
        metadata JSONB DEFAULT '{}',
        "lastIndexedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("contentType", "contentId")
      );
    `;
    
    await sequelize.query(createSearchIndexesQuery);
    console.log('✅ search_indexes table created');
    
    // Create search_histories table
    const createSearchHistoriesQuery = `
      CREATE TABLE IF NOT EXISTS search_histories (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER REFERENCES users(id),
        "sessionId" VARCHAR(255),
        query TEXT NOT NULL,
        "normalizedQuery" TEXT,
        "searchType" VARCHAR(50) DEFAULT 'fulltext' CHECK ("searchType" IN ('fulltext', 'keyword', 'tag', 'user', 'advanced')),
        filters JSONB DEFAULT '{}',
        "resultCount" INTEGER DEFAULT 0,
        "clickedResults" INTEGER[],
        "responseTime" INTEGER,
        "ipAddress" INET,
        "userAgent" TEXT,
        referer TEXT,
        language VARCHAR(10) DEFAULT 'zh',
        location JSONB,
        device JSONB,
        "isSuccessful" BOOLEAN DEFAULT true,
        "errorMessage" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createSearchHistoriesQuery);
    console.log('✅ search_histories table created');
    
    // Create recommendation_logs table
    const createRecommendationLogsQuery = `
      CREATE TABLE IF NOT EXISTS recommendation_logs (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL REFERENCES users(id),
        "contentType" VARCHAR(50) NOT NULL,
        "contentId" INTEGER NOT NULL,
        algorithm VARCHAR(50) NOT NULL,
        score FLOAT NOT NULL,
        reason TEXT,
        clicked BOOLEAN DEFAULT false,
        "clickedAt" TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createRecommendationLogsQuery);
    console.log('✅ recommendation_logs table created');
    
    // Create search_analytics table
    const createSearchAnalyticsQuery = `
      CREATE TABLE IF NOT EXISTS search_analytics (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL,
        "totalSearches" INTEGER DEFAULT 0,
        "uniqueUsers" INTEGER DEFAULT 0,
        "uniqueSessions" INTEGER DEFAULT 0,
        "avgResultsPerSearch" FLOAT DEFAULT 0,
        "avgResponseTime" FLOAT DEFAULT 0,
        "zeroResultSearches" INTEGER DEFAULT 0,
        "failedSearches" INTEGER DEFAULT 0,
        "topQueries" JSONB DEFAULT '[]',
        "topCategories" JSONB DEFAULT '[]',
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(date)
      );
    `;
    
    await sequelize.query(createSearchAnalyticsQuery);
    console.log('✅ search_analytics table created');
    
    // Create indexes
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      // Search indexes table indexes
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_content_type ON search_indexes("contentType");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_author_id ON search_indexes("authorId");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_category ON search_indexes(category);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_published_at ON search_indexes("publishedAt");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_popularity ON search_indexes(popularity);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_quality_score ON search_indexes("qualityScore");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_relevance_score ON search_indexes("relevanceScore");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_is_active ON search_indexes("isActive");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_language ON search_indexes(language);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_last_indexed_at ON search_indexes("lastIndexedAt");',
      
      // Full-text search indexes
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_search_vector ON search_indexes USING gin("searchVector");',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_keywords ON search_indexes USING gin(keywords);',
      'CREATE INDEX IF NOT EXISTS idx_search_indexes_tags ON search_indexes USING gin(tags);',
      
      // Search histories table indexes
      'CREATE INDEX IF NOT EXISTS idx_search_histories_user_id ON search_histories("userId");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_session_id ON search_histories("sessionId");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_query ON search_histories(query);',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_normalized_query ON search_histories("normalizedQuery");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_search_type ON search_histories("searchType");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_created_at ON search_histories("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_is_successful ON search_histories("isSuccessful");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_user_time ON search_histories("userId", "createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_search_histories_query_time ON search_histories(query, "createdAt");',
      
      // Recommendation logs table indexes
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_user_id ON recommendation_logs("userId");',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_content ON recommendation_logs("contentType", "contentId");',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_algorithm ON recommendation_logs(algorithm);',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_clicked ON recommendation_logs(clicked);',
      'CREATE INDEX IF NOT EXISTS idx_recommendation_logs_created_at ON recommendation_logs("createdAt");',
      
      // Search analytics table indexes
      'CREATE INDEX IF NOT EXISTS idx_search_analytics_date ON search_analytics(date);'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // Create trigger function to update search vector (simplified)
    console.log('🔧 Setting up search vector update...');

    try {
      const createTriggerFunctionQuery = `
        CREATE OR REPLACE FUNCTION update_search_vector()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW."searchVector" :=
            setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(NEW.content, '')), 'B') ||
            setweight(to_tsvector('simple', array_to_string(COALESCE(NEW.keywords, ARRAY[]::text[]), ' ')), 'C') ||
            setweight(to_tsvector('simple', array_to_string(COALESCE(NEW.tags, ARRAY[]::text[]), ' ')), 'D');
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `;

      await sequelize.query(createTriggerFunctionQuery);
      console.log('✅ Search vector trigger function created');

      // Create trigger
      const createTriggerQuery = `
        DROP TRIGGER IF EXISTS search_vector_update ON search_indexes;
        CREATE TRIGGER search_vector_update
          BEFORE INSERT OR UPDATE ON search_indexes
          FOR EACH ROW EXECUTE FUNCTION update_search_vector();
      `;

      await sequelize.query(createTriggerQuery);
      console.log('✅ Search vector trigger created');
    } catch (error) {
      console.log('⚠️ Search vector setup failed:', error.message);
    }
    
    // Insert sample search indexes for existing articles
    console.log('📝 Creating search indexes for existing articles...');
    
    try {
      await sequelize.query(`
        INSERT INTO search_indexes (
          "contentType", "contentId", title, content, keywords, tags, 
          category, "authorId", "publishedAt", "viewCount", "likeCount", 
          "commentCount", "shareCount", "isActive", language
        )
        SELECT 
          'article' as "contentType",
          a.id as "contentId",
          a.title,
          a.content,
          CASE 
            WHEN a.tags IS NOT NULL THEN string_to_array(a.tags, ',')
            ELSE ARRAY[]::text[]
          END as keywords,
          CASE 
            WHEN a.tags IS NOT NULL THEN string_to_array(a.tags, ',')
            ELSE ARRAY[]::text[]
          END as tags,
          a.category,
          a."authorId",
          a."publishedAt",
          COALESCE(a.views, 0) as "viewCount",
          0 as "likeCount",
          0 as "commentCount", 
          0 as "shareCount",
          a.published as "isActive",
          'zh' as language
        FROM articles a
        WHERE a.published = true
        ON CONFLICT ("contentType", "contentId") DO UPDATE SET
          title = EXCLUDED.title,
          content = EXCLUDED.content,
          keywords = EXCLUDED.keywords,
          tags = EXCLUDED.tags,
          category = EXCLUDED.category,
          "authorId" = EXCLUDED."authorId",
          "publishedAt" = EXCLUDED."publishedAt",
          "viewCount" = EXCLUDED."viewCount",
          "isActive" = EXCLUDED."isActive",
          "lastIndexedAt" = CURRENT_TIMESTAMP;
      `);
      console.log('✅ Search indexes created for existing articles');
    } catch (error) {
      console.log('⚠️ Failed to create search indexes for articles:', error.message);
    }
    
    // Create sample search analytics data
    console.log('📊 Creating sample search analytics...');
    
    try {
      await sequelize.query(`
        INSERT INTO search_analytics (date, "totalSearches", "uniqueUsers", "uniqueSessions")
        SELECT 
          CURRENT_DATE - INTERVAL '1 day' * generate_series(0, 6),
          (random() * 100 + 50)::integer,
          (random() * 30 + 10)::integer,
          (random() * 50 + 20)::integer
        ON CONFLICT (date) DO NOTHING;
      `);
      console.log('✅ Sample search analytics created');
    } catch (error) {
      console.log('⚠️ Failed to create sample analytics:', error.message);
    }
    
    console.log('🎉 Search and recommendation system tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create search tables:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createSearchTables()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSearchTables };
