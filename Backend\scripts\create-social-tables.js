#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function createSocialTables() {
  console.log('📱 Creating social feature tables...');
  
  try {
    // Connect to database
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Create article_versions table
    const createArticleVersionsQuery = `
      CREATE TABLE IF NOT EXISTS article_versions (
        id SERIAL PRIMARY KEY,
        "articleId" INTEGER NOT NULL REFERENCES articles(id) ON DELETE CASCADE,
        version INTEGER NOT NULL DEFAULT 1,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        content TEXT NOT NULL,
        category VARCHAR(255),
        tags TEXT[],
        "featuredImage" VARCHAR(255),
        metadata JSONB DEFAULT '{}',
        "changeType" VARCHAR(50) DEFAULT 'updated' CHECK ("changeType" IN ('created', 'updated', 'published', 'unpublished', 'deleted', 'restored')),
        "changeDescription" TEXT,
        "authorId" INTEGER NOT NULL REFERENCES users(id),
        "editorId" INTEGER REFERENCES users(id),
        "isActive" BOOLEAN DEFAULT false,
        "wordCount" INTEGER,
        "readingTime" INTEGER,
        "seoTitle" VARCHAR(255),
        "seoDescription" TEXT,
        "seoKeywords" TEXT[],
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("articleId", version)
      );
    `;
    
    await sequelize.query(createArticleVersionsQuery);
    console.log('✅ article_versions table created');
    
    // Create scheduled_posts table
    const createScheduledPostsQuery = `
      CREATE TABLE IF NOT EXISTS scheduled_posts (
        id SERIAL PRIMARY KEY,
        "articleId" INTEGER NOT NULL REFERENCES articles(id) ON DELETE CASCADE,
        "versionId" INTEGER REFERENCES article_versions(id),
        "scheduledAt" TIMESTAMP NOT NULL,
        status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'published', 'failed', 'cancelled')),
        "publishType" VARCHAR(50) DEFAULT 'scheduled' CHECK ("publishType" IN ('immediate', 'scheduled', 'recurring')),
        "recurringPattern" JSONB,
        "publishSettings" JSONB DEFAULT '{}',
        "socialMediaSettings" JSONB DEFAULT '{}',
        "authorId" INTEGER NOT NULL REFERENCES users(id),
        "scheduledBy" INTEGER NOT NULL REFERENCES users(id),
        "publishedAt" TIMESTAMP,
        "publishedBy" INTEGER REFERENCES users(id),
        "errorMessage" TEXT,
        "retryCount" INTEGER DEFAULT 0,
        "maxRetries" INTEGER DEFAULT 3,
        "nextRetryAt" TIMESTAMP,
        priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        tags TEXT[],
        metadata JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createScheduledPostsQuery);
    console.log('✅ scheduled_posts table created');
    
    // Create direct_messages table
    const createDirectMessagesQuery = `
      CREATE TABLE IF NOT EXISTS direct_messages (
        id SERIAL PRIMARY KEY,
        "conversationId" VARCHAR(255) NOT NULL,
        "senderId" INTEGER NOT NULL REFERENCES users(id),
        "receiverId" INTEGER NOT NULL REFERENCES users(id),
        "messageType" VARCHAR(50) DEFAULT 'text' CHECK ("messageType" IN ('text', 'image', 'file', 'link', 'system')),
        content TEXT NOT NULL,
        metadata JSONB DEFAULT '{}',
        "isRead" BOOLEAN DEFAULT false,
        "readAt" TIMESTAMP,
        "isDeleted" BOOLEAN DEFAULT false,
        "deletedAt" TIMESTAMP,
        "deletedBy" INTEGER REFERENCES users(id),
        "replyToId" INTEGER REFERENCES direct_messages(id),
        "editedAt" TIMESTAMP,
        "originalContent" TEXT,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createDirectMessagesQuery);
    console.log('✅ direct_messages table created');
    
    // Create user_interactions table
    const createUserInteractionsQuery = `
      CREATE TABLE IF NOT EXISTS user_interactions (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL REFERENCES users(id),
        "targetType" VARCHAR(50) NOT NULL CHECK ("targetType" IN ('article', 'comment', 'user')),
        "targetId" INTEGER NOT NULL,
        "interactionType" VARCHAR(50) NOT NULL CHECK ("interactionType" IN ('like', 'dislike', 'favorite', 'bookmark', 'share', 'follow', 'block', 'report')),
        value INTEGER DEFAULT 1,
        metadata JSONB DEFAULT '{}',
        "ipAddress" INET,
        "userAgent" TEXT,
        "isActive" BOOLEAN DEFAULT true,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("userId", "targetType", "targetId", "interactionType")
      );
    `;
    
    await sequelize.query(createUserInteractionsQuery);
    console.log('✅ user_interactions table created');
    
    // Create user_relationships table
    const createUserRelationshipsQuery = `
      CREATE TABLE IF NOT EXISTS user_relationships (
        id SERIAL PRIMARY KEY,
        "followerId" INTEGER NOT NULL REFERENCES users(id),
        "followingId" INTEGER NOT NULL REFERENCES users(id),
        "relationshipType" VARCHAR(50) DEFAULT 'follow' CHECK ("relationshipType" IN ('follow', 'block', 'mute', 'close_friend')),
        status VARCHAR(50) DEFAULT 'accepted' CHECK (status IN ('pending', 'accepted', 'rejected', 'cancelled')),
        "isActive" BOOLEAN DEFAULT true,
        metadata JSONB DEFAULT '{}',
        "notificationSettings" JSONB DEFAULT '{"posts": true, "comments": true, "likes": false, "mentions": true}',
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE("followerId", "followingId")
      );
    `;
    
    await sequelize.query(createUserRelationshipsQuery);
    console.log('✅ user_relationships table created');
    
    // Create indexes
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      // Article versions indexes
      'CREATE INDEX IF NOT EXISTS idx_article_versions_article_id ON article_versions("articleId");',
      'CREATE INDEX IF NOT EXISTS idx_article_versions_version ON article_versions(version);',
      'CREATE INDEX IF NOT EXISTS idx_article_versions_active ON article_versions("isActive");',
      
      // Scheduled posts indexes
      'CREATE INDEX IF NOT EXISTS idx_scheduled_posts_article_id ON scheduled_posts("articleId");',
      'CREATE INDEX IF NOT EXISTS idx_scheduled_posts_scheduled_at ON scheduled_posts("scheduledAt");',
      'CREATE INDEX IF NOT EXISTS idx_scheduled_posts_status ON scheduled_posts(status);',
      
      // Direct messages indexes
      'CREATE INDEX IF NOT EXISTS idx_direct_messages_conversation_id ON direct_messages("conversationId");',
      'CREATE INDEX IF NOT EXISTS idx_direct_messages_sender_id ON direct_messages("senderId");',
      'CREATE INDEX IF NOT EXISTS idx_direct_messages_receiver_id ON direct_messages("receiverId");',
      'CREATE INDEX IF NOT EXISTS idx_direct_messages_is_read ON direct_messages("isRead");',
      
      // User interactions indexes
      'CREATE INDEX IF NOT EXISTS idx_user_interactions_user_id ON user_interactions("userId");',
      'CREATE INDEX IF NOT EXISTS idx_user_interactions_target ON user_interactions("targetType", "targetId");',
      'CREATE INDEX IF NOT EXISTS idx_user_interactions_type ON user_interactions("interactionType");',
      
      // User relationships indexes
      'CREATE INDEX IF NOT EXISTS idx_user_relationships_follower_id ON user_relationships("followerId");',
      'CREATE INDEX IF NOT EXISTS idx_user_relationships_following_id ON user_relationships("followingId");',
      'CREATE INDEX IF NOT EXISTS idx_user_relationships_type ON user_relationships("relationshipType");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    console.log('🎉 Social feature tables created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create social tables:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  createSocialTables()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSocialTables };
