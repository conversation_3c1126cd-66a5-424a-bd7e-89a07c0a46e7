#!/usr/bin/env node

const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const User = require('../models/User');

// 测试账户数据
const testAccounts = [
  // 管理员账户
  {
    username: 'admin_test',
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isEmailVerified: true,
    isActive: true,
    bio: 'System Administrator for testing purposes'
  },
  {
    username: 'sysadmin_test',
    email: '<EMAIL>',
    password: 'SysAdmin123!',
    firstName: 'System',
    lastName: 'Admin',
    role: 'admin',
    isEmailVerified: true,
    isActive: true,
    bio: 'System Administrator for advanced testing'
  },

  // 内容管理员账户
  {
    username: 'moderator_test',
    email: '<EMAIL>',
    password: 'Moderator123!',
    firstName: 'Content',
    lastName: 'Moderator',
    role: 'moderator',
    isEmailVerified: true,
    isActive: true,
    bio: 'Content Moderator for testing purposes'
  },
  {
    username: 'editor_chief',
    email: '<EMAIL>',
    password: 'Editor123!',
    firstName: 'Editor',
    lastName: 'Chief',
    role: 'moderator',
    isEmailVerified: true,
    isActive: true,
    bio: 'Editor Chief for content management testing'
  },

  // 普通用户账户
  {
    username: 'user_test1',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'John',
    lastName: 'Doe',
    role: 'user',
    isEmailVerified: true,
    isActive: true,
    bio: 'Active user for basic functionality testing'
  },
  {
    username: 'user_test2',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'user',
    isEmailVerified: true,
    isActive: true,
    bio: 'Regular user for social features testing'
  },
  {
    username: 'author_test',
    email: '<EMAIL>',
    password: 'Author123!',
    firstName: 'Author',
    lastName: 'Writer',
    role: 'user',
    isEmailVerified: true,
    isActive: true,
    bio: 'Content author for article creation testing'
  },
  {
    username: 'reader_test',
    email: '<EMAIL>',
    password: 'Reader123!',
    firstName: 'Reader',
    lastName: 'User',
    role: 'user',
    isEmailVerified: true,
    isActive: true,
    bio: 'Reader user for consumption testing'
  },

  // 特殊状态账户
  {
    username: 'unverified_test',
    email: '<EMAIL>',
    password: 'Unverified123!',
    firstName: 'Unverified',
    lastName: 'User',
    role: 'user',
    isEmailVerified: false,
    isActive: true,
    bio: 'Unverified user for email verification testing'
  },
  {
    username: 'inactive_test',
    email: '<EMAIL>',
    password: 'Inactive123!',
    firstName: 'Inactive',
    lastName: 'User',
    role: 'user',
    isEmailVerified: true,
    isActive: false,
    bio: 'Inactive user for account status testing'
  },
  {
    username: 'locked_test',
    email: '<EMAIL>',
    password: 'Locked123!',
    firstName: 'Locked',
    lastName: 'User',
    role: 'user',
    isEmailVerified: true,
    isActive: true,
    bio: 'User for account locking mechanism testing',
    isLocked: true,
    lockedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 锁定24小时
  }
];

async function createTestAccounts() {
  console.log('🧪 Creating test accounts for Newzora...');
  console.log('==========================================');

  try {
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');

    // 清理现有测试账户（跳过有外键约束的用户）
    console.log('\n🧹 Cleaning existing test accounts...');
    const testEmails = testAccounts.map(account => account.email);

    try {
      const deletedCount = await User.destroy({
        where: {
          email: testEmails
        }
      });
      console.log(`✅ Deleted ${deletedCount} existing test accounts`);
    } catch (error) {
      if (error.name === 'SequelizeForeignKeyConstraintError') {
        console.log('⚠️ Some test accounts have foreign key constraints, skipping deletion');
        console.log('   Creating accounts with different emails if needed');
      } else {
        throw error;
      }
    }

    console.log('\n👥 Creating new test accounts...');
    
    for (const accountData of testAccounts) {
      try {
        // 检查用户是否已存在
        const existingUser = await User.findOne({
          where: {
            email: accountData.email
          }
        });

        if (existingUser) {
          console.log(`⚠️ User already exists: ${accountData.username} (${accountData.email})`);
          continue;
        }

        // 加密密码
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(accountData.password, saltRounds);

        // 创建用户
        const user = await User.create({
          ...accountData,
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        console.log(`✅ Created ${accountData.role}: ${accountData.username} (${accountData.email})`);

      } catch (error) {
        if (error.name === 'SequelizeUniqueConstraintError') {
          console.log(`⚠️ User already exists: ${accountData.username} (${accountData.email})`);
        } else {
          console.log(`❌ Failed to create ${accountData.username}: ${error.message}`);
        }
      }
    }

    console.log('\n📋 Test Account Summary:');
    console.log('========================');
    console.log('Role      | Username        | Email                  | Password        | Status');
    console.log('----------|-----------------|------------------------|-----------------|--------');
    testAccounts.forEach(account => {
      const status = account.isActive ?
        (account.isEmailVerified ? '✅ Active' : '📧 Unverified') :
        (account.isLocked ? '🔒 Locked' : '❌ Inactive');
      console.log(`${account.role.padEnd(9)} | ${account.username.padEnd(15)} | ${account.email.padEnd(22)} | ${account.password.padEnd(15)} | ${status}`);
    });

    console.log('\n🔐 Login Instructions:');
    console.log('======================');
    console.log('1. Use either username or email as identifier');
    console.log('2. All passwords follow the pattern: [Role]123!');
    console.log('3. Admin and Moderator have elevated permissions');
    console.log('4. Unverified user can test email verification flow');
    console.log('5. Inactive user can test account status handling');

    console.log('\n🌐 API Endpoints for Testing:');
    console.log('=============================');
    console.log('POST /api/auth-enhanced/login');
    console.log('POST /api/auth-enhanced/register');
    console.log('POST /api/auth-enhanced/forgot-password');
    console.log('POST /api/auth-enhanced/reset-password');
    console.log('POST /api/auth-enhanced/verify-email');
    console.log('GET  /api/auth-enhanced/me');

    console.log('\n🧪 Test Scenarios:');
    console.log('==================');
    console.log('✓ Admin login with elevated permissions');
    console.log('✓ Regular user login and profile access');
    console.log('✓ Email verification flow');
    console.log('✓ Password reset functionality');
    console.log('✓ Account lockout after failed attempts');
    console.log('✓ Token refresh mechanism');
    console.log('✓ Social login integration');

    console.log('\n🎉 Test accounts created successfully!');
    return true;

  } catch (error) {
    console.error('❌ Failed to create test accounts:', error.message);
    console.error('Error details:', error);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('\n🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// 运行脚本
if (require.main === module) {
  createTestAccounts()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createTestAccounts, testAccounts };
