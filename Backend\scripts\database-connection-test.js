#!/usr/bin/env node

const { sequelize, connectWithRetry, performHealthCheck, getPerformanceMetrics } = require('../config/database-enhanced');
const { testSupabaseConnection } = require('../config/supabase');
const colors = require('colors');

// 测试结果收集
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

// 记录测试结果
const recordTest = (name, passed, message, data = null) => {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}: ${message}`.green);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${message}`.red);
  }
  
  testResults.details.push({
    name,
    passed,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};

// 测试1: 基础连接测试
async function testBasicConnection() {
  console.log('\n📡 测试1: 基础数据库连接'.cyan);
  
  try {
    const connected = await connectWithRetry();
    recordTest('基础连接', connected, connected ? '数据库连接成功' : '数据库连接失败');
    return connected;
  } catch (error) {
    recordTest('基础连接', false, `连接异常: ${error.message}`);
    return false;
  }
}

// 测试2: 健康检查
async function testHealthCheck() {
  console.log('\n🏥 测试2: 数据库健康检查'.cyan);
  
  try {
    const health = await performHealthCheck();
    const isHealthy = health.status === 'healthy';
    
    recordTest('健康检查', isHealthy, 
      isHealthy ? `响应时间: ${health.responseTime}ms` : `健康检查失败: ${health.error}`,
      health
    );
    
    if (isHealthy) {
      console.log(`   数据库版本: ${health.version}`.gray);
      console.log(`   连接池状态: ${health.pool.used}/${health.pool.total} (等待: ${health.pool.waiting})`.gray);
    }
    
    return isHealthy;
  } catch (error) {
    recordTest('健康检查', false, `健康检查异常: ${error.message}`);
    return false;
  }
}

// 测试3: 查询性能测试
async function testQueryPerformance() {
  console.log('\n⚡ 测试3: 查询性能测试'.cyan);
  
  try {
    const queries = [
      { name: '简单查询', sql: 'SELECT NOW()' },
      { name: '系统信息', sql: 'SELECT version(), current_database(), current_user' },
      { name: '表统计', sql: 'SELECT count(*) FROM information_schema.tables WHERE table_schema = \'public\'' }
    ];
    
    let allPassed = true;
    const results = [];
    
    for (const query of queries) {
      const startTime = Date.now();
      try {
        await sequelize.query(query.sql);
        const duration = Date.now() - startTime;
        results.push({ name: query.name, duration, success: true });
        
        if (duration > 1000) {
          console.log(`   ⚠️ ${query.name}: ${duration}ms (较慢)`.yellow);
        } else {
          console.log(`   ✅ ${query.name}: ${duration}ms`.green);
        }
      } catch (error) {
        results.push({ name: query.name, duration: Date.now() - startTime, success: false, error: error.message });
        console.log(`   ❌ ${query.name}: ${error.message}`.red);
        allPassed = false;
      }
    }
    
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    recordTest('查询性能', allPassed, 
      allPassed ? `平均响应时间: ${avgDuration.toFixed(2)}ms` : '部分查询失败',
      results
    );
    
    return allPassed;
  } catch (error) {
    recordTest('查询性能', false, `性能测试异常: ${error.message}`);
    return false;
  }
}

// 测试4: 连接池测试
async function testConnectionPool() {
  console.log('\n🏊 测试4: 连接池压力测试'.cyan);
  
  try {
    const concurrentQueries = 10;
    const promises = [];
    
    for (let i = 0; i < concurrentQueries; i++) {
      promises.push(
        sequelize.query('SELECT pg_sleep(0.1), $1 as query_id', {
          replacements: [i],
          type: sequelize.QueryTypes.SELECT
        })
      );
    }
    
    const startTime = Date.now();
    const results = await Promise.allSettled(promises);
    const duration = Date.now() - startTime;
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    const passed = failed === 0;
    recordTest('连接池测试', passed, 
      `${successful}/${concurrentQueries} 查询成功，总耗时: ${duration}ms`,
      { successful, failed, duration, concurrentQueries }
    );
    
    return passed;
  } catch (error) {
    recordTest('连接池测试', false, `连接池测试异常: ${error.message}`);
    return false;
  }
}

// 测试5: 事务测试
async function testTransactions() {
  console.log('\n🔄 测试5: 事务功能测试'.cyan);
  
  const transaction = await sequelize.transaction();
  
  try {
    // 创建临时表进行测试
    await sequelize.query(`
      CREATE TEMP TABLE test_transaction (
        id SERIAL PRIMARY KEY,
        data TEXT
      )
    `, { transaction });
    
    // 插入测试数据
    await sequelize.query(`
      INSERT INTO test_transaction (data) VALUES ('test1'), ('test2')
    `, { transaction });
    
    // 查询数据
    const [results] = await sequelize.query(`
      SELECT COUNT(*) as count FROM test_transaction
    `, { transaction });
    
    const count = parseInt(results[0].count);
    
    // 回滚事务
    await transaction.rollback();
    
    const passed = count === 2;
    recordTest('事务测试', passed, 
      passed ? '事务创建、插入、回滚正常' : `数据计数异常: ${count}`,
      { insertedRows: count }
    );
    
    return passed;
  } catch (error) {
    await transaction.rollback();
    recordTest('事务测试', false, `事务测试异常: ${error.message}`);
    return false;
  }
}

// 测试6: Supabase连接测试
async function testSupabaseIntegration() {
  console.log('\n🚀 测试6: Supabase集成测试'.cyan);
  
  try {
    const connected = await testSupabaseConnection();
    recordTest('Supabase连接', connected, 
      connected ? 'Supabase连接正常' : 'Supabase连接失败'
    );
    return connected;
  } catch (error) {
    recordTest('Supabase连接', false, `Supabase连接异常: ${error.message}`);
    return false;
  }
}

// 测试7: 性能指标收集
async function testPerformanceMetrics() {
  console.log('\n📊 测试7: 性能指标收集'.cyan);
  
  try {
    const metrics = await getPerformanceMetrics();
    const hasMetrics = metrics && metrics.connections;
    
    recordTest('性能指标', hasMetrics, 
      hasMetrics ? '性能指标收集成功' : '性能指标收集失败',
      metrics
    );
    
    if (hasMetrics && metrics.connections) {
      console.log(`   活动连接: ${metrics.connections.active_connections}`.gray);
      console.log(`   空闲连接: ${metrics.connections.idle_connections}`.gray);
      console.log(`   数据库大小: ${metrics.database_size}`.gray);
    }
    
    return hasMetrics;
  } catch (error) {
    recordTest('性能指标', false, `性能指标收集异常: ${error.message}`);
    return false;
  }
}

// 生成测试报告
function generateReport() {
  console.log('\n📋 测试报告'.cyan.bold);
  console.log('='.repeat(50).gray);
  
  const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed}`.green);
  console.log(`失败: ${testResults.failed}`.red);
  console.log(`成功率: ${successRate}%`[successRate >= 80 ? 'green' : 'red']);
  
  console.log('\n详细结果:'.cyan);
  testResults.details.forEach(test => {
    const status = test.passed ? '✅' : '❌';
    console.log(`${status} ${test.name}: ${test.message}`);
  });
  
  // 建议
  console.log('\n💡 建议:'.cyan.bold);
  if (testResults.failed === 0) {
    console.log('🎉 所有测试通过！数据库连接状态良好。'.green);
  } else {
    console.log('⚠️ 发现问题，建议检查以下方面:'.yellow);
    
    testResults.details
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`   - ${test.name}: ${test.message}`.red);
      });
    
    console.log('\n🔧 可能的解决方案:'.cyan);
    console.log('   1. 检查数据库服务是否运行');
    console.log('   2. 验证连接参数（主机、端口、用户名、密码）');
    console.log('   3. 检查网络连接');
    console.log('   4. 查看数据库日志');
    console.log('   5. 检查防火墙设置');
  }
  
  return {
    success: testResults.failed === 0,
    successRate: parseFloat(successRate),
    details: testResults.details
  };
}

// 主测试函数
async function runDatabaseTests() {
  console.log('🚀 开始数据库连接测试...'.cyan.bold);
  console.log('='.repeat(50).gray);
  
  try {
    // 运行所有测试
    await testBasicConnection();
    await testHealthCheck();
    await testQueryPerformance();
    await testConnectionPool();
    await testTransactions();
    await testSupabaseIntegration();
    await testPerformanceMetrics();
    
    // 生成报告
    const report = generateReport();
    
    // 保存报告到文件
    const fs = require('fs');
    const reportPath = require('path').join(__dirname, '..', 'logs', 'database-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      ...report
    }, null, 2));
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`.gray);
    
    return report.success;
  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error.message);
    return false;
  } finally {
    // 清理连接
    try {
      await sequelize.close();
      console.log('\n🔌 数据库连接已关闭'.gray);
    } catch (error) {
      console.error('关闭连接时出错:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runDatabaseTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runDatabaseTests,
  testResults
};
