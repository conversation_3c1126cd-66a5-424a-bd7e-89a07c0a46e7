#!/usr/bin/env node

const { sequelize, connectWithRetry, performMaintenance } = require('../config/database-enhanced');
const { supabase } = require('../config/supabase');
const fs = require('fs');
const path = require('path');
const colors = require('colors');

// 修复操作记录
const repairLog = {
  operations: [],
  startTime: new Date(),
  endTime: null,
  success: false
};

// 记录修复操作
const logOperation = (operation, success, message, details = null) => {
  const entry = {
    operation,
    success,
    message,
    details,
    timestamp: new Date().toISOString()
  };
  
  repairLog.operations.push(entry);
  
  if (success) {
    console.log(`✅ ${operation}: ${message}`.green);
  } else {
    console.log(`❌ ${operation}: ${message}`.red);
  }
  
  if (details) {
    console.log(`   详情: ${JSON.stringify(details, null, 2)}`.gray);
  }
};

// 1. 检查并修复数据库连接
async function repairDatabaseConnection() {
  console.log('\n🔧 修复1: 数据库连接问题'.cyan);
  
  try {
    // 尝试连接数据库
    const connected = await connectWithRetry();
    
    if (connected) {
      logOperation('数据库连接', true, '连接正常');
      return true;
    } else {
      logOperation('数据库连接', false, '连接失败');
      
      // 尝试修复连接问题
      console.log('🔄 尝试修复连接问题...'.yellow);
      
      // 检查环境变量
      const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
      
      if (missingVars.length > 0) {
        logOperation('环境变量检查', false, `缺少环境变量: ${missingVars.join(', ')}`);
        return false;
      }
      
      logOperation('环境变量检查', true, '所有必需的环境变量都已设置');
      
      // 尝试使用默认配置重新连接
      try {
        await sequelize.authenticate();
        logOperation('重新连接', true, '使用默认配置连接成功');
        return true;
      } catch (error) {
        logOperation('重新连接', false, `重新连接失败: ${error.message}`);
        return false;
      }
    }
  } catch (error) {
    logOperation('数据库连接修复', false, `修复过程异常: ${error.message}`);
    return false;
  }
}

// 2. 修复数据库表结构
async function repairDatabaseSchema() {
  console.log('\n🏗️ 修复2: 数据库表结构'.cyan);
  
  try {
    // 同步数据库模型
    await sequelize.sync({ alter: true });
    logOperation('表结构同步', true, '数据库表结构已同步');
    
    // 检查关键表是否存在
    const criticalTables = ['users', 'articles', 'comments', 'roles', 'permissions'];
    const existingTables = await sequelize.getQueryInterface().showAllTables();
    
    const missingTables = criticalTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      logOperation('表完整性检查', false, `缺少关键表: ${missingTables.join(', ')}`);
      
      // 尝试重新创建表
      console.log('🔄 重新创建缺少的表...'.yellow);
      await sequelize.sync({ force: false, alter: true });
      
      // 再次检查
      const newExistingTables = await sequelize.getQueryInterface().showAllTables();
      const stillMissingTables = criticalTables.filter(table => !newExistingTables.includes(table));
      
      if (stillMissingTables.length === 0) {
        logOperation('表重建', true, '所有关键表已创建');
      } else {
        logOperation('表重建', false, `仍缺少表: ${stillMissingTables.join(', ')}`);
        return false;
      }
    } else {
      logOperation('表完整性检查', true, '所有关键表都存在');
    }
    
    return true;
  } catch (error) {
    logOperation('数据库表结构修复', false, `修复过程异常: ${error.message}`);
    return false;
  }
}

// 3. 修复数据库索引
async function repairDatabaseIndexes() {
  console.log('\n📊 修复3: 数据库索引'.cyan);
  
  try {
    const indexQueries = [
      // 用户表索引
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users("createdAt");`,
      
      // 文章表索引
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_author_id ON articles("authorId");`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_category ON articles(category);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_published ON articles(published);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_created_at ON articles("createdAt");`,
      
      // 评论表索引
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_article_id ON comments("articleId");`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_author_id ON comments("authorId");`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_created_at ON comments("createdAt");`,
      
      // 全文搜索索引
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_title_search ON articles USING gin(to_tsvector('english', title));`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_content_search ON articles USING gin(to_tsvector('english', content));`
    ];
    
    let successCount = 0;
    let failCount = 0;
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        successCount++;
      } catch (error) {
        failCount++;
        console.log(`   ⚠️ 索引创建失败: ${error.message}`.yellow);
      }
    }
    
    const success = failCount === 0;
    logOperation('数据库索引修复', success, 
      `成功: ${successCount}, 失败: ${failCount}`,
      { successCount, failCount, totalIndexes: indexQueries.length }
    );
    
    return success;
  } catch (error) {
    logOperation('数据库索引修复', false, `修复过程异常: ${error.message}`);
    return false;
  }
}

// 4. 清理和优化数据库
async function optimizeDatabase() {
  console.log('\n⚡ 修复4: 数据库优化'.cyan);
  
  try {
    // 更新表统计信息
    await sequelize.query('ANALYZE;');
    logOperation('统计信息更新', true, '表统计信息已更新');
    
    // 清理无用连接
    const [idleConnections] = await sequelize.query(`
      SELECT count(*) as count 
      FROM pg_stat_activity 
      WHERE datname = current_database() 
      AND state = 'idle' 
      AND state_change < now() - interval '1 hour'
    `);
    
    if (idleConnections[0].count > 0) {
      await sequelize.query(`
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE datname = current_database() 
        AND state = 'idle' 
        AND state_change < now() - interval '1 hour'
      `);
      logOperation('连接清理', true, `清理了 ${idleConnections[0].count} 个空闲连接`);
    } else {
      logOperation('连接清理', true, '没有需要清理的空闲连接');
    }
    
    // 检查数据库大小
    const [sizeResult] = await sequelize.query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `);
    
    logOperation('数据库大小检查', true, `当前数据库大小: ${sizeResult[0].size}`);
    
    return true;
  } catch (error) {
    logOperation('数据库优化', false, `优化过程异常: ${error.message}`);
    return false;
  }
}

// 5. 修复Supabase配置
async function repairSupabaseConfig() {
  console.log('\n🚀 修复5: Supabase配置'.cyan);
  
  try {
    // 检查Supabase环境变量
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      logOperation('Supabase环境变量', false, '缺少Supabase配置');
      
      // 使用默认配置
      console.log('🔄 使用默认Supabase配置...'.yellow);
      
      const defaultConfig = {
        url: 'https://wdpprzemflzlardkmncfk.supabase.co',
        key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndkcHByemVmbHpsYXJka21uY2ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDk4MjgsImV4cCI6MjA2NzY4NTgyOH0.yp_k9Sv6AMFZmUs_EWa_-rPZGyTxNNFTZOM4RaU668s'
      };
      
      // 更新环境变量
      process.env.SUPABASE_URL = defaultConfig.url;
      process.env.SUPABASE_ANON_KEY = defaultConfig.key;
      
      logOperation('Supabase默认配置', true, '已应用默认Supabase配置');
    } else {
      logOperation('Supabase环境变量', true, 'Supabase配置完整');
    }
    
    // 测试Supabase连接
    try {
      const { data, error } = await supabase.from('users').select('count').limit(1);
      if (error && error.code !== 'PGRST116') {
        logOperation('Supabase连接测试', false, `连接失败: ${error.message}`);
        return false;
      } else {
        logOperation('Supabase连接测试', true, 'Supabase连接正常');
      }
    } catch (error) {
      logOperation('Supabase连接测试', false, `连接异常: ${error.message}`);
      return false;
    }
    
    return true;
  } catch (error) {
    logOperation('Supabase配置修复', false, `修复过程异常: ${error.message}`);
    return false;
  }
}

// 6. 创建测试数据
async function createTestData() {
  console.log('\n📝 修复6: 创建测试数据'.cyan);
  
  try {
    // 检查是否已有管理员用户
    const [adminUsers] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM users 
      WHERE email = '<EMAIL>'
    `);
    
    if (adminUsers[0].count === 0) {
      // 创建管理员用户
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123456', 10);
      
      await sequelize.query(`
        INSERT INTO users (username, email, password, "firstName", "lastName", role, "emailVerified", "createdAt", "updatedAt")
        VALUES ('admin', '<EMAIL>', :password, 'Admin', 'User', 'admin', true, NOW(), NOW())
      `, {
        replacements: { password: hashedPassword }
      });
      
      logOperation('管理员用户创建', true, '管理员用户已创建 (<EMAIL> / admin123456)');
    } else {
      logOperation('管理员用户检查', true, '管理员用户已存在');
    }
    
    // 检查是否有测试文章
    const [testArticles] = await sequelize.query(`
      SELECT COUNT(*) as count FROM articles
    `);
    
    if (testArticles[0].count === 0) {
      // 创建测试文章
      await sequelize.query(`
        INSERT INTO articles (title, description, content, category, author, published, "createdAt", "updatedAt")
        VALUES 
        ('Welcome to Newzora', 'Getting started with our platform', 'This is your first article on Newzora...', 'General', 'Admin', true, NOW(), NOW()),
        ('How to Create Content', 'Learn the basics of content creation', 'Content creation is at the heart of Newzora...', 'Tutorial', 'Admin', true, NOW(), NOW())
      `);
      
      logOperation('测试文章创建', true, '测试文章已创建');
    } else {
      logOperation('测试文章检查', true, `已有 ${testArticles[0].count} 篇文章`);
    }
    
    return true;
  } catch (error) {
    logOperation('测试数据创建', false, `创建过程异常: ${error.message}`);
    return false;
  }
}

// 生成修复报告
function generateRepairReport() {
  repairLog.endTime = new Date();
  const duration = repairLog.endTime - repairLog.startTime;
  
  console.log('\n📋 修复报告'.cyan.bold);
  console.log('='.repeat(50).gray);
  
  const totalOperations = repairLog.operations.length;
  const successfulOperations = repairLog.operations.filter(op => op.success).length;
  const failedOperations = totalOperations - successfulOperations;
  
  console.log(`修复开始时间: ${repairLog.startTime.toISOString()}`);
  console.log(`修复结束时间: ${repairLog.endTime.toISOString()}`);
  console.log(`总耗时: ${(duration / 1000).toFixed(2)}秒`);
  console.log(`总操作数: ${totalOperations}`);
  console.log(`成功: ${successfulOperations}`.green);
  console.log(`失败: ${failedOperations}`[failedOperations > 0 ? 'red' : 'green']);
  
  console.log('\n详细操作记录:'.cyan);
  repairLog.operations.forEach(op => {
    const status = op.success ? '✅' : '❌';
    console.log(`${status} ${op.operation}: ${op.message}`);
  });
  
  const success = failedOperations === 0;
  repairLog.success = success;
  
  // 保存报告
  const reportPath = path.join(__dirname, '..', 'logs', 'database-repair-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(repairLog, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`.gray);
  
  return success;
}

// 主修复函数
async function runDatabaseRepair() {
  console.log('🔧 开始数据库修复和优化...'.cyan.bold);
  console.log('='.repeat(50).gray);
  
  try {
    // 运行所有修复操作
    await repairDatabaseConnection();
    await repairDatabaseSchema();
    await repairDatabaseIndexes();
    await optimizeDatabase();
    await repairSupabaseConfig();
    await createTestData();
    
    // 生成报告
    const success = generateRepairReport();
    
    if (success) {
      console.log('\n🎉 数据库修复完成！所有操作都成功执行。'.green.bold);
    } else {
      console.log('\n⚠️ 数据库修复完成，但有部分操作失败。请查看详细报告。'.yellow.bold);
    }
    
    return success;
  } catch (error) {
    console.error('❌ 修复过程中发生异常:', error.message);
    return false;
  } finally {
    // 清理连接
    try {
      await sequelize.close();
      console.log('\n🔌 数据库连接已关闭'.gray);
    } catch (error) {
      console.error('关闭连接时出错:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runDatabaseRepair()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('修复失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runDatabaseRepair,
  repairLog
};
