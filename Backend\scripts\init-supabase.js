#!/usr/bin/env node

/**
 * Supabase 项目初始化脚本
 * 创建初始管理员用户和基础配置
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const SupabaseUserManager = require('./supabase-user-management');

async function initializeSupabase() {
  console.log('🚀 Initializing Supabase project for Newzora...\n');
  
  const manager = new SupabaseUserManager();

  try {
    // 1. 检查现有用户
    console.log('1️⃣ Checking existing users...');
    const existingUsers = await manager.listUsers();
    
    if (existingUsers.length > 0) {
      console.log('⚠️ Users already exist in the database.');
      console.log('Do you want to continue? This will create additional users.');
      // 在实际使用中，你可能想要添加用户确认逻辑
    }

    // 2. 创建管理员用户
    console.log('\n2️⃣ Creating admin user...');
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Admin123456!'; // 强密码
    
    try {
      await manager.createAdminUser(adminEmail, adminPassword, 'Newzora Administrator');
      console.log(`✅ Admin user created: ${adminEmail}`);
      console.log(`🔑 Admin password: ${adminPassword}`);
      console.log('⚠️ Please change this password after first login!');
    } catch (error) {
      if (error.message.includes('already registered')) {
        console.log('ℹ️ Admin user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // 3. 创建演示用户
    console.log('\n3️⃣ Creating demo user...');
    const demoEmail = '<EMAIL>';
    const demoPassword = 'Demo123456!';
    
    try {
      await manager.createUser(demoEmail, demoPassword, 'Demo User', 'user');
      console.log(`✅ Demo user created: ${demoEmail}`);
      console.log(`🔑 Demo password: ${demoPassword}`);
    } catch (error) {
      if (error.message.includes('already registered')) {
        console.log('ℹ️ Demo user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // 4. 创建编辑用户
    console.log('\n4️⃣ Creating editor user...');
    const editorEmail = '<EMAIL>';
    const editorPassword = 'Editor123456!';
    
    try {
      await manager.createUser(editorEmail, editorPassword, 'Content Editor', 'editor');
      console.log(`✅ Editor user created: ${editorEmail}`);
      console.log(`🔑 Editor password: ${editorPassword}`);
    } catch (error) {
      if (error.message.includes('already registered')) {
        console.log('ℹ️ Editor user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // 5. 显示最终用户列表
    console.log('\n5️⃣ Final user list:');
    await manager.listUsers();

    console.log('\n🎉 Supabase initialization completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Update your .env file to set USE_REAL_SUPABASE=true');
    console.log('2. Configure Supabase Authentication settings in the dashboard');
    console.log('3. Set up email templates and SMTP settings');
    console.log('4. Configure social login providers if needed');
    console.log('5. Test the authentication flow');

    console.log('\n🔐 Default user accounts:');
    console.log(`Admin: ${adminEmail} / ${adminPassword}`);
    console.log(`Demo: ${demoEmail} / ${demoPassword}`);
    console.log(`Editor: ${editorEmail} / ${editorPassword}`);
    console.log('\n⚠️ IMPORTANT: Change these passwords in production!');

  } catch (error) {
    console.error('\n💥 Initialization failed:', error.message);
    console.error('\nTroubleshooting tips:');
    console.error('1. Check your SUPABASE_URL and SUPABASE_ANON_KEY in .env');
    console.error('2. Ensure your Supabase project is active');
    console.error('3. Verify your network connection');
    console.error('4. Check Supabase dashboard for any service issues');
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initializeSupabase();
}

module.exports = initializeSupabase;
