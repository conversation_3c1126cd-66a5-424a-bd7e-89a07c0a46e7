const { syncDatabase } = require('../models');

async function initializeDatabase() {
  try {
    console.log('🚀 Starting database initialization...');
    
    // 同步数据库并创建初始数据
    await syncDatabase(true); // force: true 会重新创建所有表
    
    console.log('✅ Database initialization completed successfully!');
    console.log('');
    console.log('📋 Test Accounts Created:');
    console.log('👤 Admin: <EMAIL> / admin123456');
    console.log('👤 Demo User: <EMAIL> / demo123456');
    console.log('');
    console.log('🎯 Next Steps:');
    console.log('1. Start the backend server: npm start');
    console.log('2. Start the frontend: npm run dev');
    console.log('3. Visit http://localhost:3000');
    console.log('');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
