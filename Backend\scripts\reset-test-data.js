#!/usr/bin/env node

/**
 * Newzora 测试数据重置脚本
 * 重置测试账户和相关数据
 */

const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const User = require('../models/User');

console.log('🔄 Newzora 测试数据重置');
console.log('======================');

async function resetTestData() {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    console.log('\n🧹 清理测试数据...');
    
    // 清理测试用户（保留真实用户）
    const testEmailPatterns = [
      '%@newzora.com',
      '%@example.com',
      'test%@%',
      'demo%@%'
    ];

    let deletedCount = 0;
    for (const pattern of testEmailPatterns) {
      const result = await User.destroy({
        where: {
          email: {
            [sequelize.Sequelize.Op.like]: pattern
          }
        }
      });
      deletedCount += result;
    }

    console.log(`✅ 清理了 ${deletedCount} 个测试账户`);

    // 重置自增ID（如果需要）
    try {
      await sequelize.query('ALTER SEQUENCE users_id_seq RESTART WITH 1');
      console.log('✅ 重置用户ID序列');
    } catch (error) {
      console.log('⚠️ 无法重置ID序列（可能不支持）');
    }

    console.log('\n✅ 测试数据重置完成');
    console.log('💡 现在可以重新创建测试账户');
    
  } catch (error) {
    console.error('❌ 重置失败:', error.message);
    throw error;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (error) {
      console.error('关闭连接时出错:', error.message);
    }
  }
}

// 运行重置
if (require.main === module) {
  resetTestData()
    .then(() => {
      console.log('\n🎉 测试数据重置成功！');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 重置失败:', error);
      process.exit(1);
    });
}

module.exports = { resetTestData };
