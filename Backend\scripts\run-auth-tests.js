#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Newzora Authentication Test Suite');
console.log('====================================');

async function checkServerStatus() {
  console.log('\n🔍 Checking server status...');
  
  try {
    const axios = require('axios');
    const response = await axios.get('http://localhost:5000/api/health', { timeout: 5000 });
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not accessible');
    console.log('   Please make sure the backend server is running on port 5000');
    console.log('   Run: npm start or node server.js in the Backend directory');
    return false;
  }
}

async function checkDatabase() {
  console.log('\n🗄️ Checking database connection...');
  
  try {
    const { sequelize } = require('../config/database');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    await sequelize.close();
    return true;
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    console.log('   Please check your database configuration');
    return false;
  }
}

function runScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔧 ${description}...`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: path.dirname(scriptPath)
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} completed successfully`);
        resolve(true);
      } else {
        console.log(`❌ ${description} failed with code ${code}`);
        resolve(false);
      }
    });

    child.on('error', (error) => {
      console.log(`❌ ${description} failed:`, error.message);
      resolve(false);
    });
  });
}

async function generateTestReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    }
  };

  const reportPath = path.join(__dirname, '../test-reports');
  if (!fs.existsSync(reportPath)) {
    fs.mkdirSync(reportPath, { recursive: true });
  }

  const reportFile = path.join(reportPath, `auth-test-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  console.log(`\n📊 Test report saved to: ${reportFile}`);
  return report;
}

async function runAllTests() {
  const results = [];
  let allPassed = true;

  try {
    // 1. Check prerequisites
    console.log('\n📋 Checking Prerequisites');
    console.log('=========================');

    const serverOk = await checkServerStatus();
    results.push({ test: 'Server Status', success: serverOk });
    
    const dbOk = await checkDatabase();
    results.push({ test: 'Database Connection', success: dbOk });

    if (!serverOk || !dbOk) {
      console.log('\n❌ Prerequisites not met. Please fix the issues above before running tests.');
      return false;
    }

    // 2. Create test accounts
    console.log('\n👥 Setting Up Test Data');
    console.log('=======================');

    const createAccountsSuccess = await runScript(
      path.join(__dirname, 'create-test-accounts.js'),
      'Creating test accounts'
    );
    results.push({ test: 'Create Test Accounts', success: createAccountsSuccess });
    allPassed = allPassed && createAccountsSuccess;

    // 3. Run authentication tests
    console.log('\n🔐 Running Authentication Tests');
    console.log('===============================');

    const authTestSuccess = await runScript(
      path.join(__dirname, 'test-authentication.js'),
      'Running authentication tests'
    );
    results.push({ test: 'Authentication Tests', success: authTestSuccess });
    allPassed = allPassed && authTestSuccess;

    // 4. Generate report
    const report = await generateTestReport(results);

    // 5. Display summary
    console.log('\n📈 Test Summary');
    console.log('===============');
    console.log(`Total Tests: ${report.summary.total}`);
    console.log(`Passed: ${report.summary.passed} ✅`);
    console.log(`Failed: ${report.summary.failed} ❌`);
    console.log(`Success Rate: ${((report.summary.passed / report.summary.total) * 100).toFixed(1)}%`);

    if (allPassed) {
      console.log('\n🎉 All tests passed! Authentication system is ready for use.');
      
      console.log('\n🌐 Frontend Testing');
      console.log('==================');
      console.log('1. Start the frontend development server:');
      console.log('   cd Frontend && npm run dev');
      console.log('2. Navigate to: http://localhost:3000/test-auth');
      console.log('3. Test the authentication UI with the accounts below');
      
      console.log('\n👤 Test Accounts Available:');
      console.log('===========================');
      console.log('Admin:     <EMAIL>     / Admin123!');
      console.log('Moderator: <EMAIL> / Moderator123!');
      console.log('Editor:    <EMAIL>    / Editor123!');
      console.log('User 1:    <EMAIL>     / User123!');
      console.log('User 2:    <EMAIL>     / User123!');
      
      console.log('\n🧪 Manual Testing Checklist:');
      console.log('============================');
      console.log('□ Login with email and password');
      console.log('□ Login with username and password');
      console.log('□ Register new user account');
      console.log('□ Test password strength validation');
      console.log('□ Test forgot password flow');
      console.log('□ Test email verification');
      console.log('□ Test different user roles');
      console.log('□ Test logout functionality');
      console.log('□ Test token refresh');
      console.log('□ Test rate limiting');
      
    } else {
      console.log('\n⚠️ Some tests failed. Please check the logs above for details.');
      console.log('\n🔧 Troubleshooting:');
      console.log('==================');
      console.log('1. Check server logs for errors');
      console.log('2. Verify database connection and schema');
      console.log('3. Check environment variables');
      console.log('4. Ensure all dependencies are installed');
      console.log('5. Check network connectivity');
    }

    console.log('\n📚 Additional Resources:');
    console.log('=======================');
    console.log('• API Documentation: Backend/docs/AUTHENTICATION.md');
    console.log('• Test Reports: Backend/test-reports/');
    console.log('• Frontend Test Page: Frontend/src/pages/TestAuth.tsx');
    console.log('• Backend Test Scripts: Backend/scripts/');

    return allPassed;

  } catch (error) {
    console.error('\n❌ Test suite execution failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// 运行测试套件
if (require.main === module) {
  runAllTests()
    .then(success => {
      console.log('\n' + '='.repeat(50));
      console.log(success ? '✅ Test suite completed successfully' : '❌ Test suite completed with failures');
      console.log('='.repeat(50));
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('\n❌ Test suite failed to execute:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
