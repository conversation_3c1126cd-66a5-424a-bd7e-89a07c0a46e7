const { sequelize, testConnection } = require('../config/database');
const { seedDatabase } = require('../seeders/articles');

const runSeed = async () => {
  try {
    console.log('🚀 Starting database setup and seeding...');
    
    // Test connection
    await testConnection();
    
    // Sync database (create tables)
    await sequelize.sync({ force: true }); // force: true will drop and recreate tables
    console.log('📊 Database tables synchronized');
    
    // Seed data
    await seedDatabase();
    
    console.log('✅ Database setup and seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during database setup:', error);
    process.exit(1);
  }
};

runSeed();
