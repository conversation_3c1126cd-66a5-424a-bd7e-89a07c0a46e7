const { supabase } = require('../config/supabase');

// 创建用户表的SQL
const createUsersTable = `
  CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100),
    avatar_url TEXT,
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    role VARCHAR(20) DEFAULT 'user',
    last_login_at TIMESTAMPTZ,
    email_verified_at TIMESTAMPTZ,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    articles_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );
`;

// 创建文章表的SQL
const createArticlesTable = `
  CREATE TABLE IF NOT EXISTS articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    category VARCHAR(50) DEFAULT 'General',
    tags TEXT[],
    image_url TEXT,
    published BOOLEAN DEFAULT FALSE,
    featured BOOLEAN DEFAULT FALSE,
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    read_time INTEGER,
    published_at TIMESTAMPTZ,
    slug VARCHAR(300) UNIQUE,
    meta_title VARCHAR(60),
    meta_description VARCHAR(160),
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );
`;

// 创建评论表的SQL
const createCommentsTable = `
  CREATE TABLE IF NOT EXISTS comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    likes INTEGER DEFAULT 0,
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );
`;

// 创建索引
const createIndexes = `
  CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
  CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
  CREATE INDEX IF NOT EXISTS idx_articles_author ON articles(author_id);
  CREATE INDEX IF NOT EXISTS idx_articles_published ON articles(published);
  CREATE INDEX IF NOT EXISTS idx_articles_category ON articles(category);
  CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles(slug);
  CREATE INDEX IF NOT EXISTS idx_comments_article ON comments(article_id);
  CREATE INDEX IF NOT EXISTS idx_comments_author ON comments(author_id);
  CREATE INDEX IF NOT EXISTS idx_comments_parent ON comments(parent_id);
`;

// 创建更新时间触发器
const createUpdateTrigger = `
  CREATE OR REPLACE FUNCTION update_updated_at_column()
  RETURNS TRIGGER AS $$
  BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
  END;
  $$ language 'plpgsql';

  DROP TRIGGER IF EXISTS update_users_updated_at ON users;
  CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

  DROP TRIGGER IF EXISTS update_articles_updated_at ON articles;
  CREATE TRIGGER update_articles_updated_at
    BEFORE UPDATE ON articles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

  DROP TRIGGER IF EXISTS update_comments_updated_at ON comments;
  CREATE TRIGGER update_comments_updated_at
    BEFORE UPDATE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
`;

// 启用行级安全策略
const enableRLS = `
  ALTER TABLE users ENABLE ROW LEVEL SECURITY;
  ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
  ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

  -- 用户只能查看和更新自己的记录
  CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);
  
  CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

  -- 所有人都可以查看已发布的文章
  CREATE POLICY "Anyone can view published articles" ON articles
    FOR SELECT USING (published = true);

  -- 用户可以管理自己的文章
  CREATE POLICY "Users can manage own articles" ON articles
    FOR ALL USING (auth.uid() = author_id);

  -- 所有人都可以查看评论
  CREATE POLICY "Anyone can view comments" ON comments
    FOR SELECT USING (true);

  -- 认证用户可以创建评论
  CREATE POLICY "Authenticated users can create comments" ON comments
    FOR INSERT WITH CHECK (auth.uid() = author_id);

  -- 用户可以管理自己的评论
  CREATE POLICY "Users can manage own comments" ON comments
    FOR ALL USING (auth.uid() = author_id);
`;

// 初始化Supabase数据库
async function setupSupabaseDatabase() {
  try {
    console.log('🚀 Starting Supabase database setup...');

    // 执行SQL命令
    const sqlCommands = [
      createUsersTable,
      createArticlesTable,
      createCommentsTable,
      createIndexes,
      createUpdateTrigger,
      enableRLS
    ];

    for (const sql of sqlCommands) {
      const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      if (error) {
        console.error('SQL execution error:', error);
        // 继续执行其他命令
      }
    }

    console.log('✅ Database tables created successfully!');

    // 创建示例用户
    await createSampleUsers();

    console.log('🎉 Supabase database setup completed!');
    console.log('');
    console.log('📋 Test Accounts:');
    console.log('👤 Admin: <EMAIL> / admin123456');
    console.log('👤 Demo: <EMAIL> / demo123456');
    console.log('');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    throw error;
  }
}

// 创建示例用户
async function createSampleUsers() {
  try {
    // 注册管理员用户
    const { data: adminAuth, error: adminError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'admin123456',
      options: {
        data: {
          username: 'admin',
          display_name: 'Administrator',
          role: 'admin'
        }
      }
    });

    if (adminError && adminError.message !== 'User already registered') {
      console.error('Admin user creation error:', adminError);
    } else {
      console.log('✅ Admin user created/exists');
    }

    // 注册演示用户
    const { data: demoAuth, error: demoError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'demo123456',
      options: {
        data: {
          username: 'demo_user',
          display_name: 'Demo User',
          role: 'user'
        }
      }
    });

    if (demoError && demoError.message !== 'User already registered') {
      console.error('Demo user creation error:', demoError);
    } else {
      console.log('✅ Demo user created/exists');
    }

  } catch (error) {
    console.error('Sample users creation error:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupSupabaseDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { setupSupabaseDatabase };
