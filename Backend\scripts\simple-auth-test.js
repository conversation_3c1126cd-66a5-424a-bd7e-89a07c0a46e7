#!/usr/bin/env node

const axios = require('axios');

// 测试账户
const testAccounts = [
  {
    username: 'admin_test',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin'
  },
  {
    username: 'user_test1',
    email: '<EMAIL>',
    password: 'User123!',
    role: 'user'
  }
];

console.log('🧪 Simple Authentication Test');
console.log('=============================');

async function testLogin(account) {
  try {
    console.log(`\n🔐 Testing login for ${account.username}...`);
    
    const response = await axios.post('http://localhost:5000/api/auth-enhanced/login', {
      identifier: account.email,
      password: account.password,
      rememberMe: false
    }, {
      timeout: 10000
    });

    if (response.status === 200 && response.data.success) {
      console.log(`✅ Login successful for ${account.username}`);
      console.log(`   User ID: ${response.data.data.user.id}`);
      console.log(`   Username: ${response.data.data.user.username}`);
      console.log(`   Email: ${response.data.data.user.email}`);
      console.log(`   Role: ${response.data.data.user.role}`);
      console.log(`   Email Verified: ${response.data.data.user.isEmailVerified}`);
      console.log(`   Access Token: ${response.data.data.tokens.accessToken ? 'Received' : 'Missing'}`);
      console.log(`   Refresh Token: ${response.data.data.tokens.refreshToken ? 'Received' : 'Missing'}`);
      
      // 测试Token验证
      const token = response.data.data.tokens.accessToken;
      try {
        const meResponse = await axios.get('http://localhost:5000/api/auth-enhanced/me', {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 5000
        });
        
        if (meResponse.status === 200) {
          console.log(`✅ Token validation successful`);
        } else {
          console.log(`❌ Token validation failed`);
        }
      } catch (tokenError) {
        console.log(`❌ Token validation error: ${tokenError.message}`);
      }
      
      return true;
    } else {
      console.log(`❌ Login failed for ${account.username}: Invalid response`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Login failed for ${account.username}:`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   Error: Server not running on http://localhost:5000`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

async function testRegistration() {
  try {
    console.log(`\n📝 Testing user registration...`);
    
    const newUser = {
      username: 'test_user_' + Date.now(),
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User'
    };

    const response = await axios.post('http://localhost:5000/api/auth-enhanced/register', newUser, {
      timeout: 10000
    });

    if (response.status === 201 && response.data.success) {
      console.log(`✅ Registration successful`);
      console.log(`   Username: ${newUser.username}`);
      console.log(`   Email: ${newUser.email}`);
      console.log(`   User ID: ${response.data.data.user.id}`);
      console.log(`   Access Token: ${response.data.data.tokens.accessToken ? 'Received' : 'Missing'}`);
      return true;
    } else {
      console.log(`❌ Registration failed: Invalid response`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Registration failed:`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   Error: Server not running on http://localhost:5000`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

async function testPasswordStrength() {
  try {
    console.log(`\n💪 Testing password strength validation...`);
    
    const passwords = [
      { password: '123', expected: false },
      { password: 'password', expected: false },
      { password: 'Password123!', expected: true }
    ];

    for (const test of passwords) {
      try {
        const response = await axios.post('http://localhost:5000/api/auth-enhanced/check-password-strength', {
          password: test.password
        }, {
          timeout: 5000
        });

        const isValid = response.data.data.isValid;
        const strength = response.data.data.strength;
        
        console.log(`   Password "${test.password}": ${strength} (${isValid ? 'Valid' : 'Invalid'})`);
      } catch (error) {
        console.log(`   Password "${test.password}": Error - ${error.message}`);
      }
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Password strength test failed: ${error.message}`);
    return false;
  }
}

async function checkServerHealth() {
  try {
    console.log(`\n🏥 Checking server health...`);
    
    const response = await axios.get('http://localhost:5000/api/health', {
      timeout: 5000
    });

    if (response.status === 200) {
      console.log(`✅ Server is healthy`);
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Database: ${response.data.database || 'Unknown'}`);
      console.log(`   Uptime: ${Math.floor(response.data.uptime || 0)}s`);
      return true;
    } else {
      console.log(`❌ Server health check failed`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Server health check failed:`);
    if (error.code === 'ECONNREFUSED') {
      console.log(`   Error: Server not running on http://localhost:5000`);
      console.log(`   Please start the server with: node server.js`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

async function runTests() {
  console.log('Starting authentication tests...\n');
  
  let passed = 0;
  let total = 0;

  // 1. Check server health
  total++;
  if (await checkServerHealth()) {
    passed++;
  } else {
    console.log('\n❌ Server is not accessible. Please start the server first.');
    console.log('   Run: cd Backend && node server.js');
    return;
  }

  // 2. Test password strength
  total++;
  if (await testPasswordStrength()) {
    passed++;
  }

  // 3. Test registration
  total++;
  if (await testRegistration()) {
    passed++;
  }

  // 4. Test login for each account
  for (const account of testAccounts) {
    total++;
    if (await testLogin(account)) {
      passed++;
    }
  }

  // Summary
  console.log('\n📊 Test Results');
  console.log('===============');
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed} ✅`);
  console.log(`Failed: ${total - passed} ❌`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

  if (passed === total) {
    console.log('\n🎉 All tests passed! Authentication system is working correctly.');
    
    console.log('\n📋 Available Test Accounts:');
    console.log('===========================');
    testAccounts.forEach(account => {
      console.log(`${account.role.toUpperCase().padEnd(8)} | ${account.email.padEnd(25)} | ${account.password}`);
    });
    
    console.log('\n🌐 Frontend Testing:');
    console.log('===================');
    console.log('1. Start frontend: cd Frontend && npm run dev');
    console.log('2. Visit: http://localhost:3000/test-auth');
    console.log('3. Test login/register with the accounts above');
    
  } else {
    console.log('\n⚠️ Some tests failed. Please check the server logs and configuration.');
  }
}

// Run tests
runTests().catch(error => {
  console.error('\n❌ Test execution failed:', error.message);
  process.exit(1);
});
