#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function repairDatabase() {
  console.log('🔧 Starting database repair and optimization...');
  
  try {
    // Connect to database
    console.log('📡 Connecting to database...');
    const connected = await connectWithRetry();
    
    if (!connected) {
      console.log('❌ Database connection failed');
      return false;
    }
    
    console.log('✅ Database connected successfully');
    
    // Sync database models
    console.log('🏗️ Synchronizing database models...');
    await sequelize.sync({ alter: true });
    console.log('✅ Database models synchronized');
    
    // Check critical tables
    console.log('📋 Checking critical tables...');
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('✅ Found tables:', tables.join(', '));
    
    // Create basic indexes
    console.log('📊 Creating basic indexes...');
    const indexQueries = [
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_author ON articles(author);`,
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_published ON articles(published);`
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ Index already exists');
        } else {
          console.log('⚠️ Index creation failed:', error.message);
        }
      }
    }
    
    // Update statistics
    console.log('📈 Updating table statistics...');
    await sequelize.query('ANALYZE;');
    console.log('✅ Statistics updated');
    
    // Create admin user if not exists
    console.log('👤 Checking admin user...');
    const [adminCheck] = await sequelize.query(`
      SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'
    `);
    
    if (adminCheck[0].count === '0') {
      console.log('👤 Creating admin user...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123456', 10);
      
      await sequelize.query(`
        INSERT INTO users (username, email, password, "firstName", "lastName", role, "emailVerified", "createdAt", "updatedAt")
        VALUES ('admin', '<EMAIL>', :password, 'Admin', 'User', 'admin', true, NOW(), NOW())
      `, {
        replacements: { password: hashedPassword }
      });
      
      console.log('✅ Admin user created (<EMAIL> / admin123456)');
    } else {
      console.log('✅ Admin user already exists');
    }
    
    console.log('🎉 Database repair completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Database repair failed:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the repair
if (require.main === module) {
  repairDatabase()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Repair failed:', error);
      process.exit(1);
    });
}

module.exports = { repairDatabase };
