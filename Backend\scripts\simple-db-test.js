#!/usr/bin/env node

const { sequelize, connectWithRetry } = require('../config/database-enhanced');

async function testDatabase() {
  console.log('🚀 Starting database connection test...');
  
  try {
    // Test basic connection
    console.log('📡 Testing basic connection...');
    const connected = await connectWithRetry();
    
    if (connected) {
      console.log('✅ Database connection successful');
      
      // Test simple query
      console.log('🔍 Testing simple query...');
      const result = await sequelize.query('SELECT NOW() as current_time, version() as version');
      console.log('✅ Query successful:', result[0][0]);
      
      // Test database info
      console.log('📊 Getting database info...');
      const [dbInfo] = await sequelize.query(`
        SELECT 
          current_database() as database_name,
          current_user as current_user,
          inet_server_addr() as server_address,
          inet_server_port() as server_port
      `);
      console.log('✅ Database info:', dbInfo[0]);
      
      console.log('🎉 All tests passed! Database is working correctly.');
      return true;
    } else {
      console.log('❌ Database connection failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testDatabase()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testDatabase };
