#!/usr/bin/env node

/**
 * 简化的测试账户信息展示
 * 不依赖数据库连接，直接显示测试账户信息
 */

console.log('🧪 Newzora 测试账户信息');
console.log('======================');

// 测试账户数据
const testAccounts = [
  // 管理员账户
  {
    username: 'admin_test',
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    status: '✅ Active',
    description: 'System Administrator with full permissions'
  },
  {
    username: 'sysadmin_test',
    email: '<EMAIL>',
    password: 'SysAdmin123!',
    firstName: 'System',
    lastName: 'Admin',
    role: 'admin',
    status: '✅ Active',
    description: 'System Administrator for advanced testing'
  },
  
  // 内容管理员账户
  {
    username: 'moderator_test',
    email: '<EMAIL>',
    password: 'Moderator123!',
    firstName: 'Content',
    lastName: 'Moderator',
    role: 'moderator',
    status: '✅ Active',
    description: 'Content Moderator with management permissions'
  },
  {
    username: 'editor_chief',
    email: '<EMAIL>',
    password: 'Editor123!',
    firstName: 'Editor',
    lastName: 'Chief',
    role: 'moderator',
    status: '✅ Active',
    description: 'Editor Chief for content management'
  },
  
  // 普通用户账户
  {
    username: 'user_test1',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'John',
    lastName: 'Doe',
    role: 'user',
    status: '✅ Active',
    description: 'Active user for basic functionality testing'
  },
  {
    username: 'user_test2',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'user',
    status: '✅ Active',
    description: 'Regular user for social features testing'
  },
  {
    username: 'author_test',
    email: '<EMAIL>',
    password: 'Author123!',
    firstName: 'Author',
    lastName: 'Writer',
    role: 'user',
    status: '✅ Active',
    description: 'Content author for article creation testing'
  },
  {
    username: 'reader_test',
    email: '<EMAIL>',
    password: 'Reader123!',
    firstName: 'Reader',
    lastName: 'User',
    role: 'user',
    status: '✅ Active',
    description: 'Reader user for consumption testing'
  },
  
  // 特殊状态账户
  {
    username: 'unverified_test',
    email: '<EMAIL>',
    password: 'Unverified123!',
    firstName: 'Unverified',
    lastName: 'User',
    role: 'user',
    status: '📧 Unverified',
    description: 'Unverified user for email verification testing'
  },
  {
    username: 'inactive_test',
    email: '<EMAIL>',
    password: 'Inactive123!',
    firstName: 'Inactive',
    lastName: 'User',
    role: 'user',
    status: '❌ Inactive',
    description: 'Inactive user for account status testing'
  }
];

function displayTestAccounts() {
  console.log('\n📋 测试账户列表');
  console.log('===============');
  console.log('Role      | Username        | Email                  | Password        | Status');
  console.log('----------|-----------------|------------------------|-----------------|-------------');
  
  testAccounts.forEach(account => {
    console.log(`${account.role.padEnd(9)} | ${account.username.padEnd(15)} | ${account.email.padEnd(22)} | ${account.password.padEnd(15)} | ${account.status}`);
  });

  console.log('\n📊 账户统计');
  console.log('===========');
  const stats = testAccounts.reduce((acc, account) => {
    acc[account.role] = (acc[account.role] || 0) + 1;
    return acc;
  }, {});
  
  Object.entries(stats).forEach(([role, count]) => {
    console.log(`${role}: ${count} 个账户`);
  });

  console.log('\n🔐 登录测试指南');
  console.log('===============');
  console.log('1. 邮箱登录测试:');
  console.log('   POST /api/auth-enhanced/login');
  console.log('   {');
  console.log('     "identifier": "<EMAIL>",');
  console.log('     "password": "Admin123!",');
  console.log('     "rememberMe": false');
  console.log('   }');
  
  console.log('\n2. 用户名登录测试:');
  console.log('   POST /api/auth-enhanced/login');
  console.log('   {');
  console.log('     "identifier": "user_test1",');
  console.log('     "password": "User123!",');
  console.log('     "rememberMe": true');
  console.log('   }');

  console.log('\n📝 注册测试指南');
  console.log('===============');
  console.log('POST /api/auth-enhanced/register');
  console.log('{');
  console.log('  "username": "newuser_test",');
  console.log('  "email": "<EMAIL>",');
  console.log('  "password": "NewUser123!",');
  console.log('  "firstName": "New",');
  console.log('  "lastName": "User",');
  console.log('  "acceptTerms": true');
  console.log('}');

  console.log('\n🔄 密码找回测试');
  console.log('===============');
  console.log('POST /api/auth-enhanced/forgot-password');
  console.log('{');
  console.log('  "email": "<EMAIL>"');
  console.log('}');

  console.log('\n📧 邮箱验证测试');
  console.log('===============');
  console.log('POST /api/auth-enhanced/resend-verification');
  console.log('{');
  console.log('  "email": "<EMAIL>"');
  console.log('}');

  console.log('\n💪 密码强度测试');
  console.log('===============');
  console.log('POST /api/auth-enhanced/check-password-strength');
  console.log('{');
  console.log('  "password": "TestPassword123!"');
  console.log('}');

  console.log('\n🌐 前端测试页面');
  console.log('===============');
  console.log('访问: http://localhost:3000/test-auth');
  console.log('使用上述任意账户进行快速登录测试');

  console.log('\n🧪 测试场景建议');
  console.log('===============');
  console.log('✅ 基础功能测试:');
  console.log('   - 管理员登录 (<EMAIL> / Admin123!)');
  console.log('   - 普通用户登录 (<EMAIL> / User123!)');
  console.log('   - 用户名登录 (user_test1 / User123!)');
  console.log('   - 新用户注册');
  console.log('   - 密码强度验证');
  
  console.log('\n⚠️ 异常情况测试:');
  console.log('   - 错误密码登录');
  console.log('   - 不存在用户登录');
  console.log('   - 重复用户名注册');
  console.log('   - 重复邮箱注册');
  console.log('   - 弱密码注册');
  console.log('   - 未验证用户登录 (<EMAIL>)');
  console.log('   - 停用账户登录 (<EMAIL>)');

  console.log('\n🔧 权限测试:');
  console.log('   - 管理员访问管理面板');
  console.log('   - 内容管理员权限验证');
  console.log('   - 普通用户权限限制');

  console.log('\n📊 安全测试:');
  console.log('   - 速率限制测试 (快速连续请求)');
  console.log('   - Token验证和刷新');
  console.log('   - 会话管理');
  console.log('   - CORS策略验证');

  console.log('\n💡 使用提示');
  console.log('===========');
  console.log('1. 确保后端服务器运行在 http://localhost:5000');
  console.log('2. 确保前端服务器运行在 http://localhost:3000');
  console.log('3. 使用 start-stable.bat 启动完整环境');
  console.log('4. 查看服务器日志获取详细错误信息');
  console.log('5. 使用浏览器开发者工具查看网络请求');

  console.log('\n✅ 测试账户信息展示完成！');
}

// 运行展示
if (require.main === module) {
  displayTestAccounts();
}

module.exports = { testAccounts, displayTestAccounts };
