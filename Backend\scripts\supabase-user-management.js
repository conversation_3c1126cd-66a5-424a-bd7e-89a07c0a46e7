#!/usr/bin/env node

/**
 * Supabase 用户管理脚本
 * 用于管理 Supabase 用户账户的创建、删除和更新
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const { createClient } = require('@supabase/supabase-js');

// 创建具有管理员权限的 Supabase 客户端
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

class SupabaseUserManager {
  constructor() {
    console.log('🔐 Supabase User Manager initialized');
    console.log(`📍 Supabase URL: ${process.env.SUPABASE_URL}`);
  }

  // 创建管理员用户
  async createAdminUser(email, password, displayName = 'Administrator') {
    try {
      console.log(`👤 Creating admin user: ${email}`);
      
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // 自动确认邮箱
        user_metadata: {
          display_name: displayName,
          role: 'admin',
          avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=6366f1&color=fff&size=128`
        }
      });

      if (error) {
        throw error;
      }

      console.log('✅ Admin user created successfully:', data.user.email);
      return data.user;
    } catch (error) {
      console.error('❌ Error creating admin user:', error.message);
      throw error;
    }
  }

  // 创建普通用户
  async createUser(email, password, displayName, role = 'user') {
    try {
      console.log(`👤 Creating user: ${email}`);
      
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          display_name: displayName,
          role: role,
          avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=6366f1&color=fff&size=128`
        }
      });

      if (error) {
        throw error;
      }

      console.log('✅ User created successfully:', data.user.email);
      return data.user;
    } catch (error) {
      console.error('❌ Error creating user:', error.message);
      throw error;
    }
  }

  // 列出所有用户
  async listUsers() {
    try {
      console.log('📋 Fetching all users...');
      
      const { data, error } = await supabaseAdmin.auth.admin.listUsers();

      if (error) {
        throw error;
      }

      console.log(`📊 Found ${data.users.length} users:`);
      data.users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email} (${user.user_metadata?.role || 'user'}) - Created: ${new Date(user.created_at).toLocaleDateString()}`);
      });

      return data.users;
    } catch (error) {
      console.error('❌ Error listing users:', error.message);
      throw error;
    }
  }

  // 删除用户
  async deleteUser(userId) {
    try {
      console.log(`🗑️ Deleting user: ${userId}`);
      
      const { data, error } = await supabaseAdmin.auth.admin.deleteUser(userId);

      if (error) {
        throw error;
      }

      console.log('✅ User deleted successfully');
      return data;
    } catch (error) {
      console.error('❌ Error deleting user:', error.message);
      throw error;
    }
  }

  // 重置用户密码
  async resetUserPassword(email) {
    try {
      console.log(`🔄 Sending password reset email to: ${email}`);
      
      const { data, error } = await supabaseAdmin.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password`
      });

      if (error) {
        throw error;
      }

      console.log('✅ Password reset email sent successfully');
      return data;
    } catch (error) {
      console.error('❌ Error sending password reset email:', error.message);
      throw error;
    }
  }

  // 更新用户角色
  async updateUserRole(userId, role) {
    try {
      console.log(`👤 Updating user role: ${userId} -> ${role}`);
      
      const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
        user_metadata: { role }
      });

      if (error) {
        throw error;
      }

      console.log('✅ User role updated successfully');
      return data.user;
    } catch (error) {
      console.error('❌ Error updating user role:', error.message);
      throw error;
    }
  }
}

// 命令行接口
async function main() {
  const manager = new SupabaseUserManager();
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'create-admin':
        if (args.length < 3) {
          console.log('Usage: node supabase-user-management.js create-admin <email> <password> [displayName]');
          process.exit(1);
        }
        await manager.createAdminUser(args[1], args[2], args[3]);
        break;

      case 'create-user':
        if (args.length < 4) {
          console.log('Usage: node supabase-user-management.js create-user <email> <password> <displayName> [role]');
          process.exit(1);
        }
        await manager.createUser(args[1], args[2], args[3], args[4]);
        break;

      case 'list':
        await manager.listUsers();
        break;

      case 'delete':
        if (args.length < 2) {
          console.log('Usage: node supabase-user-management.js delete <userId>');
          process.exit(1);
        }
        await manager.deleteUser(args[1]);
        break;

      case 'reset-password':
        if (args.length < 2) {
          console.log('Usage: node supabase-user-management.js reset-password <email>');
          process.exit(1);
        }
        await manager.resetUserPassword(args[1]);
        break;

      case 'update-role':
        if (args.length < 3) {
          console.log('Usage: node supabase-user-management.js update-role <userId> <role>');
          process.exit(1);
        }
        await manager.updateUserRole(args[1], args[2]);
        break;

      default:
        console.log('Available commands:');
        console.log('  create-admin <email> <password> [displayName]');
        console.log('  create-user <email> <password> <displayName> [role]');
        console.log('  list');
        console.log('  delete <userId>');
        console.log('  reset-password <email>');
        console.log('  update-role <userId> <role>');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = SupabaseUserManager;
