#!/usr/bin/env node

/**
 * API端点测试脚本
 * 验证所有API端点是否正确返回JSON
 */

const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:5000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('🔍 API端点测试');
console.log('==============');

async function testEndpoint(method, url, data = null, description = '') {
  try {
    console.log(`\n📡 测试: ${method.toUpperCase()} ${url}`);
    console.log(`描述: ${description}`);
    
    let response;
    if (method.toLowerCase() === 'get') {
      response = await api.get(url);
    } else if (method.toLowerCase() === 'post') {
      response = await api.post(url, data);
    }
    
    console.log(`✅ 状态码: ${response.status}`);
    console.log(`✅ 内容类型: ${response.headers['content-type']}`);
    
    // 检查是否是JSON响应
    const contentType = response.headers['content-type'];
    if (contentType && contentType.includes('application/json')) {
      console.log(`✅ 正确返回JSON格式`);
      
      // 显示响应数据的简要信息
      if (response.data) {
        if (response.data.success !== undefined) {
          console.log(`   成功状态: ${response.data.success}`);
        }
        if (response.data.message) {
          console.log(`   消息: ${response.data.message}`);
        }
        if (response.data.status) {
          console.log(`   状态: ${response.data.status}`);
        }
      }
    } else {
      console.log(`❌ 返回的不是JSON格式: ${contentType}`);
      console.log(`   响应内容预览: ${response.data.toString().substring(0, 100)}...`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ 请求失败:`);
    console.log(`   状态码: ${error.response?.status || 'N/A'}`);
    console.log(`   错误信息: ${error.message}`);
    
    if (error.response) {
      const contentType = error.response.headers['content-type'];
      console.log(`   响应类型: ${contentType}`);
      
      // 检查是否返回HTML
      if (contentType && contentType.includes('text/html')) {
        console.log(`❌ 返回HTML页面而不是JSON - 这是问题所在!`);
        const htmlContent = error.response.data.toString();
        if (htmlContent.includes('<!DOCTYPE')) {
          console.log(`   确认: 返回完整HTML文档`);
        }
      }
    }
    
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始API端点测试...\n');
  
  const tests = [
    // 基础端点
    {
      method: 'GET',
      url: '/api/health',
      description: '健康检查端点'
    },
    {
      method: 'GET',
      url: '/',
      description: '根路径信息'
    },
    
    // 认证端点
    {
      method: 'POST',
      url: '/api/auth-enhanced/login',
      data: {
        identifier: '<EMAIL>',
        password: 'Admin123!',
        rememberMe: false
      },
      description: '用户登录'
    },
    {
      method: 'POST',
      url: '/api/auth-enhanced/check-password-strength',
      data: {
        password: 'TestPassword123!'
      },
      description: '密码强度检查'
    },
    {
      method: 'POST',
      url: '/api/auth-enhanced/register',
      data: {
        username: `test_${Date.now()}`,
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'User'
      },
      description: '用户注册'
    },
    
    // 错误情况测试
    {
      method: 'POST',
      url: '/api/auth-enhanced/login',
      data: {
        identifier: '<EMAIL>',
        password: 'wrongpassword'
      },
      description: '错误登录（应返回401但仍是JSON）'
    },
    
    // 不存在的端点
    {
      method: 'GET',
      url: '/api/nonexistent',
      description: '不存在的端点（应返回404但仍是JSON）'
    }
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const success = await testEndpoint(test.method, test.url, test.data, test.description);
    if (success) passed++;
    
    // 等待一下避免速率限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 测试结果汇总');
  console.log('===============');
  console.log(`总测试数: ${total}`);
  console.log(`通过: ${passed}`);
  console.log(`失败: ${total - passed}`);
  console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 所有API端点都正确返回JSON格式！');
    console.log('前端的JSON解析错误可能是由其他原因引起的。');
  } else {
    console.log('\n⚠️ 部分API端点返回非JSON格式，这可能是问题的根源。');
  }
  
  console.log('\n💡 下一步建议:');
  console.log('1. 如果所有测试通过，检查前端代码中的API调用');
  console.log('2. 如果有测试失败，检查后端路由配置');
  console.log('3. 检查浏览器开发者工具中的Network标签');
  console.log('4. 确认前端和后端服务器都在正确的端口运行');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('\n💥 测试执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = { testEndpoint, runTests };
