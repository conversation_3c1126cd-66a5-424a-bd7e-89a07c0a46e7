#!/usr/bin/env node

const axios = require('axios');
const { faker } = require('@faker-js/faker');

// 配置axios
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000
});

// 测试数据
const testUser = {
  username: faker.internet.userName(),
  email: faker.internet.email(),
  password: 'TestPassword123!',
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName()
};

console.log('🧪 Testing Enhanced Authentication System');
console.log('==========================================');

async function testAuthSystem() {
  let authToken = null;
  
  try {
    // 1. 测试密码强度检查
    console.log('\n1. Testing password strength check...');
    try {
      const weakPasswordResponse = await api.post('/auth-enhanced/check-password-strength', {
        password: '123'
      });
      console.log('✅ Weak password detected:', weakPasswordResponse.data.data.strength);
      
      const strongPasswordResponse = await api.post('/auth-enhanced/check-password-strength', {
        password: testUser.password
      });
      console.log('✅ Strong password detected:', strongPasswordResponse.data.data.strength);
    } catch (error) {
      console.log('❌ Password strength check failed:', error.message);
    }

    // 2. 测试用户注册
    console.log('\n2. Testing user registration...');
    try {
      const registerResponse = await api.post('/auth-enhanced/register', testUser);
      console.log('✅ User registration successful');
      console.log('   User ID:', registerResponse.data.data.user.id);
      console.log('   Email verified:', registerResponse.data.data.user.isEmailVerified);
      authToken = registerResponse.data.data.tokens.accessToken;
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('⚠️ User already exists, trying to login...');
        
        // 尝试登录现有用户
        try {
          const loginResponse = await api.post('/auth-enhanced/login', {
            identifier: testUser.email,
            password: testUser.password
          });
          console.log('✅ Login successful');
          authToken = loginResponse.data.data.tokens.accessToken;
        } catch (loginError) {
          console.log('❌ Login failed:', loginError.response?.data?.message || loginError.message);
          return;
        }
      } else {
        console.log('❌ Registration failed:', error.response?.data?.message || error.message);
        return;
      }
    }

    // 3. 测试Token验证
    console.log('\n3. Testing token authentication...');
    try {
      const meResponse = await api.get('/auth-enhanced/me', {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Token authentication successful');
      console.log('   Username:', meResponse.data.data.user.username);
      console.log('   Email:', meResponse.data.data.user.email);
    } catch (error) {
      console.log('❌ Token authentication failed:', error.response?.data?.message || error.message);
    }

    // 4. 测试忘记密码
    console.log('\n4. Testing forgot password...');
    try {
      const forgotResponse = await api.post('/auth-enhanced/forgot-password', {
        email: testUser.email
      });
      console.log('✅ Forgot password request successful');
    } catch (error) {
      console.log('❌ Forgot password failed:', error.response?.data?.message || error.message);
    }

    // 5. 测试刷新Token
    console.log('\n5. Testing token refresh...');
    try {
      // 首先获取refresh token
      const loginResponse = await api.post('/auth-enhanced/login', {
        identifier: testUser.email,
        password: testUser.password
      });
      
      const refreshToken = loginResponse.data.data.tokens.refreshToken;
      
      const refreshResponse = await api.post('/auth-enhanced/refresh-token', {
        refreshToken
      });
      console.log('✅ Token refresh successful');
      console.log('   New access token received');
    } catch (error) {
      console.log('❌ Token refresh failed:', error.response?.data?.message || error.message);
    }

    // 6. 测试修改密码
    console.log('\n6. Testing password change...');
    try {
      const newPassword = 'NewTestPassword123!';
      const changePasswordResponse = await api.post('/auth-enhanced/change-password', {
        currentPassword: testUser.password,
        newPassword
      }, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Password change successful');
      
      // 更新测试密码
      testUser.password = newPassword;
    } catch (error) {
      console.log('❌ Password change failed:', error.response?.data?.message || error.message);
    }

    // 7. 测试用户资料更新
    console.log('\n7. Testing profile update...');
    try {
      const updateData = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        bio: faker.lorem.sentence()
      };
      
      const updateResponse = await api.put('/auth-enhanced/profile', updateData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Profile update successful');
      console.log('   Updated name:', updateResponse.data.data.user.firstName, updateResponse.data.data.user.lastName);
    } catch (error) {
      console.log('❌ Profile update failed:', error.response?.data?.message || error.message);
    }

    // 8. 测试登出
    console.log('\n8. Testing logout...');
    try {
      const logoutResponse = await api.post('/auth-enhanced/logout', {}, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Logout successful');
    } catch (error) {
      console.log('❌ Logout failed:', error.response?.data?.message || error.message);
    }

    // 9. 测试无效Token
    console.log('\n9. Testing invalid token handling...');
    try {
      await api.get('/auth-enhanced/me', {
        headers: { Authorization: 'Bearer invalid_token' }
      });
      console.log('❌ Invalid token should have been rejected');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid token properly rejected');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // 10. 测试速率限制
    console.log('\n10. Testing rate limiting...');
    try {
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          api.post('/auth-enhanced/login', {
            identifier: '<EMAIL>',
            password: 'wrongpassword'
          }).catch(err => err.response)
        );
      }
      
      const responses = await Promise.all(promises);
      const rateLimited = responses.some(response => 
        response?.status === 429 || 
        response?.data?.message?.includes('Too many')
      );
      
      if (rateLimited) {
        console.log('✅ Rate limiting is working');
      } else {
        console.log('⚠️ Rate limiting may not be configured properly');
      }
    } catch (error) {
      console.log('❌ Rate limiting test failed:', error.message);
    }

    console.log('\n🎉 Authentication system testing completed!');
    console.log('==========================================');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testAuthSystem()
    .then(() => {
      console.log('\n✅ All tests completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testAuthSystem };
