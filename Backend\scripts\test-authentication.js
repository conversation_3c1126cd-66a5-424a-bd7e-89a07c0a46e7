#!/usr/bin/env node

const axios = require('axios');
const { testAccounts } = require('./create-test-accounts');

// 配置axios
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('🧪 Testing Newzora Authentication System');
console.log('=========================================');

let testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

function logTest(testName, success, message = '') {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
}

async function testAuthentication() {
  try {
    console.log('\n1. Testing User Registration');
    console.log('============================');
    
    // 测试新用户注册
    const newUser = {
      username: 'test_register_' + Date.now(),
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User'
    };

    try {
      const registerResponse = await api.post('/auth-enhanced/register', newUser);
      logTest('User Registration', registerResponse.status === 201 && registerResponse.data.success);
      
      if (registerResponse.data.success) {
        console.log(`   📧 User created: ${newUser.username} (${newUser.email})`);
        console.log(`   🔑 Access token received: ${registerResponse.data.data.tokens.accessToken ? 'Yes' : 'No'}`);
      }
    } catch (error) {
      logTest('User Registration', false, error.response?.data?.message || error.message);
    }

    console.log('\n2. Testing User Login');
    console.log('=====================');

    // 测试各种账户登录
    for (const account of testAccounts.slice(0, 4)) { // 测试前4个账户
      try {
        const loginResponse = await api.post('/auth-enhanced/login', {
          identifier: account.email,
          password: account.password,
          rememberMe: false
        });

        const success = loginResponse.status === 200 && loginResponse.data.success;
        logTest(`Login ${account.role} (${account.username})`, success);

        if (success) {
          console.log(`   👤 User: ${loginResponse.data.data.user.username}`);
          console.log(`   🔐 Role: ${loginResponse.data.data.user.role}`);
          console.log(`   ✉️ Email verified: ${loginResponse.data.data.user.isEmailVerified}`);
          
          // 测试Token验证
          const token = loginResponse.data.data.tokens.accessToken;
          try {
            const meResponse = await api.get('/auth-enhanced/me', {
              headers: { Authorization: `Bearer ${token}` }
            });
            logTest(`Token validation for ${account.username}`, meResponse.status === 200);
          } catch (tokenError) {
            logTest(`Token validation for ${account.username}`, false, 'Token invalid');
          }
        }
      } catch (error) {
        logTest(`Login ${account.role} (${account.username})`, false, error.response?.data?.message || error.message);
      }
    }

    console.log('\n3. Testing Username Login');
    console.log('=========================');

    // 测试用户名登录
    try {
      const usernameLoginResponse = await api.post('/auth-enhanced/login', {
        identifier: testAccounts[0].username, // 使用用户名而不是邮箱
        password: testAccounts[0].password
      });

      logTest('Username Login', usernameLoginResponse.status === 200 && usernameLoginResponse.data.success);
    } catch (error) {
      logTest('Username Login', false, error.response?.data?.message || error.message);
    }

    console.log('\n4. Testing Invalid Credentials');
    console.log('==============================');

    // 测试错误密码
    try {
      await api.post('/auth-enhanced/login', {
        identifier: testAccounts[0].email,
        password: 'WrongPassword123!'
      });
      logTest('Invalid Password Rejection', false, 'Should have failed');
    } catch (error) {
      logTest('Invalid Password Rejection', error.response?.status === 401, 'Correctly rejected');
    }

    // 测试不存在的用户
    try {
      await api.post('/auth-enhanced/login', {
        identifier: '<EMAIL>',
        password: 'Password123!'
      });
      logTest('Nonexistent User Rejection', false, 'Should have failed');
    } catch (error) {
      logTest('Nonexistent User Rejection', error.response?.status === 401, 'Correctly rejected');
    }

    console.log('\n5. Testing Password Strength Validation');
    console.log('=======================================');

    const passwordTests = [
      { password: '123', expected: false, name: 'Weak password (123)' },
      { password: 'password', expected: false, name: 'Common password' },
      { password: 'Password123!', expected: true, name: 'Strong password' },
      { password: 'VeryStrongPassword123!@#', expected: true, name: 'Very strong password' }
    ];

    for (const test of passwordTests) {
      try {
        const strengthResponse = await api.post('/auth-enhanced/check-password-strength', {
          password: test.password
        });

        const isValid = strengthResponse.data.data.isValid;
        logTest(test.name, isValid === test.expected);
        
        if (strengthResponse.data.success) {
          console.log(`   💪 Strength: ${strengthResponse.data.data.strength}`);
        }
      } catch (error) {
        logTest(test.name, false, error.message);
      }
    }

    console.log('\n6. Testing Forgot Password');
    console.log('==========================');

    try {
      const forgotResponse = await api.post('/auth-enhanced/forgot-password', {
        email: testAccounts[0].email
      });

      logTest('Forgot Password Request', forgotResponse.status === 200 && forgotResponse.data.success);
      
      if (forgotResponse.data.success) {
        console.log('   📧 Password reset email would be sent');
      }
    } catch (error) {
      logTest('Forgot Password Request', false, error.response?.data?.message || error.message);
    }

    console.log('\n7. Testing Email Verification Resend');
    console.log('====================================');

    try {
      const resendResponse = await api.post('/auth-enhanced/resend-verification', {
        email: testAccounts[5].email // unverified user
      });

      logTest('Resend Verification Email', resendResponse.status === 200 && resendResponse.data.success);
    } catch (error) {
      logTest('Resend Verification Email', false, error.response?.data?.message || error.message);
    }

    console.log('\n8. Testing Token Refresh');
    console.log('========================');

    try {
      // 先登录获取tokens
      const loginResponse = await api.post('/auth-enhanced/login', {
        identifier: testAccounts[0].email,
        password: testAccounts[0].password
      });

      if (loginResponse.data.success) {
        const refreshToken = loginResponse.data.data.tokens.refreshToken;
        
        const refreshResponse = await api.post('/auth-enhanced/refresh-token', {
          refreshToken
        });

        logTest('Token Refresh', refreshResponse.status === 200 && refreshResponse.data.success);
        
        if (refreshResponse.data.success) {
          console.log('   🔄 New access token received');
        }
      }
    } catch (error) {
      logTest('Token Refresh', false, error.response?.data?.message || error.message);
    }

    console.log('\n9. Testing Rate Limiting');
    console.log('========================');

    // 测试速率限制（快速连续请求）
    const rateLimitPromises = [];
    for (let i = 0; i < 10; i++) {
      rateLimitPromises.push(
        api.post('/auth-enhanced/login', {
          identifier: '<EMAIL>',
          password: 'wrongpassword'
        }).catch(err => err.response)
      );
    }

    try {
      const responses = await Promise.all(rateLimitPromises);
      const rateLimited = responses.some(response => 
        response?.status === 429 || 
        response?.data?.message?.includes('Too many')
      );
      
      logTest('Rate Limiting', rateLimited, rateLimited ? 'Rate limiting active' : 'Rate limiting not triggered');
    } catch (error) {
      logTest('Rate Limiting', false, error.message);
    }

    console.log('\n10. Testing Inactive User Login');
    console.log('===============================');

    try {
      await api.post('/auth-enhanced/login', {
        identifier: testAccounts[6].email, // inactive user
        password: testAccounts[6].password
      });
      logTest('Inactive User Login Block', false, 'Should have been blocked');
    } catch (error) {
      logTest('Inactive User Login Block', error.response?.status === 401, 'Correctly blocked inactive user');
    }

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting authentication tests...\n');
  
  try {
    await testAuthentication();
    
    console.log('\n📊 Test Results Summary');
    console.log('=======================');
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed} ✅`);
    console.log(`Failed: ${testResults.failed} ❌`);
    console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    if (testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Authentication system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the authentication system configuration.');
    }

    console.log('\n💡 Next Steps:');
    console.log('==============');
    console.log('1. Test the frontend login/register forms');
    console.log('2. Verify email templates are working');
    console.log('3. Test social login integration');
    console.log('4. Check WebSocket authentication');
    console.log('5. Test mobile app authentication');

  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// 运行测试
if (require.main === module) {
  runTests()
    .then(() => {
      console.log('\n✅ Test execution completed');
      process.exit(testResults.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testAuthentication, testResults };
