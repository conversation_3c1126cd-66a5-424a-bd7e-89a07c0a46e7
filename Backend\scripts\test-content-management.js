const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_BASE = 'http://localhost:3000';

console.log('🧪 Starting Content Management System Testing');

async function testContentManagement() {
  try {
    // 1. Test user login and get token
    console.log('\n1. 🔐 Testing user login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'demo123456'
    });

    if (!loginResponse.data.token) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token obtained');

    // 2. Test draft creation
    console.log('\n2. ✍️ Testing draft creation...');
    const draftData = {
      title: 'Test Draft Article',
      content: '<h1>Test Content</h1><p>This is a test draft with <strong>rich text</strong> content.</p>',
      contentHtml: '<h1>Test Content</h1><p>This is a test draft with <strong>rich text</strong> content.</p>',
      category: 'Technology',
      tags: ['test', 'draft', 'cms'],
      status: 'draft'
    };

    const createDraftResponse = await axios.post(`${API_BASE}/drafts`, draftData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (createDraftResponse.data.success) {
      console.log('✅ Draft creation successful');
      console.log(`   Draft ID: ${createDraftResponse.data.data.id}`);
      console.log(`   Draft Title: ${createDraftResponse.data.data.title}`);
      console.log(`   Draft Status: ${createDraftResponse.data.data.status}`);
    }

    const draftId = createDraftResponse.data.data.id;

    // 3. Test draft auto-save
    console.log('\n3. 💾 Testing draft auto-save...');
    const autoSaveData = {
      content: '<h1>Updated Test Content</h1><p>This content was auto-saved with additional text.</p>',
      contentHtml: '<h1>Updated Test Content</h1><p>This content was auto-saved with additional text.</p>',
      title: 'Updated Test Draft Article'
    };

    const autoSaveResponse = await axios.patch(`${API_BASE}/drafts/${draftId}/auto-save`, autoSaveData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (autoSaveResponse.data.success) {
      console.log('✅ Draft auto-save successful');
      console.log(`   Last auto-save: ${autoSaveResponse.data.data.lastAutoSaveAt}`);
      console.log(`   Word count: ${autoSaveResponse.data.data.wordCount}`);
      console.log(`   Reading time: ${autoSaveResponse.data.data.readingTime} minutes`);
    }

    // 4. Test draft retrieval
    console.log('\n4. 📖 Testing draft retrieval...');
    const getDraftResponse = await axios.get(`${API_BASE}/drafts/${draftId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (getDraftResponse.data.success) {
      console.log('✅ Draft retrieval successful');
      console.log(`   Retrieved draft: ${getDraftResponse.data.data.title}`);
      console.log(`   Status: ${getDraftResponse.data.data.status}`);
      console.log(`   Version: ${getDraftResponse.data.data.version}`);
    }

    // 5. Test draft list with filters
    console.log('\n5. 📋 Testing draft list with filters...');
    const listDraftsResponse = await axios.get(`${API_BASE}/drafts?status=auto_saved&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (listDraftsResponse.data.success) {
      console.log('✅ Draft list retrieval successful');
      console.log(`   Total drafts: ${listDraftsResponse.data.pagination.total}`);
      console.log(`   Filtered drafts: ${listDraftsResponse.data.data.length}`);
    }

    // 6. Test draft version creation
    console.log('\n6. 🔄 Testing draft version creation...');
    const versionResponse = await axios.post(`${API_BASE}/drafts/${draftId}/version`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (versionResponse.data.success) {
      console.log('✅ Draft version creation successful');
      console.log(`   New version ID: ${versionResponse.data.data.id}`);
      console.log(`   Version number: ${versionResponse.data.data.version}`);
    }

    const newVersionId = versionResponse.data.data.id;

    // 7. Test version history
    console.log('\n7. 📚 Testing version history...');
    const versionsResponse = await axios.get(`${API_BASE}/drafts/${draftId}/versions`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (versionsResponse.data.success) {
      console.log('✅ Version history retrieval successful');
      console.log(`   Total versions: ${versionsResponse.data.data.length}`);
      versionsResponse.data.data.forEach(version => {
        console.log(`   - Version ${version.version}: ${version.title} (${version.status})`);
      });
    }

    // 8. Test draft status update
    console.log('\n8. 🔄 Testing draft status update...');
    const statusUpdateResponse = await axios.put(`${API_BASE}/drafts/${newVersionId}`, {
      status: 'ready_for_review'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (statusUpdateResponse.data.success) {
      console.log('✅ Draft status update successful');
      console.log(`   New status: ${statusUpdateResponse.data.data.status}`);
    }

    // 9. Test frontend pages access
    console.log('\n9. 🌐 Testing frontend pages access...');
    
    // Test drafts page
    const draftsPageResponse = await axios.get(`${FRONTEND_BASE}/drafts`);
    if (draftsPageResponse.status === 200) {
      console.log('✅ Drafts page accessible');
    }

    // Test draft preview page
    const previewPageResponse = await axios.get(`${FRONTEND_BASE}/drafts/${draftId}/preview`);
    if (previewPageResponse.status === 200) {
      console.log('✅ Draft preview page accessible');
    }

    // Test create page with draft parameter
    const createWithDraftResponse = await axios.get(`${FRONTEND_BASE}/create?draft=${draftId}`);
    if (createWithDraftResponse.status === 200) {
      console.log('✅ Create page with draft parameter accessible');
    }

    // 10. Test search functionality
    console.log('\n10. 🔍 Testing draft search...');
    const searchResponse = await axios.get(`${API_BASE}/drafts?search=Test&limit=10`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (searchResponse.data.success) {
      console.log('✅ Draft search successful');
      console.log(`   Search results: ${searchResponse.data.data.length} drafts found`);
    }

    console.log('\n🎉 Content Management System testing completed!');
    
    // Test results summary
    console.log('\n📊 Test Results Summary:');
    console.log('✅ User Authentication: Normal');
    console.log('✅ Draft Creation: Normal');
    console.log('✅ Draft Auto-Save: Normal');
    console.log('✅ Draft Retrieval: Normal');
    console.log('✅ Draft Filtering: Normal');
    console.log('✅ Version Management: Normal');
    console.log('✅ Status Management: Normal');
    console.log('✅ Frontend Pages: Normal');
    console.log('✅ Search Functionality: Normal');
    
    return true;

  } catch (error) {
    console.error('\n❌ Error occurred during testing:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
    return false;
  }
}

// Run tests
testContentManagement()
  .then(success => {
    if (success) {
      console.log('\n🎯 All tests passed! Content Management System is working properly.');
      process.exit(0);
    } else {
      console.log('\n💥 Tests failed, please check error messages.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test execution failed:', error.message);
    process.exit(1);
  });
