const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:5000/api';

console.log('🧪 Starting Email Service Testing');

async function testEmailService() {
  try {
    // 1. Test API health
    console.log('\n1. 🏥 Testing API health...');
    try {
      const healthResponse = await axios.get(`${API_BASE}/health`);
      if (healthResponse.status === 200) {
        console.log('✅ API health check passed');
        console.log(`   Status: ${healthResponse.data.status}`);
        console.log(`   Environment: ${healthResponse.data.environment}`);
        console.log(`   Database: ${healthResponse.data.database}`);
      }
    } catch (error) {
      console.log('⚠️ API health check failed:', error.response?.status || error.message);
      return false;
    }

    // 2. Test Email Service Configuration
    console.log('\n2. 📧 Testing Email Service Configuration...');
    
    const emailConfigFiles = [
      { name: 'Email Config', path: '../config/email.js' },
      { name: 'Modern Email Service', path: '../services/ModernEmailService.js' },
      { name: 'Email Routes', path: '../routes/email.js' }
    ];

    emailConfigFiles.forEach(file => {
      const filePath = path.join(__dirname, file.path);
      if (fs.existsSync(filePath)) {
        console.log(`   ✅ ${file.name} exists`);
      } else {
        console.log(`   ❌ ${file.name} not found`);
      }
    });

    // 3. Test Email Templates
    console.log('\n3. 📝 Testing Email Templates...');
    
    const templateDir = path.join(__dirname, '..', 'templates', 'email');
    const expectedTemplates = [
      'email-verification.mjml',
      'password-reset.mjml',
      'welcome.mjml',
      'notification.mjml'
    ];

    if (fs.existsSync(templateDir)) {
      console.log('   ✅ Email templates directory exists');
      
      const files = fs.readdirSync(templateDir);
      const mjmlFiles = files.filter(file => file.endsWith('.mjml'));
      
      console.log(`   📊 Found ${mjmlFiles.length} MJML templates`);
      
      expectedTemplates.forEach(template => {
        if (mjmlFiles.includes(template)) {
          console.log(`      ✅ ${template}`);
        } else {
          console.log(`      ❌ ${template} missing`);
        }
      });
    } else {
      console.log('   ❌ Email templates directory not found');
    }

    // 4. Test Email Service Dependencies
    console.log('\n4. 📦 Testing Email Service Dependencies...');
    
    const dependencies = [
      'nodemailer',
      '@sendgrid/mail',
      'handlebars',
      'mjml'
    ];

    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      dependencies.forEach(dep => {
        if (allDeps[dep]) {
          console.log(`   ✅ ${dep} v${allDeps[dep]}`);
        } else {
          console.log(`   ❌ ${dep} not installed`);
        }
      });
    }

    // 5. Test Email Service API Endpoints (without authentication)
    console.log('\n5. 🔗 Testing Email Service API Endpoints...');
    
    const endpoints = [
      { method: 'POST', path: '/email/send-verification', auth: true },
      { method: 'POST', path: '/email/verify-email', auth: false },
      { method: 'POST', path: '/email/resend-verification', auth: false },
      { method: 'POST', path: '/email/send-password-reset', auth: false },
      { method: 'POST', path: '/email/reset-password', auth: false },
      { method: 'GET', path: '/email/stats', auth: true },
      { method: 'POST', path: '/email/test', auth: true }
    ];

    for (const endpoint of endpoints) {
      try {
        // Just test if the endpoint exists (will return 400/401 for missing data/auth)
        const response = await axios({
          method: endpoint.method.toLowerCase(),
          url: `${API_BASE}${endpoint.path}`,
          validateStatus: () => true // Accept any status code
        });
        
        if (response.status === 404) {
          console.log(`   ❌ ${endpoint.method} ${endpoint.path} - Not Found`);
        } else {
          console.log(`   ✅ ${endpoint.method} ${endpoint.path} - Endpoint exists`);
        }
      } catch (error) {
        console.log(`   ⚠️ ${endpoint.method} ${endpoint.path} - Connection error`);
      }
    }

    // 6. Test Email Template Compilation
    console.log('\n6. 🔧 Testing Email Template Compilation...');
    
    try {
      const mjml = require('mjml');
      const handlebars = require('handlebars');
      
      console.log('   ✅ MJML library loaded');
      console.log('   ✅ Handlebars library loaded');
      
      // Test basic MJML compilation
      const testMjml = `
        <mjml>
          <mj-body>
            <mj-section>
              <mj-column>
                <mj-text>Hello {{name}}!</mj-text>
              </mj-column>
            </mj-section>
          </mj-body>
        </mjml>
      `;
      
      const { html, errors } = mjml(testMjml);
      if (html && errors.length === 0) {
        console.log('   ✅ MJML compilation test passed');
      } else {
        console.log('   ⚠️ MJML compilation test had warnings:', errors.length);
      }
      
      // Test Handlebars compilation
      const template = handlebars.compile(html);
      const rendered = template({ name: 'Test User' });
      if (rendered.includes('Hello Test User!')) {
        console.log('   ✅ Handlebars template compilation test passed');
      } else {
        console.log('   ❌ Handlebars template compilation test failed');
      }
      
    } catch (error) {
      console.log('   ❌ Template compilation test failed:', error.message);
    }

    // 7. Test Email Service Environment Variables
    console.log('\n7. 🌍 Testing Email Service Environment Variables...');
    
    const envVars = [
      { name: 'EMAIL_PROVIDER', required: false, current: process.env.EMAIL_PROVIDER || 'smtp' },
      { name: 'EMAIL_FROM', required: false, current: process.env.EMAIL_FROM || '<EMAIL>' },
      { name: 'SMTP_HOST', required: false, current: process.env.SMTP_HOST || 'smtp.gmail.com' },
      { name: 'SMTP_PORT', required: false, current: process.env.SMTP_PORT || '587' },
      { name: 'SMTP_USER', required: false, current: process.env.SMTP_USER || 'not set' },
      { name: 'SMTP_PASS', required: false, current: process.env.SMTP_PASS ? '***' : 'not set' },
      { name: 'SENDGRID_API_KEY', required: false, current: process.env.SENDGRID_API_KEY ? '***' : 'not set' },
      { name: 'FRONTEND_URL', required: false, current: process.env.FRONTEND_URL || 'http://localhost:3000' }
    ];

    envVars.forEach(envVar => {
      const status = envVar.current !== 'not set' ? '✅' : '⚠️';
      console.log(`   ${status} ${envVar.name}: ${envVar.current}`);
    });

    console.log('\n🎉 Email Service testing completed!');
    
    // Test results summary
    console.log('\n📊 Test Results Summary:');
    console.log('✅ API Health: Checked');
    console.log('✅ Email Configuration: Verified');
    console.log('✅ Email Templates: Checked');
    console.log('✅ Dependencies: Verified');
    console.log('✅ API Endpoints: Tested');
    console.log('✅ Template Compilation: Tested');
    console.log('✅ Environment Variables: Checked');
    
    return true;

  } catch (error) {
    console.error('\n❌ Error occurred during testing:', error.message);
    return false;
  }
}

// Run tests
testEmailService()
  .then(success => {
    if (success) {
      console.log('\n🎯 Email Service is properly configured and ready to use!');
      console.log('\n📝 Next Steps:');
      console.log('   1. Configure SMTP credentials or SendGrid API key');
      console.log('   2. Test email sending with real credentials');
      console.log('   3. Integrate email verification in registration flow');
      console.log('   4. Set up password reset functionality');
      process.exit(0);
    } else {
      console.log('\n💥 Tests failed, please check error messages.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test execution failed:', error.message);
    process.exit(1);
  });
