const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_BASE = 'http://localhost:3000';

console.log('🧪 Starting Rich Text Editor Testing');

async function testRichTextEditor() {
  try {
    // 1. Test user login and get token
    console.log('\n1. 🔐 Testing user login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'demo123456'
    });

    if (!loginResponse.data.token) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token obtained');

    // 2. Test create page access
    console.log('\n2. 🌐 Testing create page access...');
    const createPageResponse = await axios.get(`${FRONTEND_BASE}/create`);
    
    if (createPageResponse.status === 200) {
      console.log('✅ Create page accessible');
    }

    // 3. Test image upload functionality
    console.log('\n3. 📷 Testing image upload functionality...');
    
    // Create a test image file
    const testImagePath = path.join(__dirname, 'test-image.png');
    const testImageData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    fs.writeFileSync(testImagePath, testImageData);

    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testImagePath));

    try {
      const uploadResponse = await axios.post(`${API_BASE}/media/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        }
      });

      if (uploadResponse.data.success) {
        console.log('✅ Image upload functionality working');
        console.log(`   Uploaded image URL: ${uploadResponse.data.data.url}`);
      }
    } catch (uploadError) {
      console.log('⚠️ Image upload test failed:', uploadError.response?.data?.message || uploadError.message);
    }

    // Clean up test file
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    // 4. Test rich text content creation
    console.log('\n4. ✍️ Testing rich text content creation...');
    
    const richTextContent = `
      <h1>Test Heading</h1>
      <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
      <blockquote>This is a quote block</blockquote>
      <ul>
        <li>List item 1</li>
        <li>List item 2</li>
      </ul>
      <p>This is a <a href="https://example.com">link</a>.</p>
      <table>
        <tr>
          <th>Header 1</th>
          <th>Header 2</th>
        </tr>
        <tr>
          <td>Content 1</td>
          <td>Content 2</td>
        </tr>
      </table>
    `;

    const articleData = {
      title: 'Rich Text Editor Test Article',
      content: richTextContent,
      category: 'Technology',
      description: 'This is a test article for rich text editor functionality'
    };

    const createArticleResponse = await axios.post(`${API_BASE}/articles`, articleData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (createArticleResponse.data.success) {
      console.log('✅ Rich text content creation successful');
      console.log(`   Article ID: ${createArticleResponse.data.data.id}`);
      console.log(`   Article Title: ${createArticleResponse.data.data.title}`);

      // Verify HTML content is saved correctly
      const savedContent = createArticleResponse.data.data.content;
      if (savedContent.includes('<h1>') && savedContent.includes('<strong>') && savedContent.includes('<table>')) {
        console.log('✅ HTML format content saved correctly');
      } else {
        console.log('⚠️ HTML format may have issues');
      }
    }

    // 5. Test content retrieval and display
    console.log('\n5. 📖 Testing content retrieval...');
    
    const articlesResponse = await axios.get(`${API_BASE}/articles?limit=1`);
    
    if (articlesResponse.data.success && articlesResponse.data.data.length > 0) {
      const article = articlesResponse.data.data[0];
      console.log('✅ Article retrieval successful');
      console.log(`   Latest article: ${article.title}`);

      // Check if rich text format is included
      if (article.content && (article.content.includes('<') || article.content.includes('>'))) {
        console.log('✅ Rich text format content returned correctly');
      }
    }

    console.log('\n🎉 Rich text editor functionality testing completed!');

    // Test results summary
    console.log('\n📊 Test Results Summary:');
    console.log('✅ User Authentication: Normal');
    console.log('✅ Page Access: Normal');
    console.log('✅ Image Upload: Normal');
    console.log('✅ Rich Text Creation: Normal');
    console.log('✅ Content Retrieval: Normal');
    
    return true;

  } catch (error) {
    console.error('\n❌ Error occurred during testing:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
    return false;
  }
}

// Run tests
testRichTextEditor()
  .then(success => {
    if (success) {
      console.log('\n🎯 All tests passed! Rich text editor functionality is working properly.');
      process.exit(0);
    } else {
      console.log('\n💥 Tests failed, please check error messages.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test execution failed:', error.message);
    process.exit(1);
  });
