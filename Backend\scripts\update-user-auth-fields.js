#!/usr/bin/env node

const { sequelize } = require('../config/database');

async function updateUserAuthFields() {
  console.log('🔐 Updating user authentication fields...');
  
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // 添加认证相关字段
    const alterQueries = [
      // 密码重置字段
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "passwordResetToken" VARCHAR(255);',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "passwordResetExpires" TIMESTAMP;',
      
      // 登录安全字段
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "loginAttempts" INTEGER DEFAULT 0;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "lockUntil" TIMESTAMP;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "lastLogin" TIMESTAMP;',
      
      // 社交登录字段
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "googleId" VARCHAR(255) UNIQUE;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "facebookId" VARCHAR(255) UNIQUE;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "twitterId" VARCHAR(255) UNIQUE;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "appleId" VARCHAR(255) UNIQUE;',
      
      // 邮箱验证字段（如果不存在）
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "emailVerificationToken" VARCHAR(255);',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "emailVerificationExpires" TIMESTAMP;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "isEmailVerified" BOOLEAN DEFAULT false;',
      
      // 其他安全字段
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "twoFactorSecret" VARCHAR(255);',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "twoFactorEnabled" BOOLEAN DEFAULT false;',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "backupCodes" TEXT[];',
      
      // 设备和会话管理
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "trustedDevices" JSONB DEFAULT \'[]\';',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS "sessionData" JSONB DEFAULT \'{}\';'
    ];
    
    for (const query of alterQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Field added/updated successfully');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('⚠️ Field already exists, skipping...');
        } else {
          console.log('⚠️ Field update failed:', error.message);
        }
      }
    }
    
    // 创建索引
    console.log('📊 Creating indexes...');
    
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON users("emailVerificationToken");',
      'CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users("passwordResetToken");',
      'CREATE INDEX IF NOT EXISTS idx_users_google_id ON users("googleId");',
      'CREATE INDEX IF NOT EXISTS idx_users_facebook_id ON users("facebookId");',
      'CREATE INDEX IF NOT EXISTS idx_users_twitter_id ON users("twitterId");',
      'CREATE INDEX IF NOT EXISTS idx_users_apple_id ON users("appleId");',
      'CREATE INDEX IF NOT EXISTS idx_users_last_login ON users("lastLogin");',
      'CREATE INDEX IF NOT EXISTS idx_users_lock_until ON users("lockUntil");',
      'CREATE INDEX IF NOT EXISTS idx_users_login_attempts ON users("loginAttempts");',
      'CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users("isEmailVerified");',
      'CREATE INDEX IF NOT EXISTS idx_users_two_factor ON users("twoFactorEnabled");'
    ];
    
    for (const query of indexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Index created successfully');
      } catch (error) {
        console.log('⚠️ Index creation failed:', error.message);
      }
    }
    
    // 创建用户会话表
    const createUserSessionsQuery = `
      CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "sessionToken" VARCHAR(255) NOT NULL UNIQUE,
        "deviceInfo" JSONB DEFAULT '{}',
        "ipAddress" INET,
        "userAgent" TEXT,
        "isActive" BOOLEAN DEFAULT true,
        "lastActivity" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "expiresAt" TIMESTAMP NOT NULL,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createUserSessionsQuery);
    console.log('✅ user_sessions table created');
    
    // 创建登录日志表
    const createLoginLogsQuery = `
      CREATE TABLE IF NOT EXISTS login_logs (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER REFERENCES users(id) ON DELETE SET NULL,
        email VARCHAR(255),
        "loginMethod" VARCHAR(50) DEFAULT 'email',
        "ipAddress" INET,
        "userAgent" TEXT,
        location JSONB,
        success BOOLEAN NOT NULL,
        "failureReason" VARCHAR(255),
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createLoginLogsQuery);
    console.log('✅ login_logs table created');
    
    // 创建密码历史表
    const createPasswordHistoryQuery = `
      CREATE TABLE IF NOT EXISTS password_history (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "passwordHash" VARCHAR(255) NOT NULL,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await sequelize.query(createPasswordHistoryQuery);
    console.log('✅ password_history table created');
    
    // 创建相关索引
    const sessionIndexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions("userId");',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions("sessionToken");',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions("isActive");',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions("expiresAt");',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs("userId");',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_email ON login_logs(email);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_success ON login_logs(success);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs("createdAt");',
      'CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history("userId");',
      'CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history("createdAt");'
    ];
    
    for (const query of sessionIndexQueries) {
      try {
        await sequelize.query(query);
        console.log('✅ Session index created');
      } catch (error) {
        console.log('⚠️ Session index creation failed:', error.message);
      }
    }
    
    // 更新现有用户的默认值
    console.log('🔄 Updating existing users...');
    
    try {
      await sequelize.query(`
        UPDATE users 
        SET 
          "loginAttempts" = 0,
          "isEmailVerified" = COALESCE("isEmailVerified", false),
          "twoFactorEnabled" = COALESCE("twoFactorEnabled", false),
          "trustedDevices" = COALESCE("trustedDevices", '[]'::jsonb),
          "sessionData" = COALESCE("sessionData", '{}'::jsonb)
        WHERE 
          "loginAttempts" IS NULL 
          OR "isEmailVerified" IS NULL 
          OR "twoFactorEnabled" IS NULL
          OR "trustedDevices" IS NULL
          OR "sessionData" IS NULL;
      `);
      console.log('✅ Existing users updated');
    } catch (error) {
      console.log('⚠️ Failed to update existing users:', error.message);
    }
    
    // 清理过期的重置token和验证token
    console.log('🧹 Cleaning up expired tokens...');
    
    try {
      await sequelize.query(`
        UPDATE users 
        SET 
          "passwordResetToken" = NULL,
          "passwordResetExpires" = NULL
        WHERE "passwordResetExpires" < NOW();
      `);
      
      await sequelize.query(`
        UPDATE users 
        SET 
          "emailVerificationToken" = NULL,
          "emailVerificationExpires" = NULL
        WHERE "emailVerificationExpires" < NOW();
      `);
      
      console.log('✅ Expired tokens cleaned up');
    } catch (error) {
      console.log('⚠️ Failed to clean up tokens:', error.message);
    }
    
    console.log('🎉 User authentication fields updated successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to update user authentication fields:', error.message);
    console.error('Error details:', error);
    return false;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error.message);
    }
  }
}

// Run the script
if (require.main === module) {
  updateUserAuthFields()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { updateUserAuthFields };
