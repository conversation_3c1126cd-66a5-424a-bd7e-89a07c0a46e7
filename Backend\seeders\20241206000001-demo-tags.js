'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tags = [
      {
        name: 'Technology',
        slug: 'technology',
        description: 'Latest technology trends and innovations',
        color: '#3B82F6',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Business',
        slug: 'business',
        description: 'Business news and insights',
        color: '#10B981',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Health',
        slug: 'health',
        description: 'Health and wellness topics',
        color: '#F59E0B',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Science',
        slug: 'science',
        description: 'Scientific discoveries and research',
        color: '#8B5CF6',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Sports',
        slug: 'sports',
        description: 'Sports news and updates',
        color: '#EF4444',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Entertainment',
        slug: 'entertainment',
        description: 'Entertainment and celebrity news',
        color: '#EC4899',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Politics',
        slug: 'politics',
        description: 'Political news and analysis',
        color: '#6B7280',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Environment',
        slug: 'environment',
        description: 'Environmental issues and sustainability',
        color: '#059669',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Education',
        slug: 'education',
        description: 'Education and learning resources',
        color: '#DC2626',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Travel',
        slug: 'travel',
        description: 'Travel guides and destinations',
        color: '#0891B2',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Food',
        slug: 'food',
        description: 'Food, recipes, and culinary trends',
        color: '#EA580C',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Fashion',
        slug: 'fashion',
        description: 'Fashion trends and style guides',
        color: '#BE185D',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Finance',
        slug: 'finance',
        description: 'Financial news and investment advice',
        color: '#16A34A',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Gaming',
        slug: 'gaming',
        description: 'Video games and gaming industry news',
        color: '#7C3AED',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Art',
        slug: 'art',
        description: 'Art, culture, and creative content',
        color: '#DB2777',
        isActive: true,
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await queryInterface.bulkInsert('tags', tags, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('tags', null, {});
  }
};
