#!/usr/bin/env node

/**
 * Newzora 服务器启动器
 * 处理服务器启动、网络配置、错误恢复等
 */

const { app, config, networkInfo } = require('./stable-server');
const { sequelize, connectWithRetry } = require('./config/database-enhanced');
const { logger } = require('./config/logger');
const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

class ServerLauncher {
  constructor() {
    this.httpServer = null;
    this.httpsServer = null;
    this.isShuttingDown = false;
    this.retryCount = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5秒
  }

  // 预启动检查
  async preflightChecks() {
    console.log('🔍 Running preflight checks...');
    
    // 检查环境变量
    const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.warn('⚠️ Missing environment variables:', missingVars);
      console.log('   Using default values for development');
    }

    // 检查端口可用性
    await this.checkPortAvailability(config.port);
    
    if (config.enableHttps) {
      await this.checkPortAvailability(config.httpsPort);
      await this.checkSSLCertificates();
    }

    // 测试数据库连接
    await this.testDatabaseConnection();
    
    console.log('✅ Preflight checks completed');
  }

  // 检查端口可用性
  async checkPortAvailability(port) {
    return new Promise((resolve, reject) => {
      const server = require('net').createServer();
      
      server.listen(port, config.host, () => {
        server.once('close', () => resolve());
        server.close();
      });
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          console.error(`❌ Port ${port} is already in use`);
          
          // 尝试找到占用端口的进程
          if (process.platform === 'win32') {
            require('child_process').exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
              if (stdout) {
                console.log('Port usage:', stdout);
              }
            });
          }
          
          reject(new Error(`Port ${port} is already in use`));
        } else {
          reject(err);
        }
      });
    });
  }

  // 检查SSL证书
  async checkSSLCertificates() {
    const certPath = process.env.SSL_CERT_PATH || './ssl/cert.pem';
    const keyPath = process.env.SSL_KEY_PATH || './ssl/key.pem';
    
    if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
      console.warn('⚠️ SSL certificates not found, HTTPS will be disabled');
      config.enableHttps = false;
      return false;
    }
    
    console.log('✅ SSL certificates found');
    return true;
  }

  // 测试数据库连接
  async testDatabaseConnection() {
    try {
      console.log('📡 Testing database connection...');
      await connectWithRetry();
      console.log('✅ Database connection successful');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  // 启动HTTP服务器
  async startHttpServer() {
    return new Promise((resolve, reject) => {
      this.httpServer = http.createServer(app);
      
      this.httpServer.listen(config.port, config.host, () => {
        const address = this.httpServer.address();
        console.log(`🌐 HTTP Server running on ${config.host}:${address.port}`);
        
        // 显示所有访问地址
        this.displayAccessUrls('http', address.port);
        
        resolve(this.httpServer);
      });

      this.httpServer.on('error', (error) => {
        console.error('❌ HTTP Server error:', error.message);
        reject(error);
      });

      // 处理连接
      this.httpServer.on('connection', (socket) => {
        socket.setTimeout(30000); // 30秒超时
      });
    });
  }

  // 启动HTTPS服务器
  async startHttpsServer() {
    if (!config.enableHttps) return null;

    return new Promise((resolve, reject) => {
      try {
        const options = {
          key: fs.readFileSync(process.env.SSL_KEY_PATH || './ssl/key.pem'),
          cert: fs.readFileSync(process.env.SSL_CERT_PATH || './ssl/cert.pem')
        };

        this.httpsServer = https.createServer(options, app);
        
        this.httpsServer.listen(config.httpsPort, config.host, () => {
          const address = this.httpsServer.address();
          console.log(`🔒 HTTPS Server running on ${config.host}:${address.port}`);
          
          this.displayAccessUrls('https', address.port);
          
          resolve(this.httpsServer);
        });

        this.httpsServer.on('error', (error) => {
          console.error('❌ HTTPS Server error:', error.message);
          reject(error);
        });

      } catch (error) {
        console.error('❌ Failed to start HTTPS server:', error.message);
        resolve(null);
      }
    });
  }

  // 显示访问地址
  displayAccessUrls(protocol, port) {
    console.log(`\n📍 ${protocol.toUpperCase()} Access URLs:`);
    console.log(`   ${protocol}://localhost:${port}`);
    console.log(`   ${protocol}://127.0.0.1:${port}`);
    
    // 显示本机IP地址
    networkInfo.addresses.forEach(addr => {
      if (!addr.internal) {
        console.log(`   ${protocol}://${addr.address}:${port} (${addr.name})`);
      }
    });
  }

  // 设置优雅关闭
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      this.isShuttingDown = true;

      // 停止接受新连接
      if (this.httpServer) {
        this.httpServer.close(() => {
          console.log('✅ HTTP server closed');
        });
      }

      if (this.httpsServer) {
        this.httpsServer.close(() => {
          console.log('✅ HTTPS server closed');
        });
      }

      // 关闭数据库连接
      try {
        await sequelize.close();
        console.log('✅ Database connection closed');
      } catch (error) {
        console.error('❌ Error closing database:', error.message);
      }

      // 等待所有连接关闭
      setTimeout(() => {
        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      }, 5000);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart
  }

  // 错误处理
  setupErrorHandlers() {
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      console.error('💥 Uncaught Exception:', error.message);
      
      if (!this.isShuttingDown) {
        this.attemptRestart();
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection:', { reason, promise });
      console.error('💥 Unhandled Rejection:', reason);
      
      if (!this.isShuttingDown) {
        this.attemptRestart();
      }
    });
  }

  // 尝试重启
  async attemptRestart() {
    if (this.retryCount >= this.maxRetries) {
      console.error('❌ Max restart attempts reached, exiting...');
      process.exit(1);
    }

    this.retryCount++;
    console.log(`🔄 Attempting restart ${this.retryCount}/${this.maxRetries} in ${this.retryDelay/1000}s...`);
    
    setTimeout(async () => {
      try {
        await this.start();
        this.retryCount = 0; // 重置计数器
      } catch (error) {
        console.error('❌ Restart failed:', error.message);
        this.attemptRestart();
      }
    }, this.retryDelay);
  }

  // 启动服务器
  async start() {
    try {
      console.log('🚀 Starting Newzora Server...');
      console.log('============================');
      
      // 预启动检查
      await this.preflightChecks();
      
      // 设置错误处理
      this.setupErrorHandlers();
      
      // 设置优雅关闭
      this.setupGracefulShutdown();
      
      // 启动服务器
      await this.startHttpServer();
      
      if (config.enableHttps) {
        await this.startHttpsServer();
      }
      
      // 显示启动信息
      this.displayStartupInfo();
      
      console.log('\n✅ Server started successfully!');
      console.log('Press Ctrl+C to stop the server');
      
    } catch (error) {
      console.error('❌ Failed to start server:', error.message);
      
      if (this.retryCount < this.maxRetries) {
        this.attemptRestart();
      } else {
        process.exit(1);
      }
    }
  }

  // 显示启动信息
  displayStartupInfo() {
    console.log('\n🎉 Newzora Server Ready!');
    console.log('========================');
    console.log(`Environment: ${config.environment}`);
    console.log(`Platform: ${networkInfo.platform} ${networkInfo.arch}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`Memory: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
    
    console.log('\n🔗 Important Endpoints:');
    console.log(`   Health Check: http://localhost:${config.port}/api/health`);
    console.log(`   Authentication: http://localhost:${config.port}/api/auth-enhanced`);
    console.log(`   Articles: http://localhost:${config.port}/api/articles`);
    console.log(`   Admin Panel: http://localhost:${config.port}/api/admin`);
    
    console.log('\n📊 Features Enabled:');
    console.log(`   ✅ Database: PostgreSQL`);
    console.log(`   ${config.enableHttps ? '✅' : '❌'} HTTPS`);
    console.log(`   ${config.enableCompression ? '✅' : '❌'} Compression`);
    console.log(`   ${config.enableRateLimit ? '✅' : '❌'} Rate Limiting`);
    console.log(`   ✅ CORS (${config.corsOrigins.length} origins)`);
    console.log(`   ✅ Security Headers`);
    console.log(`   ✅ Request Logging`);
    console.log(`   ✅ Error Recovery`);
  }
}

// 启动服务器
if (require.main === module) {
  const launcher = new ServerLauncher();
  launcher.start().catch(error => {
    console.error('💥 Server startup failed:', error);
    process.exit(1);
  });
}

module.exports = ServerLauncher;
