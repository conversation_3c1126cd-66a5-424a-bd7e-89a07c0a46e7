// Load environment variables first
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const session = require('express-session');
const passport = require('./config/passport');
const { sequelize, connectWithRetry, performHealthCheck, gracefulShutdown: dbGracefulShutdown } = require('./config/database-enhanced');
const { testSupabaseConnection } = require('./config/supabase');
const { apiLimiter } = require('./middleware/rateLimiter');
const { applySecurityMiddleware, rateLimiters } = require('./middleware/security');
const { testEmailConfig } = require('./services/emailService');
const { initializeEmailService } = require('./config/email');
const schedulerService = require('./services/SchedulerService');
const webSocketService = require('./services/WebSocketService');
const notificationService = require('./services/NotificationService');
const analyticsService = require('./services/AnalyticsService');
const http = require('http');
// Load model associations
require('./models/associations');

// Import logging and monitoring
const { logger } = require('./config/logger');
const {
  requestLogger,
  errorLogger,
  securityLogger,
  performanceMonitor,
  setupDatabaseLogging,
  startHealthMonitoring,
  trackApiUsage
} = require('./middleware/logging');

// Import socket service
const socketService = require('./services/socketService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  preflightContinue: false,
  optionsSuccessStatus: 200
}));

// Apply security middleware (CSP, rate limiting, input validation)
applySecurityMiddleware(app);

// Logging middleware (before other middleware)
app.use(requestLogger);
app.use(securityLogger);
app.use(performanceMonitor);
app.use(trackApiUsage);

// Rate limiting
app.use('/api/', apiLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Session configuration for Passport
app.use(session({
  secret: process.env.SESSION_SECRET || process.env.JWT_SECRET || 'fallback-secret-key',
  resave: false,
  saveUninitialized: false,
  name: 'onenews.sid', // 自定义会话名称
  cookie: {
    secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS
    httpOnly: true, // 防止XSS攻击
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax' // CSRF保护
  },
  rolling: true // 每次请求都重新设置过期时间
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Authentication routes
app.use('/api/auth', require('./routes/supabaseAuth'));
app.use('/api/auth-enhanced', require('./routes/auth-enhanced'));
app.use('/api/auth/social', require('./routes/social-auth'));

// Routes with specific rate limiting
app.use('/api/articles', require('./routes/articles'));
app.use('/api/categories', require('./routes/categories'));

// User routes with enhanced security
app.use('/api/users/login', rateLimiters.auth);
app.use('/api/users/register', rateLimiters.register);
app.use('/api/users/reset-password', rateLimiters.passwordReset);
app.use('/api/users', rateLimiters.api, require('./routes/users'));
app.use('/api/comments', require('./routes/comments'));

// Social features routes
app.use('/api/follows', require('./routes/follows'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/tags', require('./routes/tags'));
app.use('/api/activities', require('./routes/activities'));
app.use('/api/shares', require('./routes/shares'));

// Content management routes
app.use('/api/drafts', require('./routes/drafts'));
app.use('/api/media', require('./routes/media'));
app.use('/api/reviews', require('./routes/reviews'));
app.use('/api/review-rules', require('./routes/review-rules'));

// Permission system routes
app.use('/api/permissions', require('./routes/permissions'));
app.use('/api/roles', require('./routes/roles'));
app.use('/api/admin/users', require('./routes/admin/users'));
app.use('/api/admin/email', require('./routes/admin/email'));
app.use('/api/content-management', require('./routes/content-management'));
app.use('/api/moderation', require('./routes/moderation'));
app.use('/api/social', require('./routes/social'));
app.use('/api/search', require('./routes/search'));
app.use('/api/reports', require('./routes/reports'));

// Email service routes
app.use('/api/email', require('./routes/email'));

// Admin dashboard routes
app.use('/api/admin/dashboard', require('./routes/admin/dashboard'));

// File upload routes
app.use('/api/upload', require('./routes/upload'));

// Serve uploaded files statically
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Analytics routes
app.use('/api/analytics', require('./routes/analytics'));

// Notification routes
app.use('/api/notifications', require('./routes/notifications'));

// Monitoring routes
app.use('/api/monitoring', require('./routes/monitoring'));

// PostgreSQL connection and database sync
const initializeDatabase = async () => {
  try {
    await connectWithRetry();
    await sequelize.sync({ force: false }); // Don't alter existing tables
    logger.info('Database synchronized successfully');

    // Setup database logging
    setupDatabaseLogging(sequelize);
    logger.info('Database logging configured');

    // Test email configuration
    const emailConfigValid = await testEmailConfig();
    if (emailConfigValid) {
      logger.info('Email service configured successfully');
    } else {
      logger.warn('Email service configuration failed - email features may not work');
    }

    // Start health monitoring
    startHealthMonitoring();
    logger.info('Health monitoring started');

    // Start scheduler service
    schedulerService.start();
    logger.info('Scheduler service started');

    // Start notification service
    notificationService.start();
    logger.info('Notification service started');

    // Start analytics service
    analyticsService.start();
    logger.info('Analytics service started');

  } catch (error) {
    logger.error('Database initialization failed', { error: error.message, stack: error.stack });
    process.exit(1);
  }
};

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Content Hub API is running!' });
});

// Simple test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API connection successful!',
    timestamp: new Date().toISOString(),
    server: 'Newzora Backend'
  });
});

// Health check endpoint for Docker
app.get('/api/health', async (req, res) => {
  try {
    // Check database connection
    await sequelize.authenticate();

    // Check if all required environment variables are set
    const requiredEnvVars = ['JWT_SECRET', 'DB_HOST', 'DB_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: 'connected',
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    };

    if (missingVars.length > 0) {
      healthStatus.warnings = `Missing environment variables: ${missingVars.join(', ')}`;
    }

    res.status(200).json(healthStatus);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected'
    });
  }
});

// Error handling middleware (must be after all routes)
app.use(errorLogger);
app.use((err, req, res, next) => {
  // Error already logged by errorLogger middleware
  const isDevelopment = process.env.NODE_ENV !== 'production';

  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(isDevelopment && { stack: err.stack })
  });
});

// Start server
const startServer = async () => {
  try {
    // Check authentication mode
    const useRealSupabase = process.env.USE_REAL_SUPABASE === 'true';
    console.log('🚀 Starting server with authentication mode:', useRealSupabase ? 'Real Supabase' : 'Mock');

    if (useRealSupabase) {
      try {
        const isConnected = await testSupabaseConnection();
        if (isConnected) {
          console.log('✅ Supabase connection successful, using real authentication');
        } else {
          console.log('❌ Supabase connection failed but USE_REAL_SUPABASE=true');
          console.log('⚠️ Server may not work properly with real authentication');
        }
      } catch (error) {
        console.log('❌ Supabase connection error:', error.message);
        console.log('⚠️ Server may not work properly with real authentication');
      }
    } else {
      console.log('🔄 Using mock authentication (USE_REAL_SUPABASE=false)');
      try {
        // Still test connection for info, but don't use it
        await testSupabaseConnection();
        console.log('ℹ️ Supabase connection available but not used');
      } catch (error) {
        console.log('ℹ️ Supabase connection not available:', error.message);
      }
    }

    // Initialize Socket.IO
    const io = socketService.initialize(server);
    logger.info('Socket.IO initialized');

    // Initialize WebSocket service
    webSocketService.initialize(server);
    logger.info('WebSocket service initialized');

    // Make socket service available to routes
    app.set('socketService', socketService);

    // Initialize Email Service
    try {
      initializeEmailService();
      logger.info('Email service initialized');
    } catch (error) {
      logger.error('Failed to initialize email service:', error.message);
    }

    server.listen(PORT, (err) => {
      if (err) {
        logger.error('Failed to start server', { error: err.message, stack: err.stack });
        process.exit(1);
      }

      logger.info(`Server started successfully`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        apiUrl: `http://localhost:${PORT}/api`,
        socketUrl: `ws://localhost:${PORT}`,
        pid: process.pid
      });

      console.log(`🚀 OneNews Server is running on port ${PORT}`);
      console.log(`📊 API available at http://localhost:${PORT}/api`);
      console.log(`🔌 Socket.IO available at ws://localhost:${PORT}`);
      console.log(`📈 Monitoring available at http://localhost:${PORT}/api/monitoring/health`);
    });

    // Handle server errors
    server.on('error', (err) => {
      logger.error('Server error', { error: err.message, stack: err.stack });
      if (err.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use`);
      }
      process.exit(1);
    });

    // Graceful shutdown handling
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Server startup failed', { error: error.message, stack: error.stack });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  try {
    // Close HTTP server
    await new Promise((resolve) => {
      server.close(resolve);
    });
    logger.info('HTTP server closed');

    // Stop scheduler service
    schedulerService.stop();
    logger.info('Scheduler service stopped');

    // Stop notification service
    notificationService.stop();
    logger.info('Notification service stopped');

    // Close WebSocket service
    webSocketService.close();
    logger.info('WebSocket service closed');

    // Stop analytics service
    analyticsService.stop();
    logger.info('Analytics service stopped');

    // Close database connections using enhanced shutdown
    await dbGracefulShutdown();
    logger.info('Database connections closed');

    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', { error: error.message });
    process.exit(1);
  }
};

startServer();
