const ContentModerationRule = require('../models/ContentModerationRule');
const ContentModerationLog = require('../models/ContentModerationLog');
const { logger } = require('../config/logger');

class AIContentModerationService {
  constructor() {
    // 扩展的敏感词库 - 按类别组织
    this.sensitiveWords = {
      // 政治敏感词
      political: new Set([
        'politics', 'government', 'election', 'vote', 'democracy', 'dictatorship',
        'revolution', 'protest', 'regime', 'coup', 'propaganda', 'censorship',
        'freedom', 'rights', 'liberty', 'oppression', 'totalitarian', 'authoritarian'
      ]),

      // 暴力相关词汇
      violence: new Set([
        'violence', 'violent', 'kill', 'murder', 'death', 'weapon', 'gun', 'knife',
        'bomb', 'explosion', 'attack', 'assault', 'fight', 'war', 'battle',
        'terrorism', 'terrorist', 'threat', 'dangerous', 'harm', 'hurt', 'injury'
      ]),

      // 污秽内容词汇
      profanity: new Set([
        'fuck', 'shit', 'damn', 'hell', 'bitch', 'bastard', 'ass', 'crap',
        'piss', 'cock', 'dick', 'pussy', 'whore', 'slut', 'faggot', 'nigger'
      ]),

      // 血腥内容词汇
      gore: new Set([
        'blood', 'bloody', 'gore', 'gory', 'corpse', 'dead', 'dying', 'torture',
        'mutilation', 'dismember', 'decapitation', 'execution', 'slaughter',
        'massacre', 'carnage', 'butcher', 'brutal', 'savage', 'gruesome'
      ]),

      // 色情内容词汇
      sexual: new Set([
        'sex', 'sexual', 'porn', 'pornography', 'nude', 'naked', 'breast',
        'penis', 'vagina', 'orgasm', 'masturbation', 'intercourse', 'erotic',
        'adult', 'xxx', 'nsfw', 'explicit', 'intimate', 'seductive'
      ]),

      // 仇恨言论词汇
      hate: new Set([
        'hate', 'hatred', 'racist', 'racism', 'discrimination', 'prejudice',
        'bigot', 'xenophobia', 'homophobia', 'islamophobia', 'antisemitism',
        'supremacy', 'nazi', 'fascist', 'genocide', 'ethnic cleansing'
      ]),

      // 垃圾信息词汇
      spam: new Set([
        'spam', 'scam', 'fraud', 'fake', 'phishing', 'malware', 'virus',
        'click here', 'free money', 'get rich', 'miracle cure', 'weight loss',
        'viagra', 'casino', 'lottery', 'winner', 'congratulations'
      ]),

      // 自残相关词汇
      selfHarm: new Set([
        'suicide', 'kill myself', 'end my life', 'self harm', 'cut myself',
        'overdose', 'jump off', 'hang myself', 'depression', 'hopeless',
        'worthless', 'want to die', 'better off dead', 'razor', 'cutting'
      ])
    };

    // AI检测阈值配置
    this.thresholds = {
      toxicity: 0.7,
      spam: 0.8,
      nsfw: 0.6,
      violence: 0.75,
      hate: 0.8,
      political: 0.85, // 政治内容需要更高阈值
      gore: 0.7,
      selfHarm: 0.9 // 自残内容需要最高敏感度
    };

    // 内容分类权重
    this.categoryWeights = {
      political: 0.3,
      violence: 0.9,
      profanity: 0.6,
      gore: 0.95,
      sexual: 0.8,
      hate: 0.9,
      spam: 0.7,
      selfHarm: 1.0
    };

    // 语言检测模式
    this.languagePatterns = {
      chinese: /[\u4e00-\u9fff]/,
      english: /[a-zA-Z]/,
      arabic: /[\u0600-\u06ff]/,
      russian: /[\u0400-\u04ff]/
    };
  }

  // 主要的内容审核入口
  async moderateContent(contentType, contentId, content, authorId, options = {}) {
    try {
      logger.info(`Starting AI moderation for ${contentType} ${contentId}`);
      
      const results = {
        approved: true,
        flags: [],
        actions: [],
        confidence: 1.0,
        details: {}
      };

      // 获取适用的规则
      const rules = await ContentModerationRule.getActiveRules(contentType);
      
      // 执行各种检查
      for (const rule of rules) {
        const ruleResult = await this.executeRule(rule, content, contentType, contentId);
        
        if (ruleResult.triggered) {
          results.flags.push({
            ruleId: rule.id,
            ruleName: rule.name,
            ruleType: rule.ruleType,
            severity: rule.severity,
            confidence: ruleResult.confidence,
            details: ruleResult.details
          });
          
          results.actions.push(rule.action);
          results.confidence = Math.min(results.confidence, ruleResult.confidence);
          
          // 记录触发日志
          await ContentModerationLog.logModeration({
            contentType,
            contentId,
            ruleId: rule.id,
            moderationType: 'automatic',
            action: rule.action,
            reason: ruleResult.reason,
            details: ruleResult.details,
            confidence: ruleResult.confidence,
            aiModel: ruleResult.model || 'rule-based',
            authorId,
            severity: rule.severity
          });
          
          // 更新规则触发计数
          await rule.incrementTriggerCount();
          
          // 如果是自动拒绝，立即返回
          if (rule.action === 'auto_reject') {
            results.approved = false;
            break;
          }
          
          // 如果需要人工审核
          if (rule.action === 'require_review') {
            results.approved = false;
            results.requiresReview = true;
          }
        }
      }
      
      // 执行增强的AI检测
      const enhancedResults = await this.performEnhancedAIDetection(content, contentType);

      // 合并增强检测结果
      if (enhancedResults.flags.length > 0) {
        results.flags.push(...enhancedResults.flags);
        results.actions.push(...enhancedResults.actions);
        results.confidence = Math.min(results.confidence, enhancedResults.confidence);
        results.approved = results.approved && enhancedResults.approved;
      }

      // 如果有标记但没有拒绝，仍然可以通过
      if (results.flags.length > 0 && results.approved) {
        const hasHighSeverity = results.flags.some(flag =>
          ['high', 'critical'].includes(flag.severity)
        );

        if (hasHighSeverity) {
          results.approved = false;
          results.requiresReview = true;
        }
      }

      // 记录审核日志
      await this.logModerationResult(contentType, contentId, authorId, results, options);

      logger.info(`AI moderation completed for ${contentType} ${contentId}: ${results.approved ? 'APPROVED' : 'FLAGGED'}`);

      return results;
    } catch (error) {
      logger.error('AI content moderation failed:', error);
      
      // 记录错误日志
      await ContentModerationLog.logModeration({
        contentType,
        contentId,
        moderationType: 'automatic',
        action: 'flagged',
        reason: 'AI moderation system error',
        details: { error: error.message },
        authorId,
        severity: 'high'
      });
      
      // 出错时默认需要人工审核
      return {
        approved: false,
        requiresReview: true,
        flags: [{
          ruleName: 'System Error',
          ruleType: 'system',
          severity: 'high',
          confidence: 0.0,
          details: { error: error.message }
        }],
        actions: ['require_review'],
        confidence: 0.0,
        details: { error: error.message }
      };
    }
  }

  // 执行单个规则
  async executeRule(rule, content, contentType, contentId) {
    const result = {
      triggered: false,
      confidence: 0.0,
      reason: '',
      details: {},
      model: 'rule-based'
    };

    try {
      switch (rule.ruleType) {
        case 'keyword':
          return this.checkKeywords(rule, content);
          
        case 'regex':
          return this.checkRegex(rule, content);
          
        case 'ai_sentiment':
          return await this.checkSentiment(rule, content);
          
        case 'ai_toxicity':
          return await this.checkToxicity(rule, content);
          
        case 'ai_spam':
          return await this.checkSpam(rule, content);
          
        case 'ai_nsfw':
          return await this.checkNSFW(rule, content);
          
        case 'length_check':
          return this.checkLength(rule, content);
          
        case 'link_analysis':
          return await this.checkLinks(rule, content);
          
        default:
          logger.warn(`Unknown rule type: ${rule.ruleType}`);
          return result;
      }
    } catch (error) {
      logger.error(`Error executing rule ${rule.id}:`, error);
      return {
        triggered: true,
        confidence: 0.0,
        reason: `Rule execution error: ${error.message}`,
        details: { error: error.message },
        model: 'error'
      };
    }
  }

  // 关键词检查
  checkKeywords(rule, content) {
    const config = rule.ruleConfig;
    const keywords = config.keywords || [];
    const caseSensitive = config.caseSensitive || false;
    const wholeWordOnly = config.wholeWordOnly || false;
    
    const text = caseSensitive ? content : content.toLowerCase();
    const foundKeywords = [];
    
    for (const keyword of keywords) {
      const searchKeyword = caseSensitive ? keyword : keyword.toLowerCase();
      
      if (wholeWordOnly) {
        const regex = new RegExp(`\\b${searchKeyword}\\b`, 'g');
        if (regex.test(text)) {
          foundKeywords.push(keyword);
        }
      } else {
        if (text.includes(searchKeyword)) {
          foundKeywords.push(keyword);
        }
      }
    }
    
    return {
      triggered: foundKeywords.length > 0,
      confidence: foundKeywords.length > 0 ? 1.0 : 0.0,
      reason: foundKeywords.length > 0 ? `Found keywords: ${foundKeywords.join(', ')}` : '',
      details: { foundKeywords },
      model: 'keyword-filter'
    };
  }

  // 正则表达式检查
  checkRegex(rule, content) {
    const config = rule.ruleConfig;
    const pattern = config.pattern;
    const flags = config.flags || 'gi';
    
    try {
      const regex = new RegExp(pattern, flags);
      const matches = content.match(regex);
      
      return {
        triggered: matches !== null,
        confidence: matches ? 1.0 : 0.0,
        reason: matches ? `Regex pattern matched: ${pattern}` : '',
        details: { matches: matches || [], pattern },
        model: 'regex-filter'
      };
    } catch (error) {
      return {
        triggered: true,
        confidence: 0.0,
        reason: `Invalid regex pattern: ${pattern}`,
        details: { error: error.message },
        model: 'regex-error'
      };
    }
  }

  // AI情感分析（模拟）
  async checkSentiment(rule, content) {
    // 这里应该调用真实的AI API，如Google Cloud Natural Language API
    // 现在使用简单的模拟逻辑
    
    const negativeWords = ['hate', 'angry', 'terrible', 'awful', 'disgusting', 'horrible'];
    const positiveWords = ['love', 'great', 'awesome', 'wonderful', 'amazing', 'excellent'];
    
    const words = content.toLowerCase().split(/\s+/);
    let negativeCount = 0;
    let positiveCount = 0;
    
    words.forEach(word => {
      if (negativeWords.includes(word)) negativeCount++;
      if (positiveWords.includes(word)) positiveCount++;
    });
    
    const totalSentimentWords = negativeCount + positiveCount;
    const negativeSentiment = totalSentimentWords > 0 ? negativeCount / totalSentimentWords : 0;
    
    const threshold = rule.ruleConfig.threshold || 0.7;
    
    return {
      triggered: negativeSentiment > threshold,
      confidence: negativeSentiment,
      reason: negativeSentiment > threshold ? `High negative sentiment detected: ${(negativeSentiment * 100).toFixed(1)}%` : '',
      details: { 
        negativeSentiment, 
        negativeCount, 
        positiveCount,
        threshold 
      },
      model: 'sentiment-analysis-v1'
    };
  }

  // AI毒性检测（模拟）
  async checkToxicity(rule, content) {
    // 模拟毒性检测
    const toxicPatterns = [
      /\b(kill|die|death)\b/gi,
      /\b(stupid|idiot|moron)\b/gi,
      /\b(hate|hatred)\b/gi
    ];
    
    let toxicityScore = 0;
    const matches = [];
    
    toxicPatterns.forEach(pattern => {
      const found = content.match(pattern);
      if (found) {
        toxicityScore += 0.3;
        matches.push(...found);
      }
    });
    
    toxicityScore = Math.min(toxicityScore, 1.0);
    const threshold = rule.ruleConfig.threshold || this.toxicityThreshold;
    
    return {
      triggered: toxicityScore > threshold,
      confidence: toxicityScore,
      reason: toxicityScore > threshold ? `High toxicity detected: ${(toxicityScore * 100).toFixed(1)}%` : '',
      details: { 
        toxicityScore, 
        matches,
        threshold 
      },
      model: 'toxicity-detector-v1'
    };
  }

  // AI垃圾内容检测（模拟）
  async checkSpam(rule, content) {
    let spamScore = 0;
    const indicators = [];
    
    // 检查重复字符
    if (/(.)\1{4,}/.test(content)) {
      spamScore += 0.3;
      indicators.push('excessive_repetition');
    }
    
    // 检查过多的大写字母
    const uppercaseRatio = (content.match(/[A-Z]/g) || []).length / content.length;
    if (uppercaseRatio > 0.5) {
      spamScore += 0.2;
      indicators.push('excessive_caps');
    }
    
    // 检查链接数量
    const linkCount = (content.match(/https?:\/\/[^\s]+/g) || []).length;
    if (linkCount > 3) {
      spamScore += 0.4;
      indicators.push('too_many_links');
    }
    
    // 检查特殊字符
    const specialCharRatio = (content.match(/[!@#$%^&*()]/g) || []).length / content.length;
    if (specialCharRatio > 0.2) {
      spamScore += 0.2;
      indicators.push('excessive_special_chars');
    }
    
    spamScore = Math.min(spamScore, 1.0);
    const threshold = rule.ruleConfig.threshold || this.spamThreshold;
    
    return {
      triggered: spamScore > threshold,
      confidence: spamScore,
      reason: spamScore > threshold ? `Spam detected: ${(spamScore * 100).toFixed(1)}%` : '',
      details: { 
        spamScore, 
        indicators,
        threshold 
      },
      model: 'spam-detector-v1'
    };
  }

  // AI不当内容检测（模拟）
  async checkNSFW(rule, content) {
    // 简单的NSFW关键词检测
    const nsfwKeywords = ['adult', 'explicit', 'nsfw', 'sexual'];
    const foundKeywords = [];
    
    const lowerContent = content.toLowerCase();
    nsfwKeywords.forEach(keyword => {
      if (lowerContent.includes(keyword)) {
        foundKeywords.push(keyword);
      }
    });
    
    const nsfwScore = foundKeywords.length > 0 ? 0.8 : 0.0;
    const threshold = rule.ruleConfig.threshold || this.nsfwThreshold;
    
    return {
      triggered: nsfwScore > threshold,
      confidence: nsfwScore,
      reason: nsfwScore > threshold ? `NSFW content detected: ${foundKeywords.join(', ')}` : '',
      details: { 
        nsfwScore, 
        foundKeywords,
        threshold 
      },
      model: 'nsfw-detector-v1'
    };
  }

  // 长度检查
  checkLength(rule, content) {
    const config = rule.ruleConfig;
    const minLength = config.minLength || 0;
    const maxLength = config.maxLength || Infinity;
    const contentLength = content.length;
    
    const tooShort = contentLength < minLength;
    const tooLong = contentLength > maxLength;
    
    return {
      triggered: tooShort || tooLong,
      confidence: tooShort || tooLong ? 1.0 : 0.0,
      reason: tooShort ? `Content too short: ${contentLength} < ${minLength}` :
              tooLong ? `Content too long: ${contentLength} > ${maxLength}` : '',
      details: { 
        contentLength, 
        minLength, 
        maxLength,
        tooShort,
        tooLong
      },
      model: 'length-checker'
    };
  }

  // 链接分析
  async checkLinks(rule, content) {
    const urlRegex = /https?:\/\/[^\s]+/g;
    const urls = content.match(urlRegex) || [];
    
    const suspiciousUrls = [];
    const config = rule.ruleConfig;
    const blockedDomains = config.blockedDomains || [];
    
    urls.forEach(url => {
      try {
        const domain = new URL(url).hostname;
        if (blockedDomains.includes(domain)) {
          suspiciousUrls.push(url);
        }
      } catch (error) {
        // 无效URL也标记为可疑
        suspiciousUrls.push(url);
      }
    });
    
    return {
      triggered: suspiciousUrls.length > 0,
      confidence: suspiciousUrls.length > 0 ? 0.9 : 0.0,
      reason: suspiciousUrls.length > 0 ? `Suspicious links detected: ${suspiciousUrls.length}` : '',
      details: { 
        totalUrls: urls.length,
        suspiciousUrls,
        blockedDomains
      },
      model: 'link-analyzer'
    };
  }

  // 批量审核
  async bulkModerate(contentItems) {
    const results = [];
    
    for (const item of contentItems) {
      try {
        const result = await this.moderateContent(
          item.contentType,
          item.contentId,
          item.content,
          item.authorId,
          item.options
        );
        
        results.push({
          contentId: item.contentId,
          contentType: item.contentType,
          ...result
        });
      } catch (error) {
        logger.error(`Bulk moderation failed for ${item.contentType} ${item.contentId}:`, error);
        results.push({
          contentId: item.contentId,
          contentType: item.contentType,
          approved: false,
          requiresReview: true,
          error: error.message
        });
      }
    }
    
    return results;
  }

  // 获取审核统计
  async getModerationStats(days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const endDate = new Date();
    
    return await ContentModerationLog.getModerationStats(startDate, endDate);
  }

  // 训练和优化（占位符）
  async trainModel(trainingData) {
    // 这里可以实现模型训练逻辑
    logger.info('Model training not implemented yet');
    return { success: false, message: 'Training not implemented' };
  }

  // 增强的AI检测方法
  async performEnhancedAIDetection(content, contentType) {
    const results = {
      approved: true,
      flags: [],
      actions: [],
      confidence: 1.0,
      details: {}
    };

    try {
      // 1. 多语言敏感词检测
      const sensitiveWordResults = await this.detectSensitiveWords(content);
      if (sensitiveWordResults.detected) {
        results.flags.push({
          ruleId: 'sensitive_words',
          ruleName: 'Sensitive Words Detection',
          ruleType: 'content_filter',
          severity: sensitiveWordResults.severity,
          confidence: sensitiveWordResults.confidence,
          details: sensitiveWordResults.details
        });
        results.actions.push(sensitiveWordResults.action);
        results.approved = false;
      }

      // 2. 上下文语义分析
      const semanticResults = await this.performSemanticAnalysis(content);
      if (semanticResults.flagged) {
        results.flags.push({
          ruleId: 'semantic_analysis',
          ruleName: 'Semantic Content Analysis',
          ruleType: 'ai_analysis',
          severity: semanticResults.severity,
          confidence: semanticResults.confidence,
          details: semanticResults.details
        });
        results.actions.push(semanticResults.action);
        results.approved = results.approved && !semanticResults.requiresReview;
      }

      // 3. 情感分析和毒性检测
      const toxicityResults = await this.detectToxicity(content);
      if (toxicityResults.toxic) {
        results.flags.push({
          ruleId: 'toxicity_detection',
          ruleName: 'Toxicity Detection',
          ruleType: 'ai_analysis',
          severity: toxicityResults.severity,
          confidence: toxicityResults.confidence,
          details: toxicityResults.details
        });
        results.actions.push(toxicityResults.action);
        results.approved = false;
      }

      // 4. 图像内容检测（如果有图像）
      if (contentType === 'image' || this.containsImageReferences(content)) {
        const imageResults = await this.detectImageContent(content);
        if (imageResults.flagged) {
          results.flags.push({
            ruleId: 'image_content',
            ruleName: 'Image Content Detection',
            ruleType: 'image_analysis',
            severity: imageResults.severity,
            confidence: imageResults.confidence,
            details: imageResults.details
          });
          results.actions.push(imageResults.action);
          results.approved = results.approved && !imageResults.requiresReview;
        }
      }

      // 5. 链接和URL安全检测
      const urlResults = await this.detectMaliciousUrls(content);
      if (urlResults.malicious) {
        results.flags.push({
          ruleId: 'url_safety',
          ruleName: 'URL Safety Check',
          ruleType: 'security_check',
          severity: 'high',
          confidence: urlResults.confidence,
          details: urlResults.details
        });
        results.actions.push('block');
        results.approved = false;
      }

      // 计算总体置信度
      if (results.flags.length > 0) {
        results.confidence = results.flags.reduce((sum, flag) => sum + flag.confidence, 0) / results.flags.length;
      }

      return results;

    } catch (error) {
      logger.error('Enhanced AI detection error:', error);
      return {
        approved: false,
        flags: [{
          ruleId: 'ai_detection_error',
          ruleName: 'AI Detection Error',
          ruleType: 'system_error',
          severity: 'medium',
          confidence: 0.0,
          details: { error: error.message }
        }],
        actions: ['manual_review'],
        confidence: 0.0
      };
    }
  }

  // 多语言敏感词检测
  async detectSensitiveWords(content) {
    const text = content.toLowerCase();
    const detectedCategories = [];
    let maxSeverity = 'low';
    let totalScore = 0;
    let wordCount = 0;

    // 检测每个类别的敏感词
    for (const [category, words] of Object.entries(this.sensitiveWords)) {
      const categoryMatches = [];

      for (const word of words) {
        if (text.includes(word.toLowerCase())) {
          categoryMatches.push(word);
          totalScore += this.categoryWeights[category] || 0.5;
          wordCount++;
        }
      }

      if (categoryMatches.length > 0) {
        detectedCategories.push({
          category,
          matches: categoryMatches,
          weight: this.categoryWeights[category] || 0.5
        });

        // 更新严重程度
        if (this.categoryWeights[category] >= 0.9) {
          maxSeverity = 'critical';
        } else if (this.categoryWeights[category] >= 0.7 && maxSeverity !== 'critical') {
          maxSeverity = 'high';
        } else if (this.categoryWeights[category] >= 0.5 && !['critical', 'high'].includes(maxSeverity)) {
          maxSeverity = 'medium';
        }
      }
    }

    const confidence = wordCount > 0 ? Math.min(totalScore / wordCount, 1.0) : 0;
    const detected = detectedCategories.length > 0;

    return {
      detected,
      severity: maxSeverity,
      confidence,
      action: this.getActionBySeverity(maxSeverity),
      details: {
        categories: detectedCategories,
        totalMatches: wordCount,
        score: totalScore
      }
    };
  }

  // 语义分析
  async performSemanticAnalysis(content) {
    try {
      // 简化的语义分析 - 实际应用中可以集成更先进的NLP模型
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
      let totalToxicityScore = 0;
      let flaggedSentences = [];

      for (const sentence of sentences) {
        const sentenceScore = await this.analyzeSentenceToxicity(sentence);
        totalToxicityScore += sentenceScore.score;

        if (sentenceScore.score > 0.6) {
          flaggedSentences.push({
            sentence: sentence.trim(),
            score: sentenceScore.score,
            categories: sentenceScore.categories
          });
        }
      }

      const averageScore = sentences.length > 0 ? totalToxicityScore / sentences.length : 0;
      const flagged = averageScore > 0.5 || flaggedSentences.length > 0;

      return {
        flagged,
        severity: this.getSemanticSeverity(averageScore),
        confidence: Math.min(averageScore * 1.2, 1.0),
        requiresReview: averageScore > 0.7,
        action: averageScore > 0.8 ? 'block' : (averageScore > 0.6 ? 'flag' : 'approve'),
        details: {
          averageScore,
          flaggedSentences,
          totalSentences: sentences.length
        }
      };

    } catch (error) {
      logger.error('Semantic analysis error:', error);
      return {
        flagged: false,
        severity: 'low',
        confidence: 0.0,
        requiresReview: false,
        action: 'approve',
        details: { error: error.message }
      };
    }
  }

  // 分析句子毒性
  async analyzeSentenceToxicity(sentence) {
    const text = sentence.toLowerCase();
    let score = 0;
    const categories = [];

    // 检查各类敏感内容
    for (const [category, words] of Object.entries(this.sensitiveWords)) {
      let categoryScore = 0;
      let matches = 0;

      for (const word of words) {
        if (text.includes(word.toLowerCase())) {
          matches++;
          categoryScore += this.categoryWeights[category] || 0.5;
        }
      }

      if (matches > 0) {
        categories.push({
          category,
          matches,
          score: categoryScore / matches
        });
        score += categoryScore;
      }
    }

    // 检查句子结构和语气
    const structuralScore = this.analyzeStructuralToxicity(text);
    score += structuralScore;

    return {
      score: Math.min(score, 1.0),
      categories
    };
  }

  // 结构性毒性分析
  analyzeStructuralToxicity(text) {
    let score = 0;

    // 检查大写字母比例（可能表示愤怒）
    const uppercaseRatio = (text.match(/[A-Z]/g) || []).length / text.length;
    if (uppercaseRatio > 0.3) score += 0.2;

    // 检查重复标点符号
    if (/[!]{2,}|[?]{2,}/.test(text)) score += 0.1;

    // 检查威胁性语言模式
    const threatPatterns = [
      /you will (regret|pay|suffer)/i,
      /i will (kill|destroy|hurt)/i,
      /watch your back/i,
      /you're dead/i
    ];

    for (const pattern of threatPatterns) {
      if (pattern.test(text)) {
        score += 0.4;
        break;
      }
    }

    return score;
  }

  // 毒性检测
  async detectToxicity(content) {
    try {
      const semanticResults = await this.performSemanticAnalysis(content);

      // 额外的毒性指标
      const personalAttacks = this.detectPersonalAttacks(content);
      const hateSymbols = this.detectHateSymbols(content);
      const extremistContent = this.detectExtremistContent(content);

      const toxicityScore = Math.max(
        semanticResults.details.averageScore,
        personalAttacks.score,
        hateSymbols.score,
        extremistContent.score
      );

      const toxic = toxicityScore > this.thresholds.toxicity;

      return {
        toxic,
        severity: this.getToxicitySeverity(toxicityScore),
        confidence: toxicityScore,
        action: toxic ? (toxicityScore > 0.9 ? 'block' : 'flag') : 'approve',
        details: {
          toxicityScore,
          personalAttacks: personalAttacks.detected,
          hateSymbols: hateSymbols.detected,
          extremistContent: extremistContent.detected,
          semanticAnalysis: semanticResults.details
        }
      };

    } catch (error) {
      logger.error('Toxicity detection error:', error);
      return {
        toxic: false,
        severity: 'low',
        confidence: 0.0,
        action: 'approve',
        details: { error: error.message }
      };
    }
  }

  // 辅助方法
  getActionBySeverity(severity) {
    switch (severity) {
      case 'critical': return 'block';
      case 'high': return 'flag';
      case 'medium': return 'review';
      case 'low': return 'approve';
      default: return 'approve';
    }
  }

  getSemanticSeverity(score) {
    if (score >= 0.9) return 'critical';
    if (score >= 0.7) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  getToxicitySeverity(score) {
    if (score >= 0.9) return 'critical';
    if (score >= 0.7) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  // 检测人身攻击
  detectPersonalAttacks(content) {
    const attackPatterns = [
      /you are (stupid|idiot|moron|dumb)/i,
      /shut up/i,
      /go kill yourself/i,
      /nobody likes you/i,
      /you suck/i
    ];

    let score = 0;
    const matches = [];

    for (const pattern of attackPatterns) {
      if (pattern.test(content)) {
        matches.push(pattern.source);
        score += 0.3;
      }
    }

    return {
      detected: matches.length > 0,
      score: Math.min(score, 1.0),
      matches
    };
  }

  // 检测仇恨符号
  detectHateSymbols(content) {
    const hateSymbols = [
      '卐', '卍', '1488', '14/88', 'KKK', 'white power',
      'blood and honor', 'race war', 'final solution'
    ];

    let score = 0;
    const matches = [];

    for (const symbol of hateSymbols) {
      if (content.toLowerCase().includes(symbol.toLowerCase())) {
        matches.push(symbol);
        score += 0.8; // 仇恨符号权重很高
      }
    }

    return {
      detected: matches.length > 0,
      score: Math.min(score, 1.0),
      matches
    };
  }

  // 检测极端主义内容
  detectExtremistContent(content) {
    const extremistKeywords = [
      'jihad', 'infidel', 'crusade', 'holy war',
      'racial purity', 'ethnic cleansing', 'master race',
      'death to', 'destroy all', 'eliminate the'
    ];

    let score = 0;
    const matches = [];

    for (const keyword of extremistKeywords) {
      if (content.toLowerCase().includes(keyword.toLowerCase())) {
        matches.push(keyword);
        score += 0.7;
      }
    }

    return {
      detected: matches.length > 0,
      score: Math.min(score, 1.0),
      matches
    };
  }

  // 检测图像内容
  async detectImageContent(content) {
    // 简化的图像检测 - 实际应用中需要集成图像识别API
    const imageUrls = this.extractImageUrls(content);

    if (imageUrls.length === 0) {
      return {
        flagged: false,
        severity: 'low',
        confidence: 0.0,
        requiresReview: false,
        details: { message: 'No images found' }
      };
    }

    // 模拟图像内容检测
    const suspiciousPatterns = ['gore', 'nsfw', 'violence', 'explicit'];
    let flagged = false;

    for (const url of imageUrls) {
      for (const pattern of suspiciousPatterns) {
        if (url.toLowerCase().includes(pattern)) {
          flagged = true;
          break;
        }
      }
    }

    return {
      flagged,
      severity: flagged ? 'high' : 'low',
      confidence: flagged ? 0.8 : 0.2,
      requiresReview: flagged,
      details: {
        imageCount: imageUrls.length,
        imageUrls: imageUrls.slice(0, 5) // 只返回前5个URL
      }
    };
  }

  // 检测恶意URL
  async detectMaliciousUrls(content) {
    const urls = this.extractUrls(content);

    if (urls.length === 0) {
      return {
        malicious: false,
        confidence: 0.0,
        details: { message: 'No URLs found' }
      };
    }

    // 简化的恶意URL检测
    const maliciousDomains = [
      'malware.com', 'phishing.net', 'scam.org',
      'virus.info', 'trojan.biz', 'spam.site'
    ];

    const suspiciousPatterns = [
      /bit\.ly\/[a-zA-Z0-9]+/,
      /tinyurl\.com\/[a-zA-Z0-9]+/,
      /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/, // IP地址
      /[a-zA-Z0-9]+-[a-zA-Z0-9]+-[a-zA-Z0-9]+\.tk$/ // 可疑域名模式
    ];

    let maliciousCount = 0;
    const flaggedUrls = [];

    for (const url of urls) {
      let isMalicious = false;

      // 检查恶意域名
      for (const domain of maliciousDomains) {
        if (url.includes(domain)) {
          isMalicious = true;
          break;
        }
      }

      // 检查可疑模式
      if (!isMalicious) {
        for (const pattern of suspiciousPatterns) {
          if (pattern.test(url)) {
            isMalicious = true;
            break;
          }
        }
      }

      if (isMalicious) {
        maliciousCount++;
        flaggedUrls.push(url);
      }
    }

    const malicious = maliciousCount > 0;
    const confidence = malicious ? Math.min(maliciousCount / urls.length * 2, 1.0) : 0.0;

    return {
      malicious,
      confidence,
      details: {
        totalUrls: urls.length,
        maliciousCount,
        flaggedUrls
      }
    };
  }

  // 辅助方法：检查是否包含图像引用
  containsImageReferences(content) {
    const imagePatterns = [
      /\.(jpg|jpeg|png|gif|bmp|webp|svg)/i,
      /<img[^>]+>/i,
      /!\[.*\]\(.*\)/,
      /data:image\//i
    ];

    return imagePatterns.some(pattern => pattern.test(content));
  }

  // 提取图像URL
  extractImageUrls(content) {
    const imageUrlPattern = /https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp|svg)/gi;
    return content.match(imageUrlPattern) || [];
  }

  // 提取所有URL
  extractUrls(content) {
    const urlPattern = /https?:\/\/[^\s]+/gi;
    return content.match(urlPattern) || [];
  }

  // 记录审核结果
  async logModerationResult(contentType, contentId, authorId, results, options) {
    try {
      await ContentModerationLog.create({
        contentType,
        contentId,
        authorId,
        moderationType: 'automatic',
        action: results.approved ? 'approved' : 'flagged',
        reason: results.flags.map(f => f.ruleName).join(', ') || 'AI Analysis',
        details: {
          flags: results.flags,
          actions: results.actions,
          confidence: results.confidence,
          aiEnhanced: true,
          ...results.details
        },
        severity: results.flags.length > 0 ?
          results.flags.reduce((max, flag) =>
            ['critical', 'high', 'medium', 'low'].indexOf(flag.severity) <
            ['critical', 'high', 'medium', 'low'].indexOf(max) ? flag.severity : max
          , 'low') : 'low',
        confidence: results.confidence,
        metadata: options
      });

      logger.info(`Moderation result logged for ${contentType} ${contentId}`);
    } catch (error) {
      logger.error('Failed to log moderation result:', error);
    }
  }
}

// 创建单例实例
const aiContentModerationService = new AIContentModerationService();

module.exports = aiContentModerationService;
