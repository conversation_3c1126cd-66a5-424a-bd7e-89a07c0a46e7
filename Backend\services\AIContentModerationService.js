const ContentModerationRule = require('../models/ContentModerationRule');
const ContentModerationLog = require('../models/ContentModerationLog');
const { logger } = require('../config/logger');

class AIContentModerationService {
  constructor() {
    this.sensitiveWords = new Set([
      // 基础敏感词库 - 实际应用中应该从数据库或配置文件加载
      'spam', 'scam', 'fraud', 'fake', 'illegal', 'violence', 'hate',
      'discrimination', 'harassment', 'abuse', 'threat', 'dangerous'
    ]);
    
    this.toxicityThreshold = 0.7;
    this.spamThreshold = 0.8;
    this.nsfwThreshold = 0.6;
  }

  // 主要的内容审核入口
  async moderateContent(contentType, contentId, content, authorId, options = {}) {
    try {
      logger.info(`Starting AI moderation for ${contentType} ${contentId}`);
      
      const results = {
        approved: true,
        flags: [],
        actions: [],
        confidence: 1.0,
        details: {}
      };

      // 获取适用的规则
      const rules = await ContentModerationRule.getActiveRules(contentType);
      
      // 执行各种检查
      for (const rule of rules) {
        const ruleResult = await this.executeRule(rule, content, contentType, contentId);
        
        if (ruleResult.triggered) {
          results.flags.push({
            ruleId: rule.id,
            ruleName: rule.name,
            ruleType: rule.ruleType,
            severity: rule.severity,
            confidence: ruleResult.confidence,
            details: ruleResult.details
          });
          
          results.actions.push(rule.action);
          results.confidence = Math.min(results.confidence, ruleResult.confidence);
          
          // 记录触发日志
          await ContentModerationLog.logModeration({
            contentType,
            contentId,
            ruleId: rule.id,
            moderationType: 'automatic',
            action: rule.action,
            reason: ruleResult.reason,
            details: ruleResult.details,
            confidence: ruleResult.confidence,
            aiModel: ruleResult.model || 'rule-based',
            authorId,
            severity: rule.severity
          });
          
          // 更新规则触发计数
          await rule.incrementTriggerCount();
          
          // 如果是自动拒绝，立即返回
          if (rule.action === 'auto_reject') {
            results.approved = false;
            break;
          }
          
          // 如果需要人工审核
          if (rule.action === 'require_review') {
            results.approved = false;
            results.requiresReview = true;
          }
        }
      }
      
      // 如果有标记但没有拒绝，仍然可以通过
      if (results.flags.length > 0 && results.approved) {
        const hasHighSeverity = results.flags.some(flag => 
          ['high', 'critical'].includes(flag.severity)
        );
        
        if (hasHighSeverity) {
          results.approved = false;
          results.requiresReview = true;
        }
      }
      
      logger.info(`AI moderation completed for ${contentType} ${contentId}: ${results.approved ? 'APPROVED' : 'FLAGGED'}`);
      
      return results;
    } catch (error) {
      logger.error('AI content moderation failed:', error);
      
      // 记录错误日志
      await ContentModerationLog.logModeration({
        contentType,
        contentId,
        moderationType: 'automatic',
        action: 'flagged',
        reason: 'AI moderation system error',
        details: { error: error.message },
        authorId,
        severity: 'high'
      });
      
      // 出错时默认需要人工审核
      return {
        approved: false,
        requiresReview: true,
        flags: [{
          ruleName: 'System Error',
          ruleType: 'system',
          severity: 'high',
          confidence: 0.0,
          details: { error: error.message }
        }],
        actions: ['require_review'],
        confidence: 0.0,
        details: { error: error.message }
      };
    }
  }

  // 执行单个规则
  async executeRule(rule, content, contentType, contentId) {
    const result = {
      triggered: false,
      confidence: 0.0,
      reason: '',
      details: {},
      model: 'rule-based'
    };

    try {
      switch (rule.ruleType) {
        case 'keyword':
          return this.checkKeywords(rule, content);
          
        case 'regex':
          return this.checkRegex(rule, content);
          
        case 'ai_sentiment':
          return await this.checkSentiment(rule, content);
          
        case 'ai_toxicity':
          return await this.checkToxicity(rule, content);
          
        case 'ai_spam':
          return await this.checkSpam(rule, content);
          
        case 'ai_nsfw':
          return await this.checkNSFW(rule, content);
          
        case 'length_check':
          return this.checkLength(rule, content);
          
        case 'link_analysis':
          return await this.checkLinks(rule, content);
          
        default:
          logger.warn(`Unknown rule type: ${rule.ruleType}`);
          return result;
      }
    } catch (error) {
      logger.error(`Error executing rule ${rule.id}:`, error);
      return {
        triggered: true,
        confidence: 0.0,
        reason: `Rule execution error: ${error.message}`,
        details: { error: error.message },
        model: 'error'
      };
    }
  }

  // 关键词检查
  checkKeywords(rule, content) {
    const config = rule.ruleConfig;
    const keywords = config.keywords || [];
    const caseSensitive = config.caseSensitive || false;
    const wholeWordOnly = config.wholeWordOnly || false;
    
    const text = caseSensitive ? content : content.toLowerCase();
    const foundKeywords = [];
    
    for (const keyword of keywords) {
      const searchKeyword = caseSensitive ? keyword : keyword.toLowerCase();
      
      if (wholeWordOnly) {
        const regex = new RegExp(`\\b${searchKeyword}\\b`, 'g');
        if (regex.test(text)) {
          foundKeywords.push(keyword);
        }
      } else {
        if (text.includes(searchKeyword)) {
          foundKeywords.push(keyword);
        }
      }
    }
    
    return {
      triggered: foundKeywords.length > 0,
      confidence: foundKeywords.length > 0 ? 1.0 : 0.0,
      reason: foundKeywords.length > 0 ? `Found keywords: ${foundKeywords.join(', ')}` : '',
      details: { foundKeywords },
      model: 'keyword-filter'
    };
  }

  // 正则表达式检查
  checkRegex(rule, content) {
    const config = rule.ruleConfig;
    const pattern = config.pattern;
    const flags = config.flags || 'gi';
    
    try {
      const regex = new RegExp(pattern, flags);
      const matches = content.match(regex);
      
      return {
        triggered: matches !== null,
        confidence: matches ? 1.0 : 0.0,
        reason: matches ? `Regex pattern matched: ${pattern}` : '',
        details: { matches: matches || [], pattern },
        model: 'regex-filter'
      };
    } catch (error) {
      return {
        triggered: true,
        confidence: 0.0,
        reason: `Invalid regex pattern: ${pattern}`,
        details: { error: error.message },
        model: 'regex-error'
      };
    }
  }

  // AI情感分析（模拟）
  async checkSentiment(rule, content) {
    // 这里应该调用真实的AI API，如Google Cloud Natural Language API
    // 现在使用简单的模拟逻辑
    
    const negativeWords = ['hate', 'angry', 'terrible', 'awful', 'disgusting', 'horrible'];
    const positiveWords = ['love', 'great', 'awesome', 'wonderful', 'amazing', 'excellent'];
    
    const words = content.toLowerCase().split(/\s+/);
    let negativeCount = 0;
    let positiveCount = 0;
    
    words.forEach(word => {
      if (negativeWords.includes(word)) negativeCount++;
      if (positiveWords.includes(word)) positiveCount++;
    });
    
    const totalSentimentWords = negativeCount + positiveCount;
    const negativeSentiment = totalSentimentWords > 0 ? negativeCount / totalSentimentWords : 0;
    
    const threshold = rule.ruleConfig.threshold || 0.7;
    
    return {
      triggered: negativeSentiment > threshold,
      confidence: negativeSentiment,
      reason: negativeSentiment > threshold ? `High negative sentiment detected: ${(negativeSentiment * 100).toFixed(1)}%` : '',
      details: { 
        negativeSentiment, 
        negativeCount, 
        positiveCount,
        threshold 
      },
      model: 'sentiment-analysis-v1'
    };
  }

  // AI毒性检测（模拟）
  async checkToxicity(rule, content) {
    // 模拟毒性检测
    const toxicPatterns = [
      /\b(kill|die|death)\b/gi,
      /\b(stupid|idiot|moron)\b/gi,
      /\b(hate|hatred)\b/gi
    ];
    
    let toxicityScore = 0;
    const matches = [];
    
    toxicPatterns.forEach(pattern => {
      const found = content.match(pattern);
      if (found) {
        toxicityScore += 0.3;
        matches.push(...found);
      }
    });
    
    toxicityScore = Math.min(toxicityScore, 1.0);
    const threshold = rule.ruleConfig.threshold || this.toxicityThreshold;
    
    return {
      triggered: toxicityScore > threshold,
      confidence: toxicityScore,
      reason: toxicityScore > threshold ? `High toxicity detected: ${(toxicityScore * 100).toFixed(1)}%` : '',
      details: { 
        toxicityScore, 
        matches,
        threshold 
      },
      model: 'toxicity-detector-v1'
    };
  }

  // AI垃圾内容检测（模拟）
  async checkSpam(rule, content) {
    let spamScore = 0;
    const indicators = [];
    
    // 检查重复字符
    if (/(.)\1{4,}/.test(content)) {
      spamScore += 0.3;
      indicators.push('excessive_repetition');
    }
    
    // 检查过多的大写字母
    const uppercaseRatio = (content.match(/[A-Z]/g) || []).length / content.length;
    if (uppercaseRatio > 0.5) {
      spamScore += 0.2;
      indicators.push('excessive_caps');
    }
    
    // 检查链接数量
    const linkCount = (content.match(/https?:\/\/[^\s]+/g) || []).length;
    if (linkCount > 3) {
      spamScore += 0.4;
      indicators.push('too_many_links');
    }
    
    // 检查特殊字符
    const specialCharRatio = (content.match(/[!@#$%^&*()]/g) || []).length / content.length;
    if (specialCharRatio > 0.2) {
      spamScore += 0.2;
      indicators.push('excessive_special_chars');
    }
    
    spamScore = Math.min(spamScore, 1.0);
    const threshold = rule.ruleConfig.threshold || this.spamThreshold;
    
    return {
      triggered: spamScore > threshold,
      confidence: spamScore,
      reason: spamScore > threshold ? `Spam detected: ${(spamScore * 100).toFixed(1)}%` : '',
      details: { 
        spamScore, 
        indicators,
        threshold 
      },
      model: 'spam-detector-v1'
    };
  }

  // AI不当内容检测（模拟）
  async checkNSFW(rule, content) {
    // 简单的NSFW关键词检测
    const nsfwKeywords = ['adult', 'explicit', 'nsfw', 'sexual'];
    const foundKeywords = [];
    
    const lowerContent = content.toLowerCase();
    nsfwKeywords.forEach(keyword => {
      if (lowerContent.includes(keyword)) {
        foundKeywords.push(keyword);
      }
    });
    
    const nsfwScore = foundKeywords.length > 0 ? 0.8 : 0.0;
    const threshold = rule.ruleConfig.threshold || this.nsfwThreshold;
    
    return {
      triggered: nsfwScore > threshold,
      confidence: nsfwScore,
      reason: nsfwScore > threshold ? `NSFW content detected: ${foundKeywords.join(', ')}` : '',
      details: { 
        nsfwScore, 
        foundKeywords,
        threshold 
      },
      model: 'nsfw-detector-v1'
    };
  }

  // 长度检查
  checkLength(rule, content) {
    const config = rule.ruleConfig;
    const minLength = config.minLength || 0;
    const maxLength = config.maxLength || Infinity;
    const contentLength = content.length;
    
    const tooShort = contentLength < minLength;
    const tooLong = contentLength > maxLength;
    
    return {
      triggered: tooShort || tooLong,
      confidence: tooShort || tooLong ? 1.0 : 0.0,
      reason: tooShort ? `Content too short: ${contentLength} < ${minLength}` :
              tooLong ? `Content too long: ${contentLength} > ${maxLength}` : '',
      details: { 
        contentLength, 
        minLength, 
        maxLength,
        tooShort,
        tooLong
      },
      model: 'length-checker'
    };
  }

  // 链接分析
  async checkLinks(rule, content) {
    const urlRegex = /https?:\/\/[^\s]+/g;
    const urls = content.match(urlRegex) || [];
    
    const suspiciousUrls = [];
    const config = rule.ruleConfig;
    const blockedDomains = config.blockedDomains || [];
    
    urls.forEach(url => {
      try {
        const domain = new URL(url).hostname;
        if (blockedDomains.includes(domain)) {
          suspiciousUrls.push(url);
        }
      } catch (error) {
        // 无效URL也标记为可疑
        suspiciousUrls.push(url);
      }
    });
    
    return {
      triggered: suspiciousUrls.length > 0,
      confidence: suspiciousUrls.length > 0 ? 0.9 : 0.0,
      reason: suspiciousUrls.length > 0 ? `Suspicious links detected: ${suspiciousUrls.length}` : '',
      details: { 
        totalUrls: urls.length,
        suspiciousUrls,
        blockedDomains
      },
      model: 'link-analyzer'
    };
  }

  // 批量审核
  async bulkModerate(contentItems) {
    const results = [];
    
    for (const item of contentItems) {
      try {
        const result = await this.moderateContent(
          item.contentType,
          item.contentId,
          item.content,
          item.authorId,
          item.options
        );
        
        results.push({
          contentId: item.contentId,
          contentType: item.contentType,
          ...result
        });
      } catch (error) {
        logger.error(`Bulk moderation failed for ${item.contentType} ${item.contentId}:`, error);
        results.push({
          contentId: item.contentId,
          contentType: item.contentType,
          approved: false,
          requiresReview: true,
          error: error.message
        });
      }
    }
    
    return results;
  }

  // 获取审核统计
  async getModerationStats(days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const endDate = new Date();
    
    return await ContentModerationLog.getModerationStats(startDate, endDate);
  }

  // 训练和优化（占位符）
  async trainModel(trainingData) {
    // 这里可以实现模型训练逻辑
    logger.info('Model training not implemented yet');
    return { success: false, message: 'Training not implemented' };
  }
}

// 创建单例实例
const aiContentModerationService = new AIContentModerationService();

module.exports = aiContentModerationService;
