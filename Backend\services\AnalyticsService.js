const Analytics = require('../models/Analytics');
const Report = require('../models/Report');
const { logger } = require('../config/logger');
const { sequelize } = require('../config/database');

class AnalyticsService {
  constructor() {
    this.eventQueue = [];
    this.batchSize = 100;
    this.flushInterval = 5000; // 5秒
    this.processingInterval = null;
    this.isProcessing = false;
  }

  // 启动分析服务
  start() {
    this.processingInterval = setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);
    
    logger.info('Analytics service started');
  }

  // 停止分析服务
  stop() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    
    // 处理剩余事件
    this.flushEvents();
    
    logger.info('Analytics service stopped');
  }

  // 追踪事件
  track(eventType, eventData = {}) {
    try {
      const event = {
        eventType,
        userId: eventData.userId || null,
        sessionId: eventData.sessionId || null,
        targetType: eventData.targetType || null,
        targetId: eventData.targetId || null,
        properties: eventData.properties || {},
        value: eventData.value || null,
        url: eventData.url || null,
        referer: eventData.referer || null,
        userAgent: eventData.userAgent || null,
        ipAddress: eventData.ipAddress || null,
        location: eventData.location || null,
        device: eventData.device || null,
        browser: eventData.browser || null,
        timestamp: eventData.timestamp || new Date()
      };

      this.eventQueue.push(event);

      // 如果队列满了，立即刷新
      if (this.eventQueue.length >= this.batchSize) {
        this.flushEvents();
      }

      return true;
    } catch (error) {
      logger.error('Error tracking event:', error);
      return false;
    }
  }

  // 批量刷新事件到数据库
  async flushEvents() {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const events = this.eventQueue.splice(0, this.batchSize);

    try {
      await Analytics.bulkCreate(events);
      logger.debug(`Flushed ${events.length} analytics events`);
    } catch (error) {
      logger.error('Error flushing analytics events:', error);
      // 将失败的事件重新加入队列
      this.eventQueue.unshift(...events);
    } finally {
      this.isProcessing = false;
    }
  }

  // 页面浏览追踪
  trackPageView(data) {
    return this.track('page_view', {
      ...data,
      properties: {
        ...data.properties,
        page_title: data.pageTitle,
        page_type: data.pageType
      }
    });
  }

  // 文章浏览追踪
  trackArticleView(articleId, data) {
    return this.track('article_view', {
      ...data,
      targetType: 'article',
      targetId: articleId,
      properties: {
        ...data.properties,
        article_category: data.category,
        article_author: data.authorId
      }
    });
  }

  // 文章阅读完成追踪
  trackArticleRead(articleId, readTime, data) {
    return this.track('article_read', {
      ...data,
      targetType: 'article',
      targetId: articleId,
      value: readTime,
      properties: {
        ...data.properties,
        read_percentage: data.readPercentage || 100,
        scroll_depth: data.scrollDepth || 100
      }
    });
  }

  // 用户登录追踪
  trackUserLogin(userId, data) {
    return this.track('user_login', {
      ...data,
      userId,
      properties: {
        ...data.properties,
        login_method: data.loginMethod || 'email'
      }
    });
  }

  // 用户注册追踪
  trackUserRegister(userId, data) {
    return this.track('user_register', {
      ...data,
      userId,
      properties: {
        ...data.properties,
        registration_method: data.registrationMethod || 'email',
        referral_source: data.referralSource
      }
    });
  }

  // 搜索追踪
  trackSearch(query, resultCount, data) {
    return this.track('search', {
      ...data,
      value: resultCount,
      properties: {
        ...data.properties,
        search_query: query,
        search_type: data.searchType || 'fulltext',
        result_count: resultCount
      }
    });
  }

  // 点击追踪
  trackClick(targetType, targetId, data) {
    return this.track('click', {
      ...data,
      targetType,
      targetId,
      properties: {
        ...data.properties,
        click_type: data.clickType,
        element_text: data.elementText,
        element_position: data.elementPosition
      }
    });
  }

  // 分享追踪
  trackShare(targetType, targetId, platform, data) {
    return this.track('share', {
      ...data,
      targetType,
      targetId,
      properties: {
        ...data.properties,
        share_platform: platform,
        share_method: data.shareMethod || 'button'
      }
    });
  }

  // 错误追踪
  trackError(errorType, errorMessage, data) {
    return this.track('error', {
      ...data,
      properties: {
        ...data.properties,
        error_type: errorType,
        error_message: errorMessage,
        error_stack: data.errorStack,
        error_url: data.url
      }
    });
  }

  // 性能指标追踪
  trackPerformance(metric, value, data) {
    return this.track('performance', {
      ...data,
      value,
      properties: {
        ...data.properties,
        metric,
        measurement_type: data.measurementType || 'timing'
      }
    });
  }

  // 自定义事件追踪
  trackCustomEvent(eventName, data) {
    return this.track('custom', {
      ...data,
      properties: {
        ...data.properties,
        custom_event_name: eventName
      }
    });
  }

  // 获取实时统计
  async getRealTimeStats() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const [hourlyStats, dailyStats] = await Promise.all([
        sequelize.query(`
          SELECT 
            event_type,
            COUNT(*) as count,
            COUNT(DISTINCT user_id) as unique_users
          FROM analytics 
          WHERE timestamp >= :oneHourAgo
          GROUP BY event_type
          ORDER BY count DESC
        `, {
          replacements: { oneHourAgo },
          type: sequelize.QueryTypes.SELECT
        }),
        
        sequelize.query(`
          SELECT 
            COUNT(*) as total_events,
            COUNT(DISTINCT user_id) as unique_users,
            COUNT(DISTINCT session_id) as unique_sessions,
            COUNT(CASE WHEN event_type = 'page_view' THEN 1 END) as page_views,
            COUNT(CASE WHEN event_type = 'article_view' THEN 1 END) as article_views
          FROM analytics 
          WHERE timestamp >= :oneDayAgo
        `, {
          replacements: { oneDayAgo },
          type: sequelize.QueryTypes.SELECT
        })
      ]);

      return {
        hourly: hourlyStats,
        daily: dailyStats[0],
        queueSize: this.eventQueue.length,
        lastUpdate: new Date()
      };
    } catch (error) {
      logger.error('Error getting real-time stats:', error);
      throw error;
    }
  }

  // 获取用户行为分析
  async getUserBehaviorAnalysis(userId, days = 30) {
    try {
      return await Analytics.getUserBehaviorAnalysis(userId, days);
    } catch (error) {
      logger.error('Error getting user behavior analysis:', error);
      throw error;
    }
  }

  // 获取内容性能分析
  async getContentPerformance(targetType = 'article', days = 30, limit = 20) {
    try {
      return await Analytics.getContentPerformance(targetType, days, limit);
    } catch (error) {
      logger.error('Error getting content performance:', error);
      throw error;
    }
  }

  // 获取流量来源分析
  async getTrafficSources(days = 30) {
    try {
      return await Analytics.getTrafficSources(days);
    } catch (error) {
      logger.error('Error getting traffic sources:', error);
      throw error;
    }
  }

  // 获取设备统计
  async getDeviceStats(days = 30) {
    try {
      return await Analytics.getDeviceStats(days);
    } catch (error) {
      logger.error('Error getting device stats:', error);
      throw error;
    }
  }

  // 获取地理统计
  async getGeographicStats(days = 30) {
    try {
      return await Analytics.getGeographicStats(days);
    } catch (error) {
      logger.error('Error getting geographic stats:', error);
      throw error;
    }
  }

  // 获取性能指标
  async getPerformanceMetrics(days = 7) {
    try {
      return await Analytics.getPerformanceMetrics(days);
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  // 获取漏斗分析
  async getFunnelAnalysis(funnelSteps, days = 30) {
    try {
      const rawData = await Analytics.getFunnelAnalysis(funnelSteps, days);
      
      // 处理漏斗数据
      const funnelData = funnelSteps.map((step, index) => {
        const stepUsers = rawData.filter(user => 
          user[`step_${index + 1}_${step}`] > 0
        ).length;
        
        return {
          step: step,
          users: stepUsers,
          conversionRate: index === 0 ? 100 : (stepUsers / rawData.length * 100)
        };
      });
      
      return funnelData;
    } catch (error) {
      logger.error('Error getting funnel analysis:', error);
      throw error;
    }
  }

  // 获取留存分析
  async getRetentionAnalysis(days = 30) {
    try {
      return await Analytics.getRetentionAnalysis(days);
    } catch (error) {
      logger.error('Error getting retention analysis:', error);
      throw error;
    }
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueSize: this.eventQueue.length,
      isProcessing: this.isProcessing,
      batchSize: this.batchSize,
      flushInterval: this.flushInterval
    };
  }

  // 清空队列
  clearQueue() {
    this.eventQueue = [];
    logger.info('Analytics event queue cleared');
  }

  // 设置批处理配置
  setBatchConfig(batchSize, flushInterval) {
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    
    // 重启定时器
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = setInterval(() => {
        this.flushEvents();
      }, this.flushInterval);
    }
    
    logger.info(`Analytics batch config updated: batchSize=${batchSize}, flushInterval=${flushInterval}`);
  }
}

// 创建单例实例
const analyticsService = new AnalyticsService();

module.exports = analyticsService;
