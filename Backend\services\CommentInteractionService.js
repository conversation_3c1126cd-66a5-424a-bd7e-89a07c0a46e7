/**
 * Newzora 评论交互服务
 * 提供多层级评论、回复、点赞、举报等功能
 */

const { logger } = require('../config/logger');

class CommentInteractionService {
  constructor() {
    // 评论类型
    this.commentTypes = {
      'article': 'Article Comment',
      'video': 'Video Comment',
      'audio': 'Audio Comment',
      'image': 'Image Comment',
      'user': 'User Profile Comment'
    };

    // 交互类型
    this.interactionTypes = {
      'like': { name: 'Like', weight: 1, icon: '👍' },
      'dislike': { name: 'Dislike', weight: -1, icon: '👎' },
      'love': { name: 'Love', weight: 2, icon: '❤️' },
      'laugh': { name: 'Laugh', weight: 1, icon: '😂' },
      'angry': { name: 'Angry', weight: -1, icon: '😠' },
      'sad': { name: 'Sad', weight: -1, icon: '😢' },
      'wow': { name: 'Wow', weight: 1, icon: '😮' }
    };

    // 举报类型
    this.reportTypes = {
      'spam': { name: 'Spam', severity: 'medium', description: 'Unwanted commercial content or repetitive messages' },
      'harassment': { name: 'Harassment', severity: 'high', description: 'Bullying, threats, or personal attacks' },
      'hate_speech': { name: 'Hate Speech', severity: 'high', description: 'Content that attacks or demeans based on identity' },
      'misinformation': { name: 'Misinformation', severity: 'medium', description: 'False or misleading information' },
      'inappropriate': { name: 'Inappropriate Content', severity: 'medium', description: 'Content not suitable for the platform' },
      'copyright': { name: 'Copyright Violation', severity: 'high', description: 'Unauthorized use of copyrighted material' },
      'violence': { name: 'Violence', severity: 'critical', description: 'Content promoting or depicting violence' },
      'other': { name: 'Other', severity: 'low', description: 'Other policy violations' }
    };

    // 评论状态
    this.commentStatuses = {
      'active': 'Active',
      'hidden': 'Hidden',
      'deleted': 'Deleted',
      'pending': 'Pending Moderation',
      'flagged': 'Flagged for Review'
    };

    // 评论排序选项
    this.sortOptions = {
      'newest': { field: 'createdAt', order: 'DESC', name: 'Newest First' },
      'oldest': { field: 'createdAt', order: 'ASC', name: 'Oldest First' },
      'popular': { field: 'likeCount', order: 'DESC', name: 'Most Popular' },
      'controversial': { field: 'replyCount', order: 'DESC', name: 'Most Discussed' }
    };

    // 内存存储（实际应用中应使用数据库）
    this.comments = new Map();
    this.interactions = new Map();
    this.reports = new Map();
    this.commentCounter = 1;
  }

  // 创建评论
  async createComment(data) {
    try {
      const {
        userId,
        targetType,
        targetId,
        content,
        parentId = null,
        mentions = [],
        attachments = []
      } = data;

      // 验证输入
      if (!userId || !targetType || !targetId || !content) {
        throw new Error('Missing required fields');
      }

      if (!this.commentTypes[targetType]) {
        throw new Error(`Invalid target type: ${targetType}`);
      }

      // 内容验证
      if (content.length > 5000) {
        throw new Error('Comment content too long');
      }

      // 检查父评论是否存在
      if (parentId && !this.comments.has(parentId)) {
        throw new Error('Parent comment not found');
      }

      // 创建评论对象
      const commentId = `comment_${this.commentCounter++}`;
      const comment = {
        id: commentId,
        userId,
        targetType,
        targetId,
        content,
        parentId,
        mentions,
        attachments,
        status: 'active',
        likeCount: 0,
        dislikeCount: 0,
        replyCount: 0,
        reportCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        editedAt: null,
        metadata: {
          ipAddress: '127.0.0.1', // 应该从请求中获取
          userAgent: 'Unknown',
          language: 'en'
        }
      };

      // 保存评论
      this.comments.set(commentId, comment);

      // 如果是回复，更新父评论的回复数
      if (parentId) {
        const parentComment = this.comments.get(parentId);
        if (parentComment) {
          parentComment.replyCount += 1;
          parentComment.updatedAt = new Date().toISOString();
        }
      }

      logger.info(`Comment created: ${commentId} by user ${userId}`);

      return {
        success: true,
        comment,
        message: 'Comment created successfully'
      };

    } catch (error) {
      logger.error('Create comment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取评论列表
  async getComments(targetType, targetId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sort = 'newest',
        includeReplies = true,
        maxDepth = 3,
        userId = null
      } = options;

      // 获取目标的所有评论
      const targetComments = Array.from(this.comments.values())
        .filter(comment => 
          comment.targetType === targetType && 
          comment.targetId === targetId &&
          comment.status === 'active'
        );

      // 分离顶级评论和回复
      const topLevelComments = targetComments.filter(comment => !comment.parentId);
      const replies = targetComments.filter(comment => comment.parentId);

      // 排序顶级评论
      const sortConfig = this.sortOptions[sort] || this.sortOptions.newest;
      topLevelComments.sort((a, b) => {
        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];
        
        if (sortConfig.order === 'DESC') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // 分页
      const startIndex = (page - 1) * limit;
      const paginatedComments = topLevelComments.slice(startIndex, startIndex + limit);

      // 构建评论树
      const commentsWithReplies = includeReplies 
        ? paginatedComments.map(comment => this.buildCommentTree(comment, replies, maxDepth, userId))
        : paginatedComments.map(comment => this.enrichComment(comment, userId));

      return {
        success: true,
        data: {
          comments: commentsWithReplies,
          pagination: {
            page,
            limit,
            total: topLevelComments.length,
            totalPages: Math.ceil(topLevelComments.length / limit)
          },
          sort,
          targetType,
          targetId
        }
      };

    } catch (error) {
      logger.error('Get comments error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 构建评论树
  buildCommentTree(comment, allReplies, maxDepth, userId, currentDepth = 0) {
    const enrichedComment = this.enrichComment(comment, userId);

    if (currentDepth >= maxDepth) {
      return enrichedComment;
    }

    // 获取直接回复
    const directReplies = allReplies
      .filter(reply => reply.parentId === comment.id)
      .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

    // 递归构建回复树
    enrichedComment.replies = directReplies.map(reply => 
      this.buildCommentTree(reply, allReplies, maxDepth, userId, currentDepth + 1)
    );

    return enrichedComment;
  }

  // 丰富评论信息
  enrichComment(comment, userId) {
    const enriched = { ...comment };

    // 添加用户交互状态
    if (userId) {
      const userInteraction = this.getUserInteraction(comment.id, userId);
      enriched.userInteraction = userInteraction;
    }

    // 添加相对时间
    enriched.relativeTime = this.getRelativeTime(comment.createdAt);

    // 添加是否可编辑/删除
    enriched.canEdit = userId === comment.userId;
    enriched.canDelete = userId === comment.userId;

    return enriched;
  }

  // 获取用户对评论的交互
  getUserInteraction(commentId, userId) {
    const interactionKey = `${commentId}_${userId}`;
    return this.interactions.get(interactionKey) || null;
  }

  // 添加交互（点赞、点踩等）
  async addInteraction(commentId, userId, interactionType) {
    try {
      if (!this.interactionTypes[interactionType]) {
        throw new Error(`Invalid interaction type: ${interactionType}`);
      }

      const comment = this.comments.get(commentId);
      if (!comment) {
        throw new Error('Comment not found');
      }

      const interactionKey = `${commentId}_${userId}`;
      const existingInteraction = this.interactions.get(interactionKey);

      // 如果已有相同交互，则移除
      if (existingInteraction && existingInteraction.type === interactionType) {
        this.interactions.delete(interactionKey);
        this.updateCommentCounts(comment, interactionType, -1);
        
        return {
          success: true,
          action: 'removed',
          interactionType,
          message: 'Interaction removed'
        };
      }

      // 如果有不同的交互，先移除旧的
      if (existingInteraction) {
        this.updateCommentCounts(comment, existingInteraction.type, -1);
      }

      // 添加新交互
      const interaction = {
        commentId,
        userId,
        type: interactionType,
        createdAt: new Date().toISOString()
      };

      this.interactions.set(interactionKey, interaction);
      this.updateCommentCounts(comment, interactionType, 1);

      return {
        success: true,
        action: existingInteraction ? 'changed' : 'added',
        interactionType,
        message: 'Interaction updated successfully'
      };

    } catch (error) {
      logger.error('Add interaction error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 更新评论计数
  updateCommentCounts(comment, interactionType, delta) {
    const interaction = this.interactionTypes[interactionType];
    
    if (interaction.weight > 0) {
      comment.likeCount = Math.max(0, comment.likeCount + delta);
    } else if (interaction.weight < 0) {
      comment.dislikeCount = Math.max(0, comment.dislikeCount + delta);
    }

    comment.updatedAt = new Date().toISOString();
  }

  // 举报评论
  async reportComment(commentId, userId, reportData) {
    try {
      const {
        type,
        reason = '',
        additionalInfo = ''
      } = reportData;

      if (!this.reportTypes[type]) {
        throw new Error(`Invalid report type: ${type}`);
      }

      const comment = this.comments.get(commentId);
      if (!comment) {
        throw new Error('Comment not found');
      }

      // 检查是否已举报
      const reportKey = `${commentId}_${userId}`;
      if (this.reports.has(reportKey)) {
        throw new Error('You have already reported this comment');
      }

      // 创建举报记录
      const report = {
        id: `report_${Date.now()}`,
        commentId,
        reporterId: userId,
        type,
        reason,
        additionalInfo,
        status: 'pending',
        createdAt: new Date().toISOString(),
        reviewedAt: null,
        reviewedBy: null,
        action: null
      };

      this.reports.set(reportKey, report);

      // 更新评论举报计数
      comment.reportCount += 1;
      comment.updatedAt = new Date().toISOString();

      // 如果举报数量过多，自动标记为待审核
      if (comment.reportCount >= 5) {
        comment.status = 'flagged';
      }

      logger.info(`Comment reported: ${commentId} by user ${userId} for ${type}`);

      return {
        success: true,
        report,
        message: 'Comment reported successfully'
      };

    } catch (error) {
      logger.error('Report comment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 编辑评论
  async editComment(commentId, userId, newContent) {
    try {
      const comment = this.comments.get(commentId);
      if (!comment) {
        throw new Error('Comment not found');
      }

      if (comment.userId !== userId) {
        throw new Error('Not authorized to edit this comment');
      }

      if (newContent.length > 5000) {
        throw new Error('Comment content too long');
      }

      // 检查编辑时间限制（例如：只能在发布后30分钟内编辑）
      const timeSinceCreation = Date.now() - new Date(comment.createdAt).getTime();
      const editTimeLimit = 30 * 60 * 1000; // 30分钟

      if (timeSinceCreation > editTimeLimit) {
        throw new Error('Edit time limit exceeded');
      }

      // 更新评论
      comment.content = newContent;
      comment.updatedAt = new Date().toISOString();
      comment.editedAt = new Date().toISOString();

      return {
        success: true,
        comment,
        message: 'Comment updated successfully'
      };

    } catch (error) {
      logger.error('Edit comment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 删除评论
  async deleteComment(commentId, userId, isAdmin = false) {
    try {
      const comment = this.comments.get(commentId);
      if (!comment) {
        throw new Error('Comment not found');
      }

      if (!isAdmin && comment.userId !== userId) {
        throw new Error('Not authorized to delete this comment');
      }

      // 软删除：标记为已删除而不是真正删除
      comment.status = 'deleted';
      comment.content = '[This comment has been deleted]';
      comment.updatedAt = new Date().toISOString();

      // 如果有回复，也需要处理
      const replies = Array.from(this.comments.values())
        .filter(c => c.parentId === commentId);

      if (replies.length > 0) {
        // 保留评论结构，但标记为已删除
        comment.content = '[This comment has been deleted, but replies are preserved]';
      }

      return {
        success: true,
        message: 'Comment deleted successfully'
      };

    } catch (error) {
      logger.error('Delete comment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取评论统计
  async getCommentStats(targetType, targetId) {
    try {
      const comments = Array.from(this.comments.values())
        .filter(comment => 
          comment.targetType === targetType && 
          comment.targetId === targetId &&
          comment.status === 'active'
        );

      const stats = {
        totalComments: comments.length,
        topLevelComments: comments.filter(c => !c.parentId).length,
        replies: comments.filter(c => c.parentId).length,
        totalLikes: comments.reduce((sum, c) => sum + c.likeCount, 0),
        totalDislikes: comments.reduce((sum, c) => sum + c.dislikeCount, 0),
        averageEngagement: comments.length > 0 ? 
          comments.reduce((sum, c) => sum + c.likeCount + c.replyCount, 0) / comments.length : 0,
        mostLikedComment: comments.reduce((max, c) => 
          c.likeCount > (max?.likeCount || 0) ? c : max, null),
        mostRepliedComment: comments.reduce((max, c) => 
          c.replyCount > (max?.replyCount || 0) ? c : max, null)
      };

      return {
        success: true,
        stats
      };

    } catch (error) {
      logger.error('Get comment stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取相对时间
  getRelativeTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}mo ago`;
    return `${Math.floor(diffInSeconds / 31536000)}y ago`;
  }

  // 获取交互类型
  getInteractionTypes() {
    return this.interactionTypes;
  }

  // 获取举报类型
  getReportTypes() {
    return this.reportTypes;
  }

  // 获取排序选项
  getSortOptions() {
    return this.sortOptions;
  }

  // 获取用户的评论历史
  async getUserComments(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sort = 'newest'
      } = options;

      const userComments = Array.from(this.comments.values())
        .filter(comment => comment.userId === userId && comment.status === 'active');

      // 排序
      const sortConfig = this.sortOptions[sort] || this.sortOptions.newest;
      userComments.sort((a, b) => {
        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];

        if (sortConfig.order === 'DESC') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // 分页
      const startIndex = (page - 1) * limit;
      const paginatedComments = userComments.slice(startIndex, startIndex + limit);

      return {
        success: true,
        data: {
          comments: paginatedComments.map(comment => this.enrichComment(comment, userId)),
          pagination: {
            page,
            limit,
            total: userComments.length,
            totalPages: Math.ceil(userComments.length / limit)
          }
        }
      };

    } catch (error) {
      logger.error('Get user comments error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 搜索评论
  async searchComments(query, options = {}) {
    try {
      const {
        targetType = null,
        targetId = null,
        userId = null,
        page = 1,
        limit = 20
      } = options;

      let comments = Array.from(this.comments.values())
        .filter(comment => comment.status === 'active');

      // 应用过滤器
      if (targetType) {
        comments = comments.filter(comment => comment.targetType === targetType);
      }
      if (targetId) {
        comments = comments.filter(comment => comment.targetId === targetId);
      }
      if (userId) {
        comments = comments.filter(comment => comment.userId === userId);
      }

      // 搜索内容
      if (query) {
        const searchTerm = query.toLowerCase();
        comments = comments.filter(comment =>
          comment.content.toLowerCase().includes(searchTerm)
        );
      }

      // 排序（按相关性和时间）
      comments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // 分页
      const startIndex = (page - 1) * limit;
      const paginatedComments = comments.slice(startIndex, startIndex + limit);

      return {
        success: true,
        data: {
          query,
          comments: paginatedComments,
          pagination: {
            page,
            limit,
            total: comments.length,
            totalPages: Math.ceil(comments.length / limit)
          }
        }
      };

    } catch (error) {
      logger.error('Search comments error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 创建单例实例
const commentInteractionService = new CommentInteractionService();

module.exports = commentInteractionService;
