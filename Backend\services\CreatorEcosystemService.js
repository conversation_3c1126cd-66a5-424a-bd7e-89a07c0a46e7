/**
 * Newzora 全球创作者生态系统服务
 * 提供创作者招募、认证、激励机制等功能
 */

const { logger } = require('../config/logger');

class CreatorEcosystemService {
  constructor() {
    // 创作者等级系统
    this.creatorLevels = {
      'newcomer': {
        name: 'Newcomer',
        minFollowers: 0,
        minArticles: 0,
        minEngagement: 0,
        benefits: ['Basic analytics', 'Community access'],
        badge: '🌱'
      },
      'rising': {
        name: 'Rising Creator',
        minFollowers: 100,
        minArticles: 10,
        minEngagement: 0.05,
        benefits: ['Advanced analytics', 'Priority support', 'Featured content'],
        badge: '⭐'
      },
      'established': {
        name: 'Established Creator',
        minFollowers: 1000,
        minArticles: 50,
        minEngagement: 0.1,
        benefits: ['Revenue sharing', 'Custom branding', 'Direct monetization'],
        badge: '💎'
      },
      'expert': {
        name: 'Expert Creator',
        minFollowers: 10000,
        minArticles: 100,
        minEngagement: 0.15,
        benefits: ['Premium revenue share', 'Exclusive events', 'Mentorship program'],
        badge: '👑'
      },
      'ambassador': {
        name: 'Brand Ambassador',
        minFollowers: 50000,
        minArticles: 200,
        minEngagement: 0.2,
        benefits: ['Maximum revenue share', 'Platform partnership', 'Global recognition'],
        badge: '🏆'
      }
    };

    // 创作者认证类型
    this.verificationTypes = {
      'identity': {
        name: 'Identity Verification',
        requirements: ['Government ID', 'Phone verification', 'Email verification'],
        benefits: ['Verified badge', 'Increased trust', 'Priority in search']
      },
      'expertise': {
        name: 'Expertise Verification',
        requirements: ['Portfolio review', 'Peer endorsements', 'Content quality assessment'],
        benefits: ['Expert badge', 'Featured in categories', 'Higher revenue share']
      },
      'organization': {
        name: 'Organization Verification',
        requirements: ['Business registration', 'Official website', 'Contact verification'],
        benefits: ['Organization badge', 'Team management', 'Enterprise features']
      }
    };

    // 激励机制配置
    this.incentivePrograms = {
      'content_bonus': {
        name: 'Content Creation Bonus',
        description: 'Earn rewards for creating high-quality content',
        criteria: {
          minViews: 1000,
          minEngagement: 0.05,
          qualityScore: 0.8
        },
        rewards: {
          newcomer: 10,
          rising: 25,
          established: 50,
          expert: 100,
          ambassador: 200
        }
      },
      'engagement_reward': {
        name: 'Community Engagement Reward',
        description: 'Get rewarded for active community participation',
        criteria: {
          minComments: 50,
          minLikes: 100,
          minShares: 20
        },
        rewards: {
          newcomer: 5,
          rising: 15,
          established: 30,
          expert: 60,
          ambassador: 120
        }
      },
      'referral_bonus': {
        name: 'Creator Referral Bonus',
        description: 'Earn bonuses for bringing new creators to the platform',
        criteria: {
          referredCreators: 1,
          referredCreatorActivity: 30 // days
        },
        rewards: {
          newcomer: 20,
          rising: 50,
          established: 100,
          expert: 200,
          ambassador: 400
        }
      }
    };

    // 全球化支持
    this.globalSupport = {
      languages: [
        'en', 'zh', 'es', 'fr', 'de', 'ja', 'ko', 'ru', 'ar', 'hi',
        'pt', 'it', 'nl', 'sv', 'da', 'no', 'fi', 'pl', 'tr', 'th'
      ],
      timezones: [
        'UTC', 'America/New_York', 'America/Los_Angeles', 'Europe/London',
        'Europe/Paris', 'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Seoul',
        'Australia/Sydney', 'America/Sao_Paulo'
      ],
      currencies: [
        'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'AUD', 'CAD', 'BRL', 'INR'
      ]
    };
  }

  // 评估创作者等级
  async evaluateCreatorLevel(userId, stats) {
    try {
      const {
        followers = 0,
        articles = 0,
        totalViews = 0,
        totalLikes = 0,
        totalComments = 0,
        totalShares = 0
      } = stats;

      // 计算参与度
      const totalEngagements = totalLikes + totalComments + totalShares;
      const engagementRate = totalViews > 0 ? totalEngagements / totalViews : 0;

      // 找到符合条件的最高等级
      let currentLevel = 'newcomer';
      
      for (const [level, criteria] of Object.entries(this.creatorLevels)) {
        if (followers >= criteria.minFollowers &&
            articles >= criteria.minArticles &&
            engagementRate >= criteria.minEngagement) {
          currentLevel = level;
        }
      }

      const levelInfo = this.creatorLevels[currentLevel];
      
      // 计算下一等级进度
      const nextLevelKey = this.getNextLevel(currentLevel);
      const nextLevel = nextLevelKey ? this.creatorLevels[nextLevelKey] : null;
      
      let progress = null;
      if (nextLevel) {
        const followerProgress = Math.min(followers / nextLevel.minFollowers, 1);
        const articleProgress = Math.min(articles / nextLevel.minArticles, 1);
        const engagementProgress = Math.min(engagementRate / nextLevel.minEngagement, 1);
        
        progress = {
          overall: (followerProgress + articleProgress + engagementProgress) / 3,
          followers: followerProgress,
          articles: articleProgress,
          engagement: engagementProgress,
          nextLevel: nextLevelKey
        };
      }

      return {
        currentLevel,
        levelInfo,
        progress,
        stats: {
          followers,
          articles,
          engagementRate: Math.round(engagementRate * 10000) / 100 // 百分比
        }
      };

    } catch (error) {
      logger.error('Creator level evaluation error:', error);
      return {
        currentLevel: 'newcomer',
        levelInfo: this.creatorLevels.newcomer,
        progress: null,
        stats: {}
      };
    }
  }

  // 获取下一等级
  getNextLevel(currentLevel) {
    const levels = Object.keys(this.creatorLevels);
    const currentIndex = levels.indexOf(currentLevel);
    return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
  }

  // 申请创作者认证
  async applyForVerification(userId, verificationType, documents) {
    try {
      const verificationInfo = this.verificationTypes[verificationType];
      
      if (!verificationInfo) {
        throw new Error(`Invalid verification type: ${verificationType}`);
      }

      // 验证所需文档
      const missingRequirements = verificationInfo.requirements.filter(
        req => !documents[req.toLowerCase().replace(/\s+/g, '_')]
      );

      if (missingRequirements.length > 0) {
        return {
          success: false,
          message: 'Missing required documents',
          missingRequirements
        };
      }

      // 创建认证申请记录
      const application = {
        id: `verification_${Date.now()}`,
        userId,
        type: verificationType,
        status: 'pending',
        documents,
        submittedAt: new Date().toISOString(),
        reviewedAt: null,
        reviewedBy: null,
        notes: ''
      };

      // 这里应该保存到数据库
      logger.info(`Verification application submitted: ${application.id}`);

      return {
        success: true,
        message: 'Verification application submitted successfully',
        application
      };

    } catch (error) {
      logger.error('Verification application error:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // 计算激励奖励
  async calculateIncentiveRewards(userId, creatorLevel, activity) {
    try {
      const rewards = [];
      
      for (const [programId, program] of Object.entries(this.incentivePrograms)) {
        const eligible = this.checkIncentiveEligibility(activity, program.criteria);
        
        if (eligible) {
          const rewardAmount = program.rewards[creatorLevel] || 0;
          
          rewards.push({
            programId,
            programName: program.name,
            amount: rewardAmount,
            currency: 'USD', // 可以根据用户地区调整
            eligibleActivity: eligible.details,
            earnedAt: new Date().toISOString()
          });
        }
      }

      return {
        totalRewards: rewards.reduce((sum, reward) => sum + reward.amount, 0),
        rewards,
        period: 'monthly' // 可以配置
      };

    } catch (error) {
      logger.error('Incentive calculation error:', error);
      return {
        totalRewards: 0,
        rewards: [],
        period: 'monthly'
      };
    }
  }

  // 检查激励资格
  checkIncentiveEligibility(activity, criteria) {
    const eligible = {
      qualified: true,
      details: {}
    };

    for (const [criterion, threshold] of Object.entries(criteria)) {
      const actualValue = activity[criterion] || 0;
      const meets = actualValue >= threshold;
      
      eligible.details[criterion] = {
        required: threshold,
        actual: actualValue,
        meets
      };
      
      if (!meets) {
        eligible.qualified = false;
      }
    }

    return eligible.qualified ? eligible : null;
  }

  // 获取创作者推荐
  async getCreatorRecommendations(userId, preferences = {}) {
    try {
      const {
        categories = [],
        languages = [],
        minFollowers = 0,
        maxResults = 20
      } = preferences;

      // 模拟推荐算法
      const recommendations = [];
      
      // 基于分类的推荐
      if (categories.length > 0) {
        recommendations.push(...this.generateCategoryBasedRecommendations(categories, maxResults / 2));
      }
      
      // 基于语言的推荐
      if (languages.length > 0) {
        recommendations.push(...this.generateLanguageBasedRecommendations(languages, maxResults / 2));
      }
      
      // 如果没有特定偏好，推荐热门创作者
      if (recommendations.length === 0) {
        recommendations.push(...this.generateTrendingCreators(maxResults));
      }

      return {
        recommendations: recommendations.slice(0, maxResults),
        total: recommendations.length,
        algorithm: 'hybrid_recommendation'
      };

    } catch (error) {
      logger.error('Creator recommendations error:', error);
      return {
        recommendations: [],
        total: 0,
        algorithm: 'fallback'
      };
    }
  }

  // 生成基于分类的推荐
  generateCategoryBasedRecommendations(categories, limit) {
    // 模拟数据
    return categories.flatMap(category => [
      {
        id: `creator_${category}_1`,
        name: `${category} Expert`,
        category,
        followers: Math.floor(Math.random() * 10000) + 1000,
        level: 'established',
        verified: true,
        avatar: `/avatars/${category}_expert.jpg`,
        bio: `Leading ${category} content creator`,
        recentArticles: Math.floor(Math.random() * 20) + 5
      }
    ]).slice(0, limit);
  }

  // 生成基于语言的推荐
  generateLanguageBasedRecommendations(languages, limit) {
    // 模拟数据
    return languages.flatMap(language => [
      {
        id: `creator_${language}_1`,
        name: `${language.toUpperCase()} Creator`,
        language,
        followers: Math.floor(Math.random() * 5000) + 500,
        level: 'rising',
        verified: false,
        avatar: `/avatars/${language}_creator.jpg`,
        bio: `Content creator in ${language}`,
        recentArticles: Math.floor(Math.random() * 15) + 3
      }
    ]).slice(0, limit);
  }

  // 生成热门创作者推荐
  generateTrendingCreators(limit) {
    const trendingCreators = [];
    
    for (let i = 0; i < limit; i++) {
      trendingCreators.push({
        id: `trending_creator_${i + 1}`,
        name: `Trending Creator ${i + 1}`,
        category: ['technology', 'lifestyle', 'business', 'entertainment'][i % 4],
        followers: Math.floor(Math.random() * 50000) + 10000,
        level: ['established', 'expert', 'ambassador'][i % 3],
        verified: Math.random() > 0.3,
        avatar: `/avatars/trending_${i + 1}.jpg`,
        bio: 'Popular content creator with engaging content',
        recentArticles: Math.floor(Math.random() * 30) + 10,
        trending: true
      });
    }
    
    return trendingCreators;
  }

  // 获取创作者统计
  async getCreatorStats(userId) {
    try {
      // 这里应该从数据库获取真实统计数据
      // 模拟数据
      const stats = {
        profile: {
          joinedAt: '2024-01-01',
          totalArticles: Math.floor(Math.random() * 100) + 10,
          totalFollowers: Math.floor(Math.random() * 10000) + 100,
          totalViews: Math.floor(Math.random() * 100000) + 1000,
          totalLikes: Math.floor(Math.random() * 10000) + 100,
          totalComments: Math.floor(Math.random() * 5000) + 50,
          totalShares: Math.floor(Math.random() * 2000) + 20
        },
        performance: {
          avgViewsPerArticle: Math.floor(Math.random() * 1000) + 100,
          avgEngagementRate: Math.random() * 0.2 + 0.05,
          topCategories: ['technology', 'business', 'lifestyle'],
          bestPerformingArticle: {
            id: 'article_123',
            title: 'Best Article Ever',
            views: 5000,
            likes: 250,
            comments: 45
          }
        },
        earnings: {
          totalEarned: Math.floor(Math.random() * 1000) + 50,
          currentMonth: Math.floor(Math.random() * 200) + 20,
          currency: 'USD',
          payoutHistory: []
        }
      };

      return stats;

    } catch (error) {
      logger.error('Creator stats error:', error);
      return null;
    }
  }

  // 获取全球化支持信息
  getGlobalSupport() {
    return this.globalSupport;
  }

  // 获取创作者等级信息
  getCreatorLevels() {
    return this.creatorLevels;
  }

  // 获取认证类型信息
  getVerificationTypes() {
    return this.verificationTypes;
  }

  // 获取激励计划信息
  getIncentivePrograms() {
    return this.incentivePrograms;
  }
}

// 创建单例实例
const creatorEcosystemService = new CreatorEcosystemService();

module.exports = creatorEcosystemService;
