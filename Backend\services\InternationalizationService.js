/**
 * Newzora 国际化服务
 * 提供多语言支持和自动翻译功能
 */

const { logger } = require('../config/logger');

class InternationalizationService {
  constructor() {
    // 支持的语言列表
    this.supportedLanguages = {
      'en': { name: 'English', nativeName: 'English', flag: '🇺🇸', rtl: false },
      'zh': { name: 'Chinese', nativeName: '中文', flag: '🇨🇳', rtl: false },
      'zh-TW': { name: 'Traditional Chinese', nativeName: '繁體中文', flag: '🇹🇼', rtl: false },
      'es': { name: 'Spanish', nativeName: 'Español', flag: '🇪🇸', rtl: false },
      'fr': { name: 'French', nativeName: 'Français', flag: '🇫🇷', rtl: false },
      'de': { name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', rtl: false },
      'ja': { name: 'Japanese', nativeName: '日本語', flag: '🇯🇵', rtl: false },
      'ko': { name: 'Korean', nativeName: '한국어', flag: '🇰🇷', rtl: false },
      'ru': { name: 'Russian', nativeName: 'Русский', flag: '🇷🇺', rtl: false },
      'ar': { name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true },
      'hi': { name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳', rtl: false },
      'pt': { name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹', rtl: false },
      'it': { name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹', rtl: false },
      'nl': { name: 'Dutch', nativeName: 'Nederlands', flag: '🇳🇱', rtl: false },
      'sv': { name: 'Swedish', nativeName: 'Svenska', flag: '🇸🇪', rtl: false },
      'da': { name: 'Danish', nativeName: 'Dansk', flag: '🇩🇰', rtl: false },
      'no': { name: 'Norwegian', nativeName: 'Norsk', flag: '🇳🇴', rtl: false },
      'fi': { name: 'Finnish', nativeName: 'Suomi', flag: '🇫🇮', rtl: false },
      'pl': { name: 'Polish', nativeName: 'Polski', flag: '🇵🇱', rtl: false },
      'tr': { name: 'Turkish', nativeName: 'Türkçe', flag: '🇹🇷', rtl: false },
      'th': { name: 'Thai', nativeName: 'ไทย', flag: '🇹🇭', rtl: false },
      'vi': { name: 'Vietnamese', nativeName: 'Tiếng Việt', flag: '🇻🇳', rtl: false },
      'id': { name: 'Indonesian', nativeName: 'Bahasa Indonesia', flag: '🇮🇩', rtl: false },
      'ms': { name: 'Malay', nativeName: 'Bahasa Melayu', flag: '🇲🇾', rtl: false },
      'tl': { name: 'Filipino', nativeName: 'Filipino', flag: '🇵🇭', rtl: false }
    };

    // 语言检测模式
    this.languagePatterns = {
      'zh': /[\u4e00-\u9fff]/,
      'ja': /[\u3040-\u309f\u30a0-\u30ff]/,
      'ko': /[\uac00-\ud7af]/,
      'ar': /[\u0600-\u06ff]/,
      'ru': /[\u0400-\u04ff]/,
      'th': /[\u0e00-\u0e7f]/,
      'hi': /[\u0900-\u097f]/
    };

    // 翻译缓存
    this.translationCache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时

    // 默认翻译
    this.defaultTranslations = {
      en: {
        'app.name': 'Newzora',
        'nav.home': 'Home',
        'nav.explore': 'Explore',
        'nav.create': 'Create',
        'nav.profile': 'Profile',
        'nav.settings': 'Settings',
        'nav.login': 'Login',
        'nav.logout': 'Logout',
        'nav.signup': 'Sign Up',
        'common.loading': 'Loading...',
        'common.error': 'Error',
        'common.success': 'Success',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.save': 'Save',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.share': 'Share',
        'common.like': 'Like',
        'common.comment': 'Comment',
        'common.follow': 'Follow',
        'common.unfollow': 'Unfollow',
        'article.readMore': 'Read More',
        'article.readTime': 'min read',
        'article.publishedAt': 'Published',
        'article.author': 'Author',
        'article.tags': 'Tags',
        'search.placeholder': 'Search articles...',
        'search.results': 'Search Results',
        'search.noResults': 'No results found',
        'user.profile': 'Profile',
        'user.followers': 'Followers',
        'user.following': 'Following',
        'user.articles': 'Articles',
        'auth.login': 'Login',
        'auth.signup': 'Sign Up',
        'auth.email': 'Email',
        'auth.password': 'Password',
        'auth.confirmPassword': 'Confirm Password',
        'auth.forgotPassword': 'Forgot Password?',
        'auth.rememberMe': 'Remember Me',
        'auth.loginSuccess': 'Login successful',
        'auth.loginError': 'Login failed',
        'auth.signupSuccess': 'Account created successfully',
        'auth.signupError': 'Failed to create account',
        'settings.general': 'General',
        'settings.privacy': 'Privacy',
        'settings.notifications': 'Notifications',
        'settings.language': 'Language',
        'settings.theme': 'Theme',
        'settings.account': 'Account',
        'notifications.new': 'New notification',
        'notifications.markRead': 'Mark as read',
        'notifications.markAllRead': 'Mark all as read',
        'editor.title': 'Title',
        'editor.content': 'Content',
        'editor.publish': 'Publish',
        'editor.draft': 'Save as Draft',
        'editor.preview': 'Preview'
      }
    };

    // 初始化其他语言的翻译（简化版本）
    this.initializeTranslations();
  }

  // 初始化翻译
  initializeTranslations() {
    // 中文翻译
    this.defaultTranslations.zh = {
      'app.name': 'Newzora',
      'nav.home': '首页',
      'nav.explore': '探索',
      'nav.create': '创作',
      'nav.profile': '个人资料',
      'nav.settings': '设置',
      'nav.login': '登录',
      'nav.logout': '退出',
      'nav.signup': '注册',
      'common.loading': '加载中...',
      'common.error': '错误',
      'common.success': '成功',
      'common.cancel': '取消',
      'common.confirm': '确认',
      'common.save': '保存',
      'common.delete': '删除',
      'common.edit': '编辑',
      'common.share': '分享',
      'common.like': '点赞',
      'common.comment': '评论',
      'common.follow': '关注',
      'common.unfollow': '取消关注',
      'article.readMore': '阅读更多',
      'article.readTime': '分钟阅读',
      'article.publishedAt': '发布于',
      'article.author': '作者',
      'article.tags': '标签',
      'search.placeholder': '搜索文章...',
      'search.results': '搜索结果',
      'search.noResults': '未找到结果',
      'user.profile': '个人资料',
      'user.followers': '粉丝',
      'user.following': '关注',
      'user.articles': '文章',
      'auth.login': '登录',
      'auth.signup': '注册',
      'auth.email': '邮箱',
      'auth.password': '密码',
      'auth.confirmPassword': '确认密码',
      'auth.forgotPassword': '忘记密码？',
      'auth.rememberMe': '记住我',
      'auth.loginSuccess': '登录成功',
      'auth.loginError': '登录失败',
      'auth.signupSuccess': '账户创建成功',
      'auth.signupError': '创建账户失败',
      'settings.general': '通用',
      'settings.privacy': '隐私',
      'settings.notifications': '通知',
      'settings.language': '语言',
      'settings.theme': '主题',
      'settings.account': '账户',
      'notifications.new': '新通知',
      'notifications.markRead': '标记为已读',
      'notifications.markAllRead': '全部标记为已读',
      'editor.title': '标题',
      'editor.content': '内容',
      'editor.publish': '发布',
      'editor.draft': '保存草稿',
      'editor.preview': '预览'
    };

    // 西班牙语翻译
    this.defaultTranslations.es = {
      'app.name': 'Newzora',
      'nav.home': 'Inicio',
      'nav.explore': 'Explorar',
      'nav.create': 'Crear',
      'nav.profile': 'Perfil',
      'nav.settings': 'Configuración',
      'nav.login': 'Iniciar Sesión',
      'nav.logout': 'Cerrar Sesión',
      'nav.signup': 'Registrarse',
      'common.loading': 'Cargando...',
      'common.error': 'Error',
      'common.success': 'Éxito',
      'common.cancel': 'Cancelar',
      'common.confirm': 'Confirmar',
      'common.save': 'Guardar',
      'common.delete': 'Eliminar',
      'common.edit': 'Editar',
      'common.share': 'Compartir',
      'common.like': 'Me Gusta',
      'common.comment': 'Comentar',
      'common.follow': 'Seguir',
      'common.unfollow': 'Dejar de Seguir',
      'article.readMore': 'Leer Más',
      'article.readTime': 'min de lectura',
      'article.publishedAt': 'Publicado',
      'article.author': 'Autor',
      'article.tags': 'Etiquetas',
      'search.placeholder': 'Buscar artículos...',
      'search.results': 'Resultados de Búsqueda',
      'search.noResults': 'No se encontraron resultados',
      'user.profile': 'Perfil',
      'user.followers': 'Seguidores',
      'user.following': 'Siguiendo',
      'user.articles': 'Artículos',
      'auth.login': 'Iniciar Sesión',
      'auth.signup': 'Registrarse',
      'auth.email': 'Correo Electrónico',
      'auth.password': 'Contraseña',
      'auth.confirmPassword': 'Confirmar Contraseña',
      'auth.forgotPassword': '¿Olvidaste tu contraseña?',
      'auth.rememberMe': 'Recordarme',
      'auth.loginSuccess': 'Inicio de sesión exitoso',
      'auth.loginError': 'Error al iniciar sesión',
      'auth.signupSuccess': 'Cuenta creada exitosamente',
      'auth.signupError': 'Error al crear cuenta',
      'settings.general': 'General',
      'settings.privacy': 'Privacidad',
      'settings.notifications': 'Notificaciones',
      'settings.language': 'Idioma',
      'settings.theme': 'Tema',
      'settings.account': 'Cuenta',
      'notifications.new': 'Nueva notificación',
      'notifications.markRead': 'Marcar como leído',
      'notifications.markAllRead': 'Marcar todo como leído',
      'editor.title': 'Título',
      'editor.content': 'Contenido',
      'editor.publish': 'Publicar',
      'editor.draft': 'Guardar como Borrador',
      'editor.preview': 'Vista Previa'
    };

    logger.info('Internationalization translations initialized');
  }

  // 检测用户语言
  detectUserLanguage(acceptLanguageHeader, userAgent = '') {
    try {
      // 解析Accept-Language头
      const languages = this.parseAcceptLanguage(acceptLanguageHeader);
      
      // 查找支持的语言
      for (const lang of languages) {
        const langCode = this.normalizeLanguageCode(lang.code);
        if (this.supportedLanguages[langCode]) {
          return langCode;
        }
        
        // 尝试匹配主语言（如 zh-CN -> zh）
        const primaryLang = langCode.split('-')[0];
        if (this.supportedLanguages[primaryLang]) {
          return primaryLang;
        }
      }
      
      // 基于用户代理的额外检测
      const uaLanguage = this.detectLanguageFromUserAgent(userAgent);
      if (uaLanguage && this.supportedLanguages[uaLanguage]) {
        return uaLanguage;
      }
      
      // 默认返回英语
      return 'en';
      
    } catch (error) {
      logger.error('Language detection error:', error);
      return 'en';
    }
  }

  // 解析Accept-Language头
  parseAcceptLanguage(acceptLanguage) {
    if (!acceptLanguage) return [];
    
    return acceptLanguage
      .split(',')
      .map(lang => {
        const parts = lang.trim().split(';');
        const code = parts[0].trim();
        const qValue = parts[1] ? parseFloat(parts[1].split('=')[1]) : 1.0;
        return { code, quality: qValue };
      })
      .sort((a, b) => b.quality - a.quality);
  }

  // 标准化语言代码
  normalizeLanguageCode(code) {
    const normalized = code.toLowerCase().replace('_', '-');
    
    // 特殊映射
    const mappings = {
      'zh-cn': 'zh',
      'zh-hans': 'zh',
      'zh-tw': 'zh-TW',
      'zh-hant': 'zh-TW'
    };
    
    return mappings[normalized] || normalized;
  }

  // 从用户代理检测语言
  detectLanguageFromUserAgent(userAgent) {
    const patterns = {
      'zh': /zh-CN|zh_CN|Chinese/i,
      'ja': /ja-JP|ja_JP|Japanese/i,
      'ko': /ko-KR|ko_KR|Korean/i,
      'ru': /ru-RU|ru_RU|Russian/i,
      'ar': /ar-SA|ar_SA|Arabic/i
    };
    
    for (const [lang, pattern] of Object.entries(patterns)) {
      if (pattern.test(userAgent)) {
        return lang;
      }
    }
    
    return null;
  }

  // 获取翻译
  getTranslation(key, language = 'en', params = {}) {
    try {
      const translations = this.defaultTranslations[language] || this.defaultTranslations.en;
      let translation = translations[key] || this.defaultTranslations.en[key] || key;
      
      // 参数替换
      Object.keys(params).forEach(param => {
        translation = translation.replace(`{${param}}`, params[param]);
      });
      
      return translation;
    } catch (error) {
      logger.error('Translation error:', error);
      return key;
    }
  }

  // 获取所有翻译
  getAllTranslations(language = 'en') {
    return this.defaultTranslations[language] || this.defaultTranslations.en;
  }

  // 获取支持的语言列表
  getSupportedLanguages() {
    return Object.entries(this.supportedLanguages).map(([code, info]) => ({
      code,
      ...info
    }));
  }

  // 检测文本语言
  async detectTextLanguage(text) {
    try {
      // 简化的语言检测
      for (const [lang, pattern] of Object.entries(this.languagePatterns)) {
        if (pattern.test(text)) {
          return lang;
        }
      }
      
      // 基于字符频率的简单检测
      const charCounts = {};
      for (const char of text) {
        charCounts[char] = (charCounts[char] || 0) + 1;
      }
      
      // 检查是否主要是拉丁字符
      const latinChars = text.match(/[a-zA-Z]/g) || [];
      const totalChars = text.replace(/\s/g, '').length;
      
      if (latinChars.length / totalChars > 0.8) {
        return 'en'; // 假设是英语
      }
      
      return 'unknown';
    } catch (error) {
      logger.error('Text language detection error:', error);
      return 'unknown';
    }
  }

  // 翻译文本（模拟API调用）
  async translateText(text, targetLanguage, sourceLanguage = 'auto') {
    try {
      const cacheKey = `${sourceLanguage}-${targetLanguage}-${this.hashText(text)}`;
      
      // 检查缓存
      const cached = this.translationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.translation;
      }
      
      // 模拟翻译API调用
      // 实际应用中应该调用Google Translate API、Azure Translator等
      const translation = await this.mockTranslateAPI(text, targetLanguage, sourceLanguage);
      
      // 缓存结果
      this.translationCache.set(cacheKey, {
        translation,
        timestamp: Date.now()
      });
      
      return translation;
    } catch (error) {
      logger.error('Text translation error:', error);
      return text; // 返回原文
    }
  }

  // 模拟翻译API
  async mockTranslateAPI(text, targetLanguage, sourceLanguage) {
    // 简化的模拟翻译
    const translations = {
      'zh': {
        'Hello': '你好',
        'Welcome': '欢迎',
        'Thank you': '谢谢',
        'Good morning': '早上好',
        'Good evening': '晚上好'
      },
      'es': {
        'Hello': 'Hola',
        'Welcome': 'Bienvenido',
        'Thank you': 'Gracias',
        'Good morning': 'Buenos días',
        'Good evening': 'Buenas tardes'
      },
      'fr': {
        'Hello': 'Bonjour',
        'Welcome': 'Bienvenue',
        'Thank you': 'Merci',
        'Good morning': 'Bonjour',
        'Good evening': 'Bonsoir'
      }
    };
    
    const langTranslations = translations[targetLanguage];
    if (langTranslations && langTranslations[text]) {
      return langTranslations[text];
    }
    
    // 如果没有找到翻译，返回原文
    return text;
  }

  // 文本哈希
  hashText(text) {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  // 格式化日期时间
  formatDateTime(date, language = 'en', options = {}) {
    try {
      const dateObj = new Date(date);
      const locale = this.getLocaleFromLanguage(language);
      
      const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      
      return dateObj.toLocaleDateString(locale, { ...defaultOptions, ...options });
    } catch (error) {
      logger.error('Date formatting error:', error);
      return date.toString();
    }
  }

  // 获取语言对应的区域设置
  getLocaleFromLanguage(language) {
    const localeMap = {
      'en': 'en-US',
      'zh': 'zh-CN',
      'zh-TW': 'zh-TW',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN',
      'pt': 'pt-PT',
      'it': 'it-IT',
      'nl': 'nl-NL',
      'sv': 'sv-SE',
      'da': 'da-DK',
      'no': 'no-NO',
      'fi': 'fi-FI',
      'pl': 'pl-PL',
      'tr': 'tr-TR',
      'th': 'th-TH',
      'vi': 'vi-VN',
      'id': 'id-ID',
      'ms': 'ms-MY',
      'tl': 'tl-PH'
    };
    
    return localeMap[language] || 'en-US';
  }

  // 清理翻译缓存
  clearTranslationCache() {
    this.translationCache.clear();
    logger.info('Translation cache cleared');
  }

  // 获取RTL语言列表
  getRTLLanguages() {
    return Object.entries(this.supportedLanguages)
      .filter(([code, info]) => info.rtl)
      .map(([code]) => code);
  }

  // 检查是否为RTL语言
  isRTLLanguage(language) {
    return this.supportedLanguages[language]?.rtl || false;
  }
}

// 创建单例实例
const internationalizationService = new InternationalizationService();

module.exports = internationalizationService;
