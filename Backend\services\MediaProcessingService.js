/**
 * Newzora 媒体处理服务
 * 提供音频和视频处理、转码、压缩等功能
 */

const path = require('path');
const fs = require('fs').promises;
const { logger } = require('../config/logger');

// 模拟FFmpeg功能（如果没有安装FFmpeg）
let ffmpeg;
try {
  ffmpeg = require('fluent-ffmpeg');
} catch (error) {
  logger.warn('FFmpeg not available, using mock implementation');
  ffmpeg = {
    ffprobe: (filePath, callback) => {
      // 模拟媒体信息
      callback(null, {
        format: {
          duration: 60,
          size: 1024000,
          bit_rate: 1000000
        },
        streams: [
          { codec_type: 'video', width: 1920, height: 1080 },
          { codec_type: 'audio', sample_rate: 44100 }
        ]
      });
    }
  };
}

class MediaProcessingService {
  constructor() {
    // 支持的媒体格式
    this.supportedFormats = {
      video: {
        input: ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', '3gp'],
        output: ['mp4', 'webm', 'mov']
      },
      audio: {
        input: ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a', 'wma'],
        output: ['mp3', 'aac', 'ogg', 'wav']
      },
      image: {
        input: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff'],
        output: ['jpg', 'png', 'webp']
      }
    };

    // 质量预设
    this.qualityPresets = {
      video: {
        '4k': { width: 3840, height: 2160, bitrate: '8000k' },
        '1080p': { width: 1920, height: 1080, bitrate: '4000k' },
        '720p': { width: 1280, height: 720, bitrate: '2000k' },
        '480p': { width: 854, height: 480, bitrate: '1000k' },
        '360p': { width: 640, height: 360, bitrate: '500k' }
      },
      audio: {
        'high': { bitrate: '320k', sampleRate: 48000 },
        'medium': { bitrate: '192k', sampleRate: 44100 },
        'low': { bitrate: '128k', sampleRate: 44100 }
      }
    };

    // 上传限制
    this.limits = {
      video: {
        maxSize: 500 * 1024 * 1024, // 500MB
        maxDuration: 3600 // 1小时
      },
      audio: {
        maxSize: 100 * 1024 * 1024, // 100MB
        maxDuration: 7200 // 2小时
      }
    };
  }

  // 获取媒体信息
  async getMediaInfo(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          logger.error('Failed to get media info:', err);
          reject(err);
        } else {
          const info = {
            format: metadata.format,
            streams: metadata.streams,
            duration: metadata.format.duration,
            size: metadata.format.size,
            bitrate: metadata.format.bit_rate,
            videoStreams: metadata.streams.filter(s => s.codec_type === 'video'),
            audioStreams: metadata.streams.filter(s => s.codec_type === 'audio')
          };
          resolve(info);
        }
      });
    });
  }

  // 验证媒体文件
  async validateMediaFile(filePath, type) {
    try {
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;
      
      // 检查文件大小
      if (fileSize > this.limits[type].maxSize) {
        throw new Error(`File size exceeds limit: ${fileSize} > ${this.limits[type].maxSize}`);
      }

      // 获取媒体信息
      const mediaInfo = await this.getMediaInfo(filePath);
      
      // 检查时长
      if (mediaInfo.duration > this.limits[type].maxDuration) {
        throw new Error(`Duration exceeds limit: ${mediaInfo.duration} > ${this.limits[type].maxDuration}`);
      }

      // 检查格式
      const extension = path.extname(filePath).toLowerCase().slice(1);
      if (!this.supportedFormats[type].input.includes(extension)) {
        throw new Error(`Unsupported format: ${extension}`);
      }

      return {
        valid: true,
        info: mediaInfo,
        size: fileSize,
        duration: mediaInfo.duration
      };

    } catch (error) {
      logger.error('Media validation failed:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // 转换视频
  async convertVideo(inputPath, outputPath, options = {}) {
    const {
      quality = '720p',
      format = 'mp4',
      codec = 'libx264',
      audioCodec = 'aac',
      removeAudio = false,
      startTime = null,
      duration = null,
      watermark = null
    } = options;

    return new Promise((resolve, reject) => {
      try {
        const preset = this.qualityPresets.video[quality];
        let command = ffmpeg(inputPath);

        // 设置视频编码器和质量
        command = command
          .videoCodec(codec)
          .videoBitrate(preset.bitrate)
          .size(`${preset.width}x${preset.height}`)
          .aspect('16:9')
          .autopad();

        // 音频处理
        if (removeAudio) {
          command = command.noAudio();
        } else {
          command = command.audioCodec(audioCodec).audioBitrate('128k');
        }

        // 时间裁剪
        if (startTime !== null) {
          command = command.seekInput(startTime);
        }
        if (duration !== null) {
          command = command.duration(duration);
        }

        // 添加水印
        if (watermark) {
          command = command.complexFilter([
            `[0:v][1:v] overlay=${watermark.x || 10}:${watermark.y || 10} [v]`
          ], 'v');
        }

        // 输出设置
        command = command
          .format(format)
          .output(outputPath)
          .on('start', (commandLine) => {
            logger.info('FFmpeg command:', commandLine);
          })
          .on('progress', (progress) => {
            logger.info(`Video conversion progress: ${progress.percent}%`);
          })
          .on('end', () => {
            logger.info('Video conversion completed');
            resolve({ success: true, outputPath });
          })
          .on('error', (err) => {
            logger.error('Video conversion failed:', err);
            reject(err);
          });

        command.run();

      } catch (error) {
        logger.error('Video conversion setup failed:', error);
        reject(error);
      }
    });
  }

  // 转换音频
  async convertAudio(inputPath, outputPath, options = {}) {
    const {
      quality = 'medium',
      format = 'mp3',
      codec = 'libmp3lame',
      startTime = null,
      duration = null,
      normalize = false
    } = options;

    return new Promise((resolve, reject) => {
      try {
        const preset = this.qualityPresets.audio[quality];
        let command = ffmpeg(inputPath);

        // 设置音频编码器和质量
        command = command
          .audioCodec(codec)
          .audioBitrate(preset.bitrate)
          .audioFrequency(preset.sampleRate)
          .audioChannels(2);

        // 时间裁剪
        if (startTime !== null) {
          command = command.seekInput(startTime);
        }
        if (duration !== null) {
          command = command.duration(duration);
        }

        // 音频标准化
        if (normalize) {
          command = command.audioFilters('loudnorm');
        }

        // 输出设置
        command = command
          .format(format)
          .output(outputPath)
          .on('start', (commandLine) => {
            logger.info('FFmpeg command:', commandLine);
          })
          .on('progress', (progress) => {
            logger.info(`Audio conversion progress: ${progress.percent}%`);
          })
          .on('end', () => {
            logger.info('Audio conversion completed');
            resolve({ success: true, outputPath });
          })
          .on('error', (err) => {
            logger.error('Audio conversion failed:', err);
            reject(err);
          });

        command.run();

      } catch (error) {
        logger.error('Audio conversion setup failed:', error);
        reject(error);
      }
    });
  }

  // 生成视频缩略图
  async generateVideoThumbnail(inputPath, outputPath, options = {}) {
    const {
      timeOffset = '00:00:01',
      width = 320,
      height = 240,
      count = 1
    } = options;

    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .screenshots({
          timestamps: [timeOffset],
          filename: path.basename(outputPath),
          folder: path.dirname(outputPath),
          size: `${width}x${height}`
        })
        .on('end', () => {
          logger.info('Thumbnail generation completed');
          resolve({ success: true, outputPath });
        })
        .on('error', (err) => {
          logger.error('Thumbnail generation failed:', err);
          reject(err);
        });
    });
  }

  // 生成音频波形图
  async generateAudioWaveform(inputPath, outputPath, options = {}) {
    const {
      width = 800,
      height = 200,
      colors = 'blue'
    } = options;

    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .complexFilter([
          `[0:a]showwavespic=s=${width}x${height}:colors=${colors}[v]`
        ])
        .map('[v]')
        .output(outputPath)
        .on('end', () => {
          logger.info('Waveform generation completed');
          resolve({ success: true, outputPath });
        })
        .on('error', (err) => {
          logger.error('Waveform generation failed:', err);
          reject(err);
        })
        .run();
    });
  }

  // 合并音视频
  async mergeAudioVideo(videoPath, audioPath, outputPath, options = {}) {
    const {
      videoCodec = 'copy',
      audioCodec = 'aac',
      syncOffset = 0
    } = options;

    return new Promise((resolve, reject) => {
      let command = ffmpeg()
        .input(videoPath)
        .input(audioPath);

      if (syncOffset !== 0) {
        command = command.inputOptions(`-itsoffset ${syncOffset}`);
      }

      command
        .videoCodec(videoCodec)
        .audioCodec(audioCodec)
        .output(outputPath)
        .on('end', () => {
          logger.info('Audio-video merge completed');
          resolve({ success: true, outputPath });
        })
        .on('error', (err) => {
          logger.error('Audio-video merge failed:', err);
          reject(err);
        })
        .run();
    });
  }

  // 提取音频
  async extractAudio(videoPath, outputPath, options = {}) {
    const {
      format = 'mp3',
      quality = 'medium'
    } = options;

    return new Promise((resolve, reject) => {
      const preset = this.qualityPresets.audio[quality];
      
      ffmpeg(videoPath)
        .noVideo()
        .audioCodec('libmp3lame')
        .audioBitrate(preset.bitrate)
        .format(format)
        .output(outputPath)
        .on('end', () => {
          logger.info('Audio extraction completed');
          resolve({ success: true, outputPath });
        })
        .on('error', (err) => {
          logger.error('Audio extraction failed:', err);
          reject(err);
        })
        .run();
    });
  }

  // 压缩媒体文件
  async compressMedia(inputPath, outputPath, type, compressionLevel = 'medium') {
    const options = {
      video: {
        low: { quality: '360p', bitrate: '500k' },
        medium: { quality: '480p', bitrate: '1000k' },
        high: { quality: '720p', bitrate: '2000k' }
      },
      audio: {
        low: { quality: 'low', bitrate: '96k' },
        medium: { quality: 'medium', bitrate: '128k' },
        high: { quality: 'high', bitrate: '192k' }
      }
    };

    const compressionOptions = options[type][compressionLevel];

    if (type === 'video') {
      return await this.convertVideo(inputPath, outputPath, compressionOptions);
    } else if (type === 'audio') {
      return await this.convertAudio(inputPath, outputPath, compressionOptions);
    } else {
      throw new Error(`Unsupported media type: ${type}`);
    }
  }

  // 获取支持的格式
  getSupportedFormats() {
    return this.supportedFormats;
  }

  // 获取质量预设
  getQualityPresets() {
    return this.qualityPresets;
  }

  // 获取上传限制
  getUploadLimits() {
    return this.limits;
  }
}

// 创建单例实例
const mediaProcessingService = new MediaProcessingService();

module.exports = mediaProcessingService;
