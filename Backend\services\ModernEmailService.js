const fs = require('fs').promises;
const path = require('path');
const handlebars = require('handlebars');
const mjml = require('mjml');
const { EMAIL_CONFIG, transporter, sgMail } = require('../config/email');
const { generateToken } = require('../middleware/auth');
const EmailLog = require('../models/EmailLog');

class ModernEmailService {
  constructor() {
    this.templateCache = new Map();
    this.rateLimitCache = new Map();
    this.initializeTemplates();
  }

  // Initialize email templates
  async initializeTemplates() {
    try {
      // Register Handlebars helpers
      this.registerHandlebarsHelpers();
      
      // Precompile common templates
      await this.precompileTemplates();
      
      console.log('✅ Email templates initialized');
    } catch (error) {
      console.error('❌ Failed to initialize email templates:', error.message);
    }
  }

  // Register custom Handlebars helpers
  registerHandlebarsHelpers() {
    handlebars.registerHelper('formatDate', (date) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    });

    handlebars.registerHelper('formatTime', (date) => {
      return new Date(date).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    });

    handlebars.registerHelper('truncate', (str, length) => {
      if (str && str.length > length) {
        return str.substring(0, length) + '...';
      }
      return str;
    });

    handlebars.registerHelper('eq', (a, b) => a === b);
    handlebars.registerHelper('ne', (a, b) => a !== b);
    handlebars.registerHelper('gt', (a, b) => a > b);
    handlebars.registerHelper('lt', (a, b) => a < b);
  }

  // Precompile email templates
  async precompileTemplates() {
    const templateDir = path.join(__dirname, '..', 'templates', 'email');
    
    try {
      const files = await fs.readdir(templateDir);
      const mjmlFiles = files.filter(file => file.endsWith('.mjml'));

      for (const file of mjmlFiles) {
        const templateName = path.basename(file, '.mjml');
        await this.compileTemplate(templateName);
      }
      
      console.log(`✅ Compiled ${mjmlFiles.length} email templates`);
    } catch (error) {
      console.warn('Template directory not found, templates will be compiled on demand');
    }
  }

  // Compile MJML template to HTML
  async compileTemplate(templateName) {
    try {
      const templatePath = path.join(__dirname, '..', 'templates', 'email', `${templateName}.mjml`);
      const mjmlContent = await fs.readFile(templatePath, 'utf8');
      
      // Compile MJML to HTML
      const { html, errors } = mjml(mjmlContent);
      
      if (errors && errors.length > 0) {
        console.warn(`MJML compilation warnings for ${templateName}:`, errors);
      }

      // Compile with Handlebars
      const template = handlebars.compile(html);
      
      // Cache the compiled template
      this.templateCache.set(templateName, template);
      
      return template;
    } catch (error) {
      console.error(`Failed to compile template ${templateName}:`, error.message);
      return null;
    }
  }

  // Get compiled template
  async getTemplate(templateName) {
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName);
    }

    // Try to compile the template if not cached
    return await this.compileTemplate(templateName);
  }

  // Check rate limits
  checkRateLimit(email, type = 'general') {
    const now = Date.now();
    const userKey = `${email}:${type}`;
    const hourKey = `${userKey}:hour:${Math.floor(now / (60 * 60 * 1000))}`;
    const dayKey = `${userKey}:day:${Math.floor(now / (24 * 60 * 60 * 1000))}`;

    const hourCount = this.rateLimitCache.get(hourKey) || 0;
    const dayCount = this.rateLimitCache.get(dayKey) || 0;

    if (hourCount >= EMAIL_CONFIG.RATE_LIMITS.PER_USER_PER_HOUR) {
      throw new Error('Hourly email limit exceeded');
    }

    if (dayCount >= EMAIL_CONFIG.RATE_LIMITS.PER_USER_PER_DAY) {
      throw new Error('Daily email limit exceeded');
    }

    // Update counters
    this.rateLimitCache.set(hourKey, hourCount + 1);
    this.rateLimitCache.set(dayKey, dayCount + 1);

    // Set expiration for cleanup
    setTimeout(() => {
      this.rateLimitCache.delete(hourKey);
    }, 60 * 60 * 1000); // 1 hour

    setTimeout(() => {
      this.rateLimitCache.delete(dayKey);
    }, 24 * 60 * 60 * 1000); // 24 hours

    return true;
  }

  // Send email using configured provider
  async sendEmail(options) {
    let emailLog = null;

    try {
      const {
        to,
        subject,
        template,
        data = {},
        from = EMAIL_CONFIG.DEFAULT_FROM,
        attachments = [],
        userId = null,
        priority = 'normal',
        scheduledAt = null,
        metadata = {}
      } = options;

      // Create email log entry
      emailLog = await EmailLog.create({
        userId: userId,
        recipient: to,
        sender: typeof from === 'string' ? from : `${from.name} <${from.email}>`,
        subject: subject,
        template: template,
        templateData: data,
        status: 'pending',
        provider: EMAIL_CONFIG.CURRENT_PROVIDER,
        priority: priority,
        scheduledAt: scheduledAt,
        metadata: metadata
      });

      // Check rate limits
      this.checkRateLimit(to, template);

      // Get and render template
      const templateFunction = await this.getTemplate(template);
      if (!templateFunction) {
        throw new Error(`Template ${template} not found`);
      }

      const html = templateFunction({
        ...data,
        baseUrl: EMAIL_CONFIG.TEMPLATES.BASE_URL,
        currentYear: new Date().getFullYear(),
        brandName: 'Newzora'
      });

      // Prepare email data
      const emailData = {
        to,
        from: typeof from === 'string' ? from : `${from.name} <${from.email}>`,
        subject,
        html,
        attachments
      };

      // Send email based on provider
      let result;
      switch (EMAIL_CONFIG.CURRENT_PROVIDER) {
        case EMAIL_CONFIG.PROVIDERS.SENDGRID:
          result = await this.sendWithSendGrid(emailData);
          break;
        default:
          result = await this.sendWithSMTP(emailData);
          break;
      }

      // Update email log with success
      await emailLog.markAsSent(result.messageId, result);

      console.log(`✅ Email sent successfully to ${to} using ${EMAIL_CONFIG.CURRENT_PROVIDER}`);
      return {
        ...result,
        logId: emailLog.id
      };

    } catch (error) {
      console.error('❌ Failed to send email:', error.message);

      // Update email log with failure
      if (emailLog) {
        await emailLog.markAsFailed(error.message);
      }

      throw error;
    }
  }

  // Send email via SendGrid
  async sendWithSendGrid(emailData) {
    try {
      const result = await sgMail.send(emailData);
      return {
        success: true,
        messageId: result[0].headers['x-message-id'],
        provider: 'sendgrid'
      };
    } catch (error) {
      throw new Error(`SendGrid error: ${error.message}`);
    }
  }

  // Send email via SMTP
  async sendWithSMTP(emailData) {
    try {
      if (!transporter) {
        throw new Error('SMTP transporter not initialized');
      }

      const result = await transporter.sendMail(emailData);
      
      // For development with Ethereal, provide preview URL
      let previewUrl = null;
      if (result.messageId && result.messageId.includes('ethereal.email')) {
        const nodemailer = require('nodemailer');
        previewUrl = nodemailer.getTestMessageUrl(result);
        console.log('📧 Preview email at:', previewUrl);
      }

      return {
        success: true,
        messageId: result.messageId,
        provider: 'smtp',
        previewUrl
      };
    } catch (error) {
      throw new Error(`SMTP error: ${error.message}`);
    }
  }

  // Send verification email
  async sendVerificationEmail(user) {
    const token = generateToken(
      { userId: user.id, type: 'email_verification' },
      EMAIL_CONFIG.EMAIL_TYPES.VERIFICATION.expiresIn
    );

    const verificationUrl = `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/verify-email?token=${token}`;

    return await this.sendEmail({
      to: user.email,
      subject: EMAIL_CONFIG.EMAIL_TYPES.VERIFICATION.subject,
      template: EMAIL_CONFIG.EMAIL_TYPES.VERIFICATION.template,
      data: {
        user: {
          firstName: user.firstName || user.username,
          username: user.username,
          email: user.email
        },
        verificationUrl,
        expiresIn: '24 hours'
      }
    });
  }

  // Send password reset email
  async sendPasswordResetEmail(user) {
    const token = generateToken(
      { userId: user.id, type: 'password_reset' },
      EMAIL_CONFIG.EMAIL_TYPES.PASSWORD_RESET.expiresIn
    );

    const resetUrl = `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/reset-password?token=${token}`;

    return await this.sendEmail({
      to: user.email,
      subject: EMAIL_CONFIG.EMAIL_TYPES.PASSWORD_RESET.subject,
      template: EMAIL_CONFIG.EMAIL_TYPES.PASSWORD_RESET.template,
      data: {
        user: {
          firstName: user.firstName || user.username,
          username: user.username,
          email: user.email
        },
        resetUrl,
        expiresIn: '1 hour'
      }
    });
  }

  // Send welcome email
  async sendWelcomeEmail(user) {
    return await this.sendEmail({
      to: user.email,
      subject: EMAIL_CONFIG.EMAIL_TYPES.WELCOME.subject,
      template: EMAIL_CONFIG.EMAIL_TYPES.WELCOME.template,
      data: {
        user: {
          firstName: user.firstName || user.username,
          username: user.username,
          email: user.email
        },
        loginUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/login`,
        profileUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/profile/${user.username}`
      }
    });
  }

  // Send notification email
  async sendNotificationEmail(user, notification) {
    return await this.sendEmail({
      to: user.email,
      subject: notification.title || EMAIL_CONFIG.EMAIL_TYPES.NOTIFICATION.subject,
      template: EMAIL_CONFIG.EMAIL_TYPES.NOTIFICATION.template,
      data: {
        user: {
          firstName: user.firstName || user.username,
          username: user.username
        },
        notification: {
          title: notification.title,
          message: notification.message,
          type: notification.type,
          createdAt: notification.createdAt
        },
        actionUrl: notification.actionUrl,
        unsubscribeUrl: `${EMAIL_CONFIG.TEMPLATES.BASE_URL}/unsubscribe?token=${user.id}`
      }
    });
  }

  // Get email service statistics
  getStats() {
    return {
      provider: EMAIL_CONFIG.CURRENT_PROVIDER,
      templatesLoaded: this.templateCache.size,
      rateLimitEntries: this.rateLimitCache.size,
      configuration: {
        rateLimits: EMAIL_CONFIG.RATE_LIMITS,
        emailTypes: Object.keys(EMAIL_CONFIG.EMAIL_TYPES)
      }
    };
  }
}

// Create singleton instance
const modernEmailService = new ModernEmailService();

module.exports = modernEmailService;
