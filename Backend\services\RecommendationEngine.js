const SearchIndex = require('../models/SearchIndex');
const SearchHistory = require('../models/SearchHistory');
const UserInteraction = require('../models/UserInteraction');
const UserRelationship = require('../models/UserRelationship');
const Article = require('../models/Article');
const User = require('../models/User');
const { sequelize } = require('../config/database');
const { logger } = require('../config/logger');

class RecommendationEngine {
  constructor() {
    this.algorithms = {
      collaborative: this.collaborativeFiltering.bind(this),
      content: this.contentBasedFiltering.bind(this),
      popularity: this.popularityBased.bind(this),
      trending: this.trendingBased.bind(this),
      social: this.socialBased.bind(this),
      hybrid: this.hybridRecommendation.bind(this)
    };
    
    this.weights = {
      collaborative: 0.3,
      content: 0.25,
      popularity: 0.15,
      trending: 0.15,
      social: 0.15
    };
  }

  // 主要推荐入口
  async getRecommendations(userId, options = {}) {
    try {
      const {
        algorithm = 'hybrid',
        contentType = 'article',
        limit = 20,
        excludeViewed = true,
        includeReasons = false
      } = options;

      logger.info(`Generating recommendations for user ${userId} using ${algorithm} algorithm`);

      let recommendations;
      
      if (this.algorithms[algorithm]) {
        recommendations = await this.algorithms[algorithm](userId, {
          contentType,
          limit: limit * 2, // 获取更多候选项用于过滤
          includeReasons
        });
      } else {
        throw new Error(`Unknown recommendation algorithm: ${algorithm}`);
      }

      // 过滤已查看的内容
      if (excludeViewed) {
        recommendations = await this.filterViewedContent(userId, recommendations);
      }

      // 限制结果数量
      recommendations = recommendations.slice(0, limit);

      // 添加推荐原因
      if (includeReasons) {
        recommendations = await this.addRecommendationReasons(userId, recommendations);
      }

      logger.info(`Generated ${recommendations.length} recommendations for user ${userId}`);
      
      return recommendations;
    } catch (error) {
      logger.error('Error generating recommendations:', error);
      throw error;
    }
  }

  // 协同过滤推荐
  async collaborativeFiltering(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户的交互历史
    const userInteractions = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        interactionType: {
          [sequelize.Sequelize.Op.in]: ['like', 'favorite', 'share']
        },
        isActive: true,
        value: 1
      },
      attributes: ['targetId']
    });

    const userItemIds = userInteractions.map(i => i.targetId);

    if (userItemIds.length === 0) {
      return [];
    }

    // 找到有相似兴趣的用户
    const similarUsers = await sequelize.query(`
      SELECT 
        ui.user_id,
        COUNT(*) as common_items,
        COUNT(*) * 1.0 / (
          SELECT COUNT(DISTINCT target_id) 
          FROM user_interactions 
          WHERE user_id = ui.user_id 
            AND target_type = :contentType
            AND interaction_type IN ('like', 'favorite', 'share')
            AND is_active = true
            AND value = 1
        ) as similarity_score
      FROM user_interactions ui
      WHERE ui.target_id IN (:userItemIds)
        AND ui.user_id != :userId
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
      GROUP BY ui.user_id
      HAVING COUNT(*) >= 2
      ORDER BY similarity_score DESC, common_items DESC
      LIMIT 50
    `, {
      replacements: { 
        userId, 
        contentType, 
        userItemIds 
      },
      type: sequelize.QueryTypes.SELECT
    });

    if (similarUsers.length === 0) {
      return [];
    }

    const similarUserIds = similarUsers.map(u => u.user_id);

    // 获取相似用户喜欢但当前用户未交互的内容
    const recommendations = await sequelize.query(`
      SELECT 
        ui.target_id,
        COUNT(*) as recommendation_score,
        AVG(si.popularity) as avg_popularity,
        AVG(si.quality_score) as avg_quality
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id IN (:similarUserIds)
        AND ui.target_type = :contentType
        AND ui.target_id NOT IN (:userItemIds)
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
      GROUP BY ui.target_id
      ORDER BY recommendation_score DESC, avg_popularity DESC
      LIMIT :limit
    `, {
      replacements: { 
        similarUserIds, 
        contentType, 
        userItemIds: userItemIds.length > 0 ? userItemIds : [0],
        limit 
      },
      type: sequelize.QueryTypes.SELECT
    });

    return recommendations.map(r => ({
      contentId: r.target_id,
      contentType,
      score: parseFloat(r.recommendation_score),
      algorithm: 'collaborative',
      metadata: {
        avgPopularity: parseFloat(r.avg_popularity),
        avgQuality: parseFloat(r.avg_quality)
      }
    }));
  }

  // 基于内容的推荐
  async contentBasedFiltering(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户喜欢的内容的特征
    const userPreferences = await sequelize.query(`
      SELECT 
        si.category,
        si.tags,
        si.keywords,
        COUNT(*) as preference_weight
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id = :userId
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
      GROUP BY si.category, si.tags, si.keywords
      ORDER BY preference_weight DESC
    `, {
      replacements: { userId, contentType },
      type: sequelize.QueryTypes.SELECT
    });

    if (userPreferences.length === 0) {
      return [];
    }

    // 提取用户偏好的分类、标签和关键词
    const preferredCategories = [...new Set(userPreferences.map(p => p.category).filter(Boolean))];
    const preferredTags = [...new Set(userPreferences.flatMap(p => p.tags || []))];
    const preferredKeywords = [...new Set(userPreferences.flatMap(p => p.keywords || []))];

    // 获取用户已交互的内容ID
    const userInteractedIds = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        isActive: true
      },
      attributes: ['targetId']
    }).then(interactions => interactions.map(i => i.targetId));

    // 基于内容特征推荐
    const whereConditions = [];
    const replacements = { contentType, limit };

    if (preferredCategories.length > 0) {
      whereConditions.push('si.category = ANY(:categories)');
      replacements.categories = preferredCategories;
    }

    if (preferredTags.length > 0) {
      whereConditions.push('si.tags && :tags');
      replacements.tags = preferredTags;
    }

    if (preferredKeywords.length > 0) {
      whereConditions.push('si.keywords && :keywords');
      replacements.keywords = preferredKeywords;
    }

    if (userInteractedIds.length > 0) {
      whereConditions.push('si.content_id NOT IN (:excludeIds)');
      replacements.excludeIds = userInteractedIds;
    }

    if (whereConditions.length === 0) {
      return [];
    }

    const recommendations = await sequelize.query(`
      SELECT 
        si.content_id,
        si.popularity,
        si.quality_score,
        (
          CASE WHEN si.category = ANY(:categories) THEN 1 ELSE 0 END +
          CASE WHEN si.tags && :tags THEN 1 ELSE 0 END +
          CASE WHEN si.keywords && :keywords THEN 1 ELSE 0 END
        ) as content_match_score
      FROM search_indexes si
      WHERE si.content_type = :contentType
        AND si.is_active = true
        AND (${whereConditions.join(' OR ')})
      ORDER BY content_match_score DESC, si.popularity DESC, si.quality_score DESC
      LIMIT :limit
    `, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    return recommendations.map(r => ({
      contentId: r.content_id,
      contentType,
      score: parseFloat(r.content_match_score) * parseFloat(r.popularity) * parseFloat(r.quality_score),
      algorithm: 'content',
      metadata: {
        popularity: parseFloat(r.popularity),
        quality: parseFloat(r.quality_score),
        matchScore: parseInt(r.content_match_score)
      }
    }));
  }

  // 基于热度的推荐
  async popularityBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    const popular = await SearchIndex.getPopularContent(contentType, '7d', limit);
    
    return popular.map(item => ({
      contentId: item.contentId,
      contentType,
      score: item.popularity,
      algorithm: 'popularity',
      metadata: {
        popularity: item.popularity,
        quality: item.qualityScore
      }
    }));
  }

  // 基于趋势的推荐
  async trendingBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    const trending = await SearchIndex.getTrendingContent(contentType, limit);
    
    return trending.map(item => ({
      contentId: item.contentId,
      contentType,
      score: item.popularity * item.qualityScore,
      algorithm: 'trending',
      metadata: {
        popularity: item.popularity,
        quality: item.qualityScore
      }
    }));
  }

  // 基于社交关系的推荐
  async socialBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户关注的人
    const following = await UserRelationship.getFollowing(userId, { includeUser: false });
    const followingIds = following.rows.map(f => f.followingId);

    if (followingIds.length === 0) {
      return [];
    }

    // 获取关注的人最近喜欢的内容
    const socialRecommendations = await sequelize.query(`
      SELECT 
        ui.target_id,
        COUNT(*) as social_score,
        AVG(si.popularity) as avg_popularity,
        MAX(ui.created_at) as latest_interaction
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id IN (:followingIds)
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
        AND ui.created_at >= NOW() - INTERVAL '7 days'
      GROUP BY ui.target_id
      ORDER BY social_score DESC, latest_interaction DESC
      LIMIT :limit
    `, {
      replacements: { followingIds, contentType, limit },
      type: sequelize.QueryTypes.SELECT
    });

    return socialRecommendations.map(r => ({
      contentId: r.target_id,
      contentType,
      score: parseFloat(r.social_score) * parseFloat(r.avg_popularity),
      algorithm: 'social',
      metadata: {
        socialScore: parseFloat(r.social_score),
        avgPopularity: parseFloat(r.avg_popularity),
        latestInteraction: r.latest_interaction
      }
    }));
  }

  // 混合推荐算法
  async hybridRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 并行执行多种推荐算法
    const [
      collaborative,
      content,
      popularity,
      trending,
      social
    ] = await Promise.all([
      this.collaborativeFiltering(userId, { contentType, limit: Math.ceil(limit * 0.4) }),
      this.contentBasedFiltering(userId, { contentType, limit: Math.ceil(limit * 0.4) }),
      this.popularityBased(userId, { contentType, limit: Math.ceil(limit * 0.3) }),
      this.trendingBased(userId, { contentType, limit: Math.ceil(limit * 0.3) }),
      this.socialBased(userId, { contentType, limit: Math.ceil(limit * 0.3) })
    ]);

    // 合并和加权
    const allRecommendations = new Map();

    const addRecommendations = (recs, weight) => {
      recs.forEach(rec => {
        const key = `${rec.contentType}_${rec.contentId}`;
        if (allRecommendations.has(key)) {
          const existing = allRecommendations.get(key);
          existing.score += rec.score * weight;
          existing.algorithms.push(rec.algorithm);
        } else {
          allRecommendations.set(key, {
            ...rec,
            score: rec.score * weight,
            algorithms: [rec.algorithm]
          });
        }
      });
    };

    addRecommendations(collaborative, this.weights.collaborative);
    addRecommendations(content, this.weights.content);
    addRecommendations(popularity, this.weights.popularity);
    addRecommendations(trending, this.weights.trending);
    addRecommendations(social, this.weights.social);

    // 排序并返回
    const hybridResults = Array.from(allRecommendations.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(rec => ({
        ...rec,
        algorithm: 'hybrid',
        metadata: {
          ...rec.metadata,
          algorithms: rec.algorithms
        }
      }));

    return hybridResults;
  }

  // 过滤已查看的内容
  async filterViewedContent(userId, recommendations) {
    if (recommendations.length === 0) return recommendations;

    const contentIds = recommendations.map(r => r.contentId);
    const contentType = recommendations[0].contentType;

    const viewedIds = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        targetId: {
          [sequelize.Sequelize.Op.in]: contentIds
        },
        interactionType: {
          [sequelize.Sequelize.Op.in]: ['view', 'like', 'favorite', 'share']
        },
        isActive: true
      },
      attributes: ['targetId']
    }).then(interactions => interactions.map(i => i.targetId));

    return recommendations.filter(rec => !viewedIds.includes(rec.contentId));
  }

  // 添加推荐原因
  async addRecommendationReasons(userId, recommendations) {
    // 这里可以添加更详细的推荐原因逻辑
    return recommendations.map(rec => ({
      ...rec,
      reason: this.generateRecommendationReason(rec)
    }));
  }

  // 生成推荐原因
  generateRecommendationReason(recommendation) {
    const { algorithm, metadata } = recommendation;

    switch (algorithm) {
      case 'collaborative':
        return 'Based on users with similar interests';
      case 'content':
        return 'Based on your reading preferences';
      case 'popularity':
        return 'Popular content this week';
      case 'trending':
        return 'Trending now';
      case 'social':
        return 'Liked by people you follow';
      case 'hybrid':
        return `Recommended by ${metadata.algorithms?.join(', ') || 'multiple factors'}`;
      default:
        return 'Recommended for you';
    }
  }

  // 更新推荐权重
  updateWeights(newWeights) {
    this.weights = { ...this.weights, ...newWeights };
    logger.info('Recommendation weights updated:', this.weights);
  }

  // 获取推荐统计
  async getRecommendationStats(userId, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return sequelize.query(`
      SELECT 
        algorithm,
        COUNT(*) as recommendation_count,
        AVG(score) as avg_score,
        COUNT(CASE WHEN clicked = true THEN 1 END) as click_count,
        COUNT(CASE WHEN clicked = true THEN 1 END) * 1.0 / COUNT(*) as click_rate
      FROM recommendation_logs 
      WHERE user_id = :userId 
        AND created_at >= :startDate
      GROUP BY algorithm
      ORDER BY recommendation_count DESC
    `, {
      replacements: { userId, startDate },
      type: sequelize.QueryTypes.SELECT
    });
  }
}

// 创建单例实例
const recommendationEngine = new RecommendationEngine();

module.exports = recommendationEngine;
