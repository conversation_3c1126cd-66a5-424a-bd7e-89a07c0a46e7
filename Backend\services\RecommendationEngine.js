const SearchIndex = require('../models/SearchIndex');
const SearchHistory = require('../models/SearchHistory');
const UserInteraction = require('../models/UserInteraction');
const UserRelationship = require('../models/UserRelationship');
const Article = require('../models/Article');
const User = require('../models/User');
const { sequelize } = require('../config/database');
const { logger } = require('../config/logger');

class RecommendationEngine {
  constructor() {
    this.algorithms = {
      collaborative: this.collaborativeFiltering.bind(this),
      content: this.contentBasedFiltering.bind(this),
      popularity: this.popularityBased.bind(this),
      trending: this.trendingBased.bind(this),
      social: this.socialBased.bind(this),
      hybrid: this.hybridRecommendation.bind(this),
      // 新增大数据算法
      deepLearning: this.deepLearningRecommendation.bind(this),
      realTime: this.realTimeRecommendation.bind(this),
      contextual: this.contextualRecommendation.bind(this),
      multiArmed: this.multiArmedBanditRecommendation.bind(this)
    };

    this.weights = {
      collaborative: 0.25,
      content: 0.20,
      popularity: 0.10,
      trending: 0.10,
      social: 0.10,
      deepLearning: 0.15,
      realTime: 0.05,
      contextual: 0.05
    };

    // 用户行为权重配置
    this.behaviorWeights = {
      view: 1,
      like: 3,
      share: 5,
      comment: 4,
      bookmark: 6,
      follow: 7,
      subscribe: 8,
      download: 4,
      search: 2
    };

    // 实时推荐缓存
    this.realTimeCache = new Map();
    this.userSessionData = new Map();

    // 多臂老虎机算法参数
    this.banditArms = new Map(); // 存储每个推荐策略的表现
    this.explorationRate = 0.1; // 探索率
  }

  // 主要推荐入口
  async getRecommendations(userId, options = {}) {
    try {
      const {
        algorithm = 'hybrid',
        contentType = 'article',
        limit = 20,
        excludeViewed = true,
        includeReasons = false
      } = options;

      logger.info(`Generating recommendations for user ${userId} using ${algorithm} algorithm`);

      let recommendations;
      
      if (this.algorithms[algorithm]) {
        recommendations = await this.algorithms[algorithm](userId, {
          contentType,
          limit: limit * 2, // 获取更多候选项用于过滤
          includeReasons
        });
      } else {
        throw new Error(`Unknown recommendation algorithm: ${algorithm}`);
      }

      // 过滤已查看的内容
      if (excludeViewed) {
        recommendations = await this.filterViewedContent(userId, recommendations);
      }

      // 限制结果数量
      recommendations = recommendations.slice(0, limit);

      // 添加推荐原因
      if (includeReasons) {
        recommendations = await this.addRecommendationReasons(userId, recommendations);
      }

      logger.info(`Generated ${recommendations.length} recommendations for user ${userId}`);
      
      return recommendations;
    } catch (error) {
      logger.error('Error generating recommendations:', error);
      throw error;
    }
  }

  // 协同过滤推荐
  async collaborativeFiltering(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户的交互历史
    const userInteractions = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        interactionType: {
          [sequelize.Sequelize.Op.in]: ['like', 'favorite', 'share']
        },
        isActive: true,
        value: 1
      },
      attributes: ['targetId']
    });

    const userItemIds = userInteractions.map(i => i.targetId);

    if (userItemIds.length === 0) {
      return [];
    }

    // 找到有相似兴趣的用户
    const similarUsers = await sequelize.query(`
      SELECT 
        ui.user_id,
        COUNT(*) as common_items,
        COUNT(*) * 1.0 / (
          SELECT COUNT(DISTINCT target_id) 
          FROM user_interactions 
          WHERE user_id = ui.user_id 
            AND target_type = :contentType
            AND interaction_type IN ('like', 'favorite', 'share')
            AND is_active = true
            AND value = 1
        ) as similarity_score
      FROM user_interactions ui
      WHERE ui.target_id IN (:userItemIds)
        AND ui.user_id != :userId
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
      GROUP BY ui.user_id
      HAVING COUNT(*) >= 2
      ORDER BY similarity_score DESC, common_items DESC
      LIMIT 50
    `, {
      replacements: { 
        userId, 
        contentType, 
        userItemIds 
      },
      type: sequelize.QueryTypes.SELECT
    });

    if (similarUsers.length === 0) {
      return [];
    }

    const similarUserIds = similarUsers.map(u => u.user_id);

    // 获取相似用户喜欢但当前用户未交互的内容
    const recommendations = await sequelize.query(`
      SELECT 
        ui.target_id,
        COUNT(*) as recommendation_score,
        AVG(si.popularity) as avg_popularity,
        AVG(si.quality_score) as avg_quality
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id IN (:similarUserIds)
        AND ui.target_type = :contentType
        AND ui.target_id NOT IN (:userItemIds)
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
      GROUP BY ui.target_id
      ORDER BY recommendation_score DESC, avg_popularity DESC
      LIMIT :limit
    `, {
      replacements: { 
        similarUserIds, 
        contentType, 
        userItemIds: userItemIds.length > 0 ? userItemIds : [0],
        limit 
      },
      type: sequelize.QueryTypes.SELECT
    });

    return recommendations.map(r => ({
      contentId: r.target_id,
      contentType,
      score: parseFloat(r.recommendation_score),
      algorithm: 'collaborative',
      metadata: {
        avgPopularity: parseFloat(r.avg_popularity),
        avgQuality: parseFloat(r.avg_quality)
      }
    }));
  }

  // 基于内容的推荐
  async contentBasedFiltering(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户喜欢的内容的特征
    const userPreferences = await sequelize.query(`
      SELECT 
        si.category,
        si.tags,
        si.keywords,
        COUNT(*) as preference_weight
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id = :userId
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
      GROUP BY si.category, si.tags, si.keywords
      ORDER BY preference_weight DESC
    `, {
      replacements: { userId, contentType },
      type: sequelize.QueryTypes.SELECT
    });

    if (userPreferences.length === 0) {
      return [];
    }

    // 提取用户偏好的分类、标签和关键词
    const preferredCategories = [...new Set(userPreferences.map(p => p.category).filter(Boolean))];
    const preferredTags = [...new Set(userPreferences.flatMap(p => p.tags || []))];
    const preferredKeywords = [...new Set(userPreferences.flatMap(p => p.keywords || []))];

    // 获取用户已交互的内容ID
    const userInteractedIds = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        isActive: true
      },
      attributes: ['targetId']
    }).then(interactions => interactions.map(i => i.targetId));

    // 基于内容特征推荐
    const whereConditions = [];
    const replacements = { contentType, limit };

    if (preferredCategories.length > 0) {
      whereConditions.push('si.category = ANY(:categories)');
      replacements.categories = preferredCategories;
    }

    if (preferredTags.length > 0) {
      whereConditions.push('si.tags && :tags');
      replacements.tags = preferredTags;
    }

    if (preferredKeywords.length > 0) {
      whereConditions.push('si.keywords && :keywords');
      replacements.keywords = preferredKeywords;
    }

    if (userInteractedIds.length > 0) {
      whereConditions.push('si.content_id NOT IN (:excludeIds)');
      replacements.excludeIds = userInteractedIds;
    }

    if (whereConditions.length === 0) {
      return [];
    }

    const recommendations = await sequelize.query(`
      SELECT 
        si.content_id,
        si.popularity,
        si.quality_score,
        (
          CASE WHEN si.category = ANY(:categories) THEN 1 ELSE 0 END +
          CASE WHEN si.tags && :tags THEN 1 ELSE 0 END +
          CASE WHEN si.keywords && :keywords THEN 1 ELSE 0 END
        ) as content_match_score
      FROM search_indexes si
      WHERE si.content_type = :contentType
        AND si.is_active = true
        AND (${whereConditions.join(' OR ')})
      ORDER BY content_match_score DESC, si.popularity DESC, si.quality_score DESC
      LIMIT :limit
    `, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    return recommendations.map(r => ({
      contentId: r.content_id,
      contentType,
      score: parseFloat(r.content_match_score) * parseFloat(r.popularity) * parseFloat(r.quality_score),
      algorithm: 'content',
      metadata: {
        popularity: parseFloat(r.popularity),
        quality: parseFloat(r.quality_score),
        matchScore: parseInt(r.content_match_score)
      }
    }));
  }

  // 基于热度的推荐
  async popularityBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    const popular = await SearchIndex.getPopularContent(contentType, '7d', limit);
    
    return popular.map(item => ({
      contentId: item.contentId,
      contentType,
      score: item.popularity,
      algorithm: 'popularity',
      metadata: {
        popularity: item.popularity,
        quality: item.qualityScore
      }
    }));
  }

  // 基于趋势的推荐
  async trendingBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    const trending = await SearchIndex.getTrendingContent(contentType, limit);
    
    return trending.map(item => ({
      contentId: item.contentId,
      contentType,
      score: item.popularity * item.qualityScore,
      algorithm: 'trending',
      metadata: {
        popularity: item.popularity,
        quality: item.qualityScore
      }
    }));
  }

  // 基于社交关系的推荐
  async socialBased(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户关注的人
    const following = await UserRelationship.getFollowing(userId, { includeUser: false });
    const followingIds = following.rows.map(f => f.followingId);

    if (followingIds.length === 0) {
      return [];
    }

    // 获取关注的人最近喜欢的内容
    const socialRecommendations = await sequelize.query(`
      SELECT 
        ui.target_id,
        COUNT(*) as social_score,
        AVG(si.popularity) as avg_popularity,
        MAX(ui.created_at) as latest_interaction
      FROM user_interactions ui
      JOIN search_indexes si ON si.content_id = ui.target_id AND si.content_type = ui.target_type
      WHERE ui.user_id IN (:followingIds)
        AND ui.target_type = :contentType
        AND ui.interaction_type IN ('like', 'favorite', 'share')
        AND ui.is_active = true
        AND ui.value = 1
        AND si.is_active = true
        AND ui.created_at >= NOW() - INTERVAL '7 days'
      GROUP BY ui.target_id
      ORDER BY social_score DESC, latest_interaction DESC
      LIMIT :limit
    `, {
      replacements: { followingIds, contentType, limit },
      type: sequelize.QueryTypes.SELECT
    });

    return socialRecommendations.map(r => ({
      contentId: r.target_id,
      contentType,
      score: parseFloat(r.social_score) * parseFloat(r.avg_popularity),
      algorithm: 'social',
      metadata: {
        socialScore: parseFloat(r.social_score),
        avgPopularity: parseFloat(r.avg_popularity),
        latestInteraction: r.latest_interaction
      }
    }));
  }

  // 混合推荐算法
  async hybridRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 并行执行多种推荐算法
    const [
      collaborative,
      content,
      popularity,
      trending,
      social
    ] = await Promise.all([
      this.collaborativeFiltering(userId, { contentType, limit: Math.ceil(limit * 0.4) }),
      this.contentBasedFiltering(userId, { contentType, limit: Math.ceil(limit * 0.4) }),
      this.popularityBased(userId, { contentType, limit: Math.ceil(limit * 0.3) }),
      this.trendingBased(userId, { contentType, limit: Math.ceil(limit * 0.3) }),
      this.socialBased(userId, { contentType, limit: Math.ceil(limit * 0.3) })
    ]);

    // 合并和加权
    const allRecommendations = new Map();

    const addRecommendations = (recs, weight) => {
      recs.forEach(rec => {
        const key = `${rec.contentType}_${rec.contentId}`;
        if (allRecommendations.has(key)) {
          const existing = allRecommendations.get(key);
          existing.score += rec.score * weight;
          existing.algorithms.push(rec.algorithm);
        } else {
          allRecommendations.set(key, {
            ...rec,
            score: rec.score * weight,
            algorithms: [rec.algorithm]
          });
        }
      });
    };

    addRecommendations(collaborative, this.weights.collaborative);
    addRecommendations(content, this.weights.content);
    addRecommendations(popularity, this.weights.popularity);
    addRecommendations(trending, this.weights.trending);
    addRecommendations(social, this.weights.social);

    // 排序并返回
    const hybridResults = Array.from(allRecommendations.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(rec => ({
        ...rec,
        algorithm: 'hybrid',
        metadata: {
          ...rec.metadata,
          algorithms: rec.algorithms
        }
      }));

    return hybridResults;
  }

  // 过滤已查看的内容
  async filterViewedContent(userId, recommendations) {
    if (recommendations.length === 0) return recommendations;

    const contentIds = recommendations.map(r => r.contentId);
    const contentType = recommendations[0].contentType;

    const viewedIds = await UserInteraction.findAll({
      where: {
        userId,
        targetType: contentType,
        targetId: {
          [sequelize.Sequelize.Op.in]: contentIds
        },
        interactionType: {
          [sequelize.Sequelize.Op.in]: ['view', 'like', 'favorite', 'share']
        },
        isActive: true
      },
      attributes: ['targetId']
    }).then(interactions => interactions.map(i => i.targetId));

    return recommendations.filter(rec => !viewedIds.includes(rec.contentId));
  }

  // 添加推荐原因
  async addRecommendationReasons(userId, recommendations) {
    // 这里可以添加更详细的推荐原因逻辑
    return recommendations.map(rec => ({
      ...rec,
      reason: this.generateRecommendationReason(rec)
    }));
  }

  // 生成推荐原因
  generateRecommendationReason(recommendation) {
    const { algorithm, metadata } = recommendation;

    switch (algorithm) {
      case 'collaborative':
        return 'Based on users with similar interests';
      case 'content':
        return 'Based on your reading preferences';
      case 'popularity':
        return 'Popular content this week';
      case 'trending':
        return 'Trending now';
      case 'social':
        return 'Liked by people you follow';
      case 'hybrid':
        return `Recommended by ${metadata.algorithms?.join(', ') || 'multiple factors'}`;
      default:
        return 'Recommended for you';
    }
  }

  // 更新推荐权重
  updateWeights(newWeights) {
    this.weights = { ...this.weights, ...newWeights };
    logger.info('Recommendation weights updated:', this.weights);
  }

  // 获取推荐统计
  async getRecommendationStats(userId, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return sequelize.query(`
      SELECT
        algorithm,
        COUNT(*) as recommendation_count,
        AVG(score) as avg_score,
        COUNT(CASE WHEN clicked = true THEN 1 END) as click_count,
        COUNT(CASE WHEN clicked = true THEN 1 END) * 1.0 / COUNT(*) as click_rate
      FROM recommendation_logs
      WHERE user_id = :userId
        AND created_at >= :startDate
      GROUP BY algorithm
      ORDER BY recommendation_count DESC
    `, {
      replacements: { userId, startDate },
      type: sequelize.QueryTypes.SELECT
    });
  }

  // 深度学习推荐算法
  async deepLearningRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    try {
      // 获取用户的深度特征向量
      const userVector = await this.getUserDeepFeatures(userId);
      const contentVectors = await this.getContentDeepFeatures(contentType, limit * 3);

      // 计算相似度并排序
      const similarities = contentVectors.map(content => ({
        ...content,
        similarity: this.calculateCosineSimilarity(userVector, content.features),
        algorithm: 'deep_learning'
      }));

      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

    } catch (error) {
      logger.error('Deep learning recommendation error:', error);
      return [];
    }
  }

  // 实时推荐算法
  async realTimeRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取用户最近的行为
    const recentBehaviors = await this.getRecentUserBehaviors(userId, 100);
    if (recentBehaviors.length === 0) return [];

    // 基于最近行为的实时推荐
    const recentInterests = this.extractRecentInterests(recentBehaviors);
    const recommendations = await this.findSimilarContentByInterests(recentInterests, contentType, limit);

    return recommendations.map(rec => ({
      ...rec,
      algorithm: 'real_time',
      freshness: this.calculateFreshness(rec.publishedAt)
    }));
  }

  // 上下文感知推荐
  async contextualRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20, context = {} } = options;

    const {
      timeOfDay = this.getCurrentTimeSlot(),
      deviceType = 'desktop',
      location = null,
      weather = null,
      dayOfWeek = new Date().getDay()
    } = context;

    // 基于上下文调整推荐权重
    const contextWeights = this.getContextualWeights(timeOfDay, deviceType, dayOfWeek);

    // 获取基础推荐
    const baseRecommendations = await this.hybridRecommendation(userId, { contentType, limit: limit * 2 });

    // 应用上下文权重
    const contextualRecs = baseRecommendations.map(rec => ({
      ...rec,
      contextualScore: rec.score * (contextWeights[rec.category] || 1),
      algorithm: 'contextual'
    }));

    return contextualRecs
      .sort((a, b) => b.contextualScore - a.contextualScore)
      .slice(0, limit);
  }

  // 多臂老虎机推荐算法
  async multiArmedBanditRecommendation(userId, options = {}) {
    const { contentType = 'article', limit = 20 } = options;

    // 获取各个算法的历史表现
    const armPerformances = this.getBanditArmPerformances(userId);

    // 选择算法（探索vs利用）
    const selectedAlgorithms = this.selectBanditArms(armPerformances, Math.ceil(limit / 2));

    const recommendations = [];

    for (const algorithm of selectedAlgorithms) {
      if (this.algorithms[algorithm] && algorithm !== 'multiArmed') {
        const algoRecs = await this.algorithms[algorithm](userId, {
          contentType,
          limit: Math.ceil(limit / selectedAlgorithms.length)
        });

        recommendations.push(...algoRecs.map(rec => ({
          ...rec,
          selectedBy: 'multi_armed_bandit',
          sourceAlgorithm: algorithm
        })));
      }
    }

    return this.diversifyRecommendations(recommendations, limit);
  }

  // 获取用户深度特征向量
  async getUserDeepFeatures(userId) {
    const interactions = await UserInteraction.findAll({
      where: { userId, isActive: true },
      limit: 1000,
      order: [['createdAt', 'DESC']]
    });

    // 构建用户特征向量（简化版本）
    const featureVector = new Array(50).fill(0); // 50维特征向量
    const categoryWeights = new Map();
    const tagWeights = new Map();

    let totalWeight = 0;

    for (const interaction of interactions) {
      const weight = this.behaviorWeights[interaction.interactionType] || 1;

      // 基于内容ID生成简单特征
      const contentHash = this.simpleHash(interaction.targetId);
      const featureIndex = contentHash % featureVector.length;
      featureVector[featureIndex] += weight;

      totalWeight += weight;
    }

    // 归一化
    if (totalWeight > 0) {
      return featureVector.map(value => value / totalWeight);
    }

    return featureVector;
  }

  // 简单哈希函数
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  // 获取内容深度特征向量
  async getContentDeepFeatures(contentType, limit) {
    const contents = await SearchIndex.findAll({
      where: {
        contentType,
        isActive: true
      },
      attributes: ['contentId', 'title', 'category', 'tags', 'publishedAt'],
      limit,
      order: [['createdAt', 'DESC']]
    });

    return contents.map(content => {
      // 生成简单的内容特征向量
      const features = new Array(50).fill(0);

      // 基于标题和内容ID生成特征
      const titleHash = this.simpleHash(content.title || '');
      const idHash = this.simpleHash(content.contentId);

      features[titleHash % features.length] = 1;
      features[idHash % features.length] = 1;

      // 基于分类生成特征
      if (content.category) {
        const categoryHash = this.simpleHash(content.category);
        features[categoryHash % features.length] = 1;
      }

      return {
        id: content.contentId,
        title: content.title,
        category: content.category,
        tags: content.tags,
        publishedAt: content.publishedAt,
        features
      };
    });
  }

  // 计算余弦相似度
  calculateCosineSimilarity(vectorA, vectorB) {
    if (!vectorA || !vectorB || vectorA.length !== vectorB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA === 0 || normB === 0) return 0;

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  // 获取用户最近行为
  async getRecentUserBehaviors(userId, limit = 100) {
    return await UserInteraction.findAll({
      where: {
        userId,
        isActive: true,
        createdAt: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      },
      limit,
      order: [['createdAt', 'DESC']]
    });
  }

  // 提取最近兴趣
  extractRecentInterests(behaviors) {
    const interests = new Map();

    behaviors.forEach(behavior => {
      const weight = this.behaviorWeights[behavior.interactionType] || 1;
      const timeDecay = this.calculateTimeDecay(behavior.createdAt);
      const finalWeight = weight * timeDecay;

      // 基于目标ID生成兴趣
      const interestKey = `content_${behavior.targetId}`;
      interests.set(interestKey, (interests.get(interestKey) || 0) + finalWeight);
    });

    return Array.from(interests.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([interest, score]) => ({ interest, score }));
  }

  // 计算时间衰减
  calculateTimeDecay(timestamp) {
    const hoursSince = (Date.now() - new Date(timestamp).getTime()) / (1000 * 60 * 60);
    return Math.exp(-hoursSince / 24); // 24小时半衰期
  }

  // 根据兴趣查找相似内容
  async findSimilarContentByInterests(interests, contentType, limit) {
    if (interests.length === 0) return [];

    // 获取最新内容
    const contents = await SearchIndex.findAll({
      where: {
        contentType,
        isActive: true
      },
      limit: limit * 2,
      order: [['createdAt', 'DESC']]
    });

    return contents.map(content => ({
      id: content.contentId,
      title: content.title,
      category: content.category,
      tags: content.tags,
      score: Math.random() * 100, // 简化的评分
      publishedAt: content.publishedAt
    })).sort((a, b) => b.score - a.score).slice(0, limit);
  }

  // 计算内容新鲜度
  calculateFreshness(publishedAt) {
    const hoursSince = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60);
    return Math.max(0, 1 - hoursSince / (24 * 7)); // 一周内的新鲜度
  }

  // 获取当前时间段
  getCurrentTimeSlot() {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 24) return 'evening';
    return 'night';
  }

  // 获取上下文权重
  getContextualWeights(timeOfDay, deviceType, dayOfWeek) {
    const weights = {
      'technology': 1.0,
      'business': 1.0,
      'entertainment': 1.0,
      'sports': 1.0,
      'health': 1.0,
      'science': 1.0
    };

    // 时间段调整
    if (timeOfDay === 'morning') {
      weights.business *= 1.3;
      weights.technology *= 1.2;
    } else if (timeOfDay === 'evening') {
      weights.entertainment *= 1.4;
      weights.sports *= 1.3;
    }

    // 设备类型调整
    if (deviceType === 'mobile') {
      weights.entertainment *= 1.2;
      weights.sports *= 1.1;
    }

    // 工作日vs周末
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      weights.business *= 1.2;
      weights.technology *= 1.1;
    } else {
      weights.entertainment *= 1.3;
      weights.sports *= 1.2;
    }

    return weights;
  }

  // 获取多臂老虎机算法表现
  getBanditArmPerformances(userId) {
    const userKey = `user_${userId}`;
    if (!this.banditArms.has(userKey)) {
      // 初始化所有算法的表现
      this.banditArms.set(userKey, {
        collaborative: { clicks: 0, impressions: 0, reward: 0 },
        content: { clicks: 0, impressions: 0, reward: 0 },
        popularity: { clicks: 0, impressions: 0, reward: 0 },
        trending: { clicks: 0, impressions: 0, reward: 0 },
        social: { clicks: 0, impressions: 0, reward: 0 }
      });
    }
    return this.banditArms.get(userKey);
  }

  // 选择多臂老虎机算法
  selectBanditArms(performances, numArms) {
    const algorithms = Object.keys(performances);
    const selected = [];

    for (let i = 0; i < numArms && i < algorithms.length; i++) {
      if (Math.random() < this.explorationRate) {
        // 探索：随机选择
        const randomAlgo = algorithms[Math.floor(Math.random() * algorithms.length)];
        if (!selected.includes(randomAlgo)) {
          selected.push(randomAlgo);
        }
      } else {
        // 利用：选择表现最好的
        const bestAlgo = algorithms
          .filter(algo => !selected.includes(algo))
          .sort((a, b) => {
            const aRate = performances[a].impressions > 0 ? performances[a].clicks / performances[a].impressions : 0;
            const bRate = performances[b].impressions > 0 ? performances[b].clicks / performances[b].impressions : 0;
            return bRate - aRate;
          })[0];

        if (bestAlgo) {
          selected.push(bestAlgo);
        }
      }
    }

    return selected.length > 0 ? selected : ['hybrid'];
  }

  // 多样化推荐结果
  diversifyRecommendations(recommendations, limit) {
    const diversified = [];
    const usedCategories = new Set();

    // 首先选择不同分类的内容
    for (const rec of recommendations) {
      if (diversified.length >= limit) break;

      if (!usedCategories.has(rec.category) || diversified.length < limit * 0.7) {
        diversified.push(rec);
        if (rec.category) usedCategories.add(rec.category);
      }
    }

    // 填充剩余位置
    for (const rec of recommendations) {
      if (diversified.length >= limit) break;
      if (!diversified.find(d => d.id === rec.id)) {
        diversified.push(rec);
      }
    }

    return diversified.slice(0, limit);
  }

  // 记录推荐反馈
  async recordRecommendationFeedback(userId, contentId, action, algorithm) {
    const userKey = `user_${userId}`;
    const performances = this.getBanditArmPerformances(userId);

    if (performances[algorithm]) {
      performances[algorithm].impressions += 1;

      if (action === 'click' || action === 'like' || action === 'share') {
        performances[algorithm].clicks += 1;
        performances[algorithm].reward += this.behaviorWeights[action] || 1;
      }

      this.banditArms.set(userKey, performances);
    }

    logger.info(`Recorded feedback: ${userId} ${action} ${contentId} via ${algorithm}`);
  }
}

// 创建单例实例
const recommendationEngine = new RecommendationEngine();

module.exports = recommendationEngine;
