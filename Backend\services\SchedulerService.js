const cron = require('node-cron');
const ScheduledPost = require('../models/ScheduledPost');
const Article = require('../models/Article');
const ArticleVersion = require('../models/ArticleVersion');
const User = require('../models/User');
const { logger } = require('../config/logger');

class SchedulerService {
  constructor() {
    this.isRunning = false;
    this.processingQueue = new Set();
    this.stats = {
      processed: 0,
      published: 0,
      failed: 0,
      lastRun: null
    };
  }

  // 启动调度服务
  start() {
    if (this.isRunning) {
      logger.warn('Scheduler service is already running');
      return;
    }

    logger.info('Starting scheduler service...');
    
    // 每分钟检查一次待发布的内容
    this.publishTask = cron.schedule('* * * * *', async () => {
      await this.processScheduledPosts();
    }, {
      scheduled: false
    });

    // 每5分钟检查一次需要重试的内容
    this.retryTask = cron.schedule('*/5 * * * *', async () => {
      await this.processRetryablePosts();
    }, {
      scheduled: false
    });

    // 每小时清理过期的调度记录
    this.cleanupTask = cron.schedule('0 * * * *', async () => {
      await this.cleanupExpiredSchedules();
    }, {
      scheduled: false
    });

    this.publishTask.start();
    this.retryTask.start();
    this.cleanupTask.start();

    this.isRunning = true;
    logger.info('Scheduler service started successfully');
  }

  // 停止调度服务
  stop() {
    if (!this.isRunning) {
      logger.warn('Scheduler service is not running');
      return;
    }

    logger.info('Stopping scheduler service...');

    if (this.publishTask) {
      this.publishTask.stop();
    }
    if (this.retryTask) {
      this.retryTask.stop();
    }
    if (this.cleanupTask) {
      this.cleanupTask.stop();
    }

    this.isRunning = false;
    logger.info('Scheduler service stopped');
  }

  // 处理待发布的内容
  async processScheduledPosts() {
    if (this.processingQueue.size > 0) {
      logger.debug('Previous processing still in progress, skipping...');
      return;
    }

    try {
      this.stats.lastRun = new Date();
      
      const pendingPosts = await ScheduledPost.getPendingPosts(50);
      
      if (pendingPosts.length === 0) {
        return;
      }

      logger.info(`Processing ${pendingPosts.length} scheduled posts`);

      const promises = pendingPosts.map(post => this.publishPost(post));
      await Promise.allSettled(promises);

      logger.info(`Completed processing scheduled posts. Processed: ${this.stats.processed}, Published: ${this.stats.published}, Failed: ${this.stats.failed}`);
    } catch (error) {
      logger.error('Error processing scheduled posts:', error);
    }
  }

  // 处理需要重试的内容
  async processRetryablePosts() {
    try {
      const retryablePosts = await ScheduledPost.getRetryablePosts(20);
      
      if (retryablePosts.length === 0) {
        return;
      }

      logger.info(`Processing ${retryablePosts.length} retryable posts`);

      const promises = retryablePosts.map(post => this.publishPost(post));
      await Promise.allSettled(promises);
    } catch (error) {
      logger.error('Error processing retryable posts:', error);
    }
  }

  // 发布单个内容
  async publishPost(scheduledPost) {
    const postId = scheduledPost.id;
    
    if (this.processingQueue.has(postId)) {
      return;
    }

    this.processingQueue.add(postId);
    this.stats.processed++;

    try {
      logger.info(`Publishing scheduled post ${postId} for article ${scheduledPost.articleId}`);

      // 标记为处理中
      await scheduledPost.markAsProcessing();

      // 获取文章和版本信息
      const article = await Article.findByPk(scheduledPost.articleId);
      if (!article) {
        throw new Error('Article not found');
      }

      let versionToPublish = null;
      if (scheduledPost.versionId) {
        versionToPublish = await ArticleVersion.findByPk(scheduledPost.versionId);
        if (!versionToPublish) {
          throw new Error('Specified version not found');
        }
      } else {
        // 使用当前活跃版本
        versionToPublish = await ArticleVersion.getActiveVersion(scheduledPost.articleId);
      }

      // 执行发布操作
      await this.executePublish(article, versionToPublish, scheduledPost);

      // 标记为已发布
      await scheduledPost.markAsPublished();
      this.stats.published++;

      logger.info(`Successfully published scheduled post ${postId}`);

      // 如果是循环发布，创建下一个调度
      if (scheduledPost.publishType === 'recurring' && scheduledPost.recurringPattern) {
        await this.createNextRecurringSchedule(scheduledPost);
      }

    } catch (error) {
      logger.error(`Failed to publish scheduled post ${postId}:`, error);
      
      // 标记为失败并设置重试
      await scheduledPost.markAsFailed(error.message, true);
      this.stats.failed++;
    } finally {
      this.processingQueue.delete(postId);
    }
  }

  // 执行实际的发布操作
  async executePublish(article, version, scheduledPost) {
    const publishSettings = scheduledPost.publishSettings || {};
    
    // 更新文章状态为已发布
    const updateData = {
      published: true,
      publishedAt: new Date()
    };

    // 如果指定了版本，使用版本的内容
    if (version) {
      updateData.title = version.title;
      updateData.description = version.description;
      updateData.content = version.content;
      updateData.category = version.category;
      updateData.featuredImage = version.featuredImage;
      
      // 激活该版本
      await version.activate();
    }

    // 应用发布设置
    if (publishSettings.category) {
      updateData.category = publishSettings.category;
    }
    if (publishSettings.tags) {
      updateData.tags = publishSettings.tags;
    }
    if (publishSettings.featuredImage) {
      updateData.featuredImage = publishSettings.featuredImage;
    }

    await article.update(updateData);

    // 处理社交媒体发布
    if (scheduledPost.socialMediaSettings && Object.keys(scheduledPost.socialMediaSettings).length > 0) {
      await this.publishToSocialMedia(article, scheduledPost.socialMediaSettings);
    }

    // 发送通知
    await this.sendPublishNotifications(article, scheduledPost);
  }

  // 发布到社交媒体
  async publishToSocialMedia(article, socialMediaSettings) {
    try {
      // 这里可以集成各种社交媒体API
      // 例如：Twitter, Facebook, LinkedIn等
      
      if (socialMediaSettings.twitter && socialMediaSettings.twitter.enabled) {
        await this.publishToTwitter(article, socialMediaSettings.twitter);
      }
      
      if (socialMediaSettings.facebook && socialMediaSettings.facebook.enabled) {
        await this.publishToFacebook(article, socialMediaSettings.facebook);
      }
      
      logger.info(`Social media publishing completed for article ${article.id}`);
    } catch (error) {
      logger.error('Social media publishing failed:', error);
      // 不抛出错误，因为主要发布已经成功
    }
  }

  // 发布到Twitter（示例）
  async publishToTwitter(article, twitterSettings) {
    // 这里实现Twitter API集成
    logger.info(`Publishing to Twitter: ${article.title}`);
    
    const tweetText = twitterSettings.customText || 
      `${article.title} ${twitterSettings.includeLink ? `- ${process.env.FRONTEND_URL}/articles/${article.id}` : ''}`;
    
    // 实际的Twitter API调用会在这里
    // await twitterClient.post('statuses/update', { status: tweetText });
  }

  // 发布到Facebook（示例）
  async publishToFacebook(article, facebookSettings) {
    // 这里实现Facebook API集成
    logger.info(`Publishing to Facebook: ${article.title}`);
    
    // 实际的Facebook API调用会在这里
  }

  // 发送发布通知
  async sendPublishNotifications(article, scheduledPost) {
    try {
      // 通知作者
      const author = await User.findByPk(article.authorId);
      if (author) {
        // 这里可以发送邮件或应用内通知
        logger.info(`Notifying author ${author.username} about published article ${article.id}`);
      }

      // 通知调度创建者（如果不是作者）
      if (scheduledPost.scheduledBy !== article.authorId) {
        const scheduler = await User.findByPk(scheduledPost.scheduledBy);
        if (scheduler) {
          logger.info(`Notifying scheduler ${scheduler.username} about published article ${article.id}`);
        }
      }
    } catch (error) {
      logger.error('Failed to send publish notifications:', error);
    }
  }

  // 创建下一个循环调度
  async createNextRecurringSchedule(scheduledPost) {
    try {
      const pattern = scheduledPost.recurringPattern;
      if (!pattern || !pattern.frequency) {
        return;
      }

      let nextScheduledAt;
      const currentScheduledAt = new Date(scheduledPost.scheduledAt);

      switch (pattern.frequency) {
        case 'daily':
          nextScheduledAt = new Date(currentScheduledAt.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'weekly':
          nextScheduledAt = new Date(currentScheduledAt.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          nextScheduledAt = new Date(currentScheduledAt);
          nextScheduledAt.setMonth(nextScheduledAt.getMonth() + 1);
          break;
        default:
          return;
      }

      // 检查是否有结束日期限制
      if (pattern.endDate && nextScheduledAt > new Date(pattern.endDate)) {
        return;
      }

      // 创建新的调度
      await ScheduledPost.create({
        articleId: scheduledPost.articleId,
        versionId: scheduledPost.versionId,
        scheduledAt: nextScheduledAt,
        publishType: 'recurring',
        recurringPattern: pattern,
        publishSettings: scheduledPost.publishSettings,
        socialMediaSettings: scheduledPost.socialMediaSettings,
        authorId: scheduledPost.authorId,
        scheduledBy: scheduledPost.scheduledBy,
        priority: scheduledPost.priority
      });

      logger.info(`Created next recurring schedule for article ${scheduledPost.articleId} at ${nextScheduledAt}`);
    } catch (error) {
      logger.error('Failed to create next recurring schedule:', error);
    }
  }

  // 清理过期的调度记录
  async cleanupExpiredSchedules() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const result = await ScheduledPost.destroy({
        where: {
          status: ['published', 'cancelled'],
          updatedAt: {
            [require('../config/database').sequelize.Sequelize.Op.lt]: thirtyDaysAgo
          }
        }
      });

      if (result > 0) {
        logger.info(`Cleaned up ${result} expired schedule records`);
      }
    } catch (error) {
      logger.error('Failed to cleanup expired schedules:', error);
    }
  }

  // 获取服务状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      processingQueue: this.processingQueue.size,
      stats: this.stats
    };
  }

  // 重置统计
  resetStats() {
    this.stats = {
      processed: 0,
      published: 0,
      failed: 0,
      lastRun: null
    };
  }
}

// 创建单例实例
const schedulerService = new SchedulerService();

module.exports = schedulerService;
