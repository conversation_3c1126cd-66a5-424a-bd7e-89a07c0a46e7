const SearchIndex = require('../models/SearchIndex');
const SearchHistory = require('../models/SearchHistory');
const Article = require('../models/Article');
const User = require('../models/User');
const { sequelize } = require('../config/database');
const { logger } = require('../config/logger');

class SearchService {
  constructor() {
    this.stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
    ]);
    
    this.searchCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  // 主要搜索入口
  async search(query, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        userId = null,
        sessionId = null,
        searchType = 'fulltext',
        contentType = null,
        filters = {},
        sortBy = 'relevance',
        sortOrder = 'DESC',
        page = 1,
        limit = 20,
        language = 'zh',
        includeHighlights = true,
        includeFacets = false
      } = options;

      logger.info(`Search query: "${query}" by user ${userId || 'anonymous'}`);

      // 标准化查询
      const normalizedQuery = this.normalizeQuery(query, language);
      
      // 检查缓存
      const cacheKey = this.generateCacheKey(normalizedQuery, options);
      const cachedResult = this.getFromCache(cacheKey);
      if (cachedResult) {
        logger.info(`Cache hit for query: "${query}"`);
        return cachedResult;
      }

      let results;
      const offset = (page - 1) * limit;

      // 根据搜索类型执行搜索
      switch (searchType) {
        case 'fulltext':
          results = await this.fullTextSearch(normalizedQuery, {
            contentType,
            filters,
            sortBy,
            sortOrder,
            limit,
            offset,
            language
          });
          break;
          
        case 'keyword':
          results = await this.keywordSearch(normalizedQuery, {
            contentType,
            limit,
            offset
          });
          break;
          
        case 'tag':
          results = await this.tagSearch(normalizedQuery, {
            contentType,
            limit,
            offset
          });
          break;
          
        case 'user':
          results = await this.userSearch(normalizedQuery, {
            limit,
            offset
          });
          break;
          
        case 'advanced':
          results = await this.advancedSearch(normalizedQuery, filters, {
            sortBy,
            sortOrder,
            limit,
            offset,
            language
          });
          break;
          
        default:
          throw new Error(`Unknown search type: ${searchType}`);
      }

      // 添加高亮
      if (includeHighlights && searchType !== 'user') {
        results.rows = await this.addHighlights(results.rows, query, language);
      }

      // 添加分面信息
      let facets = {};
      if (includeFacets) {
        facets = await this.generateFacets(normalizedQuery, contentType, language);
      }

      const responseTime = Date.now() - startTime;
      
      const searchResult = {
        query,
        normalizedQuery,
        results: results.rows,
        total: results.count,
        page,
        limit,
        totalPages: Math.ceil(results.count / limit),
        responseTime,
        facets,
        suggestions: await this.getSearchSuggestions(normalizedQuery, userId)
      };

      // 缓存结果
      this.setCache(cacheKey, searchResult);

      // 记录搜索历史
      await this.logSearch({
        userId,
        sessionId,
        query,
        normalizedQuery,
        searchType,
        filters,
        resultCount: results.count,
        responseTime,
        language,
        isSuccessful: true
      });

      logger.info(`Search completed: ${results.count} results in ${responseTime}ms`);
      
      return searchResult;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('Search error:', error);
      
      // 记录失败的搜索
      await this.logSearch({
        userId: options.userId,
        sessionId: options.sessionId,
        query,
        normalizedQuery: this.normalizeQuery(query, options.language || 'zh'),
        searchType: options.searchType || 'fulltext',
        filters: options.filters || {},
        resultCount: 0,
        responseTime,
        language: options.language || 'zh',
        isSuccessful: false,
        errorMessage: error.message
      });
      
      throw error;
    }
  }

  // 全文搜索
  async fullTextSearch(query, options = {}) {
    const {
      contentType,
      filters,
      sortBy,
      sortOrder,
      limit,
      offset,
      language
    } = options;

    return await SearchIndex.fullTextSearch(query, {
      contentType,
      category: filters.category,
      authorId: filters.authorId,
      language,
      limit,
      offset,
      sortBy,
      sortOrder
    });
  }

  // 关键词搜索
  async keywordSearch(query, options = {}) {
    const keywords = this.extractKeywords(query);
    return await SearchIndex.searchByKeywords(keywords, options);
  }

  // 标签搜索
  async tagSearch(query, options = {}) {
    const tags = query.split(/[,\s]+/).filter(tag => tag.length > 0);
    return await SearchIndex.searchByTags(tags, options);
  }

  // 用户搜索
  async userSearch(query, options = {}) {
    const { limit, offset } = options;
    
    const users = await User.findAndCountAll({
      where: {
        [sequelize.Sequelize.Op.or]: [
          {
            username: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          },
          {
            firstName: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          },
          {
            lastName: {
              [sequelize.Sequelize.Op.iLike]: `%${query}%`
            }
          }
        ],
        emailVerified: true
      },
      attributes: ['id', 'username', 'firstName', 'lastName', 'avatar', 'bio'],
      order: [['username', 'ASC']],
      limit,
      offset
    });

    return users;
  }

  // 高级搜索
  async advancedSearch(query, filters, options = {}) {
    const {
      sortBy,
      sortOrder,
      limit,
      offset,
      language
    } = options;

    const where = {
      isActive: true,
      language
    };

    // 构建复杂的查询条件
    if (query) {
      where[sequelize.Sequelize.Op.and] = [
        sequelize.literal(`search_vector @@ plainto_tsquery('${language}', '${query}')`)
      ];
    }

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.authorId) {
      where.authorId = filters.authorId;
    }

    if (filters.dateRange) {
      where.publishedAt = {
        [sequelize.Sequelize.Op.between]: [
          new Date(filters.dateRange.start),
          new Date(filters.dateRange.end)
        ]
      };
    }

    if (filters.tags && filters.tags.length > 0) {
      where.tags = {
        [sequelize.Sequelize.Op.overlap]: filters.tags
      };
    }

    if (filters.minPopularity) {
      where.popularity = {
        [sequelize.Sequelize.Op.gte]: filters.minPopularity
      };
    }

    return await SearchIndex.findAndCountAll({
      where,
      order: this.buildOrderClause(sortBy, sortOrder),
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'firstName', 'lastName', 'avatar']
        }
      ]
    });
  }

  // 标准化查询
  normalizeQuery(query, language = 'zh') {
    if (!query) return '';
    
    // 转换为小写
    let normalized = query.toLowerCase().trim();
    
    // 移除特殊字符
    normalized = normalized.replace(/[^\w\s\u4e00-\u9fff]/g, ' ');
    
    // 分词并移除停用词
    const words = normalized.split(/\s+/).filter(word => 
      word.length > 0 && !this.stopWords.has(word)
    );
    
    return words.join(' ');
  }

  // 提取关键词
  extractKeywords(query) {
    const normalized = this.normalizeQuery(query);
    return normalized.split(/\s+/).filter(word => word.length > 1);
  }

  // 添加搜索高亮
  async addHighlights(results, query, language = 'zh') {
    const keywords = this.extractKeywords(query);
    
    return results.map(result => {
      if (result.title) {
        result.highlightedTitle = this.highlightText(result.title, keywords);
      }
      
      if (result.content) {
        result.highlightedContent = this.highlightText(
          result.content.substring(0, 200) + '...',
          keywords
        );
      }
      
      return result;
    });
  }

  // 高亮文本
  highlightText(text, keywords) {
    let highlighted = text;
    
    keywords.forEach(keyword => {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlighted = highlighted.replace(regex, '<mark>$1</mark>');
    });
    
    return highlighted;
  }

  // 生成搜索分面
  async generateFacets(query, contentType, language) {
    const facets = {};
    
    // 分类分面
    const categories = await sequelize.query(`
      SELECT category, COUNT(*) as count
      FROM search_indexes 
      WHERE search_vector @@ plainto_tsquery(:language, :query)
        AND is_active = true
        ${contentType ? 'AND content_type = :contentType' : ''}
      GROUP BY category
      ORDER BY count DESC
      LIMIT 10
    `, {
      replacements: { query, language, contentType },
      type: sequelize.QueryTypes.SELECT
    });
    
    facets.categories = categories;
    
    // 作者分面
    const authors = await sequelize.query(`
      SELECT u.username, u.id, COUNT(*) as count
      FROM search_indexes si
      JOIN users u ON u.id = si.author_id
      WHERE si.search_vector @@ plainto_tsquery(:language, :query)
        AND si.is_active = true
        ${contentType ? 'AND si.content_type = :contentType' : ''}
      GROUP BY u.id, u.username
      ORDER BY count DESC
      LIMIT 10
    `, {
      replacements: { query, language, contentType },
      type: sequelize.QueryTypes.SELECT
    });
    
    facets.authors = authors;
    
    return facets;
  }

  // 获取搜索建议
  async getSearchSuggestions(query, userId = null) {
    if (!query || query.length < 2) return [];
    
    return await SearchHistory.getSearchSuggestions(query, userId, 5);
  }

  // 构建排序子句
  buildOrderClause(sortBy, sortOrder) {
    const order = [];
    
    switch (sortBy) {
      case 'relevance':
        order.push(['relevanceScore', sortOrder]);
        order.push(['popularity', 'DESC']);
        break;
      case 'popularity':
        order.push(['popularity', sortOrder]);
        break;
      case 'quality':
        order.push(['qualityScore', sortOrder]);
        break;
      case 'date':
        order.push(['publishedAt', sortOrder]);
        break;
      default:
        order.push(['relevanceScore', 'DESC']);
    }
    
    return order;
  }

  // 记录搜索历史
  async logSearch(data) {
    try {
      await SearchHistory.logSearch(data);
    } catch (error) {
      logger.error('Error logging search:', error);
    }
  }

  // 缓存相关方法
  generateCacheKey(query, options) {
    const keyData = {
      query,
      contentType: options.contentType,
      filters: options.filters,
      sortBy: options.sortBy,
      page: options.page,
      limit: options.limit
    };
    
    return JSON.stringify(keyData);
  }

  getFromCache(key) {
    const cached = this.searchCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    this.searchCache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.searchCache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    // 清理过期缓存
    if (this.searchCache.size > 1000) {
      this.clearExpiredCache();
    }
  }

  clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.searchCache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.searchCache.delete(key);
      }
    }
  }

  // 清空缓存
  clearCache() {
    this.searchCache.clear();
  }

  // 获取搜索统计
  async getSearchStats(startDate, endDate) {
    return await SearchHistory.getSearchAnalytics(startDate, endDate);
  }
}

// 创建单例实例
const searchService = new SearchService();

module.exports = searchService;
