const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { logger } = require('../config/logger');
const User = require('../models/User');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // userId -> Set of WebSocket connections
    this.connectionStats = {
      totalConnections: 0,
      activeConnections: 0,
      messagesPerSecond: 0,
      lastMessageTime: Date.now()
    };
    this.messageQueue = new Map(); // userId -> Array of messages
    this.heartbeatInterval = null;
  }

  // 初始化WebSocket服务器
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();
    
    logger.info('WebSocket service initialized');
  }

  // 验证客户端连接
  async verifyClient(info) {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');
      
      if (!token) {
        logger.warn('WebSocket connection rejected: No token provided');
        return false;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.id);
      
      if (!user) {
        logger.warn('WebSocket connection rejected: Invalid user');
        return false;
      }

      // 将用户信息附加到请求对象
      info.req.user = user;
      return true;
    } catch (error) {
      logger.warn('WebSocket connection rejected:', error.message);
      return false;
    }
  }

  // 处理新连接
  handleConnection(ws, req) {
    const user = req.user;
    const userId = user.id;
    
    // 设置连接属性
    ws.userId = userId;
    ws.isAlive = true;
    ws.connectedAt = new Date();
    ws.lastActivity = new Date();

    // 添加到客户端映射
    if (!this.clients.has(userId)) {
      this.clients.set(userId, new Set());
    }
    this.clients.get(userId).add(ws);

    // 更新统计
    this.connectionStats.totalConnections++;
    this.connectionStats.activeConnections++;

    logger.info(`WebSocket connected: User ${userId} (${user.username})`);

    // 发送欢迎消息
    this.sendToConnection(ws, {
      type: 'connection_established',
      data: {
        userId: userId,
        timestamp: new Date().toISOString(),
        serverTime: Date.now()
      }
    });

    // 发送离线期间的消息
    this.sendQueuedMessages(userId);

    // 设置事件监听器
    ws.on('message', (data) => this.handleMessage(ws, data));
    ws.on('pong', () => this.handlePong(ws));
    ws.on('close', () => this.handleDisconnection(ws));
    ws.on('error', (error) => this.handleError(ws, error));
  }

  // 处理收到的消息
  handleMessage(ws, data) {
    try {
      ws.lastActivity = new Date();
      const message = JSON.parse(data);
      
      logger.debug(`WebSocket message from user ${ws.userId}:`, message.type);

      switch (message.type) {
        case 'ping':
          this.sendToConnection(ws, { type: 'pong', timestamp: Date.now() });
          break;
          
        case 'subscribe':
          this.handleSubscription(ws, message.data);
          break;
          
        case 'unsubscribe':
          this.handleUnsubscription(ws, message.data);
          break;
          
        case 'mark_notification_read':
          this.handleMarkNotificationRead(ws, message.data);
          break;
          
        case 'typing_start':
          this.handleTypingStart(ws, message.data);
          break;
          
        case 'typing_stop':
          this.handleTypingStop(ws, message.data);
          break;
          
        default:
          logger.warn(`Unknown WebSocket message type: ${message.type}`);
      }
    } catch (error) {
      logger.error('Error handling WebSocket message:', error);
      this.sendToConnection(ws, {
        type: 'error',
        message: 'Invalid message format'
      });
    }
  }

  // 处理订阅
  handleSubscription(ws, data) {
    if (!ws.subscriptions) {
      ws.subscriptions = new Set();
    }
    
    const { channel } = data;
    ws.subscriptions.add(channel);
    
    this.sendToConnection(ws, {
      type: 'subscribed',
      data: { channel }
    });
    
    logger.debug(`User ${ws.userId} subscribed to ${channel}`);
  }

  // 处理取消订阅
  handleUnsubscription(ws, data) {
    if (ws.subscriptions) {
      const { channel } = data;
      ws.subscriptions.delete(channel);
      
      this.sendToConnection(ws, {
        type: 'unsubscribed',
        data: { channel }
      });
      
      logger.debug(`User ${ws.userId} unsubscribed from ${channel}`);
    }
  }

  // 处理通知已读标记
  handleMarkNotificationRead(ws, data) {
    // 这里可以触发数据库更新
    this.sendToConnection(ws, {
      type: 'notification_read_confirmed',
      data: data
    });
  }

  // 处理输入状态开始
  handleTypingStart(ws, data) {
    const { conversationId, targetUserId } = data;
    
    if (targetUserId) {
      this.sendToUser(targetUserId, {
        type: 'typing_start',
        data: {
          userId: ws.userId,
          conversationId,
          timestamp: Date.now()
        }
      });
    }
  }

  // 处理输入状态结束
  handleTypingStop(ws, data) {
    const { conversationId, targetUserId } = data;
    
    if (targetUserId) {
      this.sendToUser(targetUserId, {
        type: 'typing_stop',
        data: {
          userId: ws.userId,
          conversationId,
          timestamp: Date.now()
        }
      });
    }
  }

  // 处理Pong响应
  handlePong(ws) {
    ws.isAlive = true;
    ws.lastActivity = new Date();
  }

  // 处理连接断开
  handleDisconnection(ws) {
    const userId = ws.userId;
    
    if (userId && this.clients.has(userId)) {
      this.clients.get(userId).delete(ws);
      
      if (this.clients.get(userId).size === 0) {
        this.clients.delete(userId);
      }
    }

    this.connectionStats.activeConnections--;
    
    logger.info(`WebSocket disconnected: User ${userId}`);
  }

  // 处理连接错误
  handleError(ws, error) {
    logger.error(`WebSocket error for user ${ws.userId}:`, error);
  }

  // 发送消息到特定连接
  sendToConnection(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
        this.updateMessageStats();
        return true;
      } catch (error) {
        logger.error('Error sending WebSocket message:', error);
        return false;
      }
    }
    return false;
  }

  // 发送消息到特定用户的所有连接
  sendToUser(userId, message) {
    const userConnections = this.clients.get(userId);
    
    if (!userConnections || userConnections.size === 0) {
      // 用户不在线，将消息加入队列
      this.queueMessage(userId, message);
      return false;
    }

    let sent = false;
    userConnections.forEach(ws => {
      if (this.sendToConnection(ws, message)) {
        sent = true;
      }
    });

    return sent;
  }

  // 广播消息到所有连接
  broadcast(message, filter = null) {
    let sentCount = 0;
    
    this.clients.forEach((connections, userId) => {
      if (filter && !filter(userId)) {
        return;
      }
      
      connections.forEach(ws => {
        if (this.sendToConnection(ws, message)) {
          sentCount++;
        }
      });
    });

    return sentCount;
  }

  // 发送通知
  sendNotification(userId, notification) {
    const message = {
      type: 'notification',
      data: {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt,
        actionUrl: notification.actionUrl,
        imageUrl: notification.imageUrl
      }
    };

    return this.sendToUser(userId, message);
  }

  // 将消息加入队列（用户离线时）
  queueMessage(userId, message) {
    if (!this.messageQueue.has(userId)) {
      this.messageQueue.set(userId, []);
    }
    
    const queue = this.messageQueue.get(userId);
    queue.push({
      ...message,
      queuedAt: Date.now()
    });

    // 限制队列大小
    if (queue.length > 100) {
      queue.shift();
    }
  }

  // 发送队列中的消息
  sendQueuedMessages(userId) {
    const queue = this.messageQueue.get(userId);
    
    if (!queue || queue.length === 0) {
      return;
    }

    queue.forEach(message => {
      this.sendToUser(userId, message);
    });

    this.messageQueue.delete(userId);
    logger.info(`Sent ${queue.length} queued messages to user ${userId}`);
  }

  // 开始心跳检测
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach((connections, userId) => {
        connections.forEach(ws => {
          if (!ws.isAlive) {
            logger.info(`Terminating inactive WebSocket connection for user ${userId}`);
            ws.terminate();
            return;
          }

          ws.isAlive = false;
          ws.ping();
        });
      });
    }, 30000); // 30秒心跳间隔
  }

  // 停止心跳检测
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 更新消息统计
  updateMessageStats() {
    const now = Date.now();
    const timeDiff = now - this.connectionStats.lastMessageTime;
    
    if (timeDiff >= 1000) {
      this.connectionStats.messagesPerSecond = 0;
      this.connectionStats.lastMessageTime = now;
    } else {
      this.connectionStats.messagesPerSecond++;
    }
  }

  // 获取连接统计
  getStats() {
    return {
      ...this.connectionStats,
      activeUsers: this.clients.size,
      queuedMessages: Array.from(this.messageQueue.values()).reduce((sum, queue) => sum + queue.length, 0)
    };
  }

  // 获取用户连接状态
  isUserOnline(userId) {
    return this.clients.has(userId) && this.clients.get(userId).size > 0;
  }

  // 关闭服务
  close() {
    this.stopHeartbeat();
    
    if (this.wss) {
      this.wss.close();
    }
    
    this.clients.clear();
    this.messageQueue.clear();
    
    logger.info('WebSocket service closed');
  }
}

// 创建单例实例
const webSocketService = new WebSocketService();

module.exports = webSocketService;
