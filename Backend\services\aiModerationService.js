const axios = require('axios');

class AIModerationService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.openaiBaseUrl = 'https://api.openai.com/v1';
    
    // 配置各种AI服务
    this.services = {
      openai: {
        enabled: !!this.openaiApiKey,
        endpoint: `${this.openaiBaseUrl}/moderations`,
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          'Content-Type': 'application/json'
        }
      }
    };

    // 内容分类映射
    this.categoryMapping = {
      // OpenAI 分类映射
      'hate': 'hate_speech',
      'hate/threatening': 'hate_speech',
      'harassment': 'harassment',
      'harassment/threatening': 'harassment',
      'self-harm': 'self_harm',
      'self-harm/intent': 'self_harm',
      'self-harm/instructions': 'self_harm',
      'sexual': 'adult_content',
      'sexual/minors': 'adult_content',
      'violence': 'violence',
      'violence/graphic': 'violence'
    };

    // 风险等级阈值
    this.riskThresholds = {
      low: 0.3,
      medium: 0.6,
      high: 0.8,
      critical: 0.95
    };
  }

  /**
   * 主要的内容审核方法
   * @param {string} content - 要审核的内容
   * @param {string} contentType - 内容类型 (text, image, video, audio)
   * @param {Object} options - 审核选项
   * @returns {Object} 审核结果
   */
  async moderateContent(content, contentType = 'text', options = {}) {
    try {
      const results = [];
      
      // 文本内容审核
      if (contentType === 'text' || contentType === 'draft' || contentType === 'article') {
        if (this.services.openai.enabled) {
          const openaiResult = await this.moderateWithOpenAI(content);
          results.push(openaiResult);
        }
        
        // 可以添加其他AI服务
        // const googleResult = await this.moderateWithGoogle(content);
        // results.push(googleResult);
      }

      // 图片内容审核
      if (contentType === 'image') {
        // 图片审核逻辑
        const imageResult = await this.moderateImage(content, options);
        results.push(imageResult);
      }

      // 合并所有结果
      return this.combineResults(results);
    } catch (error) {
      console.error('AI moderation error:', error);
      return {
        success: false,
        error: error.message,
        riskScore: 0,
        riskLevel: 'unknown',
        categories: [],
        flags: [],
        recommendations: ['Manual review required due to AI service error']
      };
    }
  }

  /**
   * 使用 OpenAI Moderation API 审核文本
   * @param {string} text - 要审核的文本
   * @returns {Object} 审核结果
   */
  async moderateWithOpenAI(text) {
    try {
      const response = await axios.post(
        this.services.openai.endpoint,
        { input: text },
        { headers: this.services.openai.headers }
      );

      const result = response.data.results[0];
      const categories = [];
      const flags = [];
      let maxScore = 0;

      // 处理分类结果
      Object.keys(result.categories).forEach(category => {
        if (result.categories[category]) {
          const mappedCategory = this.categoryMapping[category] || category;
          categories.push(mappedCategory);
          flags.push({
            category: mappedCategory,
            confidence: result.category_scores[category],
            source: 'openai'
          });
          maxScore = Math.max(maxScore, result.category_scores[category]);
        }
      });

      return {
        service: 'openai',
        flagged: result.flagged,
        categories,
        flags,
        riskScore: maxScore,
        riskLevel: this.calculateRiskLevel(maxScore),
        details: result
      };
    } catch (error) {
      console.error('OpenAI moderation error:', error);
      throw new Error(`OpenAI moderation failed: ${error.message}`);
    }
  }

  /**
   * 图片内容审核（示例实现）
   * @param {string} imagePath - 图片路径或URL
   * @param {Object} options - 审核选项
   * @returns {Object} 审核结果
   */
  async moderateImage(imagePath, options = {}) {
    // 这里可以集成 Google Cloud Vision API, AWS Rekognition 等
    // 目前返回基础结果
    return {
      service: 'image_analyzer',
      flagged: false,
      categories: [],
      flags: [],
      riskScore: 0,
      riskLevel: 'low',
      details: {
        message: 'Image moderation not implemented yet'
      }
    };
  }

  /**
   * 合并多个AI服务的结果
   * @param {Array} results - 各个服务的结果数组
   * @returns {Object} 合并后的结果
   */
  combineResults(results) {
    if (results.length === 0) {
      return {
        success: false,
        riskScore: 0,
        riskLevel: 'unknown',
        categories: [],
        flags: [],
        recommendations: ['No AI services available']
      };
    }

    // 取最高风险分数
    const maxRiskScore = Math.max(...results.map(r => r.riskScore || 0));
    const riskLevel = this.calculateRiskLevel(maxRiskScore);
    
    // 合并所有分类
    const allCategories = [...new Set(results.flatMap(r => r.categories || []))];
    
    // 合并所有标记
    const allFlags = results.flatMap(r => r.flags || []);
    
    // 检查是否有任何服务标记为有问题
    const flagged = results.some(r => r.flagged);

    // 生成建议
    const recommendations = this.generateRecommendations(riskLevel, allCategories, flagged);

    return {
      success: true,
      flagged,
      riskScore: maxRiskScore,
      riskLevel,
      categories: allCategories,
      flags: allFlags,
      recommendations,
      serviceResults: results
    };
  }

  /**
   * 根据风险分数计算风险等级
   * @param {number} score - 风险分数 (0-1)
   * @returns {string} 风险等级
   */
  calculateRiskLevel(score) {
    if (score >= this.riskThresholds.critical) return 'critical';
    if (score >= this.riskThresholds.high) return 'high';
    if (score >= this.riskThresholds.medium) return 'medium';
    if (score >= this.riskThresholds.low) return 'low';
    return 'safe';
  }

  /**
   * 生成审核建议
   * @param {string} riskLevel - 风险等级
   * @param {Array} categories - 问题分类
   * @param {boolean} flagged - 是否被标记
   * @returns {Array} 建议列表
   */
  generateRecommendations(riskLevel, categories, flagged) {
    const recommendations = [];

    if (!flagged && riskLevel === 'safe') {
      recommendations.push('Content appears safe for publication');
      return recommendations;
    }

    switch (riskLevel) {
      case 'critical':
        recommendations.push('IMMEDIATE ACTION REQUIRED: Content contains severe violations');
        recommendations.push('Block publication and escalate to senior moderators');
        recommendations.push('Consider account suspension if pattern of violations');
        break;
      
      case 'high':
        recommendations.push('High-risk content detected - requires manual review');
        recommendations.push('Do not auto-approve');
        recommendations.push('Assign to experienced moderator');
        break;
      
      case 'medium':
        recommendations.push('Moderate risk detected - review recommended');
        recommendations.push('Check context and intent');
        break;
      
      case 'low':
        recommendations.push('Low risk detected - brief review sufficient');
        break;
    }

    // 基于分类的具体建议
    if (categories.includes('hate_speech')) {
      recommendations.push('Review for hate speech and discriminatory language');
    }
    if (categories.includes('violence')) {
      recommendations.push('Check for violent content and threats');
    }
    if (categories.includes('adult_content')) {
      recommendations.push('Verify age-appropriate content guidelines');
    }
    if (categories.includes('harassment')) {
      recommendations.push('Review for harassment and bullying behavior');
    }

    return recommendations;
  }

  /**
   * 批量审核内容
   * @param {Array} contents - 内容数组
   * @param {string} contentType - 内容类型
   * @returns {Array} 审核结果数组
   */
  async moderateMultiple(contents, contentType = 'text') {
    const results = [];
    
    // 并发处理，但限制并发数量
    const batchSize = 5;
    for (let i = 0; i < contents.length; i += batchSize) {
      const batch = contents.slice(i, i + batchSize);
      const batchPromises = batch.map(content => 
        this.moderateContent(content, contentType)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map(r => 
        r.status === 'fulfilled' ? r.value : { success: false, error: r.reason }
      ));
    }
    
    return results;
  }

  /**
   * 获取服务状态
   * @returns {Object} 各个AI服务的状态
   */
  getServiceStatus() {
    return {
      openai: {
        enabled: this.services.openai.enabled,
        configured: !!this.openaiApiKey
      }
      // 可以添加其他服务状态
    };
  }
}

module.exports = new AIModerationService();
