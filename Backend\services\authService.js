const { supabase } = require('../config/supabase');
const mockSupabaseAuth = require('./mockSupabaseAuth');

class AuthService {
  constructor() {
    // 检查是否使用真实的 Supabase 还是模拟认证
    this.useRealSupabase = process.env.NODE_ENV === 'production' || process.env.USE_REAL_SUPABASE === 'true';
    console.log(`🔐 AuthService initialized with ${this.useRealSupabase ? 'real Supabase' : 'mock'} authentication`);
  }

  // 用户注册
  async register(userData) {
    try {
      const { email, password, username, displayName } = userData;

      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              username,
              display_name: displayName || username,
              avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName || username)}&background=6366f1&color=fff&size=128`
            }
          }
        });

        if (error) {
          throw new Error(error.message);
        }

        return {
          user: data.user,
          session: data.session,
          needsEmailConfirmation: !data.session
        };
      } else {
        // 使用模拟Supabase Auth
        const { data, error } = await mockSupabaseAuth.signUp(email, password, {
          username,
          display_name: displayName || username,
          avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName || username)}&background=6366f1&color=fff&size=128`
        });

        if (error) {
          throw new Error(error.message);
        }

        return {
          user: data.user,
          session: data.session,
          needsEmailConfirmation: !data.session
        };
      }

    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // 用户登录
  async login(email, password) {
    try {
      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (error) {
          throw new Error(error.message);
        }

        return {
          user: data.user,
          session: data.session
        };
      } else {
        // 使用模拟Supabase Auth
        const { data, error } = await mockSupabaseAuth.signInWithPassword(email, password);

        if (error) {
          throw new Error(error.message);
        }

        return {
          user: data.user,
          session: data.session
        };
      }

    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // 获取用户信息
  async getUser(userId) {
    try {
      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        const { data, error } = await supabase.auth.getUser();

        if (error) {
          throw new Error(error.message);
        }

        return data.user;
      } else {
        // 使用模拟Supabase Auth
        const { data, error } = await mockSupabaseAuth.getUserProfile(userId);

        if (error) {
          throw new Error(error.message);
        }

        return data;
      }
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }

  // 更新用户资料
  async updateProfile(userId, updateData) {
    try {
      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        const { data, error } = await supabase.auth.updateUser({
          data: updateData
        });

        if (error) {
          throw new Error(error.message);
        }

        return data.user;
      } else {
        // 使用模拟Supabase Auth
        const { data, error } = await mockSupabaseAuth.updateUserProfile(userId, updateData);

        if (error) {
          throw new Error(error.message);
        }

        return data;
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  // 修改密码
  async changePassword(newPassword) {
    try {
      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        const { data, error } = await supabase.auth.updateUser({
          password: newPassword
        });

        if (error) {
          throw error;
        }

        return data;
      } else {
        // 模拟认证不支持密码修改
        throw new Error('Password change not supported in mock mode');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // 发送密码重置邮件
  async resetPassword(email) {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password`
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // 验证会话
  async verifySession(accessToken) {
    try {
      const { data, error } = await supabase.auth.getUser(accessToken);

      if (error) {
        throw error;
      }

      return data.user;
    } catch (error) {
      console.error('Verify session error:', error);
      throw error;
    }
  }

  // 登出
  async logout() {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // 刷新会话
  async refreshSession(refreshToken) {
    try {
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: refreshToken
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Refresh session error:', error);
      throw error;
    }
  }

  // 检查用户名是否可用
  async checkUsernameAvailability(username) {
    try {
      const { exists, error } = await mockSupabaseAuth.checkUsername(username);

      if (error) {
        console.error('Check username error:', error);
        return false;
      }

      return !exists;
    } catch (error) {
      console.error('Check username error:', error);
      return false;
    }
  }

  // 检查邮箱是否可用
  async checkEmailAvailability(email) {
    try {
      if (this.useRealSupabase) {
        // 使用真实的 Supabase Auth
        // 注意：Supabase 不提供直接检查邮箱的API，我们需要尝试注册来检查
        // 这里我们返回true，因为实际检查会在注册时进行
        return true;
      } else {
        // 使用模拟Supabase Auth
        const { exists, error } = await mockSupabaseAuth.checkEmail(email);

        if (error) {
          console.error('Check email error:', error);
          return false;
        }

        return !exists;
      }
    } catch (error) {
      console.error('Check email error:', error);
      return false;
    }
  }

  // 搜索用户
  async searchUsers(query, limit = 10) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, display_name, avatar_url, is_verified')
        .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
        .eq('is_active', true)
        .order('followers_count', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Search users error:', error);
      throw error;
    }
  }
}

module.exports = new AuthService();
