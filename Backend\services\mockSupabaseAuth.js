const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 模拟用户数据存储
let users = [];

// 初始化用户数据
async function initializeUsers() {
  if (users.length === 0) {
    users = [
      {
        id: '550e8400-e29b-41d4-a716-446655440000',
        email: '<EMAIL>',
        username: 'admin',
        display_name: 'Administrator',
        avatar_url: 'https://ui-avatars.com/api/?name=Administrator&background=6366f1&color=fff&size=128',
        role: 'admin',
        bio: 'Platform administrator',
        is_verified: true,
        is_active: true,
        password_hash: await bcrypt.hash('admin123456', 12),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        email: '<EMAIL>',
        username: 'demo_user',
        display_name: 'Demo User',
        avatar_url: 'https://ui-avatars.com/api/?name=Demo+User&background=6366f1&color=fff&size=128',
        role: 'user',
        bio: 'This is a demo user account for testing purposes.',
        is_verified: true,
        is_active: true,
        password_hash: await bcrypt.hash('demo123456', 12),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 生成JWT Token (与Supabase认证路由格式一致)
function generateToken(user) {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role || 'user'
    },
    process.env.JWT_SECRET || 'mock-jwt-secret',
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
}

class MockSupabaseAuth {
  // 用户注册
  async signUp(email, password, userData = {}) {
    try {
      await initializeUsers();
      // 检查邮箱是否已存在
      const existingUser = users.find(u => u.email === email.toLowerCase());
      if (existingUser) {
        return {
          data: { user: null, session: null },
          error: { message: 'User already registered' }
        };
      }

      // 检查用户名是否已存在
      if (userData.username) {
        const existingUsername = users.find(u => u.username === userData.username.toLowerCase());
        if (existingUsername) {
          return {
            data: { user: null, session: null },
            error: { message: 'Username already taken' }
          };
        }
      }

      // 创建新用户
      const userId = generateUUID();
      const passwordHash = await bcrypt.hash(password, 12);
      
      const newUser = {
        id: userId,
        email: email.toLowerCase(),
        username: userData.username?.toLowerCase() || `user_${Date.now()}`,
        display_name: userData.display_name || userData.username || 'New User',
        avatar_url: userData.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.display_name || 'User')}&background=6366f1&color=fff&size=128`,
        role: userData.role || 'user',
        bio: userData.bio || '',
        is_verified: false,
        is_active: true,
        password_hash: passwordHash,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      users.push(newUser);

      // 生成session
      const token = generateToken(newUser);
      const session = {
        access_token: token,
        refresh_token: generateToken(newUser),
        expires_in: 7 * 24 * 60 * 60,
        token_type: 'bearer',
        user: {
          id: newUser.id,
          email: newUser.email,
          email_confirmed_at: new Date().toISOString(),
          user_metadata: {
            username: newUser.username,
            display_name: newUser.display_name
          }
        }
      };

      return {
        data: { user: session.user, session },
        error: null
      };

    } catch (error) {
      return {
        data: { user: null, session: null },
        error: { message: error.message }
      };
    }
  }

  // 用户登录
  async signInWithPassword(email, password) {
    try {
      await initializeUsers();
      const user = users.find(u => u.email === email.toLowerCase());
      
      if (!user) {
        return {
          data: { user: null, session: null },
          error: { message: 'Invalid login credentials' }
        };
      }

      if (!user.is_active) {
        return {
          data: { user: null, session: null },
          error: { message: 'Account is deactivated' }
        };
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        return {
          data: { user: null, session: null },
          error: { message: 'Invalid login credentials' }
        };
      }

      // 更新最后登录时间
      user.last_login_at = new Date().toISOString();

      // 生成session
      const token = generateToken(user);
      const session = {
        access_token: token,
        refresh_token: generateToken(user),
        expires_in: 7 * 24 * 60 * 60,
        token_type: 'bearer',
        user: {
          id: user.id,
          email: user.email,
          email_confirmed_at: user.is_verified ? new Date().toISOString() : null,
          user_metadata: {
            username: user.username,
            display_name: user.display_name
          }
        }
      };

      return {
        data: { user: session.user, session },
        error: null
      };

    } catch (error) {
      return {
        data: { user: null, session: null },
        error: { message: error.message }
      };
    }
  }

  // 获取用户信息
  async getUser(token) {
    try {
      await initializeUsers(); // 确保用户已初始化
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mock-jwt-secret');
      // 支持新的token格式 (id) 和旧的格式 (sub)
      const userId = decoded.id || decoded.sub;

      console.log('🔍 Mock getUser debug:');
      console.log('  - Decoded token:', JSON.stringify(decoded, null, 2));
      console.log('  - Looking for user ID:', userId);
      console.log('  - Available users:', users.map(u => ({ id: u.id, email: u.email })));

      const user = users.find(u => u.id === userId);

      if (!user) {
        console.log('  - User not found!');
        return {
          data: { user: null },
          error: { message: 'User not found' }
        };
      }

      console.log('  - User found:', user.email);

      return {
        data: {
          user: {
            id: user.id,
            email: user.email,
            email_confirmed_at: user.is_verified ? new Date().toISOString() : null,
            user_metadata: {
              username: user.username,
              display_name: user.display_name
            }
          }
        },
        error: null
      };

    } catch (error) {
      return {
        data: { user: null },
        error: { message: 'Invalid token' }
      };
    }
  }

  // 获取用户资料
  async getUserProfile(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) {
      return { data: null, error: { message: 'User not found' } };
    }

    const { password_hash, ...profile } = user;
    return { data: profile, error: null };
  }

  // 更新用户资料
  async updateUserProfile(userId, updates) {
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return { data: null, error: { message: 'User not found' } };
    }

    users[userIndex] = {
      ...users[userIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    const { password_hash, ...profile } = users[userIndex];
    return { data: profile, error: null };
  }

  // 检查用户名可用性
  async checkUsername(username) {
    const exists = users.some(u => u.username === username.toLowerCase());
    return { exists, error: null };
  }

  // 检查邮箱可用性
  async checkEmail(email) {
    const exists = users.some(u => u.email === email.toLowerCase());
    return { exists, error: null };
  }

  // 登出
  async signOut() {
    return { error: null };
  }

  // 重置密码
  async resetPasswordForEmail(email) {
    const user = users.find(u => u.email === email.toLowerCase());
    if (!user) {
      // 为了安全，即使用户不存在也返回成功
      return { data: {}, error: null };
    }

    console.log(`Password reset email would be sent to: ${email}`);
    return { data: {}, error: null };
  }

  // 获取所有用户（仅用于测试）
  getAllUsers() {
    return users.map(({ password_hash, ...user }) => user);
  }
}

module.exports = new MockSupabaseAuth();
