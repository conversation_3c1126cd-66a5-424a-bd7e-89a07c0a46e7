const Notification = require('../models/Notification');
const NotificationPreference = require('../models/NotificationPreference');
const socketService = require('./socketService');
const emailService = require('./emailService');
const pushService = require('./pushService');
const webSocketService = require('./WebSocketService');

class NotificationService {
  constructor() {
    this.socketService = socketService;
    this.emailService = emailService;
    this.pushService = pushService;
  }

  /**
   * 创建并发送通知
   * @param {Object} notificationData - 通知数据
   * @param {number} notificationData.userId - 接收用户ID
   * @param {string} notificationData.type - 通知类型
   * @param {string} notificationData.title - 通知标题
   * @param {string} notificationData.content - 通知内容
   * @param {Object} notificationData.data - 通知相关数据
   * @param {string} notificationData.priority - 优先级 (low, normal, high, urgent)
   * @param {Array} notificationData.channels - 发送渠道 (web, email, push)
   * @param {string} notificationData.actionUrl - 点击跳转URL
   * @param {string} notificationData.imageUrl - 通知图片URL
   * @param {Date} notificationData.scheduledAt - 计划发送时间
   * @param {Date} notificationData.expiresAt - 过期时间
   * @param {Object} notificationData.metadata - 额外元数据
   */
  async createAndSendNotification(notificationData) {
    try {
      const {
        userId,
        type,
        title,
        content,
        data = {},
        priority = 'normal',
        channels = ['web'],
        actionUrl = null,
        imageUrl = null,
        scheduledAt = null,
        expiresAt = null,
        metadata = {}
      } = notificationData;

      // 检查用户通知偏好
      const enabledChannels = await this.getEnabledChannels(userId, type, channels);
      
      if (enabledChannels.length === 0) {
        console.log(`📵 User ${userId} has disabled all channels for ${type} notifications`);
        return null;
      }

      // 创建通知记录
      const notification = await Notification.create({
        userId,
        type,
        title,
        content,
        data,
        priority,
        channels: enabledChannels,
        actionUrl,
        imageUrl,
        scheduledAt,
        expiresAt,
        metadata,
        sentAt: scheduledAt ? null : new Date()
      });

      // 如果是计划发送，则不立即发送
      if (scheduledAt && scheduledAt > new Date()) {
        console.log(`⏰ Notification scheduled for ${scheduledAt}: ${title}`);
        return notification;
      }

      // 立即发送通知
      await this.sendNotification(notification, enabledChannels);

      return notification;
    } catch (error) {
      console.error('Error creating and sending notification:', error);
      throw error;
    }
  }

  /**
   * 发送通知到各个渠道
   */
  async sendNotification(notification, channels) {
    const results = {
      web: false,
      email: false,
      push: false
    };

    try {
      // 发送Web实时通知
      if (channels.includes('web')) {
        results.web = await this.sendWebNotification(notification);
      }

      // 发送邮件通知
      if (channels.includes('email')) {
        results.email = await this.sendEmailNotification(notification);
      }

      // 发送推送通知
      if (channels.includes('push')) {
        results.push = await this.sendPushNotification(notification);
      }

      console.log(`📤 Notification sent - Web: ${results.web}, Email: ${results.email}, Push: ${results.push}`);
      return results;
    } catch (error) {
      console.error('Error sending notification:', error);
      return results;
    }
  }

  /**
   * 发送Web实时通知
   */
  async sendWebNotification(notification) {
    try {
      return await this.socketService.sendNotificationToUser(notification.userId, notification);
    } catch (error) {
      console.error('Error sending web notification:', error);
      return false;
    }
  }

  /**
   * 发送邮件通知
   */
  async sendEmailNotification(notification) {
    try {
      // 获取用户信息
      const User = require('../models/User');
      const user = await User.findByPk(notification.userId, {
        attributes: ['id', 'username', 'email']
      });

      if (!user || !user.email) {
        console.log(`📧 No email address for user ${notification.userId}`);
        return false;
      }

      // 发送邮件通知
      const success = await this.emailService.sendNotificationEmail(user, notification);

      if (success) {
        console.log(`📧 Email notification sent to ${user.email}: ${notification.title}`);
        return true;
      } else {
        console.log(`📧 Failed to send email notification to ${user.email}`);
        return false;
      }
    } catch (error) {
      console.error('Error sending email notification:', error);
      return false;
    }
  }

  /**
   * 发送推送通知
   */
  async sendPushNotification(notification) {
    try {
      const success = await this.pushService.sendPushNotification(notification.userId, notification);

      if (success) {
        console.log(`📱 Push notification sent to user ${notification.userId}: ${notification.title}`);
        return true;
      } else {
        console.log(`📱 Failed to send push notification to user ${notification.userId}`);
        return false;
      }
    } catch (error) {
      console.error('Error sending push notification:', error);
      return false;
    }
  }

  /**
   * 获取用户启用的通知渠道
   */
  async getEnabledChannels(userId, notificationType, requestedChannels) {
    try {
      const enabledChannels = await NotificationPreference.getEnabledChannels(userId, notificationType);
      
      // 返回请求的渠道和用户启用的渠道的交集
      return requestedChannels.filter(channel => enabledChannels.includes(channel));
    } catch (error) {
      console.error('Error getting enabled channels:', error);
      return ['web']; // 默认只启用web通知
    }
  }

  /**
   * 批量发送通知给多个用户
   */
  async sendBulkNotifications(userIds, notificationData) {
    const results = [];
    
    for (const userId of userIds) {
      try {
        const notification = await this.createAndSendNotification({
          ...notificationData,
          userId
        });
        results.push({ userId, success: true, notification });
      } catch (error) {
        console.error(`Error sending notification to user ${userId}:`, error);
        results.push({ userId, success: false, error: error.message });
      }
    }

    return results;
  }

  /**
   * 批量发送邮件通知
   */
  async sendBulkEmailNotifications(notifications) {
    try {
      // 获取所有用户信息
      const User = require('../models/User');
      const userIds = [...new Set(notifications.map(n => n.userId))];
      const users = await User.findAll({
        where: { id: userIds },
        attributes: ['id', 'username', 'email']
      });

      // 创建用户映射
      const userMap = {};
      users.forEach(user => {
        userMap[user.id] = user;
      });

      // 准备邮件数据
      const emailNotifications = notifications.map(notification => ({
        ...notification,
        user: userMap[notification.userId]
      })).filter(n => n.user && n.user.email);

      // 批量发送邮件
      const results = await this.emailService.sendBulkNotificationEmails(emailNotifications);

      console.log(`📧 Bulk email notifications sent: ${results.filter(r => r.success).length}/${results.length} successful`);
      return results;
    } catch (error) {
      console.error('Error sending bulk email notifications:', error);
      return [];
    }
  }

  /**
   * 发送系统通知给所有用户
   */
  async sendSystemNotification(notificationData) {
    try {
      // 这里可以实现给所有用户发送系统通知的逻辑
      // 可以通过Socket.IO广播或者数据库批量插入
      const notification = await Notification.create({
        userId: null, // 系统通知可能没有特定用户
        ...notificationData,
        type: 'system',
        sentAt: new Date()
      });

      // 通过Socket.IO广播给所有在线用户
      await this.socketService.broadcastNotification(notification);

      return notification;
    } catch (error) {
      console.error('Error sending system notification:', error);
      throw error;
    }
  }

  /**
   * 处理计划发送的通知
   */
  async processScheduledNotifications() {
    try {
      const now = new Date();
      const scheduledNotifications = await Notification.findAll({
        where: {
          scheduledAt: {
            [require('sequelize').Op.lte]: now
          },
          sentAt: null
        }
      });

      console.log(`⏰ Processing ${scheduledNotifications.length} scheduled notifications`);

      for (const notification of scheduledNotifications) {
        try {
          const enabledChannels = await this.getEnabledChannels(
            notification.userId, 
            notification.type, 
            notification.channels
          );

          if (enabledChannels.length > 0) {
            await this.sendNotification(notification, enabledChannels);
            notification.sentAt = new Date();
            await notification.save();
          }
        } catch (error) {
          console.error(`Error processing scheduled notification ${notification.id}:`, error);
        }
      }

      return scheduledNotifications.length;
    } catch (error) {
      console.error('Error processing scheduled notifications:', error);
      return 0;
    }
  }

  /**
   * 清理过期通知
   */
  async cleanupExpiredNotifications() {
    try {
      const deletedCount = await Notification.cleanupExpired();
      if (deletedCount > 0) {
        console.log(`🗑️ Cleaned up ${deletedCount} expired notifications`);
      }
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      return 0;
    }
  }

  /**
   * 创建特定类型的通知的便捷方法
   */
  async createLikeNotification(userId, likerName, articleTitle, articleId) {
    return await this.createAndSendNotification({
      userId,
      type: 'like',
      title: '新的点赞',
      content: `${likerName} 点赞了您的文章《${articleTitle}》`,
      data: { articleId, likerName },
      actionUrl: `/article/${articleId}`,
      priority: 'low'
    });
  }

  async createCommentNotification(userId, commenterName, articleTitle, articleId, commentContent) {
    return await this.createAndSendNotification({
      userId,
      type: 'comment',
      title: '新的评论',
      content: `${commenterName} 评论了您的文章《${articleTitle}》: ${commentContent.substring(0, 50)}...`,
      data: { articleId, commenterName, commentContent },
      actionUrl: `/article/${articleId}#comments`,
      priority: 'normal'
    });
  }

  async createFollowNotification(userId, followerName, followerId) {
    return await this.createAndSendNotification({
      userId,
      type: 'follow',
      title: '新的关注者',
      content: `${followerName} 开始关注您`,
      data: { followerId, followerName },
      actionUrl: `/profile/${followerId}`,
      priority: 'normal'
    });
  }

  async createMessageNotification(userId, senderName, senderId, messagePreview) {
    return await this.createAndSendNotification({
      userId,
      type: 'message',
      title: '新消息',
      content: `${senderName}: ${messagePreview}`,
      data: { senderId, senderName },
      actionUrl: `/messages/${senderId}`,
      priority: 'high',
      channels: ['web', 'push'] // 私信通常不发邮件
    });
  }

  /**
   * 获取用户通知偏好
   */
  async getNotificationPreferences(userId) {
    try {
      let preferences = await NotificationPreference.findOne({
        where: { userId: userId }
      });

      if (!preferences) {
        // 创建默认偏好设置
        preferences = await NotificationPreference.create({
          userId: userId,
          preferences: {
            like: { web: true, email: true, push: true },
            comment: { web: true, email: true, push: true },
            follow: { web: true, email: false, push: true },
            message: { web: true, email: true, push: true },
            system: { web: true, email: false, push: true }
          },
          globalSettings: {
            enabled: true,
            quietHours: {
              enabled: false,
              start: '22:00',
              end: '08:00'
            }
          },
          emailSettings: {
            enabled: true,
            frequency: 'immediate',
            types: ['like', 'comment', 'follow', 'message']
          },
          pushSettings: {
            enabled: true,
            types: ['like', 'comment', 'follow', 'message', 'system']
          }
        });
      }

      return preferences;
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      throw error;
    }
  }

  /**
   * 更新用户通知偏好
   */
  async updateNotificationPreferences(userId, newPreferences) {
    try {
      let preferences = await NotificationPreference.findOne({
        where: { userId: userId }
      });

      if (!preferences) {
        // 创建新的偏好设置
        preferences = await NotificationPreference.create({
          userId: userId,
          ...newPreferences,
          lastUpdated: new Date()
        });
      } else {
        // 更新现有偏好设置
        await preferences.update({
          ...newPreferences,
          lastUpdated: new Date()
        });
      }

      return preferences;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  /**
   * 标记通知为已读
   */
  async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        where: { id: notificationId, userId: userId }
      });

      if (!notification) {
        throw new Error('Notification not found');
      }

      await notification.update({
        isRead: true,
        readAt: new Date()
      });

      return notification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * 标记所有通知为已读
   */
  async markAllAsRead(userId) {
    try {
      const [updatedCount] = await Notification.update(
        {
          isRead: true,
          readAt: new Date()
        },
        {
          where: {
            userId: userId,
            isRead: false
          }
        }
      );

      return updatedCount;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }
}

// 创建单例实例
const notificationService = new NotificationService();

module.exports = notificationService;
