/**
 * 简单的测试服务器 - 用于验证API路由
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟文章数据
const mockArticles = [
  {
    id: "1",
    title: "The Future of Artificial Intelligence",
    content: `
      <div class="prose prose-lg max-w-none">
        <h1>The Future of Artificial Intelligence</h1>
        <p>Artificial Intelligence is rapidly transforming our world. From healthcare to transportation, AI is revolutionizing how we live and work.</p>
        <h2>Current Applications</h2>
        <p>Today, AI is being used in various fields including:</p>
        <ul>
          <li>Healthcare diagnostics</li>
          <li>Autonomous vehicles</li>
          <li>Natural language processing</li>
          <li>Computer vision</li>
        </ul>
        <h2>Future Prospects</h2>
        <p>The future of AI holds even more promise. We can expect to see advances in general AI, quantum computing integration, and more sophisticated machine learning algorithms.</p>
        <p>As we move forward, it's important to consider the ethical implications and ensure that AI development benefits all of humanity.</p>
      </div>
    `,
    excerpt: "Exploring the current state and future potential of artificial intelligence technology.",
    author: {
      id: 1,
      name: "Dr. <PERSON> <PERSON>",
      username: "sarahchen",
      avatar: "https://ui-avatars.com/api/?name=Sarah+Chen&background=6366f1&color=fff&size=64",
      bio: "AI researcher and technology enthusiast with over 10 years of experience in machine learning."
    },
    category: "Technology",
    tags: ["AI", "Technology", "Future"],
    publishedAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
    readTime: 8,
    image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop",
    likes: 124,
    comments: 18,
    shares: 32,
    views: 1250,
    published: true
  },
  {
    id: "2",
    title: "Sustainable Business Practices",
    content: `
      <div class="prose prose-lg max-w-none">
        <h1>Sustainable Business Practices for the Modern Era</h1>
        <p>In today's world, sustainability is not just a buzzword—it's a business imperative. Companies that embrace sustainable practices are not only helping the environment but also improving their bottom line.</p>
        <h2>Why Sustainability Matters</h2>
        <p>Sustainable business practices offer numerous benefits:</p>
        <ul>
          <li>Reduced operational costs</li>
          <li>Enhanced brand reputation</li>
          <li>Improved employee satisfaction</li>
          <li>Better risk management</li>
        </ul>
        <h2>Implementation Strategies</h2>
        <p>Companies can implement sustainability through various approaches including energy efficiency, waste reduction, sustainable sourcing, and green technology adoption.</p>
        <p>The key is to start small and gradually expand sustainable practices across all business operations.</p>
      </div>
    `,
    excerpt: "How companies are adapting to environmental challenges while maintaining profitability.",
    author: {
      id: 2,
      name: "Michael Rodriguez",
      username: "mrodriguez",
      avatar: "https://ui-avatars.com/api/?name=Michael+Rodriguez&background=10b981&color=fff&size=64",
      bio: "Business sustainability consultant and environmental advocate."
    },
    category: "Business",
    tags: ["Sustainability", "Business", "Environment"],
    publishedAt: "2024-01-14T14:20:00Z",
    updatedAt: "2024-01-14T14:20:00Z",
    readTime: 6,
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop",
    likes: 89,
    comments: 12,
    shares: 24,
    views: 890,
    published: true
  },
  {
    id: "3",
    title: "Quantum Computing Breakthrough",
    content: `
      <div class="prose prose-lg max-w-none">
        <h1>Breakthrough in Quantum Computing Research</h1>
        <p>Scientists have achieved a new milestone in quantum computing that could revolutionize data processing and encryption as we know it.</p>
        <h2>The Discovery</h2>
        <p>Researchers have successfully demonstrated quantum supremacy in a practical application, solving complex problems that would take classical computers thousands of years.</p>
        <h2>Implications</h2>
        <p>This breakthrough has significant implications for:</p>
        <ul>
          <li>Cryptography and security</li>
          <li>Drug discovery</li>
          <li>Financial modeling</li>
          <li>Climate simulation</li>
        </ul>
        <p>While quantum computers are still in their early stages, this advancement brings us closer to a quantum-powered future.</p>
      </div>
    `,
    excerpt: "Scientists achieve new milestone in quantum computing with revolutionary implications.",
    author: {
      id: 3,
      name: "Prof. Emily Watson",
      username: "ewatson",
      avatar: "https://ui-avatars.com/api/?name=Emily+Watson&background=8b5cf6&color=fff&size=64",
      bio: "Quantum physics professor and researcher at leading technology institute."
    },
    category: "Science",
    tags: ["Quantum Computing", "Science", "Research"],
    publishedAt: "2024-01-13T09:15:00Z",
    updatedAt: "2024-01-13T09:15:00Z",
    readTime: 10,
    image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop",
    likes: 156,
    comments: 24,
    shares: 45,
    views: 1680,
    published: true
  }
];

// 文章列表API
app.get('/api/articles', (req, res) => {
  console.log('📋 GET /api/articles');
  
  const { page = 1, limit = 10 } = req.query;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  
  const paginatedArticles = mockArticles.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      articles: paginatedArticles,
      totalPages: Math.ceil(mockArticles.length / limit),
      currentPage: parseInt(page),
      total: mockArticles.length
    }
  });
});

// 文章详情API
app.get('/api/articles/:id', (req, res) => {
  const { id } = req.params;
  console.log(`📄 GET /api/articles/${id}`);
  
  const article = mockArticles.find(a => a.id === id);
  
  if (article) {
    res.json({
      success: true,
      data: article
    });
  } else {
    // 如果找不到文章，创建一个默认文章
    const defaultArticle = {
      id: id,
      title: `Sample Article ${id}`,
      content: `
        <div class="prose prose-lg max-w-none">
          <h1>Sample Article ${id}</h1>
          <p>This is a sample article with ID ${id}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
          <h2>Content</h2>
          <p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
          <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>
      `,
      excerpt: `This is a sample article with ID ${id}.`,
      author: {
        id: 999,
        name: "Sample Author",
        username: "sampleauthor",
        avatar: "https://ui-avatars.com/api/?name=Sample+Author&background=6366f1&color=fff&size=64",
        bio: "Sample author for demonstration purposes."
      },
      category: "Sample",
      tags: ["sample", "demo"],
      publishedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      readTime: 5,
      image: `https://picsum.photos/800/400?random=${id}`,
      likes: Math.floor(Math.random() * 100),
      comments: Math.floor(Math.random() * 20),
      shares: Math.floor(Math.random() * 10),
      views: Math.floor(Math.random() * 1000),
      published: true
    };
    
    res.json({
      success: true,
      data: defaultArticle
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log(`  GET  /api/articles`);
  console.log(`  GET  /api/articles/:id`);
  console.log(`  GET  /api/health`);
  console.log('');
  console.log('✅ Server ready to handle requests');
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

module.exports = app;
