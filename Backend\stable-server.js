#!/usr/bin/env node

/**
 * Newzora 稳定生产服务器
 * 支持多种网络环境，包括翻墙网络
 * 功能完整，长期稳定运行
 */

require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const os = require('os');

// 导入配置和服务
const { sequelize, connectWithRetry } = require('./config/database-enhanced');
const { logger } = require('./config/logger');
const passport = require('./config/passport');

// 创建Express应用
const app = express();

// 服务器配置
const config = {
  port: parseInt(process.env.PORT) || 5000,
  httpsPort: parseInt(process.env.HTTPS_PORT) || 5443,
  host: process.env.HOST || '0.0.0.0', // 绑定所有接口
  environment: process.env.NODE_ENV || 'development',
  enableHttps: process.env.ENABLE_HTTPS === 'true',
  enableCompression: process.env.ENABLE_COMPRESSION !== 'false',
  enableRateLimit: process.env.ENABLE_RATE_LIMIT !== 'false',
  corsOrigins: process.env.CORS_ORIGINS ? 
    process.env.CORS_ORIGINS.split(',') : 
    [
      'http://localhost:3000',
      'http://127.0.0.1:3000',
      'http://localhost:3001',
      'http://127.0.0.1:3001',
      'https://localhost:3000',
      'https://127.0.0.1:3000'
    ]
};

// 获取本机网络信息
function getNetworkInfo() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4') {
        addresses.push({
          name,
          address: interface.address,
          internal: interface.internal
        });
      }
    }
  }
  
  return {
    hostname: os.hostname(),
    platform: os.platform(),
    arch: os.arch(),
    addresses
  };
}

const networkInfo = getNetworkInfo();

// 动态添加本机IP到CORS白名单
networkInfo.addresses.forEach(addr => {
  if (!addr.internal) {
    config.corsOrigins.push(`http://${addr.address}:3000`);
    config.corsOrigins.push(`https://${addr.address}:3000`);
  }
});

console.log('🚀 Newzora Stable Server Starting...');
console.log('====================================');
console.log(`Environment: ${config.environment}`);
console.log(`Network Info:`, networkInfo);

// 1. 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "ws:", "wss:", "https:"],
      fontSrc: ["'self'", "https:", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https:"],
      frameSrc: ["'self'"]
    }
  },
  crossOriginResourcePolicy: { policy: "cross-origin" },
  crossOriginEmbedderPolicy: false
}));

// 2. 压缩中间件
if (config.enableCompression) {
  app.use(compression({
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    },
    level: 6,
    threshold: 1024
  }));
}

// 3. CORS配置 - 支持多种网络环境
app.use(cors({
  origin: (origin, callback) => {
    // 允许无origin的请求（如移动应用、Postman等）
    if (!origin) return callback(null, true);
    
    // 检查是否在白名单中
    if (config.corsOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // 开发环境允许localhost的任何端口
    if (config.environment === 'development') {
      if (origin.match(/^https?:\/\/(localhost|127\.0\.0\.1|0\.0\.0\.0)(:\d+)?$/)) {
        return callback(null, true);
      }
    }
    
    // 记录被拒绝的origin
    logger.warn('CORS blocked origin:', origin);
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Access-Token'
  ],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400 // 24小时
}));

// 4. 基础中间件
app.use(express.json({ 
  limit: '50mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ 
  extended: true, 
  limit: '50mb' 
}));

// 5. Session配置
app.use(session({
  secret: process.env.SESSION_SECRET || 'newzora-stable-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: config.enableHttps,
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24小时
    sameSite: config.environment === 'production' ? 'strict' : 'lax'
  },
  rolling: true
}));

// 6. Passport中间件
app.use(passport.initialize());
app.use(passport.session());

// 7. 速率限制
if (config.enableRateLimit) {
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: config.environment === 'development' ? 1000 : 100, // 开发环境更宽松
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // 跳过健康检查和静态资源
      return req.path === '/api/health' || req.path.startsWith('/static/');
    }
  });
  
  app.use(limiter);
}

// 8. 请求日志中间件
app.use((req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info('HTTP Request', {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    });
  });
  
  next();
});

// 9. 健康检查端点
app.get('/api/health', async (req, res) => {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.environment,
      version: process.env.npm_package_version || '1.0.0',
      database: {
        status: 'connected',
        type: 'PostgreSQL'
      },
      server: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        memory: process.memoryUsage(),
        loadAverage: os.loadavg()
      },
      network: {
        hostname: networkInfo.hostname,
        addresses: networkInfo.addresses.map(addr => ({
          address: addr.address,
          internal: addr.internal
        }))
      }
    };
    
    res.json(healthData);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: {
        status: 'disconnected'
      }
    });
  }
});

// 后端测试页面
app.get('/test', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Backend API Test</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 1200px; margin: 0 auto; padding: 20px;
                background: #f5f5f5;
            }
            .container {
                background: white; padding: 30px; border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .endpoint {
                margin: 15px 0; padding: 15px; border: 1px solid #ddd;
                border-radius: 4px; background: #f9f9f9;
            }
            .method {
                display: inline-block; padding: 4px 8px; border-radius: 3px;
                font-weight: bold; color: white; margin-right: 10px;
            }
            .get { background: #28a745; }
            .post { background: #007bff; }
            .put { background: #ffc107; color: #000; }
            .delete { background: #dc3545; }
            button {
                background: #007bff; color: white; padding: 8px 16px;
                border: none; border-radius: 4px; cursor: pointer; margin: 5px;
            }
            button:hover { background: #0056b3; }
            .result {
                margin-top: 15px; padding: 15px; border-radius: 4px;
                background: #f8f9fa; border: 1px solid #dee2e6;
                white-space: pre-wrap; font-family: monospace; font-size: 12px;
            }
            .success { background: #d4edda; border-color: #c3e6cb; }
            .error { background: #f8d7da; border-color: #f5c6cb; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Newzora Backend API Test</h1>
            <p>测试后端API端点的功能和响应</p>

            <h2>📊 服务器信息</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/health</strong> - 健康检查
                <button onclick="testEndpoint('GET', '/api/health')">测试</button>
            </div>

            <h2>🔐 认证API</h2>
            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth-enhanced/login</strong> - 用户登录
                <button onclick="testLogin()">测试登录</button>
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth-enhanced/register</strong> - 用户注册
                <button onclick="testRegister()">测试注册</button>
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/auth-enhanced/check-password-strength</strong> - 密码强度检查
                <button onclick="testPasswordStrength()">测试密码强度</button>
            </div>

            <h2>📝 文章API</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/articles</strong> - 获取文章列表
                <button onclick="testEndpoint('GET', '/api/articles')">测试</button>
            </div>

            <h2>👥 用户API</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/users</strong> - 获取用户列表
                <button onclick="testEndpoint('GET', '/api/users')">测试</button>
            </div>

            <h2>🔍 搜索API</h2>
            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/search?q=test</strong> - 搜索功能
                <button onclick="testEndpoint('GET', '/api/search?q=test')">测试</button>
            </div>

            <div id="result"></div>
        </div>

        <script>
            function showResult(data, isError = false) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result ' + (isError ? 'error' : 'success');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            }

            async function testEndpoint(method, url) {
                try {
                    const response = await fetch(url, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();

                    showResult({
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, !response.ok);
                } catch (error) {
                    showResult({
                        error: 'Network Error',
                        message: error.message
                    }, true);
                }
            }

            async function testLogin() {
                try {
                    const response = await fetch('/api/auth-enhanced/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            identifier: '<EMAIL>',
                            password: 'Admin123!',
                            rememberMe: false
                        })
                    });

                    const data = await response.json();

                    showResult({
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, !response.ok);
                } catch (error) {
                    showResult({
                        error: 'Network Error',
                        message: error.message
                    }, true);
                }
            }

            async function testRegister() {
                const timestamp = Date.now();
                try {
                    const response = await fetch('/api/auth-enhanced/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: \`testuser_\${timestamp}\`,
                            email: \`test\${timestamp}@example.com\`,
                            password: 'TestPassword123!',
                            firstName: 'Test',
                            lastName: 'User',
                            acceptTerms: true
                        })
                    });

                    const data = await response.json();

                    showResult({
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, !response.ok);
                } catch (error) {
                    showResult({
                        error: 'Network Error',
                        message: error.message
                    }, true);
                }
            }

            async function testPasswordStrength() {
                try {
                    const response = await fetch('/api/auth-enhanced/check-password-strength', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            password: 'TestPassword123!'
                        })
                    });

                    const data = await response.json();

                    showResult({
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, !response.ok);
                } catch (error) {
                    showResult({
                        error: 'Network Error',
                        message: error.message
                    }, true);
                }
            }

            // 页面加载时显示服务器信息
            window.addEventListener('load', () => {
                testEndpoint('GET', '/api/health');
            });
        </script>
    </body>
    </html>
  `);
});

// 10. 根路径信息
app.get('/', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  
  res.json({
    name: 'Newzora API Server',
    version: process.env.npm_package_version || '1.0.0',
    status: 'running',
    environment: config.environment,
    timestamp: new Date().toISOString(),
    endpoints: {
      health: `${baseUrl}/api/health`,
      auth: `${baseUrl}/api/auth-enhanced`,
      articles: `${baseUrl}/api/articles`,
      users: `${baseUrl}/api/users`,
      admin: `${baseUrl}/api/admin`,
      docs: `${baseUrl}/api/docs`
    },
    network: {
      accessUrls: [
        `http://localhost:${config.port}`,
        `http://127.0.0.1:${config.port}`,
        ...networkInfo.addresses
          .filter(addr => !addr.internal)
          .map(addr => `http://${addr.address}:${config.port}`)
      ]
    }
  });
});

// 11. API路由 - 按功能模块组织
console.log('📡 Loading API routes...');

// 认证相关路由
try {
  app.use('/api/auth', require('./routes/supabaseAuth'));
  app.use('/api/auth-enhanced', require('./routes/auth-enhanced'));
  app.use('/api/auth/social', require('./routes/social-auth'));
  console.log('✅ Authentication routes loaded');
} catch (error) {
  console.error('❌ Failed to load auth routes:', error.message);
}

// 核心功能路由
try {
  app.use('/api/articles', require('./routes/articles'));
  app.use('/api/categories', require('./routes/categories'));
  app.use('/api/users', require('./routes/users'));
  app.use('/api/comments', require('./routes/comments'));
  app.use('/api/tags', require('./routes/tags'));
  app.use('/api/notifications', require('./routes/notifications'));
  console.log('✅ Core feature routes loaded');
} catch (error) {
  console.error('❌ Failed to load core routes:', error.message);
}

// 社交功能路由
try {
  app.use('/api/follows', require('./routes/follows'));
  app.use('/api/messages', require('./routes/messages'));
  app.use('/api/activities', require('./routes/activities'));
  app.use('/api/shares', require('./routes/shares'));
  app.use('/api/social', require('./routes/social'));
  console.log('✅ Social feature routes loaded');
} catch (error) {
  console.error('❌ Failed to load social routes:', error.message);
}

// 管理功能路由
try {
  app.use('/api/admin/users', require('./routes/admin/users'));
  app.use('/api/admin/email', require('./routes/admin/email'));
  app.use('/api/admin/dashboard', require('./routes/admin/dashboard'));
  app.use('/api/permissions', require('./routes/permissions'));
  app.use('/api/roles', require('./routes/roles'));
  app.use('/api/content-management', require('./routes/content-management'));
  app.use('/api/moderation', require('./routes/moderation'));
  console.log('✅ Admin feature routes loaded');
} catch (error) {
  console.error('❌ Failed to load admin routes:', error.message);
}

// 高级功能路由
try {
  app.use('/api/search', require('./routes/search'));
  app.use('/api/reports', require('./routes/reports'));
  app.use('/api/email', require('./routes/email'));
  app.use('/api/monitoring', require('./routes/monitoring'));
  console.log('✅ Advanced feature routes loaded');
} catch (error) {
  console.error('❌ Failed to load advanced routes:', error.message);
}

// 12. 错误处理中间件
app.use((error, req, res, next) => {
  logger.error('Server Error:', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userId: req.user?.id
  });

  // 不泄露敏感信息
  const isDevelopment = config.environment === 'development';
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(isDevelopment && { 
      stack: error.stack,
      details: error.details 
    })
  });
});

// 13. 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: [
      'GET /api/health',
      'POST /api/auth-enhanced/login',
      'POST /api/auth-enhanced/register',
      'GET /api/articles',
      'GET /api/users',
      'GET /api/admin/dashboard'
    ]
  });
});

module.exports = { app, config, networkInfo };
