<mjml>
  <mj-head>
    <mj-title>{{notification.title}} - Newzora</mj-title>
    <mj-preview>{{notification.message}}</mj-preview>
    <mj-attributes>
      <mj-all font-family="'Helvetica Neue', Helvetica, Arial, sans-serif"></mj-all>
      <mj-text font-weight="400" font-size="16px" color="#000000" line-height="24px"></mj-text>
    </mj-attributes>
    <mj-style inline="inline">
      .brand-color { color: #6366f1 !important; }
      .btn-primary { background-color: #6366f1 !important; }
      .notification-like { background-color: #fef2f2; border-left: 4px solid #ef4444; }
      .notification-comment { background-color: #f0f9ff; border-left: 4px solid #3b82f6; }
      .notification-follow { background-color: #f0fdf4; border-left: 4px solid #10b981; }
      .notification-article { background-color: #fefce8; border-left: 4px solid #eab308; }
      .notification-system { background-color: #f3f4f6; border-left: 4px solid #6b7280; }
    </mj-style>
  </mj-head>
  <mj-body background-color="#f8fafc">
    <!-- Header -->
    <mj-section background-color="#ffffff" padding="20px 0">
      <mj-column>
        <mj-image 
          src="{{baseUrl}}/images/logo.png" 
          alt="Newzora" 
          width="120px"
          align="center"
        />
      </mj-column>
    </mj-section>

    <!-- Notification Content -->
    <mj-section background-color="#ffffff" padding="40px 20px">
      <mj-column>
        <mj-text align="center" font-size="24px" font-weight="bold" color="#1f2937">
          {{notification.title}}
        </mj-text>
        
        <mj-text align="center" font-size="16px" color="#6b7280" padding="20px 0">
          Hi {{user.firstName}},
        </mj-text>
      </mj-column>
    </mj-section>

    <!-- Notification Box -->
    <mj-section 
      background-color="{{#eq notification.type 'like'}}#fef2f2{{/eq}}{{#eq notification.type 'comment'}}#f0f9ff{{/eq}}{{#eq notification.type 'follow'}}#f0fdf4{{/eq}}{{#eq notification.type 'article'}}#fefce8{{/eq}}{{#eq notification.type 'system'}}#f3f4f6{{/eq}}" 
      padding="30px 20px"
      border-left="4px solid {{#eq notification.type 'like'}}#ef4444{{/eq}}{{#eq notification.type 'comment'}}#3b82f6{{/eq}}{{#eq notification.type 'follow'}}#10b981{{/eq}}{{#eq notification.type 'article'}}#eab308{{/eq}}{{#eq notification.type 'system'}}#6b7280{{/eq}}"
    >
      <mj-column>
        <!-- Notification Icon -->
        <mj-text align="center" font-size="48px" padding="0 0 20px 0">
          {{#eq notification.type 'like'}}❤️{{/eq}}
          {{#eq notification.type 'comment'}}💬{{/eq}}
          {{#eq notification.type 'follow'}}👥{{/eq}}
          {{#eq notification.type 'article'}}📝{{/eq}}
          {{#eq notification.type 'system'}}🔔{{/eq}}
        </mj-text>
        
        <mj-text align="center" font-size="18px" font-weight="bold" color="#1f2937">
          {{notification.message}}
        </mj-text>
        
        <mj-text align="center" font-size="14px" color="#6b7280" padding="15px 0">
          {{formatDate notification.createdAt}} at {{formatTime notification.createdAt}}
        </mj-text>
        
        {{#if actionUrl}}
        <mj-button 
          background-color="#6366f1" 
          color="#ffffff" 
          font-size="16px" 
          font-weight="bold"
          border-radius="8px"
          padding="15px 30px"
          href="{{actionUrl}}"
          css-class="btn-primary"
        >
          {{#eq notification.type 'like'}}View Article{{/eq}}
          {{#eq notification.type 'comment'}}View Comment{{/eq}}
          {{#eq notification.type 'follow'}}View Profile{{/eq}}
          {{#eq notification.type 'article'}}Read Article{{/eq}}
          {{#eq notification.type 'system'}}View Details{{/eq}}
        </mj-button>
        {{/if}}
      </mj-column>
    </mj-section>

    <!-- Additional Content for Specific Notification Types -->
    {{#eq notification.type 'comment'}}
    <mj-section background-color="#ffffff" padding="30px 20px">
      <mj-column>
        <mj-text align="center" font-size="16px" color="#374151" font-style="italic">
          "{{truncate notification.commentText 150}}"
        </mj-text>
      </mj-column>
    </mj-section>
    {{/eq}}

    {{#eq notification.type 'article'}}
    <mj-section background-color="#ffffff" padding="30px 20px">
      <mj-column>
        <mj-text align="center" font-size="16px" color="#374151">
          Your article "{{notification.articleTitle}}" has been published and is now live!
        </mj-text>
        
        <mj-text align="center" font-size="14px" color="#6b7280" padding="15px 0">
          Share it with your followers and start engaging with your readers.
        </mj-text>
      </mj-column>
    </mj-section>
    {{/eq}}

    {{#eq notification.type 'follow'}}
    <mj-section background-color="#ffffff" padding="30px 20px">
      <mj-column>
        <mj-text align="center" font-size="16px" color="#374151">
          {{notification.followedUsername}} started following you! 
          Check out their profile and consider following them back.
        </mj-text>
      </mj-column>
    </mj-section>
    {{/eq}}

    <!-- Notification Settings -->
    <mj-section background-color="#f9fafb" padding="30px 20px">
      <mj-column>
        <mj-divider border-color="#e5e7eb" border-width="1px" padding="0 0 20px 0" />
        
        <mj-text align="center" font-size="14px" color="#6b7280">
          You're receiving this email because you have notifications enabled for this type of activity.
        </mj-text>
        
        <mj-text align="center" font-size="14px" color="#6b7280" padding="10px 0">
          <a href="{{baseUrl}}/settings/notifications" style="color: #6366f1;">
            Manage your notification preferences
          </a> | 
          <a href="{{unsubscribeUrl}}" style="color: #6366f1;">
            Unsubscribe from email notifications
          </a>
        </mj-text>
        
        <mj-text align="center" font-size="14px" color="#6b7280" padding="20px 0 0 0">
          <strong>{{brandName}}</strong><br/>
          Building the future of content creation
        </mj-text>
        
        <mj-text align="center" font-size="12px" color="#9ca3af" padding="20px 0 0 0">
          © {{currentYear}} Newzora. All rights reserved.
        </mj-text>
        
        <mj-social font-size="15px" icon-size="30px" mode="horizontal" padding="20px 0 0 0" align="center">
          <mj-social-element name="twitter" href="https://twitter.com/newzora" background-color="#1da1f2" />
          <mj-social-element name="facebook" href="https://facebook.com/newzora" background-color="#3b5998" />
          <mj-social-element name="linkedin" href="https://linkedin.com/company/newzora" background-color="#0077b5" />
        </mj-social>
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>
