/**
 * 测试文章API修复
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testArticleApis() {
  console.log('🧪 Testing Article API fixes...\n');

  try {
    // 测试文章列表
    console.log('1. Testing article list...');
    const listResponse = await axios.get(`${BASE_URL}/articles?page=1&limit=5`);
    console.log('✅ Article list response structure:', {
      success: listResponse.data.success,
      hasData: !!listResponse.data.data,
      hasArticles: !!listResponse.data.data?.articles,
      articleCount: listResponse.data.data?.articles?.length || 0
    });

    // 测试文章详情
    console.log('\n2. Testing article detail...');
    const detailResponse = await axios.get(`${BASE_URL}/articles/test-article-1`);
    console.log('✅ Article detail response structure:', {
      success: detailResponse.data.success,
      hasData: !!detailResponse.data.data,
      hasTitle: !!detailResponse.data.data?.title,
      hasAuthor: !!detailResponse.data.data?.author,
      title: detailResponse.data.data?.title
    });

    // 测试推荐文章
    console.log('\n3. Testing recommended articles...');
    const recommendedResponse = await axios.get(`${BASE_URL}/articles/recommended?limit=3`);
    console.log('✅ Recommended articles response structure:', {
      success: recommendedResponse.data.success,
      hasData: !!recommendedResponse.data.data,
      isArray: Array.isArray(recommendedResponse.data.data),
      count: Array.isArray(recommendedResponse.data.data) ? recommendedResponse.data.data.length : 0
    });

    console.log('\n🎉 All tests passed! Article API fixes are working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// 运行测试
testArticleApis();
