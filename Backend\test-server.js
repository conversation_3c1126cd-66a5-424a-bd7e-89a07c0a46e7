const express = require('express');
const cors = require('cors');
const { sequelize, connectWithRetry } = require('./config/database-enhanced');

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/test', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Server is running!',
    timestamp: new Date().toISOString()
  });
});

// Health check route
app.get('/api/health', async (req, res) => {
  try {
    await sequelize.authenticate();
    res.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Start server
async function startServer() {
  try {
    console.log('🚀 Starting test server...');
    
    // Connect to database
    console.log('📡 Connecting to database...');
    const connected = await connectWithRetry();
    
    if (connected) {
      console.log('✅ Database connected successfully');
    } else {
      console.log('⚠️ Database connection failed, but server will continue');
    }
    
    // Start HTTP server
    const server = app.listen(PORT, () => {
      console.log(`🎉 Test server is running on port ${PORT}`);
      console.log(`📊 Test API: http://localhost:${PORT}/api/test`);
      console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
    });
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        sequelize.close().then(() => {
          console.log('Database connection closed');
          process.exit(0);
        });
      });
    });
    
    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        sequelize.close().then(() => {
          console.log('Database connection closed');
          process.exit(0);
        });
      });
    });
    
  } catch (error) {
    console.error('❌ Server startup failed:', error.message);
    process.exit(1);
  }
}

startServer();
