/**
 * Newzora API 测试工具
 * 自动测试所有API路由的可用性
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class ApiTester {
  constructor(baseUrl = 'http://localhost:5000/api') {
    this.baseUrl = baseUrl;
    this.testResults = [];
    this.authToken = null;
  }

  // 记录测试结果
  logResult(endpoint, method, status, message, duration) {
    const result = {
      endpoint,
      method,
      status,
      message,
      duration,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${method} ${endpoint} - ${message} (${duration}ms)`);
  }

  // 执行HTTP请求
  async makeRequest(method, endpoint, data = null, headers = {}) {
    const startTime = Date.now();
    
    try {
      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        timeout: 10000
      };

      if (data) {
        config.data = data;
      }

      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }

      const response = await axios(config);
      const duration = Date.now() - startTime;
      
      this.logResult(endpoint, method, 'PASS', `Status: ${response.status}`, duration);
      return { success: true, data: response.data, status: response.status };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const status = error.response?.status || 'NETWORK_ERROR';
      const message = error.response?.data?.message || error.message;
      
      this.logResult(endpoint, method, 'FAIL', `${status}: ${message}`, duration);
      return { success: false, error: message, status };
    }
  }

  // 测试认证相关API
  async testAuthApis() {
    console.log('\n🔐 Testing Authentication APIs...');
    
    // 测试注册（使用随机邮箱避免冲突）
    const randomEmail = `test${Date.now()}@example.com`;
    await this.makeRequest('POST', '/auth/register', {
      email: randomEmail,
      password: 'testpassword123',
      name: 'Test User'
    });

    // 测试登录
    const loginResult = await this.makeRequest('POST', '/auth/login', {
      email: randomEmail,
      password: 'testpassword123'
    });

    if (loginResult.success && loginResult.data.token) {
      this.authToken = loginResult.data.token;
      console.log('🔑 Authentication token obtained');
    }

    // 测试获取用户资料
    await this.makeRequest('GET', '/auth/profile');

    // 测试更新用户资料
    await this.makeRequest('PUT', '/auth/profile', {
      name: 'Updated Test User'
    });

    // 测试登出
    await this.makeRequest('POST', '/auth/logout');
  }

  // 测试推荐系统API
  async testRecommendationApis() {
    console.log('\n🎯 Testing Recommendation APIs...');
    
    await this.makeRequest('GET', '/recommendations/personalized?limit=10');
    await this.makeRequest('GET', '/recommendations/realtime?limit=5');
    await this.makeRequest('GET', '/recommendations/contextual?limit=15');
    await this.makeRequest('GET', '/recommendations/deep-learning?limit=10');
    await this.makeRequest('GET', '/recommendations/multi-armed-bandit?limit=10');
    await this.makeRequest('GET', '/recommendations/popular?limit=20');
    await this.makeRequest('GET', '/recommendations/trending?limit=20');
    
    await this.makeRequest('POST', '/recommendations/feedback', {
      contentId: 'test_content_123',
      action: 'click',
      algorithm: 'personalized'
    });
    
    await this.makeRequest('GET', '/recommendations/stats?days=30');
  }

  // 测试AI审核API
  async testAiModerationApis() {
    console.log('\n🤖 Testing AI Moderation APIs...');
    
    await this.makeRequest('POST', '/ai-moderation/moderate', {
      contentType: 'text',
      contentId: 'test_123',
      content: 'This is a test message for moderation.'
    });

    await this.makeRequest('POST', '/ai-moderation/detect-sensitive-words', {
      content: 'This is a normal message without sensitive content.'
    });

    await this.makeRequest('POST', '/ai-moderation/detect-toxicity', {
      content: 'This is a friendly and positive message.'
    });

    await this.makeRequest('POST', '/ai-moderation/semantic-analysis', {
      content: 'This is a sample text for semantic analysis testing.'
    });

    await this.makeRequest('POST', '/ai-moderation/detect-image-content', {
      content: 'Text content with image references: https://example.com/image.jpg'
    });

    await this.makeRequest('POST', '/ai-moderation/detect-malicious-urls', {
      content: 'Check this link: https://example.com/safe-link'
    });

    await this.makeRequest('GET', '/ai-moderation/test');
  }

  // 测试国际化API
  async testI18nApis() {
    console.log('\n🌍 Testing Internationalization APIs...');
    
    await this.makeRequest('GET', '/i18n/detect-language');
    await this.makeRequest('GET', '/i18n/languages');
    await this.makeRequest('GET', '/i18n/translations/en');
    await this.makeRequest('GET', '/i18n/translations/zh?keys=nav.home,nav.explore');
    
    await this.makeRequest('POST', '/i18n/translate', {
      text: 'Hello, world!',
      targetLanguage: 'zh',
      sourceLanguage: 'en'
    });

    await this.makeRequest('POST', '/i18n/translate-batch', {
      texts: ['Hello', 'Welcome', 'Thank you'],
      targetLanguage: 'es',
      sourceLanguage: 'en'
    });

    await this.makeRequest('POST', '/i18n/detect-text-language', {
      text: 'This is an English text for language detection.'
    });

    await this.makeRequest('POST', '/i18n/format-datetime', {
      date: new Date().toISOString(),
      language: 'en',
      options: { year: 'numeric', month: 'long', day: 'numeric' }
    });

    await this.makeRequest('GET', '/i18n/stats');
  }

  // 测试创作者生态系统API
  async testCreatorApis() {
    console.log('\n👥 Testing Creator Ecosystem APIs...');
    
    await this.makeRequest('GET', '/creators/levels');
    await this.makeRequest('GET', '/creators/evaluate-level');
    await this.makeRequest('GET', '/creators/verification-types');
    await this.makeRequest('GET', '/creators/incentive-programs');
    await this.makeRequest('GET', '/creators/calculate-rewards');
    await this.makeRequest('GET', '/creators/recommendations?categories=technology&maxResults=10');
    await this.makeRequest('GET', '/creators/stats');
    await this.makeRequest('GET', '/creators/global-support');
    await this.makeRequest('GET', '/creators/search?query=test&category=technology&limit=10');
    
    await this.makeRequest('POST', '/creators/apply-verification', {
      verificationType: 'identity',
      documents: {
        government_id: 'test_id_document.pdf',
        phone_verification: 'verified',
        email_verification: 'verified'
      }
    });
  }

  // 测试评论系统API
  async testCommentApis() {
    console.log('\n💬 Testing Comment System APIs...');
    
    await this.makeRequest('GET', '/comments/interaction-types');
    await this.makeRequest('GET', '/comments/report-types');
    
    // 创建测试评论
    const commentResult = await this.makeRequest('POST', '/comments/enhanced', {
      targetType: 'article',
      targetId: 'test_article_123',
      content: 'This is a test comment for API testing.',
      mentions: [],
      attachments: []
    });

    if (commentResult.success && commentResult.data?.comment?.id) {
      const commentId = commentResult.data.comment.id;
      
      // 测试评论交互
      await this.makeRequest('POST', `/comments/${commentId}/interact`, {
        type: 'like'
      });

      // 测试举报评论
      await this.makeRequest('POST', `/comments/${commentId}/report`, {
        type: 'spam',
        reason: 'This is a test report',
        additionalInfo: 'Testing the report functionality'
      });
    }

    await this.makeRequest('GET', '/comments/enhanced/article/test_article_123?page=1&limit=10');
    await this.makeRequest('GET', '/comments/stats/article/test_article_123');
  }

  // 测试媒体管理API
  async testMediaApis() {
    console.log('\n📁 Testing Media Management APIs...');
    
    await this.makeRequest('GET', '/media?page=1&limit=10');
    
    // 注意：实际文件上传需要multipart/form-data，这里只测试路由可达性
    console.log('ℹ️  Media upload requires multipart/form-data - skipping file upload test');
    
    // 测试其他媒体API（使用模拟ID）
    const testMediaId = 'test_media_123';
    await this.makeRequest('GET', `/media/${testMediaId}`);
    
    await this.makeRequest('POST', `/media/convert/${testMediaId}`, {
      outputFormat: 'mp4',
      quality: 'medium'
    });

    await this.makeRequest('POST', `/media/compress/${testMediaId}`, {
      compressionLevel: 'medium'
    });

    await this.makeRequest('POST', `/media/extract-audio/${testMediaId}`, {
      format: 'mp3',
      quality: 'medium'
    });
  }

  // 测试其他API
  async testOtherApis() {
    console.log('\n🔍 Testing Other APIs...');
    
    // 搜索API
    await this.makeRequest('GET', '/search?q=test&type=articles&limit=10');
    await this.makeRequest('GET', '/search/articles?q=technology&limit=5');
    await this.makeRequest('GET', '/search/users?q=test&limit=5');
    await this.makeRequest('GET', '/search/suggestions?q=tech');

    // 文章API
    await this.makeRequest('GET', '/articles?page=1&limit=10');
    await this.makeRequest('GET', '/articles/test_article_123');

    // 用户API
    await this.makeRequest('GET', '/users?page=1&limit=10');
    await this.makeRequest('GET', '/users/test_user_123');

    // 系统监控API
    await this.makeRequest('GET', '/monitoring/health');
    await this.makeRequest('GET', '/monitoring/stats');
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 Starting Newzora API Tests...\n');
    const startTime = Date.now();

    try {
      await this.testAuthApis();
      await this.testRecommendationApis();
      await this.testAiModerationApis();
      await this.testI18nApis();
      await this.testCreatorApis();
      await this.testCommentApis();
      await this.testMediaApis();
      await this.testOtherApis();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }

    const totalTime = Date.now() - startTime;
    await this.generateReport(totalTime);
  }

  // 生成测试报告
  async generateReport(totalTime) {
    const passCount = this.testResults.filter(r => r.status === 'PASS').length;
    const failCount = this.testResults.filter(r => r.status === 'FAIL').length;
    const totalCount = this.testResults.length;
    const successRate = ((passCount / totalCount) * 100).toFixed(2);

    const report = {
      summary: {
        total: totalCount,
        passed: passCount,
        failed: failCount,
        successRate: `${successRate}%`,
        totalTime: `${totalTime}ms`,
        timestamp: new Date().toISOString()
      },
      results: this.testResults
    };

    console.log('\n📊 Test Summary:');
    console.log(`Total Tests: ${totalCount}`);
    console.log(`Passed: ${passCount} ✅`);
    console.log(`Failed: ${failCount} ❌`);
    console.log(`Success Rate: ${successRate}%`);
    console.log(`Total Time: ${totalTime}ms`);

    // 保存报告到文件
    try {
      const reportPath = path.join(__dirname, '../test-reports');
      await fs.mkdir(reportPath, { recursive: true });
      
      const reportFile = path.join(reportPath, `api-test-${Date.now()}.json`);
      await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
      
      console.log(`\n📄 Report saved to: ${reportFile}`);
    } catch (error) {
      console.error('Failed to save report:', error);
    }

    if (failCount > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  ${r.method} ${r.endpoint} - ${r.message}`);
        });
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new ApiTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ApiTester;
