{"name": "newzora-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "stable": "node stable-server.js", "serve": "node stable-server.js", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tinymce/tinymce-react": "^6.2.1", "express": "^5.1.0", "express-slow-down": "^2.1.0", "http-proxy-middleware": "^3.0.5", "next": "14.2.5", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/supertest": "^6.0.3", "eslint": "^8", "eslint-config-next": "14.2.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "supertest": "^7.1.3", "tailwindcss": "^4", "typescript": "^5"}}