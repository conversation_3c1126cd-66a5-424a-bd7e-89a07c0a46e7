#!/usr/bin/env node

/**
 * 简化的前端服务器
 * 用于测试和开发
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = 3000;
const BACKEND_URL = 'http://localhost:5000';

console.log('🎨 启动简化前端服务器...');
console.log('============================');

// 1. API代理中间件
app.use('/api', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  secure: false,
  timeout: 30000,
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({
      error: 'Backend server not available',
      message: 'Please ensure backend server is running on port 5000'
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`代理请求: ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
  }
}));

// 2. 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    backend: BACKEND_URL,
    frontend: `http://localhost:${PORT}`
  });
});

// 3. 测试页面
app.get('/test-auth', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Authentication Test</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 800px; margin: 0 auto; padding: 20px; 
                background: #f5f5f5;
            }
            .container { 
                background: white; padding: 30px; border-radius: 8px; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input { 
                width: 100%; padding: 10px; border: 1px solid #ddd; 
                border-radius: 4px; font-size: 16px;
            }
            button { 
                background: #007bff; color: white; padding: 12px 24px; 
                border: none; border-radius: 4px; cursor: pointer; font-size: 16px;
            }
            button:hover { background: #0056b3; }
            .result { 
                margin-top: 20px; padding: 15px; border-radius: 4px; 
                white-space: pre-wrap; font-family: monospace;
            }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .account-btn {
                display: inline-block; margin: 5px; padding: 8px 16px;
                background: #28a745; color: white; text-decoration: none;
                border-radius: 4px; font-size: 14px;
            }
            .account-btn:hover { background: #218838; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 Newzora Authentication Test</h1>
            
            <h2>Quick Test Accounts</h2>
            <div>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Admin123!')">Admin Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'User123!')">User Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Moderator123!')">Moderator Login</a>
            </div>
            
            <h2>Manual Login Test</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="identifier">Email or Username:</label>
                    <input type="text" id="identifier" name="identifier" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" placeholder="Admin123!">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="rememberMe" name="rememberMe"> Remember Me
                    </label>
                </div>
                <button type="submit">Login</button>
            </form>
            
            <h2>Registration Test</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="regUsername">Username:</label>
                    <input type="text" id="regUsername" name="username" placeholder="newuser">
                </div>
                <div class="form-group">
                    <label for="regEmail">Email:</label>
                    <input type="email" id="regEmail" name="email" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="regPassword">Password:</label>
                    <input type="password" id="regPassword" name="password" placeholder="NewPassword123!">
                </div>
                <div class="form-group">
                    <label for="regFirstName">First Name:</label>
                    <input type="text" id="regFirstName" name="firstName" placeholder="John">
                </div>
                <div class="form-group">
                    <label for="regLastName">Last Name:</label>
                    <input type="text" id="regLastName" name="lastName" placeholder="Doe">
                </div>
                <button type="submit">Register</button>
            </form>
            
            <div id="result"></div>
        </div>

        <script>
            function showResult(data, isError = false) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result ' + (isError ? 'error' : 'success');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            }

            function quickLogin(identifier, password) {
                login(identifier, password, false);
            }

            async function login(identifier, password, rememberMe) {
                try {
                    const response = await fetch('/api/auth-enhanced/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            identifier,
                            password,
                            rememberMe
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Login successful!',
                            user: data.data.user,
                            hasToken: !!data.data.tokens.accessToken
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Login failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message,
                        note: 'This might be the JSON parsing error you encountered'
                    }, true);
                }
            }

            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                await login(
                    formData.get('identifier'),
                    formData.get('password'),
                    formData.get('rememberMe') === 'on'
                );
            });

            document.getElementById('registerForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                
                try {
                    const response = await fetch('/api/auth-enhanced/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: formData.get('username'),
                            email: formData.get('email'),
                            password: formData.get('password'),
                            firstName: formData.get('firstName'),
                            lastName: formData.get('lastName'),
                            acceptTerms: true
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Registration successful!',
                            user: data.data.user
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Registration failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message
                    }, true);
                }
            });

            // 页面加载时测试API连接
            window.addEventListener('load', async () => {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    console.log('Backend health check:', data);
                } catch (error) {
                    console.error('Backend connection failed:', error);
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 3.5 Newzora完整应用页面
app.get('/app', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora - Modern Content Platform</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                background: #f8fafc; color: #1a202c; line-height: 1.6;
            }
            .header {
                background: white; border-bottom: 1px solid #e2e8f0;
                padding: 1rem 0; position: sticky; top: 0; z-index: 100;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .nav {
                max-width: 1200px; margin: 0 auto; padding: 0 1rem;
                display: flex; justify-content: space-between; align-items: center;
            }
            .logo {
                font-size: 1.5rem; font-weight: bold; color: #3182ce;
                text-decoration: none;
            }
            .nav-links {
                display: flex; gap: 2rem; list-style: none;
            }
            .nav-links a {
                text-decoration: none; color: #4a5568; font-weight: 500;
                padding: 0.5rem 1rem; border-radius: 6px; transition: all 0.2s;
            }
            .nav-links a:hover { background: #edf2f7; color: #2d3748; }
            .container {
                max-width: 1200px; margin: 0 auto; padding: 2rem 1rem;
                display: grid; grid-template-columns: 1fr 300px; gap: 2rem;
            }
            .main-content { background: white; border-radius: 8px; padding: 2rem; }
            .sidebar { background: white; border-radius: 8px; padding: 1.5rem; height: fit-content; }
            .article-card {
                border: 1px solid #e2e8f0; border-radius: 8px; padding: 1.5rem;
                margin-bottom: 1.5rem; transition: all 0.2s;
            }
            .article-card:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.1); transform: translateY(-2px);
            }
            .article-title {
                font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem;
                color: #2d3748;
            }
            .article-meta {
                color: #718096; font-size: 0.875rem; margin-bottom: 1rem;
                display: flex; gap: 1rem; align-items: center;
            }
            .article-excerpt { color: #4a5568; margin-bottom: 1rem; }
            .article-tags { display: flex; gap: 0.5rem; flex-wrap: wrap; }
            .tag {
                background: #edf2f7; color: #4a5568; padding: 0.25rem 0.75rem;
                border-radius: 20px; font-size: 0.75rem; font-weight: 500;
            }
            .category-item {
                display: block; padding: 0.75rem 1rem; margin-bottom: 0.5rem;
                background: #f7fafc; border-radius: 6px; text-decoration: none;
                color: #4a5568; transition: all 0.2s;
            }
            .category-item:hover { background: #edf2f7; color: #2d3748; }
            .btn {
                display: inline-block; padding: 0.75rem 1.5rem; border-radius: 6px;
                text-decoration: none; font-weight: 500; transition: all 0.2s;
                border: none; cursor: pointer;
            }
            .btn-primary { background: #3182ce; color: white; }
            .btn-primary:hover { background: #2c5aa0; }
            .btn-outline {
                background: transparent; color: #3182ce; border: 1px solid #3182ce;
            }
            .btn-outline:hover { background: #3182ce; color: white; }
            .hero {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; padding: 3rem 0; text-align: center; margin-bottom: 2rem;
            }
            .hero h1 { font-size: 2.5rem; margin-bottom: 1rem; }
            .hero p { font-size: 1.125rem; opacity: 0.9; }
            @media (max-width: 768px) {
                .container { grid-template-columns: 1fr; }
                .nav-links { display: none; }
                .hero h1 { font-size: 2rem; }
            }
        </style>
    </head>
    <body>
        <header class="header">
            <nav class="nav">
                <a href="/app" class="logo">🚀 Newzora</a>
                <ul class="nav-links">
                    <li><a href="/app">Home</a></li>
                    <li><a href="/app/explore">Explore</a></li>
                    <li><a href="/app/create">Create</a></li>
                    <li><a href="/app/profile">Profile</a></li>
                    <li><a href="/app/notifications">Notifications</a></li>
                    <li><a href="/app/settings">Settings</a></li>
                </ul>
                <div>
                    <a href="/test-auth" class="btn btn-outline">Login</a>
                </div>
            </nav>
        </header>

        <div class="hero">
            <h1>Welcome to Newzora</h1>
            <p>Discover, create, and share amazing content with the world</p>
        </div>

        <div class="container">
            <main class="main-content">
                <h2 style="margin-bottom: 1.5rem; color: #2d3748;">Latest Articles</h2>

                <article class="article-card">
                    <h3 class="article-title">Getting Started with Modern Web Development</h3>
                    <div class="article-meta">
                        <span>👤 John Doe</span>
                        <span>📅 2 hours ago</span>
                        <span>👁️ 1.2k views</span>
                        <span>💬 23 comments</span>
                    </div>
                    <p class="article-excerpt">
                        Learn the fundamentals of modern web development with React, Next.js, and TypeScript.
                        This comprehensive guide covers everything you need to know to build scalable applications.
                    </p>
                    <div class="article-tags">
                        <span class="tag">React</span>
                        <span class="tag">Next.js</span>
                        <span class="tag">TypeScript</span>
                        <span class="tag">Web Development</span>
                    </div>
                </article>

                <article class="article-card">
                    <h3 class="article-title">The Future of AI in Content Creation</h3>
                    <div class="article-meta">
                        <span>👤 Sarah Wilson</span>
                        <span>📅 5 hours ago</span>
                        <span>👁️ 2.8k views</span>
                        <span>💬 45 comments</span>
                    </div>
                    <p class="article-excerpt">
                        Explore how artificial intelligence is revolutionizing content creation, from automated writing
                        to intelligent editing tools that enhance creativity and productivity.
                    </p>
                    <div class="article-tags">
                        <span class="tag">AI</span>
                        <span class="tag">Content Creation</span>
                        <span class="tag">Technology</span>
                        <span class="tag">Future</span>
                    </div>
                </article>

                <article class="article-card">
                    <h3 class="article-title">Building Scalable Backend APIs</h3>
                    <div class="article-meta">
                        <span>👤 Mike Chen</span>
                        <span>📅 1 day ago</span>
                        <span>👁️ 1.5k views</span>
                        <span>💬 18 comments</span>
                    </div>
                    <p class="article-excerpt">
                        Best practices for designing and implementing scalable backend APIs using Node.js, Express,
                        and PostgreSQL. Learn about authentication, rate limiting, and performance optimization.
                    </p>
                    <div class="article-tags">
                        <span class="tag">Backend</span>
                        <span class="tag">API</span>
                        <span class="tag">Node.js</span>
                        <span class="tag">PostgreSQL</span>
                    </div>
                </article>

                <div style="text-align: center; margin-top: 2rem;">
                    <button class="btn btn-primary" onclick="loadMore()">Load More Articles</button>
                </div>
            </main>

            <aside class="sidebar">
                <h3 style="margin-bottom: 1rem; color: #2d3748;">Categories</h3>
                <a href="/app/category/technology" class="category-item">
                    💻 Technology <span style="float: right; color: #718096;">124</span>
                </a>
                <a href="/app/category/design" class="category-item">
                    🎨 Design <span style="float: right; color: #718096;">89</span>
                </a>
                <a href="/app/category/business" class="category-item">
                    💼 Business <span style="float: right; color: #718096;">67</span>
                </a>
                <a href="/app/category/lifestyle" class="category-item">
                    🌟 Lifestyle <span style="float: right; color: #718096;">45</span>
                </a>
                <a href="/app/category/science" class="category-item">
                    🔬 Science <span style="float: right; color: #718096;">32</span>
                </a>

                <h3 style="margin: 2rem 0 1rem; color: #2d3748;">Trending Tags</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                    <span class="tag">React</span>
                    <span class="tag">AI</span>
                    <span class="tag">Design</span>
                    <span class="tag">JavaScript</span>
                    <span class="tag">UI/UX</span>
                    <span class="tag">Backend</span>
                    <span class="tag">Mobile</span>
                    <span class="tag">DevOps</span>
                </div>

                <h3 style="margin: 2rem 0 1rem; color: #2d3748;">Quick Actions</h3>
                <a href="/app/create" class="btn btn-primary" style="width: 100%; text-align: center; margin-bottom: 0.5rem;">
                    ✏️ Write Article
                </a>
                <a href="/app/drafts" class="btn btn-outline" style="width: 100%; text-align: center; margin-bottom: 0.5rem;">
                    📝 My Drafts
                </a>
                <a href="/app/bookmarks" class="btn btn-outline" style="width: 100%; text-align: center;">
                    🔖 Bookmarks
                </a>
            </aside>
        </div>

        <script>
            function loadMore() {
                alert('Loading more articles... (This would connect to the backend API)');
            }

            // 模拟无限滚动
            window.addEventListener('scroll', () => {
                if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000) {
                    console.log('Near bottom - would load more articles');
                }
            });

            // 模拟实时更新
            setInterval(() => {
                const viewCounts = document.querySelectorAll('.article-meta span:nth-child(3)');
                viewCounts.forEach(span => {
                    const current = parseInt(span.textContent.match(/\\d+/)[0]);
                    const increment = Math.floor(Math.random() * 3);
                    if (increment > 0) {
                        span.textContent = span.textContent.replace(/\\d+/, current + increment);
                    }
                });
            }, 10000);
        </script>
    </body>
    </html>
  `);
});

// 4. 根路径 - 欢迎页面
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Frontend Server</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 800px; margin: 0 auto; padding: 40px 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh; color: white;
            }
            .container {
                background: rgba(255,255,255,0.1); padding: 40px; border-radius: 16px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3); backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            }
            h1 {
                font-size: 2.5em; margin-bottom: 10px; text-align: center;
                background: linear-gradient(45deg, #fff, #f0f0f0);
                -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            .subtitle {
                text-align: center; font-size: 1.2em; margin-bottom: 40px;
                opacity: 0.9;
            }
            .endpoint-card {
                background: rgba(255,255,255,0.1); margin: 20px 0; padding: 25px;
                border-radius: 12px; border: 1px solid rgba(255,255,255,0.2);
                transition: all 0.3s ease;
            }
            .endpoint-card:hover {
                background: rgba(255,255,255,0.2); transform: translateY(-2px);
                box-shadow: 0 12px 24px rgba(0,0,0,0.2);
            }
            .endpoint-title {
                font-size: 1.3em; font-weight: bold; margin-bottom: 10px;
                display: flex; align-items: center;
            }
            .endpoint-icon {
                font-size: 1.5em; margin-right: 15px;
            }
            .endpoint-desc {
                opacity: 0.9; margin-bottom: 15px; line-height: 1.6;
            }
            .endpoint-link {
                display: inline-block; padding: 12px 24px;
                background: rgba(255,255,255,0.2); color: white; text-decoration: none;
                border-radius: 8px; font-weight: bold; transition: all 0.3s ease;
                border: 1px solid rgba(255,255,255,0.3);
            }
            .endpoint-link:hover {
                background: rgba(255,255,255,0.3); transform: scale(1.05);
                text-decoration: none; color: white;
            }
            .status-info {
                background: rgba(0,255,0,0.1); padding: 20px; border-radius: 12px;
                border: 1px solid rgba(0,255,0,0.3); margin: 30px 0;
                text-align: center;
            }
            .status-badge {
                display: inline-block; padding: 6px 12px;
                background: rgba(0,255,0,0.2); border-radius: 20px;
                font-size: 0.9em; font-weight: bold;
            }
            .footer {
                text-align: center; margin-top: 40px; opacity: 0.7;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Newzora</h1>
            <div class="subtitle">Modern Content Platform - Frontend Server</div>

            <div class="status-info">
                <div class="status-badge">✅ Server Running</div>
                <div style="margin-top: 10px;">
                    Frontend: <strong>http://localhost:3000</strong><br>
                    Backend: <strong>${BACKEND_URL}</strong>
                </div>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">🏠</span>
                    Newzora Homepage
                </div>
                <div class="endpoint-desc">
                    Complete Newzora application with article feeds, infinite scroll, categories,
                    user authentication, and full social features.
                </div>
                <a href="/app" class="endpoint-link">Open Newzora App</a>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">🧪</span>
                    Authentication Test Page
                </div>
                <div class="endpoint-desc">
                    Complete authentication testing interface with quick login buttons for test accounts,
                    manual login forms, user registration, and password strength validation.
                </div>
                <a href="/test-auth" class="endpoint-link">Open Test Page</a>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">📊</span>
                    Health Check
                </div>
                <div class="endpoint-desc">
                    Server health status and system information endpoint for monitoring
                    frontend server status and backend connectivity.
                </div>
                <a href="/health" class="endpoint-link">Check Health</a>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">🔧</span>
                    Backend API Test
                </div>
                <div class="endpoint-desc">
                    Comprehensive backend API testing interface with endpoints for authentication,
                    articles, users, search, and more. Test all backend functionality.
                </div>
                <a href="${BACKEND_URL}/test" class="endpoint-link">Open Backend Test</a>
            </div>

            <div class="footer">
                <p>Newzora Frontend Server v1.0.0</p>
                <p>Built with Node.js + Express</p>
            </div>
        </div>

        <script>
            // 自动检查后端连接状态
            async function checkBackendStatus() {
                try {
                    const response = await fetch('${BACKEND_URL}/api/health');
                    if (response.ok) {
                        console.log('✅ Backend connection: OK');
                    } else {
                        console.log('⚠️ Backend connection: Issues detected');
                    }
                } catch (error) {
                    console.log('❌ Backend connection: Failed');
                }
            }

            // 页面加载时检查后端状态
            window.addEventListener('load', checkBackendStatus);
        </script>
    </body>
    </html>
  `);
});

// 5. 错误处理
app.use((error, req, res, next) => {
  console.error('Frontend Error:', error);
  res.status(500).json({
    error: 'Frontend server error',
    message: error.message
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ 前端服务器启动成功!`);
  console.log(`🌐 访问地址: http://localhost:${PORT}`);
  console.log(`🧪 测试页面: http://localhost:${PORT}/test-auth`);
  console.log(`🔗 后端代理: ${BACKEND_URL}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用`);
    console.log('💡 请先停止其他服务或使用不同端口');
  } else {
    console.error('❌ 服务器启动失败:', error.message);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

console.log('🎯 前端服务器配置完成，等待连接...');
