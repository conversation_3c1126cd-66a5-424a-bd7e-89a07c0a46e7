#!/usr/bin/env node

/**
 * 简化的前端服务器
 * 用于测试和开发
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = 3000;
const BACKEND_URL = 'http://localhost:5000';

console.log('🎨 启动简化前端服务器...');
console.log('============================');

// 1. API代理中间件
app.use('/api', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  secure: false,
  timeout: 30000,
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({
      error: 'Backend server not available',
      message: 'Please ensure backend server is running on port 5000'
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`代理请求: ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
  }
}));

// 2. 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    backend: BACKEND_URL,
    frontend: `http://localhost:${PORT}`
  });
});

// 3. 测试页面
app.get('/test-auth', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Authentication Test</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 800px; margin: 0 auto; padding: 20px; 
                background: #f5f5f5;
            }
            .container { 
                background: white; padding: 30px; border-radius: 8px; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input { 
                width: 100%; padding: 10px; border: 1px solid #ddd; 
                border-radius: 4px; font-size: 16px;
            }
            button { 
                background: #007bff; color: white; padding: 12px 24px; 
                border: none; border-radius: 4px; cursor: pointer; font-size: 16px;
            }
            button:hover { background: #0056b3; }
            .result { 
                margin-top: 20px; padding: 15px; border-radius: 4px; 
                white-space: pre-wrap; font-family: monospace;
            }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .account-btn {
                display: inline-block; margin: 5px; padding: 8px 16px;
                background: #28a745; color: white; text-decoration: none;
                border-radius: 4px; font-size: 14px;
            }
            .account-btn:hover { background: #218838; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 Newzora Authentication Test</h1>
            
            <h2>Quick Test Accounts</h2>
            <div>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Admin123!')">Admin Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'User123!')">User Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Moderator123!')">Moderator Login</a>
            </div>
            
            <h2>Manual Login Test</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="identifier">Email or Username:</label>
                    <input type="text" id="identifier" name="identifier" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" placeholder="Admin123!">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="rememberMe" name="rememberMe"> Remember Me
                    </label>
                </div>
                <button type="submit">Login</button>
            </form>
            
            <h2>Registration Test</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="regUsername">Username:</label>
                    <input type="text" id="regUsername" name="username" placeholder="newuser">
                </div>
                <div class="form-group">
                    <label for="regEmail">Email:</label>
                    <input type="email" id="regEmail" name="email" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="regPassword">Password:</label>
                    <input type="password" id="regPassword" name="password" placeholder="NewPassword123!">
                </div>
                <div class="form-group">
                    <label for="regFirstName">First Name:</label>
                    <input type="text" id="regFirstName" name="firstName" placeholder="John">
                </div>
                <div class="form-group">
                    <label for="regLastName">Last Name:</label>
                    <input type="text" id="regLastName" name="lastName" placeholder="Doe">
                </div>
                <button type="submit">Register</button>
            </form>
            
            <div id="result"></div>
        </div>

        <script>
            function showResult(data, isError = false) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result ' + (isError ? 'error' : 'success');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            }

            function quickLogin(identifier, password) {
                login(identifier, password, false);
            }

            async function login(identifier, password, rememberMe) {
                try {
                    const response = await fetch('/api/auth-enhanced/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            identifier,
                            password,
                            rememberMe
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Login successful!',
                            user: data.data.user,
                            hasToken: !!data.data.tokens.accessToken
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Login failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message,
                        note: 'This might be the JSON parsing error you encountered'
                    }, true);
                }
            }

            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                await login(
                    formData.get('identifier'),
                    formData.get('password'),
                    formData.get('rememberMe') === 'on'
                );
            });

            document.getElementById('registerForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                
                try {
                    const response = await fetch('/api/auth-enhanced/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: formData.get('username'),
                            email: formData.get('email'),
                            password: formData.get('password'),
                            firstName: formData.get('firstName'),
                            lastName: formData.get('lastName'),
                            acceptTerms: true
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Registration successful!',
                            user: data.data.user
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Registration failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message
                    }, true);
                }
            });

            // 页面加载时测试API连接
            window.addEventListener('load', async () => {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    console.log('Backend health check:', data);
                } catch (error) {
                    console.error('Backend connection failed:', error);
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 4. 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'Newzora Frontend Server',
    status: 'running',
    endpoints: {
      health: '/health',
      testAuth: '/test-auth',
      api: '/api/*'
    },
    backend: BACKEND_URL
  });
});

// 5. 错误处理
app.use((error, req, res, next) => {
  console.error('Frontend Error:', error);
  res.status(500).json({
    error: 'Frontend server error',
    message: error.message
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ 前端服务器启动成功!`);
  console.log(`🌐 访问地址: http://localhost:${PORT}`);
  console.log(`🧪 测试页面: http://localhost:${PORT}/test-auth`);
  console.log(`🔗 后端代理: ${BACKEND_URL}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用`);
    console.log('💡 请先停止其他服务或使用不同端口');
  } else {
    console.error('❌ 服务器启动失败:', error.message);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

console.log('🎯 前端服务器配置完成，等待连接...');
