#!/usr/bin/env node

/**
 * 简化的前端服务器
 * 用于测试和开发
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = 3000;
const BACKEND_URL = 'http://localhost:5000';

console.log('🎨 启动简化前端服务器...');
console.log('============================');

// 1. API代理中间件
app.use('/api', createProxyMiddleware({
  target: BACKEND_URL,
  changeOrigin: true,
  secure: false,
  timeout: 30000,
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({
      error: 'Backend server not available',
      message: 'Please ensure backend server is running on port 5000'
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`代理请求: ${req.method} ${req.url} -> ${BACKEND_URL}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
  }
}));

// 2. 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    backend: BACKEND_URL,
    frontend: `http://localhost:${PORT}`
  });
});

// 3. 测试页面
app.get('/test-auth', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Authentication Test</title>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 800px; margin: 0 auto; padding: 20px; 
                background: #f5f5f5;
            }
            .container { 
                background: white; padding: 30px; border-radius: 8px; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input { 
                width: 100%; padding: 10px; border: 1px solid #ddd; 
                border-radius: 4px; font-size: 16px;
            }
            button { 
                background: #007bff; color: white; padding: 12px 24px; 
                border: none; border-radius: 4px; cursor: pointer; font-size: 16px;
            }
            button:hover { background: #0056b3; }
            .result { 
                margin-top: 20px; padding: 15px; border-radius: 4px; 
                white-space: pre-wrap; font-family: monospace;
            }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .account-btn {
                display: inline-block; margin: 5px; padding: 8px 16px;
                background: #28a745; color: white; text-decoration: none;
                border-radius: 4px; font-size: 14px;
            }
            .account-btn:hover { background: #218838; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 Newzora Authentication Test</h1>
            
            <h2>Quick Test Accounts</h2>
            <div>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Admin123!')">Admin Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'User123!')">User Login</a>
                <a href="#" class="account-btn" onclick="quickLogin('<EMAIL>', 'Moderator123!')">Moderator Login</a>
            </div>
            
            <h2>Manual Login Test</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="identifier">Email or Username:</label>
                    <input type="text" id="identifier" name="identifier" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" placeholder="Admin123!">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="rememberMe" name="rememberMe"> Remember Me
                    </label>
                </div>
                <button type="submit">Login</button>
            </form>
            
            <h2>Registration Test</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="regUsername">Username:</label>
                    <input type="text" id="regUsername" name="username" placeholder="newuser">
                </div>
                <div class="form-group">
                    <label for="regEmail">Email:</label>
                    <input type="email" id="regEmail" name="email" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="regPassword">Password:</label>
                    <input type="password" id="regPassword" name="password" placeholder="NewPassword123!">
                </div>
                <div class="form-group">
                    <label for="regFirstName">First Name:</label>
                    <input type="text" id="regFirstName" name="firstName" placeholder="John">
                </div>
                <div class="form-group">
                    <label for="regLastName">Last Name:</label>
                    <input type="text" id="regLastName" name="lastName" placeholder="Doe">
                </div>
                <button type="submit">Register</button>
            </form>
            
            <div id="result"></div>
        </div>

        <script>
            function showResult(data, isError = false) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result ' + (isError ? 'error' : 'success');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            }

            function quickLogin(identifier, password) {
                login(identifier, password, false);
            }

            async function login(identifier, password, rememberMe) {
                try {
                    const response = await fetch('/api/auth-enhanced/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            identifier,
                            password,
                            rememberMe
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Login successful!',
                            user: data.data.user,
                            hasToken: !!data.data.tokens.accessToken
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Login failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message,
                        note: 'This might be the JSON parsing error you encountered'
                    }, true);
                }
            }

            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                await login(
                    formData.get('identifier'),
                    formData.get('password'),
                    formData.get('rememberMe') === 'on'
                );
            });

            document.getElementById('registerForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                
                try {
                    const response = await fetch('/api/auth-enhanced/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: formData.get('username'),
                            email: formData.get('email'),
                            password: formData.get('password'),
                            firstName: formData.get('firstName'),
                            lastName: formData.get('lastName'),
                            acceptTerms: true
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        showResult({
                            success: true,
                            message: 'Registration successful!',
                            user: data.data.user
                        });
                    } else {
                        showResult({
                            success: false,
                            message: data.message || 'Registration failed',
                            status: response.status
                        }, true);
                    }
                } catch (error) {
                    showResult({
                        success: false,
                        message: 'Network error: ' + error.message
                    }, true);
                }
            });

            // 页面加载时测试API连接
            window.addEventListener('load', async () => {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    console.log('Backend health check:', data);
                } catch (error) {
                    console.error('Backend connection failed:', error);
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 4. 根路径 - 欢迎页面
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Newzora Frontend Server</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 800px; margin: 0 auto; padding: 40px 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh; color: white;
            }
            .container {
                background: rgba(255,255,255,0.1); padding: 40px; border-radius: 16px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3); backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            }
            h1 {
                font-size: 2.5em; margin-bottom: 10px; text-align: center;
                background: linear-gradient(45deg, #fff, #f0f0f0);
                -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            .subtitle {
                text-align: center; font-size: 1.2em; margin-bottom: 40px;
                opacity: 0.9;
            }
            .endpoint-card {
                background: rgba(255,255,255,0.1); margin: 20px 0; padding: 25px;
                border-radius: 12px; border: 1px solid rgba(255,255,255,0.2);
                transition: all 0.3s ease;
            }
            .endpoint-card:hover {
                background: rgba(255,255,255,0.2); transform: translateY(-2px);
                box-shadow: 0 12px 24px rgba(0,0,0,0.2);
            }
            .endpoint-title {
                font-size: 1.3em; font-weight: bold; margin-bottom: 10px;
                display: flex; align-items: center;
            }
            .endpoint-icon {
                font-size: 1.5em; margin-right: 15px;
            }
            .endpoint-desc {
                opacity: 0.9; margin-bottom: 15px; line-height: 1.6;
            }
            .endpoint-link {
                display: inline-block; padding: 12px 24px;
                background: rgba(255,255,255,0.2); color: white; text-decoration: none;
                border-radius: 8px; font-weight: bold; transition: all 0.3s ease;
                border: 1px solid rgba(255,255,255,0.3);
            }
            .endpoint-link:hover {
                background: rgba(255,255,255,0.3); transform: scale(1.05);
                text-decoration: none; color: white;
            }
            .status-info {
                background: rgba(0,255,0,0.1); padding: 20px; border-radius: 12px;
                border: 1px solid rgba(0,255,0,0.3); margin: 30px 0;
                text-align: center;
            }
            .status-badge {
                display: inline-block; padding: 6px 12px;
                background: rgba(0,255,0,0.2); border-radius: 20px;
                font-size: 0.9em; font-weight: bold;
            }
            .footer {
                text-align: center; margin-top: 40px; opacity: 0.7;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Newzora</h1>
            <div class="subtitle">Modern Content Platform - Frontend Server</div>

            <div class="status-info">
                <div class="status-badge">✅ Server Running</div>
                <div style="margin-top: 10px;">
                    Frontend: <strong>http://localhost:3000</strong><br>
                    Backend: <strong>${BACKEND_URL}</strong>
                </div>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">🧪</span>
                    Authentication Test Page
                </div>
                <div class="endpoint-desc">
                    Complete authentication testing interface with quick login buttons for test accounts,
                    manual login forms, user registration, and password strength validation.
                </div>
                <a href="/test-auth" class="endpoint-link">Open Test Page</a>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">📊</span>
                    Health Check
                </div>
                <div class="endpoint-desc">
                    Server health status and system information endpoint for monitoring
                    frontend server status and backend connectivity.
                </div>
                <a href="/health" class="endpoint-link">Check Health</a>
            </div>

            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-icon">🔧</span>
                    Backend API Test
                </div>
                <div class="endpoint-desc">
                    Comprehensive backend API testing interface with endpoints for authentication,
                    articles, users, search, and more. Test all backend functionality.
                </div>
                <a href="${BACKEND_URL}/test" class="endpoint-link">Open Backend Test</a>
            </div>

            <div class="footer">
                <p>Newzora Frontend Server v1.0.0</p>
                <p>Built with Node.js + Express</p>
            </div>
        </div>

        <script>
            // 自动检查后端连接状态
            async function checkBackendStatus() {
                try {
                    const response = await fetch('${BACKEND_URL}/api/health');
                    if (response.ok) {
                        console.log('✅ Backend connection: OK');
                    } else {
                        console.log('⚠️ Backend connection: Issues detected');
                    }
                } catch (error) {
                    console.log('❌ Backend connection: Failed');
                }
            }

            // 页面加载时检查后端状态
            window.addEventListener('load', checkBackendStatus);
        </script>
    </body>
    </html>
  `);
});

// 5. 错误处理
app.use((error, req, res, next) => {
  console.error('Frontend Error:', error);
  res.status(500).json({
    error: 'Frontend server error',
    message: error.message
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ 前端服务器启动成功!`);
  console.log(`🌐 访问地址: http://localhost:${PORT}`);
  console.log(`🧪 测试页面: http://localhost:${PORT}/test-auth`);
  console.log(`🔗 后端代理: ${BACKEND_URL}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用`);
    console.log('💡 请先停止其他服务或使用不同端口');
  } else {
    console.error('❌ 服务器启动失败:', error.message);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 前端服务器已关闭');
    process.exit(0);
  });
});

console.log('🎯 前端服务器配置完成，等待连接...');
