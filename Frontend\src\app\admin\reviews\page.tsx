'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';

interface ContentReview {
  id: number;
  contentType: 'article' | 'comment' | 'media';
  contentId: number;
  title: string;
  author: string;
  status: 'pending' | 'approved' | 'rejected';
  riskLevel: 'low' | 'medium' | 'high';
  flaggedReasons: string[];
  aiScore: number;
  createdAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  content: string;
}

export default function AdminReviewsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [reviews, setReviews] = useState<ContentReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [riskFilter, setRiskFilter] = useState<string>('all');
  const [selectedReview, setSelectedReview] = useState<ContentReview | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Check if user is admin or moderator
    if (user && !['admin', 'moderator'].includes(user.role)) {
      router.push('/');
      return;
    }

    fetchReviews();
  }, [user, router, statusFilter, riskFilter]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      // Mock data for now - will be replaced with real API calls
      setTimeout(() => {
        const mockReviews: ContentReview[] = [
          {
            id: 1,
            contentType: 'article',
            contentId: 101,
            title: 'Breaking News: Major Political Development',
            author: 'john_reporter',
            status: 'pending',
            riskLevel: 'high',
            flaggedReasons: ['Political Content', 'Potential Misinformation'],
            aiScore: 0.85,
            createdAt: '2024-01-20T10:30:00Z',
            content: 'This article discusses recent political developments that may require careful review...'
          },
          {
            id: 2,
            contentType: 'comment',
            contentId: 205,
            title: 'User comment on "Tech Innovation"',
            author: 'tech_enthusiast',
            status: 'pending',
            riskLevel: 'medium',
            flaggedReasons: ['Spam Detection', 'Promotional Content'],
            aiScore: 0.65,
            createdAt: '2024-01-20T09:15:00Z',
            content: 'Check out this amazing product that will change your life! Visit our website...'
          },
          {
            id: 3,
            contentType: 'article',
            contentId: 102,
            title: 'Health Tips for Better Living',
            author: 'health_writer',
            status: 'approved',
            riskLevel: 'low',
            flaggedReasons: [],
            aiScore: 0.15,
            createdAt: '2024-01-20T08:45:00Z',
            reviewedAt: '2024-01-20T09:00:00Z',
            reviewedBy: 'moderator_jane',
            content: 'Here are some evidence-based health tips for maintaining a healthy lifestyle...'
          },
          {
            id: 4,
            contentType: 'media',
            contentId: 301,
            title: 'Uploaded Image: protest_scene.jpg',
            author: 'news_photographer',
            status: 'pending',
            riskLevel: 'high',
            flaggedReasons: ['Violence Detection', 'Sensitive Content'],
            aiScore: 0.92,
            createdAt: '2024-01-20T07:20:00Z',
            content: 'Image contains scenes from a public demonstration with potential sensitive content...'
          },
          {
            id: 5,
            contentType: 'article',
            contentId: 103,
            title: 'Inappropriate Content Example',
            author: 'bad_actor',
            status: 'rejected',
            riskLevel: 'high',
            flaggedReasons: ['Hate Speech', 'Harassment'],
            aiScore: 0.95,
            createdAt: '2024-01-19T16:30:00Z',
            reviewedAt: '2024-01-19T17:00:00Z',
            reviewedBy: 'admin_user',
            content: 'This content was flagged for containing inappropriate material...'
          }
        ];

        setReviews(mockReviews);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch reviews:', error);
      setLoading(false);
    }
  };

  const handleReviewAction = async (reviewId: number, action: 'approve' | 'reject', reason?: string) => {
    try {
      // API call to update review status
      console.log(`${action} review ${reviewId}`, reason);
      
      // Update local state
      setReviews(reviews.map(review => 
        review.id === reviewId 
          ? { 
              ...review, 
              status: action === 'approve' ? 'approved' : 'rejected',
              reviewedAt: new Date().toISOString(),
              reviewedBy: user?.username || 'current_user'
            }
          : review
      ));
      
      setShowModal(false);
      setSelectedReview(null);
    } catch (error) {
      console.error('Failed to update review:', error);
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'comment':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'media':
        return (
          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredReviews = reviews.filter(review => {
    const statusMatch = statusFilter === 'all' || review.status === statusFilter;
    const riskMatch = riskFilter === 'all' || review.riskLevel === riskFilter;
    return statusMatch && riskMatch;
  });

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-64 mb-8"></div>
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-6 border-b border-gray-200">
                  <div className="h-6 bg-gray-300 rounded w-32 mb-4"></div>
                  <div className="flex space-x-4">
                    <div className="h-10 bg-gray-300 rounded w-32"></div>
                    <div className="h-10 bg-gray-300 rounded w-32"></div>
                  </div>
                </div>
                <div className="divide-y divide-gray-200">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-300 rounded"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-300 rounded w-64 mb-2"></div>
                          <div className="h-3 bg-gray-300 rounded w-32"></div>
                        </div>
                        <div className="h-6 bg-gray-300 rounded w-16"></div>
                        <div className="h-6 bg-gray-300 rounded w-16"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Content Review</h1>
                <p className="mt-2 text-gray-600">Review and moderate platform content</p>
              </div>
              <button
                onClick={() => router.push('/admin')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Dashboard
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reviews.filter(r => r.status === 'pending').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reviews.filter(r => r.status === 'approved').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reviews.filter(r => r.status === 'rejected').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">High Risk</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reviews.filter(r => r.riskLevel === 'high').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Table Header with Filters */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <h2 className="text-lg font-semibold text-gray-900">
                  Content Reviews ({filteredReviews.length})
                </h2>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                  {/* Status Filter */}
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>

                  {/* Risk Filter */}
                  <select
                    value={riskFilter}
                    onChange={(e) => setRiskFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Risk Levels</option>
                    <option value="high">High Risk</option>
                    <option value="medium">Medium Risk</option>
                    <option value="low">Low Risk</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Reviews List */}
            <div className="divide-y divide-gray-200">
              {filteredReviews.map((review) => (
                <div key={review.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      {/* Content Type Icon */}
                      <div className="flex-shrink-0 mt-1">
                        {getContentTypeIcon(review.contentType)}
                      </div>

                      {/* Content Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {review.title}
                          </h3>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(review.status)}`}>
                            {review.status}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskColor(review.riskLevel)}`}>
                            {review.riskLevel} risk
                          </span>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                          <span>By {review.author}</span>
                          <span>•</span>
                          <span>{formatDate(review.createdAt)}</span>
                          <span>•</span>
                          <span>AI Score: {(review.aiScore * 100).toFixed(0)}%</span>
                        </div>

                        {/* Flagged Reasons */}
                        {review.flaggedReasons.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {review.flaggedReasons.map((reason, index) => (
                              <span
                                key={index}
                                className="inline-flex px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded"
                              >
                                {reason}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Content Preview */}
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {review.content}
                        </p>

                        {/* Review Info */}
                        {review.reviewedAt && (
                          <div className="mt-2 text-xs text-gray-500">
                            Reviewed by {review.reviewedBy} on {formatDate(review.reviewedAt)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex-shrink-0 ml-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedReview(review);
                            setShowModal(true);
                          }}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          Review
                        </button>

                        {review.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleReviewAction(review.id, 'approve')}
                              className="inline-flex items-center px-3 py-1 border border-transparent rounded-md text-xs font-medium text-white bg-green-600 hover:bg-green-700"
                            >
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              Approve
                            </button>
                            <button
                              onClick={() => handleReviewAction(review.id, 'reject')}
                              className="inline-flex items-center px-3 py-1 border border-transparent rounded-md text-xs font-medium text-white bg-red-600 hover:bg-red-700"
                            >
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                              Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {filteredReviews.length === 0 && (
                <div className="p-12 text-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    No content reviews match your current filters.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Review Modal */}
          {showModal && selectedReview && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
              <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                  {/* Modal Header */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Review Content
                    </h3>
                    <button
                      onClick={() => setShowModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  {/* Content Details */}
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Content Information</h4>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Type:</span> {selectedReview.contentType}
                          </div>
                          <div>
                            <span className="font-medium">Author:</span> {selectedReview.author}
                          </div>
                          <div>
                            <span className="font-medium">Risk Level:</span>
                            <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getRiskColor(selectedReview.riskLevel)}`}>
                              {selectedReview.riskLevel}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium">AI Score:</span> {(selectedReview.aiScore * 100).toFixed(0)}%
                          </div>
                        </div>
                      </div>
                    </div>

                    {selectedReview.flaggedReasons.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Flagged Reasons</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedReview.flaggedReasons.map((reason, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 text-sm bg-red-100 text-red-800 rounded-full"
                            >
                              {reason}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Content</h4>
                      <div className="bg-gray-50 p-4 rounded-lg max-h-64 overflow-y-auto">
                        <p className="text-sm text-gray-700 whitespace-pre-wrap">
                          {selectedReview.content}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Modal Actions */}
                  {selectedReview.status === 'pending' && (
                    <div className="flex justify-end space-x-3 mt-6">
                      <button
                        onClick={() => setShowModal(false)}
                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleReviewAction(selectedReview.id, 'reject')}
                        className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                      >
                        Reject
                      </button>
                      <button
                        onClick={() => handleReviewAction(selectedReview.id, 'approve')}
                        className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                      >
                        Approve
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
