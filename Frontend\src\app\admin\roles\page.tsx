'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Permission {
  id: string;
  name: string;
  displayName: string;
  description: string;
  resource: string;
  action: string;
  scope: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  level: number;
  isSystem: boolean;
  isActive: boolean;
  permissions: Permission[];
  userCount: number;
  createdAt: string;
}

export default function AdminRoles() {
  const { user } = useAuth();
  const router = useRouter();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (user.role !== 'admin' && user.role !== 'super_admin') {
      router.push('/unauthorized');
      return;
    }

    fetchRolesAndPermissions();
  }, [user, router]);

  const fetchRolesAndPermissions = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls for now
      setTimeout(() => {
        // Mock data
        const mockRoles: Role[] = [
          {
            id: '1',
            name: 'super_admin',
            displayName: 'Super Administrator',
            description: 'Full system access with all permissions',
            level: 100,
            isSystem: true,
            isActive: true,
            permissions: [],
            userCount: 1,
            createdAt: new Date().toISOString()
          },
          {
            id: '2',
            name: 'admin',
            displayName: 'Administrator',
            description: 'Administrative access to manage users and content',
            level: 80,
            isSystem: true,
            isActive: true,
            permissions: [],
            userCount: 3,
            createdAt: new Date().toISOString()
          },
          {
            id: '3',
            name: 'moderator',
            displayName: 'Moderator',
            description: 'Content moderation and user management',
            level: 60,
            isSystem: true,
            isActive: true,
            permissions: [],
            userCount: 5,
            createdAt: new Date().toISOString()
          },
          {
            id: '4',
            name: 'user',
            displayName: 'User',
            description: 'Standard user with basic permissions',
            level: 10,
            isSystem: true,
            isActive: true,
            permissions: [],
            userCount: 150,
            createdAt: new Date().toISOString()
          }
        ];

        const mockPermissions: Permission[] = [
          {
            id: '1',
            name: 'user:read',
            displayName: 'View Users',
            description: 'View user profiles and information',
            resource: 'user',
            action: 'read',
            scope: 'all'
          },
          {
            id: '2',
            name: 'user:manage',
            displayName: 'Manage Users',
            description: 'Create, update, and delete users',
            resource: 'user',
            action: 'manage',
            scope: 'all'
          },
          {
            id: '3',
            name: 'article:read',
            displayName: 'View Articles',
            description: 'View published articles',
            resource: 'article',
            action: 'read',
            scope: 'all'
          },
          {
            id: '4',
            name: 'article:create',
            displayName: 'Create Articles',
            description: 'Create new articles',
            resource: 'article',
            action: 'create',
            scope: 'own'
          },
          {
            id: '5',
            name: 'article:manage',
            displayName: 'Manage Articles',
            description: 'Edit and delete any articles',
            resource: 'article',
            action: 'manage',
            scope: 'all'
          }
        ];

        setRoles(mockRoles);
        setPermissions(mockPermissions);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Fetch roles error:', error);
      setError('Failed to load roles and permissions');
      setLoading(false);
    }
  };

  const getRoleBadgeColor = (role: Role) => {
    if (role.level >= 100) return 'bg-purple-100 text-purple-800';
    if (role.level >= 80) return 'bg-red-100 text-red-800';
    if (role.level >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600 mr-8">
                Newzora
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Roles & Permissions</h1>
            </div>
            <Link
              href="/admin/dashboard"
              className="bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-200"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-indigo-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 py-4">
            <Link href="/admin/dashboard" className="text-indigo-200 hover:text-white">
              Dashboard
            </Link>
            <Link href="/admin/users" className="text-indigo-200 hover:text-white">
              Users
            </Link>
            <Link href="/admin/roles" className="text-white font-medium border-b-2 border-white pb-2">
              Roles & Permissions
            </Link>
            <Link href="/admin/content" className="text-indigo-200 hover:text-white">
              Content
            </Link>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Action Bar */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Role Management</h2>
            <p className="text-sm text-gray-500">Manage user roles and their permissions</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowPermissionModal(true)}
              className="bg-white text-gray-700 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
            >
              Manage Permissions
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm hover:bg-indigo-700"
            >
              Create Role
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="bg-white shadow rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading roles and permissions...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Roles List */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Roles ({roles.length})</h3>
              </div>
              <div className="divide-y divide-gray-200">
                {roles.map((role) => (
                  <div
                    key={role.id}
                    className={`p-6 hover:bg-gray-50 cursor-pointer ${
                      selectedRole?.id === role.id ? 'bg-indigo-50 border-l-4 border-indigo-500' : ''
                    }`}
                    onClick={() => setSelectedRole(role)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div>
                          <div className="flex items-center">
                            <h4 className="text-sm font-medium text-gray-900">{role.displayName}</h4>
                            <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeColor(role)}`}>
                              Level {role.level}
                            </span>
                            {role.isSystem && (
                              <span className="ml-2 px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                System
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{role.description}</p>
                          <div className="flex items-center mt-2 text-xs text-gray-400">
                            <span>{role.userCount} users</span>
                            <span className="mx-2">•</span>
                            <span>Created {formatDate(role.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {!role.isSystem && (
                          <>
                            <button className="text-indigo-600 hover:text-indigo-900 text-sm">
                              Edit
                            </button>
                            <button className="text-red-600 hover:text-red-900 text-sm">
                              Delete
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Role Details / Permissions */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {selectedRole ? `${selectedRole.displayName} Permissions` : 'Select a Role'}
                </h3>
              </div>
              <div className="p-6">
                {selectedRole ? (
                  <div>
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Role Information</h4>
                      <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Name</dt>
                          <dd className="text-sm text-gray-900">{selectedRole.name}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Level</dt>
                          <dd className="text-sm text-gray-900">{selectedRole.level}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Users</dt>
                          <dd className="text-sm text-gray-900">{selectedRole.userCount}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Status</dt>
                          <dd className="text-sm text-gray-900">
                            {selectedRole.isActive ? 'Active' : 'Inactive'}
                          </dd>
                        </div>
                      </dl>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="text-sm font-medium text-gray-900">Permissions</h4>
                        {!selectedRole.isSystem && (
                          <button className="text-indigo-600 hover:text-indigo-900 text-sm">
                            Edit Permissions
                          </button>
                        )}
                      </div>
                      
                      {permissions.length > 0 ? (
                        <div className="space-y-2">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{permission.displayName}</div>
                                <div className="text-xs text-gray-500">{permission.description}</div>
                              </div>
                              <div className="flex items-center">
                                <span className="text-xs text-gray-400 mr-2">{permission.resource}:{permission.action}</span>
                                <input
                                  type="checkbox"
                                  checked={Math.random() > 0.5} // Mock checked state
                                  disabled={selectedRole.isSystem}
                                  className="rounded border-gray-300"
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center text-gray-500 py-4">
                          <p>No permissions configured</p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <p className="mt-2">Select a role to view its permissions</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Permission Summary */}
        <div className="mt-6 bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Permission Overview</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">{roles.length}</div>
                <div className="text-sm text-gray-500">Total Roles</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{permissions.length}</div>
                <div className="text-sm text-gray-500">Total Permissions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {roles.reduce((sum, role) => sum + role.userCount, 0)}
                </div>
                <div className="text-sm text-gray-500">Total Users</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
