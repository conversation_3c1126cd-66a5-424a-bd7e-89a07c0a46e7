'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import ArticleModal from '@/components/ArticleModal';
import { mockArticles } from '@/data/mockArticles';

export default function AlternativeSolutions() {
  const router = useRouter();
  const [modalArticleId, setModalArticleId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (articleId: string) => {
    setModalArticleId(articleId);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setModalArticleId(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🔄 替代解决方案</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">📋 可用的替代方案</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            {/* 方案1: 查询参数方式 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">方案1: 查询参数方式</h3>
              <p className="text-sm text-gray-600 mb-4">
                使用 /article-viewer?id=1 的方式，避免动态路由问题
              </p>
              <div className="space-y-2">
                {mockArticles.slice(0, 3).map((article) => (
                  <button
                    key={article.id}
                    onClick={() => router.push(`/article-viewer?id=${article.id}`)}
                    className="w-full text-left p-2 border border-gray-200 rounded hover:bg-gray-50"
                  >
                    <div className="text-sm font-medium">{article.title.substring(0, 30)}...</div>
                    <div className="text-xs text-gray-500">ID: {article.id}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 方案2: 弹窗方式 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">方案2: 弹窗方式</h3>
              <p className="text-sm text-gray-600 mb-4">
                在当前页面弹出模态框显示文章内容
              </p>
              <div className="space-y-2">
                {mockArticles.slice(0, 3).map((article) => (
                  <button
                    key={article.id}
                    onClick={() => openModal(article.id)}
                    className="w-full text-left p-2 border border-gray-200 rounded hover:bg-gray-50"
                  >
                    <div className="text-sm font-medium">{article.title.substring(0, 30)}...</div>
                    <div className="text-xs text-gray-500">ID: {article.id}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 方案3: 直接跳转外部链接 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">方案3: 外部链接方式</h3>
              <p className="text-sm text-gray-600 mb-4">
                使用 window.location.href 直接跳转
              </p>
              <div className="space-y-2">
                {mockArticles.slice(0, 3).map((article) => (
                  <button
                    key={article.id}
                    onClick={() => window.location.href = `/article-viewer?id=${article.id}`}
                    className="w-full text-left p-2 border border-gray-200 rounded hover:bg-gray-50"
                  >
                    <div className="text-sm font-medium">{article.title.substring(0, 30)}...</div>
                    <div className="text-xs text-gray-500">ID: {article.id}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* 方案4: 新窗口打开 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">方案4: 新窗口打开</h3>
              <p className="text-sm text-gray-600 mb-4">
                在新标签页中打开文章
              </p>
              <div className="space-y-2">
                {mockArticles.slice(0, 3).map((article) => (
                  <button
                    key={article.id}
                    onClick={() => window.open(`/article-viewer?id=${article.id}`, '_blank')}
                    className="w-full text-left p-2 border border-gray-200 rounded hover:bg-gray-50"
                  >
                    <div className="text-sm font-medium">{article.title.substring(0, 30)}...</div>
                    <div className="text-xs text-gray-500">ID: {article.id}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">💡 推荐方案</h2>
          <div className="text-sm text-blue-700 space-y-2">
            <p><strong>最稳定</strong>: 方案1 (查询参数方式) - 不依赖动态路由</p>
            <p><strong>最流畅</strong>: 方案2 (弹窗方式) - 用户体验最好</p>
            <p><strong>最简单</strong>: 方案3 (外部链接) - 最直接的跳转方式</p>
            <p><strong>最灵活</strong>: 方案4 (新窗口) - 不影响当前页面</p>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 当前状态</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ ArticleCard已更新</strong>: 现在跳转到 /article-viewer?id=</p>
            <p><strong>✅ Explore页面已更新</strong>: 现在跳转到 /article-viewer?id=</p>
            <p><strong>✅ 新查看器已创建</strong>: /article-viewer 页面可用</p>
            <p><strong>✅ 弹窗组件已创建</strong>: ArticleModal 组件可用</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🏠 测试首页
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              🔍 测试探索页面
            </button>
            <button
              onClick={() => router.push('/article-viewer?id=1')}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              🧪 测试查看器
            </button>
          </div>
        </div>
      </div>

      {/* 文章弹窗 */}
      <ArticleModal
        articleId={modalArticleId}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
}
