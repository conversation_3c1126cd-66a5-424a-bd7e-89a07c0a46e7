'use client';

import { useState } from 'react';
import Header from '@/components/Header';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface TestResult {
  endpoint: string;
  method: string;
  status: number | null;
  success: boolean | null;
  data: any;
  error: string | null;
  timestamp: string;
}

export default function ApiTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);

  const apiEndpoints = [
    { method: 'GET', endpoint: '/health', description: '健康检查' },
    { method: 'POST', endpoint: '/auth-enhanced/debug-login', description: '调试登录', 
      data: { identifier: '<EMAIL>', password: 'Admin123!' } },
    { method: 'POST', endpoint: '/auth-enhanced/check-password-strength', description: '密码强度检查',
      data: { password: 'Admin123!' } },
    { method: 'GET', endpoint: '/articles/1', description: '获取文章详情' },
    { method: 'GET', endpoint: '/articles', description: '获取文章列表' }
  ];

  const testEndpoint = async (endpoint: any) => {
    const startTime = new Date().toISOString();
    
    try {
      const url = `${API_BASE}${endpoint.endpoint}`;
      const options: RequestInit = {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (endpoint.data) {
        options.body = JSON.stringify(endpoint.data);
      }

      const response = await fetch(url, options);
      const data = await response.json();

      const result: TestResult = {
        endpoint: endpoint.endpoint,
        method: endpoint.method,
        status: response.status,
        success: response.ok,
        data: data,
        error: null,
        timestamp: startTime
      };

      setTestResults(prev => [result, ...prev]);
      return result;

    } catch (error) {
      const result: TestResult = {
        endpoint: endpoint.endpoint,
        method: endpoint.method,
        status: null,
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: startTime
      };

      setTestResults(prev => [result, ...prev]);
      return result;
    }
  };

  const testAllEndpoints = async () => {
    setTesting(true);
    setTestResults([]);

    for (const endpoint of apiEndpoints) {
      await testEndpoint(endpoint);
      // 添加小延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setTesting(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">API 端点测试</h1>
          <p className="text-gray-600 mb-8">
            测试后端API端点的连通性和响应。
          </p>

          {/* 控制按钮 */}
          <div className="flex space-x-4 mb-8">
            <button
              onClick={testAllEndpoints}
              disabled={testing}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {testing ? '测试中...' : '测试所有端点'}
            </button>
            <button
              onClick={clearResults}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              清除结果
            </button>
          </div>

          {/* API端点列表 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">可测试的端点</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {apiEndpoints.map((endpoint, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded ${
                      endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                      endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {endpoint.method}
                    </span>
                    <button
                      onClick={() => testEndpoint(endpoint)}
                      disabled={testing}
                      className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 transition-colors"
                    >
                      测试
                    </button>
                  </div>
                  <code className="text-sm text-gray-800">{endpoint.endpoint}</code>
                  <p className="text-sm text-gray-600 mt-1">{endpoint.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">测试结果</h2>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className={`border rounded-lg p-4 ${
                    result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded ${
                          result.method === 'GET' ? 'bg-green-100 text-green-800' :
                          result.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {result.method}
                        </span>
                        <code className="text-sm">{result.endpoint}</code>
                      </div>
                      <div className="flex items-center space-x-2">
                        {result.status && (
                          <span className={`px-2 py-1 text-xs font-medium rounded ${
                            result.status >= 200 && result.status < 300 ? 'bg-green-100 text-green-800' :
                            result.status >= 400 ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {result.status}
                          </span>
                        )}
                        <span className={`text-sm font-medium ${
                          result.success ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {result.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                      </div>
                    </div>
                    
                    {result.error && (
                      <div className="mb-2">
                        <span className="text-sm font-medium text-red-600">错误: </span>
                        <span className="text-sm text-red-700">{result.error}</span>
                      </div>
                    )}
                    
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-sm font-medium text-gray-700 cursor-pointer">
                          查看响应数据
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                    
                    <div className="text-xs text-gray-500 mt-2">
                      测试时间: {new Date(result.timestamp).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
