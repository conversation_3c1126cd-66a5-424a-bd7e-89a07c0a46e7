import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 检查前端应用状态
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'Newzora Frontend',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      checks: {
        frontend: 'ok',
        api_connection: 'checking...'
      }
    };

    // 尝试连接后端API
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 设置较短的超时时间
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        healthData.checks.api_connection = 'ok';
      } else {
        healthData.checks.api_connection = `error: ${response.status}`;
      }
    } catch (error) {
      healthData.checks.api_connection = `error: ${error instanceof Error ? error.message : 'unknown'}`;
    }

    return NextResponse.json(healthData, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'Newzora Frontend',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
