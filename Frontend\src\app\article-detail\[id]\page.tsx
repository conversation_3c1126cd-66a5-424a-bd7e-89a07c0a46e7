'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';

export default function ArticleDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [article, setArticle] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params && params.id) {
      const articleId = (params.id as string).split('-')[0]; // 处理扩展的ID格式 (article.id-0)
      console.log('🔍 Looking for article with ID:', articleId);
      console.log('📋 Available articles:', mockArticles.map(a => ({ id: a.id, title: a.title })));
      
      // 查找原始文章
      const foundArticle = mockArticles.find(a => a.id === articleId);
      
      if (foundArticle) {
        console.log('✅ Found article:', foundArticle.title);
        setArticle(foundArticle);
      } else {
        console.log('❌ Article not found, creating default');
        // 创建默认文章
        setArticle({
          id: articleId,
          title: `Sample Article ${articleId}`,
          description: 'This is a sample article description.',
          content: `
            <div class="prose prose-lg max-w-none">
              <h1>Sample Article ${articleId}</h1>
              <p>This is the content for article ${articleId}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
              <h2>Introduction</h2>
              <p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
              <h2>Main Content</h2>
              <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident.</p>
            </div>
          `,
          author: 'Sample Author',
          category: 'Technology',
          readTime: 5,
          views: 100,
          likes: 10,
          image: `https://picsum.photos/800/400?random=${articleId}`,
          publishedAt: new Date().toISOString(),
          tags: ['sample', 'demo']
        });
      }
      setLoading(false);
    }
  }, [params?.id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Breadcrumb */}
      <nav className="max-w-4xl mx-auto px-4 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button onClick={() => router.push('/')} className="hover:text-blue-600">
            Home
          </button>
          <span>/</span>
          <span className="text-gray-900">{article?.title || 'Article'}</span>
        </div>
      </nav>

      {/* Article Content */}
      <article className="max-w-4xl mx-auto px-4 pb-8">
        {/* Article Image */}
        {article?.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=800x400`;
              }}
            />
          </div>
        )}

        {/* Article Header */}
        <header className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            {article?.category && (
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                {article.category}
              </span>
            )}
            {article?.tags && article.tags.map((tag: string) => (
              <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                #{tag}
              </span>
            ))}
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {article?.title || 'Loading...'}
          </h1>
          
          <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-4">
              <span>By {article ? (typeof article.author === 'string' ? article.author : article.author.name) : 'Unknown'}</span>
              <span>{article?.readTime || 5} min read</span>
              <span>{article?.views || 0} views</span>
            </div>
            <div className="flex items-center gap-4">
              <span>❤️ {article?.likes || 0}</span>
              <button
                onClick={() => router.back()}
                className="text-blue-600 hover:text-blue-800"
              >
                ← Back
              </button>
            </div>
          </div>
        </header>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-8">
          {article?.content ? (
            <div dangerouslySetInnerHTML={{ __html: article.content }} />
          ) : (
            <div>
              <p className="text-lg text-gray-700 mb-6">{article?.description || 'Loading content...'}</p>
              <h2>Article Content</h2>
              <p>This is the main content of the article. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
              <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            </div>
          )}
        </div>

        {/* Article Actions */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100">
                ❤️ Like ({article?.likes || 0})
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100">
                📤 Share
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100">
                🔖 Save
              </button>
            </div>
            <div className="text-sm text-gray-500">
              Article ID: {article?.id || params?.id}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 pt-6 border-t">
          <div className="flex justify-between">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              ← Back to Home
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Explore More →
            </button>
          </div>
        </div>
      </article>
    </div>
  );
}
