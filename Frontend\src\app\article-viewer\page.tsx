'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';
import { Article } from '@/types';

export default function ArticleViewer() {
  const searchParams = useSearchParams();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const articleId = searchParams.get('id');
    if (articleId) {
      console.log('🔍 查找文章 ID:', articleId);
      const foundArticle = mockArticles.find(a => a.id === articleId);
      
      if (foundArticle) {
        console.log('✅ 找到文章:', foundArticle.title);
        setArticle(foundArticle);
      } else {
        console.log('❌ 未找到文章');
        setArticle(null);
      }
    }
    setLoading(false);
  }, [searchParams]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">文章未找到</h1>
          <p className="text-gray-600 mb-6">请检查文章ID是否正确</p>
          <a href="/explore" className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            返回探索页面
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <article className="max-w-4xl mx-auto px-4 py-8">
        {/* 文章图片 */}
        {article.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
            />
          </div>
        )}

        {/* 文章头部 */}
        <header className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
              {article.category}
            </span>
            {article.tags?.map((tag) => (
              <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                #{tag}
              </span>
            ))}
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {article.title}
          </h1>
          
          <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-4">
              <span>作者：{typeof article.author === 'string' ? article.author : article.author.name}</span>
              <span>{article.readTime} 分钟阅读</span>
              <span>{article.views} 次浏览</span>
            </div>
            <div className="flex items-center gap-4">
              <span>❤️ {article.likes}</span>
              <span>💬 {article.comments}</span>
            </div>
          </div>
        </header>

        {/* 文章内容 */}
        <div className="prose prose-lg max-w-none mb-8">
          <p className="text-lg text-gray-700 leading-relaxed mb-6">
            {article.description}
          </p>
          
          <div className="space-y-4">
            <p>
              {article.content}
            </p>
            
            <h2>更多内容</h2>
            <p>
              这里可以显示文章的完整内容。在实际应用中，您可以从后端API获取完整的文章内容并在这里显示。
              文章内容可以包含多个段落、图片、引用等富文本内容。
            </p>
            
            <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-700">
              "这是一个引用示例，展示文章内容的丰富性。"
            </blockquote>
            
            <p>
              文章的结尾部分，总结主要观点和见解。
            </p>
          </div>
        </div>

        {/* 文章操作 */}
        <div className="border-t pt-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100">
                ❤️ 点赞 ({article.likes})
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100">
                💬 评论 ({article.comments})
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100">
                📤 分享
              </button>
            </div>
            <div className="text-sm text-gray-500">
              文章ID: {article.id}
            </div>
          </div>
        </div>

        {/* 导航 */}
        <div className="border-t pt-6">
          <div className="flex justify-between">
            <a
              href="/explore"
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              ← 返回探索页面
            </a>
            <a
              href="/"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              返回首页 →
            </a>
          </div>
        </div>
      </article>
    </div>
  );
}
