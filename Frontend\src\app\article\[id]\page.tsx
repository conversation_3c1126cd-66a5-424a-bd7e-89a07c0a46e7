'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
// import Image from 'next/image'; // Temporarily disabled due to config issues
import Header from '@/components/Header';
// import VideoPlayer from '@/components/VideoPlayer';
// import AuthorCard from '@/components/AuthorCard';
// import InteractionStats from '@/components/InteractionStats';
// import CommentSection from '@/components/CommentSection';
import { Article, Comment } from '@/types';

export default function ArticlePage() {
  const params = useParams();
  const router = useRouter();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    if (params.id) {
      fetchArticle(params.id as string);
      fetchComments(params.id as string);
      fetchRelatedArticles(params.id as string);
    }
  }, [params.id]);

  const fetchArticle = async (id: string) => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:5000/api/articles/${id}`);
      if (response.ok) {
        const data = await response.json();
        setArticle(data);
      } else {
        console.error('Article not found');
      }
    } catch (error) {
      console.error('Error fetching article:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = async (articleId: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/comments/article/${articleId}`);
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    }
  };

  const fetchRelatedArticles = async (currentArticleId: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/articles?limit=3`);
      if (response.ok) {
        const data = await response.json();
        // Filter out current article and take first 3
        const filtered = data.data.filter((a: Article) => a.id.toString() !== currentArticleId).slice(0, 3);
        setRelatedArticles(filtered);
      }
    } catch (error) {
      console.error('Error fetching related articles:', error);
    }
  };

  const handleLike = async () => {
    if (!article) return;

    try {
      const response = await fetch(`http://localhost:5000/api/articles/${article.id}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setArticle(prev => prev ? { ...prev, likes: data.likes } : null);
        setIsLiked(!isLiked);
      }
    } catch (error) {
      console.error('Error liking article:', error);
    }
  };

  const handleSave = async () => {
    if (!article) return;

    try {
      // This would typically save to user's bookmarks
      // For now, just toggle the state
      setIsSaved(!isSaved);
      console.log('Article saved/unsaved');
    } catch (error) {
      console.error('Error saving article:', error);
    }
  };

  const handleShare = () => {
    if (!article) return;

    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.description,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const handleCommentSubmit = async () => {
    if (!newComment.trim() || !article) return;

    try {
      const response = await fetch(`http://localhost:5000/api/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: article.id,
          content: newComment.trim(),
          author: {
            id: 1,
            name: 'Current User',
            username: 'currentuser',
            avatar: 'https://ui-avatars.com/api/?name=Current+User&background=6366f1&color=fff&size=48'
          }
        }),
      });

      if (response.ok) {
        const newCommentData = await response.json();
        setComments(prev => [newCommentData, ...prev]);
        setNewComment('');
        console.log('Comment added successfully');
      } else {
        console.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Breadcrumb Navigation */}
      <nav className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Articles
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {article?.title ? (article.title.length > 50 ? article.title.substring(0, 50) + '...' : article.title) : 'Loading...'}
          </span>
        </div>
      </nav>

      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        {/* Article Image Section */}
        {article?.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=800x320`;
              }}
            />
          </div>
        )}

        {/* Article Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4 leading-tight">
          {article?.title || "Loading..."}
        </h1>

        {/* Author Info */}
        <div className="mb-4 flex items-center space-x-3">
          <img
            src={typeof article?.author === 'object' ? article.author.avatar : "https://ui-avatars.com/api/?name=Unknown+Author&background=6366f1&color=fff&size=40"}
            alt={typeof article?.author === 'object' ? article.author.name : 'Unknown Author'}
            className="w-10 h-10 rounded-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = `https://ui-avatars.com/api/?name=Author&background=6366f1&color=fff&size=40`;
            }}
          />
          <div>
            <h3 className="font-medium text-gray-900">
              {typeof article?.author === 'object' ? article.author.name : (article?.author || 'Unknown Author')}
            </h3>
            <p className="text-sm text-gray-500">
              {new Date(article?.publishedAt || article?.createdAt || new Date()).toLocaleDateString()} • {article?.readTime || 5} min read
            </p>
          </div>
        </div>

        {/* Interaction Stats */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span>👁️ {article?.views || 0} views</span>
            <span>💬 {comments.length} comments</span>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handleLike}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isLiked
                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>{isLiked ? '❤️' : '🤍'}</span>
              <span>{article?.likes || 0}</span>
            </button>

            <button
              onClick={handleSave}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isSaved
                  ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>{isSaved ? '🔖' : '📑'}</span>
              <span>{isSaved ? 'Saved' : 'Save'}</span>
            </button>

            <button
              onClick={handleShare}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <span>📤</span>
              <span>Share</span>
            </button>
          </div>
        </div>
        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12">
          {article?.content ? (
            <div
              className="text-gray-700 leading-relaxed text-lg"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />
          ) : (
            <div className="text-gray-500 text-center py-8">
              <p>Loading article content...</p>
            </div>
          )}
        </div>

        {/* Comments Section */}
        <div id="comments-section" className="mt-12">
          <h3 className="text-xl font-bold text-gray-900 mb-6">Comments ({comments.length})</h3>

          {/* Comment Form */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Write a comment..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
            />
            <div className="mt-3 flex justify-between items-center">
              <span className="text-sm text-gray-500">
                {newComment.length}/500 characters
              </span>
              <button
                onClick={handleCommentSubmit}
                disabled={!newComment.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                Post Comment
              </button>
            </div>
          </div>

          {/* Comments List */}
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="flex space-x-4">
                <img
                  src={typeof comment.author === 'object' ? comment.author.avatar : "https://ui-avatars.com/api/?name=Anonymous&background=6366f1&color=fff&size=48"}
                  alt={typeof comment.author === 'object' ? comment.author.name : 'Anonymous'}
                  className="w-10 h-10 rounded-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=User&background=6366f1&color=fff&size=40`;
                  }}
                />
                <div className="flex-1">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900">
                        {typeof comment.author === 'object' ? comment.author.name : 'Anonymous'}
                      </h4>
                      <span className="text-sm text-gray-500">
                        {new Date(comment.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-gray-700">{comment.content}</p>
                  </div>
                  <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <button className="hover:text-blue-600">❤️ {comment.likes || 0}</button>
                    <button className="hover:text-blue-600">Reply</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <div className="mt-16 pt-8 border-t border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedArticles.map((relatedArticle) => (
                <div
                  key={relatedArticle.id}
                  onClick={() => router.push(`/article/${relatedArticle.id}`)}
                  className="cursor-pointer group"
                >
                  <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
                    <div className="aspect-video bg-gray-200 relative overflow-hidden">
                      <img
                        src={relatedArticle.image}
                        alt={relatedArticle.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    </div>
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          {relatedArticle.category}
                        </span>
                        <span className="text-xs text-gray-500">
                          {relatedArticle.readTime} min read
                        </span>
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {relatedArticle.title}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                        {relatedArticle.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>By {typeof relatedArticle.author === 'object' ? relatedArticle.author.name : relatedArticle.author}</span>
                        <div className="flex items-center space-x-3">
                          <span>{relatedArticle.views} views</span>
                          <span>{relatedArticle.likes} likes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </article>
    </div>
  );
}
