
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';

export default function ArticleDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [article, setArticle] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params && params.id) {
      const articleId = params.id as string;
      console.log('🔍 Looking for article with ID:', articleId);
      fetchArticle(articleId);
    }
  }, [params]);

  const fetchArticle = async (id: string) => {
    try {
      setLoading(true);
      console.log('🔍 Looking for article with ID:', id);
      console.log('📋 Available articles:', mockArticles.map(a => ({ id: a.id, title: a.title })));

      // 直接从模拟数据中查找
      const foundArticle = mockArticles.find(a => a.id === id);

      if (foundArticle) {
        console.log('✅ Found article in mock data:', foundArticle.title);
        setArticle(foundArticle);
      } else {
        console.log('❌ Article not found, creating default');
        // 创建默认文章
        setArticle({
          id: id,
          title: `Sample Article ${id}`,
          description: 'This is a sample article description.',
          content: `
            <div class="prose prose-lg max-w-none">
              <h1>Sample Article ${id}</h1>
                <p>This is the content for article ${id}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                <h2>Introduction</h2>
                <p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                <h2>Main Content</h2>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
              </div>
            `,
            author: 'Sample Author',
            category: 'Technology',
            readTime: 5,
            views: 100,
            likes: 10,
            image: `https://picsum.photos/800/400?random=${id}`,
            publishedAt: new Date().toISOString(),
            tags: ['sample', 'demo']
          });
        }
      }
    } catch (error) {
      console.error('Error fetching article:', error);
      // 出错时显示默认内容
      setArticle({
        id: id,
        title: `Article ${id}`,
        description: 'Unable to load article details',
        content: '<p>Sorry, we encountered an error while loading this article.</p>',
        author: 'Unknown',
        category: 'General',
        readTime: 1,
        views: 0,
        likes: 0,
        image: `https://picsum.photos/800/400?blur=3`,
        publishedAt: new Date().toISOString(),
        tags: []
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Breadcrumb */}
      <nav className="max-w-4xl mx-auto px-4 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button onClick={() => router.push('/')} className="hover:text-blue-600">
            Home
          </button>
          <span>/</span>
          <span className="text-gray-900">{article?.title || 'Article'}</span>
        </div>
      </nav>

      {/* Article Content */}
      <article className="max-w-4xl mx-auto px-4 pb-8">
        {/* Article Image */}
        {article?.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://via.placeholder.com/1200x600?text=Article+Image+Not+Available';
              }}
            />
          </div>
        )}

        {/* Article Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 leading-tight">
            {article.title}
          </h1>
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <span className="font-medium text-gray-700">
              {typeof article.author === 'object' && article.author?.name
                ? article.author.name
                : typeof article.author === 'string'
                ? article.author
                : 'Anonymous'}
            </span>
            <span className="mx-2">•</span>
            <time dateTime={article.publishedAt || article.createdAt}>
              {new Date(article.publishedAt || article.createdAt).toLocaleDateString()}
            </time>
            <span className="mx-2">•</span>
            <span>{article.readTime || 5} min read</span>
          </div>
          <p className="text-lg text-gray-600 leading-relaxed">
            {article.description}
          </p>
        </div>

        {/* Article Body */}
        <div className="prose lg:prose-xl max-w-none mb-10">
          <div dangerouslySetInnerHTML={{ __html: article.content }} />
        </div>

        {/* Article Meta */}
        <div className="flex items-center gap-6 text-gray-500 border-t pt-6 border-gray-200">
          <div className="flex items-center gap-2">
            <span className="text-xl">👁️</span>
            <span>{article.views || 0} views</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xl">❤️</span>
            <span>{article.likes || 0} likes</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xl">💬</span>
            <span>{article.comments || 0} comments</span>
          </div>
        </div>

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="mt-8">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Related Topics</h3>
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag: string) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-gray-200 transition-colors cursor-pointer"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Back button */}
        <div className="mt-10">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to articles
          </button>
        </div>
      </article>
    </div>
  );
}
