'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import { Article } from '@/types';
import { mockArticles as globalMockArticles } from '@/data/mockArticles';

export default function ArticlePage() {
  const params = useParams();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 从mockArticles中找到原始文章
        const originalId = (params?.id as string)?.split('-')[0]; // 处理扩展的ID格式 (article.id-0)
        const foundArticle = globalMockArticles.find(
          article => article.id === originalId
        );

        if (foundArticle) {
          setArticle(foundArticle);
        }
      } catch (error) {
        console.error('Error fetching article:', error);
      } finally {
        setLoading(false);
      }
    };

    if (params?.id) {
      fetchArticle();
    }
  }, [params?.id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">文章未找到</h1>
          <p className="text-gray-600 mb-6">抱歉，我们找不到您请求的文章。</p>
          <Link
            href="/explore"
            className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回浏览页面
          </Link>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '最近发布';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* 面包屑导航 */}
      <nav className="max-w-4xl mx-auto px-4 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Link href="/" className="hover:text-blue-600">首页</Link>
          <span>/</span>
          <Link href="/explore" className="hover:text-blue-600">文章</Link>
          <span>/</span>
          <span className="text-gray-900">{article.title}</span>
        </div>
      </nav>

      {/* 文章内容 */}
      <article className="max-w-4xl mx-auto px-4 pb-8">
        {/* 文章图片 */}
        {article.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=800x400`;
              }}
            />
          </div>
        )}

        {/* 文章头部 */}
        <header className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
              {article.category}
            </span>
            {article.tags?.map((tag) => (
              <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                #{tag}
              </span>
            ))}
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {article.title}
          </h1>
          
          <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <img
                  src={typeof article.author === 'object' 
                    ? article.author.avatar 
                    : `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author)}&background=6366f1&color=fff&size=32`}
                  alt={typeof article.author === 'object' ? article.author.name : article.author}
                  className="w-8 h-8 rounded-full"
                />
                <span>作者：{typeof article.author === 'object' ? article.author.name : article.author}</span>
              </div>
              <span>{article.readTime} 分钟阅读</span>
            </div>
            <div className="flex items-center gap-4">
              <span>❤️ {article.likes}</span>
              <span>💬 {article.comments}</span>
            </div>
          </div>
        </header>

        {/* 文章正文 */}
        <div className="prose prose-lg max-w-none mb-8">
          <p className="text-gray-800 leading-relaxed">
            {article.excerpt}
          </p>
          <div className="mt-6">
            {/* 这里可以添加完整的文章内容 */}
            <p>
              这是一个示例文章内容。在实际应用中，您需要从后端API获取完整的文章内容并在这里显示。
              文章内容可以包含多个段落、图片、引用等富文本内容。
            </p>
          </div>
        </div>

        {/* 文章操作 */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100">
                ❤️ 点赞 ({article.likes})
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100">
                💬 评论 ({article.comments})
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100">
                📤 分享
              </button>
            </div>
          </div>
        </div>

        {/* 作者信息 */}
        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-4">
            <img
              src={typeof article.author === 'object' 
                ? article.author.avatar 
                : `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author)}&background=6366f1&color=fff&size=64`}
              alt={typeof article.author === 'object' ? article.author.name : article.author}
              className="w-16 h-16 rounded-full"
            />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {typeof article.author === 'object' ? article.author.name : article.author}
              </h3>
              <p className="text-gray-600 text-sm mt-1">
                发布于：{formatDate(article.publishedAt)}
              </p>
            </div>
          </div>
        </div>

        {/* 导航 */}
        <div className="mt-8 pt-6 border-t">
          <div className="flex justify-between">
            <Link
              href="/explore"
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              ← 返回文章列表
            </Link>
          </div>
        </div>
      </article>
    </div>
  );
}
