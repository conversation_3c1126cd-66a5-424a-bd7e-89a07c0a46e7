'use client';

import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';

export default function ContentTestPage() {
  const router = useRouter();

  const testContentLink = (articleId: string) => {
    console.log(`🧪 测试内容详情页: ${articleId}`);
    router.push(`/content/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🆕 全新内容详情页测试</h1>
        
        {/* 清理完成状态 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 清理完成</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
            <div>
              <h3 className="font-medium mb-2">🗑️ 已删除的文件:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>/app/article/[id]/page.tsx</li>
                <li>/app/article-detail/[id]/page.tsx</li>
                <li>/app/article-viewer/page.tsx</li>
                <li>/app/read/page.tsx</li>
                <li>所有测试页面和组件</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">🆕 新创建的文件:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>/app/content/[slug]/page.tsx</li>
                <li>全新的内容详情页面</li>
                <li>现代化的UI设计</li>
                <li>完整的功能实现</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 新页面特性 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">🎨 新页面特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-blue-700">
            <div>
              <h3 className="font-medium mb-2">🎯 技术特性:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>使用动态路由 [slug]</li>
                <li>支持ID和slug访问</li>
                <li>完整的错误处理</li>
                <li>优雅的加载状态</li>
                <li>响应式设计</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">🎨 UI设计:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>现代化卡片设计</li>
                <li>美观的头图展示</li>
                <li>清晰的排版布局</li>
                <li>丰富的交互元素</li>
                <li>专业的配色方案</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">⚡ 功能特性:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>面包屑导航</li>
                <li>作者信息展示</li>
                <li>文章操作按钮</li>
                <li>相关文章推荐</li>
                <li>社交分享功能</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 测试区域 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 测试新的内容详情页</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testContentLink(article.id)}
              >
                <div className="aspect-video overflow-hidden bg-gray-200">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=200&fit=crop`;
                      }}
                    />
                  )}
                </div>
                <div className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {article.category}
                    </span>
                    <span className="text-xs text-gray-500">ID: {article.id}</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {article.title}
                  </h3>
                  <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                    {article.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                    <span>{article.readTime} min</span>
                  </div>
                  <div className="mt-3 text-center">
                    <span className="text-sm text-blue-600 font-medium">点击查看详情 →</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 直接链接测试 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🔗 直接链接测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockArticles.slice(0, 4).map((article) => (
              <div key={article.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-mono text-gray-600 min-w-[60px]">ID: {article.id}</span>
                <a 
                  href={`/content/${article.id}`}
                  className="text-blue-600 hover:text-blue-800 underline font-mono text-sm"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /content/{article.id}
                </a>
                <span className="text-sm text-gray-500 flex-1 truncate">{article.title}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 路由说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">📋 路由说明</h2>
          <div className="text-sm text-yellow-700 space-y-3">
            <div>
              <h3 className="font-medium">新路由格式:</h3>
              <p><code className="bg-yellow-100 px-2 py-1 rounded">/content/[slug]</code> - 支持ID和slug访问</p>
            </div>
            <div>
              <h3 className="font-medium">访问方式:</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><code>/content/1</code> - 通过文章ID访问</li>
                <li><code>/content/article-title</code> - 通过文章slug访问</li>
                <li><code>/content/the-future-of-ai</code> - 通过标题生成的slug访问</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium">更新状态:</h3>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>✅ ArticleCard组件已更新</li>
                <li>✅ Explore页面已更新</li>
                <li>✅ 所有链接指向新路由</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 导航按钮 */}
        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🏠 测试首页
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              🔍 测试探索页面
            </button>
            <button
              onClick={() => testContentLink('1')}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              📖 测试内容详情
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
