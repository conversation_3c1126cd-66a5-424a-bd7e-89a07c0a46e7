'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { notFound } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';
import { Article } from '@/types';

export default function ContentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);

  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true);
        
        const slug = params.slug as string;
        console.log('🔍 加载内容，slug:', slug);
        
        // 查找文章 - 支持ID和slug
        let foundArticle = mockArticles.find(article => 
          article.id === slug || 
          article.slug === slug ||
          article.title.toLowerCase().replace(/\s+/g, '-') === slug.toLowerCase()
        );

        if (!foundArticle) {
          console.log('❌ 未找到内容');
          notFound();
          return;
        }

        console.log('✅ 找到内容:', foundArticle.title);
        setArticle(foundArticle);

        // 加载相关文章
        const related = mockArticles
          .filter(a => a.id !== foundArticle!.id && a.category === foundArticle!.category)
          .slice(0, 3);
        setRelatedArticles(related);

      } catch (error) {
        console.error('❌ 加载内容失败:', error);
        notFound();
      } finally {
        setLoading(false);
      }
    };

    if (params.slug) {
      loadContent();
    }
  }, [params.slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="animate-pulse">
              <div className="h-64 bg-gray-300"></div>
              <div className="p-8">
                <div className="h-8 bg-gray-300 rounded mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-6 w-3/4"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded w-5/6"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* 面包屑导航 */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <button 
              onClick={() => router.push('/')} 
              className="text-gray-500 hover:text-blue-600 transition-colors"
            >
              首页
            </button>
            <span className="text-gray-400">/</span>
            <button 
              onClick={() => router.push('/explore')} 
              className="text-gray-500 hover:text-blue-600 transition-colors"
            >
              探索
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-blue-600 font-medium">{article.category}</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium truncate">{article.title}</span>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <article className="bg-white rounded-xl shadow-sm overflow-hidden">
          {/* 文章头图 */}
          {article.image && (
            <div className="aspect-video overflow-hidden">
              <img
                src={article.image}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop&crop=center`;
                }}
              />
            </div>
          )}

          <div className="p-8">
            {/* 文章元信息 */}
            <header className="mb-8">
              <div className="flex items-center gap-3 mb-6">
                <span className="px-4 py-2 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                  {article.category}
                </span>
                {article.tags?.map((tag) => (
                  <span key={tag} className="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    #{tag}
                  </span>
                ))}
              </div>
              
              <h1 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">
                {article.title}
              </h1>
              
              <div className="flex items-center justify-between pb-6 border-b border-gray-200">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold">
                        {typeof article.author === 'string' 
                          ? article.author.charAt(0).toUpperCase()
                          : article.author.name.charAt(0).toUpperCase()
                        }
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {typeof article.author === 'string' ? article.author : article.author.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(article.publishedAt || article.createdAt).toLocaleDateString('zh-CN', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {article.readTime} 分钟阅读
                    </span>
                    <span className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      {article.views || 0} 次浏览
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1 text-red-500">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                    {article.likes}
                  </span>
                  <span className="flex items-center gap-1 text-blue-500">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    {article.comments || 0}
                  </span>
                </div>
              </div>
            </header>

            {/* 文章摘要 */}
            <div className="mb-8">
              <p className="text-xl text-gray-700 leading-relaxed font-light">
                {article.description}
              </p>
            </div>

            {/* 文章正文 */}
            <div className="prose prose-lg max-w-none mb-12">
              <div className="text-gray-800 leading-relaxed space-y-6">
                <p>{article.content}</p>
                
                <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">深入探讨</h2>
                <p>
                  在这个快速发展的时代，我们需要深入思考和分析各种现象背后的本质。
                  通过仔细观察和研究，我们可以发现许多有趣的规律和趋势。
                </p>
                
                <blockquote className="border-l-4 border-blue-500 pl-6 py-4 bg-blue-50 rounded-r-lg my-8">
                  <p className="text-blue-800 italic text-lg">
                    "知识的真正价值不在于我们知道什么，而在于我们如何运用所知来创造更美好的未来。"
                  </p>
                </blockquote>
                
                <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">实践应用</h2>
                <p>
                  理论知识只有与实践相结合，才能发挥其真正的价值。
                  我们应该将学到的知识应用到实际生活和工作中，不断验证和完善我们的理解。
                </p>
                
                <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">总结与展望</h2>
                <p>
                  通过本文的探讨，我们可以看到这个话题的复杂性和重要性。
                  未来，我们需要继续关注相关发展，保持开放的心态，不断学习和成长。
                </p>
              </div>
            </div>

            {/* 文章操作栏 */}
            <div className="border-t border-gray-200 pt-8 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 px-6 py-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                    <span>点赞 ({article.likes})</span>
                  </button>
                  <button className="flex items-center gap-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span>评论 ({article.comments || 0})</span>
                  </button>
                  <button className="flex items-center gap-2 px-6 py-3 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span>分享</span>
                  </button>
                  <button className="flex items-center gap-2 px-6 py-3 bg-yellow-50 text-yellow-600 rounded-lg hover:bg-yellow-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                    <span>收藏</span>
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  文章ID: {article.id}
                </div>
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="border-t border-gray-200 pt-8">
              <div className="flex justify-between">
                <button
                  onClick={() => router.back()}
                  className="flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>返回上一页</span>
                </button>
                <div className="flex gap-3">
                  <button
                    onClick={() => router.push('/explore')}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    探索更多内容
                  </button>
                  <button
                    onClick={() => router.push('/')}
                    className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    返回首页
                  </button>
                </div>
              </div>
            </div>
          </div>
        </article>

        {/* 相关文章 */}
        {relatedArticles.length > 0 && (
          <section className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">相关文章</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedArticles.map((relatedArticle) => (
                <div
                  key={relatedArticle.id}
                  className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => router.push(`/content/${relatedArticle.id}`)}
                >
                  <div className="aspect-video overflow-hidden">
                    <img
                      src={relatedArticle.image}
                      alt={relatedArticle.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {relatedArticle.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {relatedArticle.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}
      </main>
    </div>
  );
}
