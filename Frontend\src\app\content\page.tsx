'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Link from 'next/link';
import DraftManager from '@/components/content/DraftManager';
import MediaManager from '@/components/content/MediaManager';
import ReviewManager from '@/components/content/ReviewManager';
import {
  DocumentTextIcon,
  PhotoIcon,
  ShieldCheckIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

type TabType = 'drafts' | 'media' | 'reviews';

const tabs = [
  {
    id: 'drafts' as TabType,
    name: 'Draft Management',
    icon: DocumentTextIcon,
    description: 'Manage your article drafts'
  },
  {
    id: 'media' as TabType,
    name: 'Media Files',
    icon: PhotoIcon,
    description: 'Manage images, videos and audio files'
  },
  {
    id: 'reviews' as TabType,
    name: 'Review Records',
    icon: ShieldCheckIcon,
    description: 'View content review status'
  },
];

export default function ContentManagementPage() {
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('drafts');

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Management</h2>
          <p className="text-gray-600 mb-6">
            Please log in to manage your content
          </p>
          <Link
            href="/auth/login"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Login Now
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
              <p className="mt-2 text-sm text-gray-600">
                Manage your article drafts, media files and review records
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/content/editor"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Article
              </Link>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon
                      className={`mr-2 h-5 w-5 ${
                        activeTab === tab.id
                          ? 'text-blue-500'
                          : 'text-gray-400 group-hover:text-gray-500'
                      }`}
                    />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Description */}
          <div className="mt-4">
            <p className="text-sm text-gray-600">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'drafts' && (
            <div>
              <DraftManager />
            </div>
          )}

          {activeTab === 'media' && (
            <div>
              <MediaManager />
            </div>
          )}

          {activeTab === 'reviews' && (
            <div>
              <ReviewManager userRole={user?.role === 'admin' ? 'admin' : 'user'} />
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/content/editor"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <DocumentTextIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h4 className="font-medium text-gray-900">Create New Article</h4>
                <p className="text-sm text-gray-500">Start writing new article content</p>
              </div>
            </Link>

            <button
              onClick={() => setActiveTab('media')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
            >
              <PhotoIcon className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <h4 className="font-medium text-gray-900">Upload Media Files</h4>
                <p className="text-sm text-gray-500">Add images, videos or audio</p>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('reviews')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
            >
              <ShieldCheckIcon className="h-8 w-8 text-purple-600 mr-3" />
              <div>
                <h4 className="font-medium text-gray-900">View Review Status</h4>
                <p className="text-sm text-gray-500">Check content review progress</p>
              </div>
            </button>
          </div>
        </div>

        {/* Statistics */}
        {user && (
          <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics Overview</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">-</div>
                <div className="text-sm text-gray-600">Total Drafts</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">-</div>
                <div className="text-sm text-gray-600">Published Articles</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">-</div>
                <div className="text-sm text-gray-600">Pending Review</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">-</div>
                <div className="text-sm text-gray-600">Media Files</div>
              </div>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                Statistics will show real-time data once components are fully integrated
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
