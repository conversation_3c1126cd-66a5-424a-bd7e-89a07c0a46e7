'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import Header from '@/components/Header';
import dynamic from 'next/dynamic';
import '@/styles/highlight.css';

// 动态导入高级富文本编辑器以避免SSR问题
const SimpleAdvancedEditor = dynamic(() => import('@/components/SimpleAdvancedEditor'), {
  ssr: false,
  loading: () => (
    <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
      <div className="flex items-center space-x-2">
        <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-gray-600">Loading advanced editor...</span>
      </div>
    </div>
  )
});

const categories = [
  'Technology',
  'Travel', 
  'Food',
  'Lifestyle',
  'Health'
];

export default function CreatePostPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isAuthenticated, isLoading } = useSupabaseAuth();
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: 'Technology'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [currentDraftId, setCurrentDraftId] = useState<number | null>(null);
  const [isDraftMode, setIsDraftMode] = useState(false);

  // 认证检查和草稿加载
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login?redirect=/create');
      return;
    }

    // 检查是否有草稿ID参数
    const draftId = searchParams.get('draft');
    if (draftId && isAuthenticated) {
      loadDraft(parseInt(draftId));
    }
  }, [isAuthenticated, isLoading, router, searchParams]);

  // 加载草稿
  const loadDraft = async (draftId: number) => {
    try {
      const response = await fetch(`/api/drafts/${draftId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const draft = data.data;
        setFormData({
          title: draft.title,
          content: draft.contentHtml || draft.content,
          category: draft.category || 'Technology'
        });
        setCurrentDraftId(draft.id);
        setIsDraftMode(true);
        setLastSaved(new Date(draft.lastAutoSaveAt || draft.updatedAt));
      }
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  };

  // 保存草稿
  const saveDraft = async (autoSave = false) => {
    try {
      setIsSaving(true);

      const draftData = {
        title: formData.title || 'Untitled Draft',
        content: formData.content,
        contentHtml: formData.content,
        category: formData.category,
        status: autoSave ? 'auto_saved' : 'draft'
      };

      let response;
      if (currentDraftId) {
        // 更新现有草稿
        const endpoint = autoSave ? `/api/drafts/${currentDraftId}/auto-save` : `/api/drafts/${currentDraftId}`;
        const method = autoSave ? 'PATCH' : 'PUT';

        response = await fetch(endpoint, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          },
          body: JSON.stringify(draftData)
        });
      } else {
        // 创建新草稿
        response = await fetch('/api/drafts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          },
          body: JSON.stringify(draftData)
        });
      }

      if (response.ok) {
        const data = await response.json();
        if (!currentDraftId) {
          setCurrentDraftId(data.data.id);
          setIsDraftMode(true);
        }
        setLastSaved(new Date());
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error saving draft:', error);
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // 如果未认证，显示加载或重定向
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 实时验证
    let fieldError = '';
    if (name === 'title') {
      fieldError = validateTitle(value);
    } else if (name === 'content') {
      fieldError = validateContent(value);
    }

    setErrors(prev => ({
      ...prev,
      [name]: fieldError,
      submit: '' // 清除提交错误
    }));
  };

  const handleCategorySelect = (category: string) => {
    setFormData(prev => ({
      ...prev,
      category
    }));
  };

  // 处理编辑器内容变化
  const handleContentChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      content
    }));

    // 清除内容错误
    if (errors.content) {
      setErrors(prev => ({
        ...prev,
        content: ''
      }));
    }
  };

  // 自动保存草稿
  const handleAutoSave = async (content: string) => {
    if (!formData.title.trim() && !content.trim()) return;

    // 更新内容到formData
    setFormData(prev => ({ ...prev, content }));

    // 调用草稿保存API
    await saveDraft(true);
  };

  // 详细验证函数
  const validateTitle = (title: string) => {
    const trimmedTitle = title.trim();
    if (!trimmedTitle) {
      return 'Title is required';
    }
    if (trimmedTitle.length < 5) {
      return 'Title must be at least 5 characters long';
    }
    if (trimmedTitle.length > 100) {
      return 'Title must be less than 100 characters';
    }
    return '';
  };

  const validateContent = (content: string) => {
    const trimmedContent = content.trim();
    if (!trimmedContent) {
      return 'Content is required';
    }
    if (trimmedContent.length < 50) {
      return 'Content must be at least 50 characters long';
    }
    if (trimmedContent.length > 10000) {
      return 'Content must be less than 10,000 characters';
    }
    return '';
  };

  const validateForm = () => {
    const titleError = validateTitle(formData.title);
    const contentError = validateContent(formData.content);

    const newErrors: {[key: string]: string} = {};
    if (titleError) newErrors.title = titleError;
    if (contentError) newErrors.content = contentError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message
      setShowSuccess(true);

      // Reset form after success
      setTimeout(() => {
        setFormData({
          title: '',
          content: '',
          category: 'Technology'
        });
        setShowSuccess(false);
        router.push('/');
      }, 2000);

    } catch (error) {
      console.error('Error creating post:', error);
      setErrors({ submit: 'Failed to publish post. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-6">
        {/* Page Title */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Create New Post</h1>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mb-8 p-4 bg-green-50 border border-green-200 rounded-xl">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <p className="text-green-700 font-medium">Post published successfully! Redirecting...</p>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errors.submit && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-xl">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <p className="text-red-700 font-medium">{errors.submit}</p>
            </div>
          </div>
        )}

        {/* Create Post Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title Input */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-900 mb-2">
              Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter your post title"
              maxLength={100}
              className={`w-full px-3 py-2 text-base border rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.title ? 'border-red-300' : 'border-gray-200'
              }`}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Content Textarea */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-900 mb-2">
              Content
            </label>
            <SimpleAdvancedEditor
              content={formData.content}
              onChange={handleContentChange}
              onSave={handleAutoSave}
              placeholder="Start writing your article..."
              height={400}
              disabled={isSubmitting}
              showWordCount={false}
              maxWords={10000}
              autoSave={true}
              autoSaveInterval={30000}
            />
            {errors.content && (
              <p className="mt-1 text-sm text-red-600">{errors.content}</p>
            )}
          </div>

          {/* Category Selection */}
          <div>
            <div className="flex flex-wrap gap-3">
              {categories.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => handleCategorySelect(category)}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 ${
                    formData.category === category
                      ? 'bg-blue-100 text-blue-700 border-2 border-blue-200'
                      : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Draft Status and Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              {/* Draft Status */}
              {isDraftMode && (
                <div className="flex items-center text-sm text-gray-600">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                    Draft Mode
                  </span>
                  {lastSaved && (
                    <span>
                      Last saved: {lastSaved.toLocaleTimeString()}
                    </span>
                  )}
                  {isSaving && (
                    <span className="text-blue-600 ml-2">Saving...</span>
                  )}
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              {/* Save Draft Button */}
              <button
                type="button"
                onClick={() => saveDraft(false)}
                disabled={isSaving}
                className="px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isSaving ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save Draft'
                )}
              </button>

              {/* Publish Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Publishing...</span>
                  </div>
                ) : (
                  'Publish'
                )}
              </button>
            </div>
          </div>
        </form>
      </main>
    </div>
  );
}
