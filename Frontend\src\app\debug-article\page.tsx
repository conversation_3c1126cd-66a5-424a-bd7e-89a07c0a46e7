'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function DebugArticle() {
  const router = useRouter();

  const directTest = (articleId: string) => {
    console.log('=== 直接测试 ===');
    console.log('文章ID:', articleId);
    console.log('所有文章:', mockArticles.map(a => ({ id: a.id, title: a.title })));
    
    const found = mockArticles.find(article => article.id === articleId);
    console.log('查找结果:', found ? found.title : '未找到');
    
    // 直接跳转
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">文章调试页面</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🔍 调试信息</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">当前mockArticles数据:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
                {JSON.stringify(mockArticles.map(a => ({ id: a.id, title: a.title })), null, 2)}
              </pre>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 直接测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockArticles.slice(0, 4).map((article) => (
              <button
                key={article.id}
                onClick={() => directTest(article.id)}
                className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-left"
              >
                <h3 className="font-semibold text-gray-900 mb-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-blue-600">点击测试 →</p>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🔗 手动链接测试</h2>
          <div className="space-y-2">
            {mockArticles.slice(0, 3).map((article) => (
              <div key={article.id} className="flex items-center gap-4">
                <span className="text-sm text-gray-600">ID: {article.id}</span>
                <a 
                  href={`/article/${article.id}`}
                  className="text-blue-600 hover:text-blue-800 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /article/{article.id}
                </a>
                <span className="text-sm text-gray-500">{article.title}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">⚠️ 如果仍然显示"Article Not Found"</h2>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>可能的原因:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>浏览器缓存问题 - 尝试硬刷新 (Ctrl+F5)</li>
              <li>Next.js缓存问题 - 重启开发服务器</li>
              <li>还有其他article-detail路由存在</li>
              <li>TypeScript类型问题</li>
            </ul>
            <p className="mt-4"><strong>解决步骤:</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>打开浏览器开发者工具</li>
              <li>查看Console标签页的日志</li>
              <li>点击上方测试按钮</li>
              <li>查看控制台输出的调试信息</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
