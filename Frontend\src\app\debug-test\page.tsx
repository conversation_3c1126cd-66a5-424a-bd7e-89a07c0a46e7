'use client';

import { useState } from 'react';
import Header from '@/components/Header';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function DebugTestPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testDebugLogin = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🧪 Testing debug login...');
      
      const response = await fetch(`${API_BASE}/auth-enhanced/debug-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'Admin123!'
        })
      });

      console.log('📊 Response status:', response.status);
      console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

      const data = await response.json();
      console.log('📊 Response data:', data);

      setResult({
        status: response.status,
        ok: response.ok,
        data: data,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Test error:', error);
      setResult({
        status: null,
        ok: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">调试登录测试</h1>
          <p className="text-gray-600 mb-8">
            测试调试登录API端点的功能。
          </p>

          <div className="mb-8">
            <button
              onClick={testDebugLogin}
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? '测试中...' : '测试调试登录'}
            </button>
          </div>

          {/* 测试参数 */}
          <div className="mb-8 bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">测试参数</h3>
            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium text-gray-600">端点: </span>
                <code className="text-sm bg-white px-2 py-1 rounded border">
                  POST {API_BASE}/auth-enhanced/debug-login
                </code>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">请求体: </span>
                <pre className="text-sm bg-white p-2 rounded border mt-1">
{JSON.stringify({
  identifier: '<EMAIL>',
  password: 'Admin123!'
}, null, 2)}
                </pre>
              </div>
            </div>
          </div>

          {/* 测试结果 */}
          {result && (
            <div className={`border rounded-lg p-4 ${
              result.ok ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
            }`}>
              <h3 className="text-lg font-semibold mb-3">测试结果</h3>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-600">状态码:</span>
                  <span className={`px-2 py-1 text-xs font-medium rounded ${
                    result.status >= 200 && result.status < 300 ? 'bg-green-100 text-green-800' :
                    result.status >= 400 ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {result.status || 'N/A'}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-600">成功:</span>
                  <span className={`text-sm font-medium ${
                    result.ok ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {result.ok ? '✅ 是' : '❌ 否'}
                  </span>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-600">时间:</span>
                  <span className="text-sm text-gray-700 ml-2">
                    {new Date(result.timestamp).toLocaleString()}
                  </span>
                </div>
              </div>

              {result.error && (
                <div className="mb-4">
                  <span className="text-sm font-medium text-red-600">错误: </span>
                  <span className="text-sm text-red-700">{result.error}</span>
                </div>
              )}

              {result.data && (
                <details className="mt-4">
                  <summary className="text-sm font-medium text-gray-700 cursor-pointer">
                    查看完整响应
                  </summary>
                  <pre className="mt-2 text-xs bg-white p-3 rounded border overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          )}

          {/* 说明 */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">说明</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• 这个测试直接调用后端的调试登录API</li>
              <li>• 如果成功，应该返回200状态码和用户信息</li>
              <li>• 如果失败，检查控制台日志获取更多信息</li>
              <li>• 确保后端服务器正在运行在 http://localhost:5000</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
