'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function DebugPage() {
  const router = useRouter();

  const testPost = (id: string) => {
    console.log('测试文章:', id);
    router.push(`/post/${id}`);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>调试页面</h1>
      
      <div style={{ backgroundColor: '#f0f0f0', padding: '15px', marginBottom: '20px' }}>
        <h2>当前状态:</h2>
        <p>✅ 创建了 /post/[id]/page.tsx</p>
        <p>✅ 更新了 ArticleCard 组件</p>
        <p>✅ 更新了 explore 页面</p>
        <p>✅ 使用最简单的代码</p>
      </div>

      <div style={{ backgroundColor: '#e8f5e8', padding: '15px', marginBottom: '20px' }}>
        <h2>mockArticles 数据:</h2>
        <p>总数: {mockArticles.length}</p>
        <p>第一篇文章ID: {mockArticles[0]?.id}</p>
        <p>第一篇文章标题: {mockArticles[0]?.title}</p>
      </div>

      <h2>测试按钮:</h2>
      <div style={{ marginBottom: '20px' }}>
        {mockArticles.slice(0, 5).map((article) => (
          <button
            key={article.id}
            onClick={() => testPost(article.id)}
            style={{
              margin: '5px',
              padding: '10px 15px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            测试文章 {article.id}
          </button>
        ))}
      </div>

      <h2>直接链接:</h2>
      <div style={{ marginBottom: '20px' }}>
        {mockArticles.slice(0, 5).map((article) => (
          <div key={article.id} style={{ margin: '10px 0' }}>
            <a 
              href={`/post/${article.id}`}
              style={{ color: 'blue', textDecoration: 'underline' }}
            >
              /post/{article.id} - {article.title}
            </a>
          </div>
        ))}
      </div>

      <h2>测试不存在的文章:</h2>
      <a 
        href="/post/999"
        style={{ color: 'red', textDecoration: 'underline' }}
      >
        /post/999 (应该显示"文章未找到")
      </a>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#fff3cd', border: '1px solid #ffeaa7' }}>
        <h3>如果还是不工作:</h3>
        <ol>
          <li>检查浏览器控制台是否有错误</li>
          <li>确认 Next.js 开发服务器正在运行</li>
          <li>尝试硬刷新页面 (Ctrl+F5)</li>
          <li>检查 /post/[id] 目录是否正确创建</li>
        </ol>
      </div>

      <p style={{ marginTop: '20px' }}>
        <a href="/" style={{ color: 'blue', textDecoration: 'underline' }}>返回首页</a>
      </p>
    </div>
  );
}
