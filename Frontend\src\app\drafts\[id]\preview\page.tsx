'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import Header from '@/components/Header';
import {
  ArrowLeftIcon,
  PencilIcon,
  EyeIcon,
  ClockIcon,
  CalendarIcon,
  TagIcon,
  UserIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface Draft {
  id: number;
  title: string;
  content: string;
  contentHtml: string;
  excerpt: string;
  category: string;
  tags: string[];
  status: string;
  wordCount: number;
  readingTime: number;
  featuredImage?: string;
  lastAutoSaveAt: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: number;
    username: string;
    avatar: string;
  };
}

const DraftPreviewPage = () => {
  const { user, loading } = useSupabaseAuth();
  const router = useRouter();
  const params = useParams();
  const [draft, setDraft] = useState<Draft | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }
    if (user && params.id) {
      fetchDraft();
    }
  }, [user, loading, params.id]);

  const fetchDraft = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/drafts/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDraft(data.data);
      } else {
        setError('Draft not found');
      }
    } catch (error) {
      console.error('Error fetching draft:', error);
      setError('Failed to load draft');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', text: 'Draft' },
      auto_saved: { color: 'bg-blue-100 text-blue-800', text: 'Auto Saved' },
      ready_for_review: { color: 'bg-yellow-100 text-yellow-800', text: 'Ready for Review' },
      under_review: { color: 'bg-orange-100 text-orange-800', text: 'Under Review' },
      approved: { color: 'bg-green-100 text-green-800', text: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800', text: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-12 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !draft) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Draft not found</h3>
            <p className="mt-1 text-sm text-gray-500">{error || 'The draft you are looking for does not exist.'}</p>
            <div className="mt-6">
              <button
                onClick={() => router.push('/drafts')}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                Back to Drafts
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={() => router.push('/drafts')}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Drafts
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={() => router.push(`/create?draft=${draft.id}`)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PencilIcon className="w-5 h-5 mr-2" />
              Edit Draft
            </button>
          </div>
        </div>

        {/* Draft Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          {/* Status and Meta */}
          <div className="flex items-center justify-between mb-6">
            {getStatusBadge(draft.status)}
            <div className="flex items-center text-sm text-gray-500 space-x-4">
              <div className="flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                {draft.readingTime} min read
              </div>
              <div className="flex items-center">
                <DocumentTextIcon className="w-4 h-4 mr-1" />
                {draft.wordCount} words
              </div>
            </div>
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
            {draft.title}
          </h1>

          {/* Excerpt */}
          {draft.excerpt && (
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              {draft.excerpt}
            </p>
          )}

          {/* Featured Image */}
          {draft.featuredImage && (
            <div className="mb-6">
              <img
                src={draft.featuredImage}
                alt={draft.title}
                className="w-full h-64 object-cover rounded-lg"
              />
            </div>
          )}

          {/* Author and Dates */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center mb-4 sm:mb-0">
              <div className="flex items-center">
                <UserIcon className="w-5 h-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600">
                  By {draft.author.username}
                </span>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center text-sm text-gray-500 space-y-2 sm:space-y-0 sm:space-x-6">
              <div className="flex items-center">
                <CalendarIcon className="w-4 h-4 mr-1" />
                Created: {formatDate(draft.createdAt)}
              </div>
              {draft.lastAutoSaveAt && (
                <div className="flex items-center">
                  <ClockIcon className="w-4 h-4 mr-1" />
                  Last saved: {formatDate(draft.lastAutoSaveAt)}
                </div>
              )}
            </div>
          </div>

          {/* Category and Tags */}
          <div className="flex flex-wrap items-center gap-4 mt-6 pt-6 border-t border-gray-200">
            {draft.category && (
              <div className="flex items-center">
                <span className="text-sm text-gray-500 mr-2">Category:</span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {draft.category}
                </span>
              </div>
            )}
            
            {draft.tags && draft.tags.length > 0 && (
              <div className="flex items-center flex-wrap gap-2">
                <TagIcon className="w-4 h-4 text-gray-400" />
                {draft.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Draft Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ 
              __html: draft.contentHtml || draft.content.replace(/\n/g, '<br>') 
            }}
          />
        </div>

        {/* Actions Footer */}
        <div className="mt-8 flex justify-center">
          <button
            onClick={() => router.push(`/create?draft=${draft.id}`)}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PencilIcon className="w-5 h-5 mr-2" />
            Continue Editing
          </button>
        </div>
      </div>
    </div>
  );
};

export default DraftPreviewPage;
