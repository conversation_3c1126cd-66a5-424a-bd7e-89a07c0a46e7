'use client';

import { useState, useEffect, useCallback } from 'react';
import Header from '@/components/Header';
import Link from 'next/link';
import { Article } from '@/types';
import { mockArticles as globalMockArticles } from '@/data/mockArticles';

interface Category {
  id: string;
  name: string;
  count: number;
  color: string;
}

const categories: Category[] = [
  { id: 'all', name: 'All', count: 156, color: 'bg-gray-100 text-gray-800' },
  { id: 'technology', name: 'Technology', count: 42, color: 'bg-blue-100 text-blue-800' },
  { id: 'business', name: 'Business', count: 38, color: 'bg-green-100 text-green-800' },
  { id: 'science', name: 'Science', count: 29, color: 'bg-purple-100 text-purple-800' },
  { id: 'health', name: 'Health', count: 24, color: 'bg-red-100 text-red-800' },
  { id: 'lifestyle', name: 'Lifestyle', count: 23, color: 'bg-yellow-100 text-yellow-800' },
];

export default function ExplorePage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchArticles = async (page: number = 1, category: string = 'all', append: boolean = false) => {
    try {
      if (page === 1) {
        setLoading(true);
        setCurrentPage(1);
      } else {
        setLoadingMore(true);
      }

      await new Promise(resolve => setTimeout(resolve, 800));

      const itemsPerPage = 6;
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;

      const filteredMockArticles = category === 'all'
        ? globalMockArticles
        : globalMockArticles.filter((article: Article) =>
            article.category?.toLowerCase() === category.toLowerCase()
          );

      const extendedArticles = [];
      for (let i = 0; i < 5; i++) {
        extendedArticles.push(...filteredMockArticles.map((article: Article) => ({
          ...article,
          id: `${article.id}-${i}`,
          title: `${article.title} (Page ${Math.floor((parseInt(article.id) + i * 1000) / itemsPerPage) + 1})`
        })));
      }

      const paginatedArticles = extendedArticles.slice(startIndex, endIndex);
      const totalItems = extendedArticles.length;
      const calculatedTotalPages = Math.ceil(totalItems / itemsPerPage);

      if (append) {
        setArticles(prev => [...prev, ...paginatedArticles]);
      } else {
        setArticles(paginatedArticles);
      }

      setTotalPages(calculatedTotalPages);
      setCurrentPage(page);
      setHasMore(page < calculatedTotalPages);

    } catch (error) {
      console.error('Error fetching articles:', error);
      if (!append) {
        const filteredArticles = category === 'all'
          ? globalMockArticles
          : globalMockArticles.filter((article: Article) =>
              article.category?.toLowerCase() === category.toLowerCase()
            );
        setArticles(filteredArticles);
        setTotalPages(1);
        setCurrentPage(1);
        setHasMore(false);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      fetchArticles(1, selectedCategory, false);
    }
  }, [selectedCategory]);

  const handleScroll = useCallback(() => {
    if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loadingMore) {
      return;
    }

    if (hasMore && currentPage < totalPages) {
      fetchArticles(currentPage + 1, selectedCategory, true);
    }
  }, [currentPage, totalPages, loadingMore, hasMore, selectedCategory]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTimeAgo = (dateString: string) => {
    if (!dateString) return 'Recently';
    
    try {
      const now = Date.now();
      const date = new Date(dateString).getTime();
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
      
      if (diffInHours < 24) {
        return `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
      }
    } catch (error) {
      return 'Recently';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Explore</h1>
          <p className="text-lg text-gray-600">Discover trending articles and stories from our community</p>
        </div>

        <div className="mb-8">
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : `${category.color} hover:shadow-md`
                }`}
              >
                {category.name}
                <span className="ml-2 text-xs opacity-75">({category.count})</span>
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {articles.map((article) => (
            <Link
              key={article.id}
              href={`/article/${article.id}`}
              className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden"
            >
              {article.image && (
                <div className="aspect-video overflow-hidden">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x250`;
                    }}
                  />
                </div>
              )}

              <div className="p-6">
                <div className="mb-3">
                  <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                    categories.find(c => c.id === article.category)?.color || 'bg-gray-100 text-gray-800'
                  }`}>
                    {categories.find(c => c.id === article.category)?.name || article.category}
                  </span>
                </div>

                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                  {article.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {article.excerpt}
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={typeof article.author === 'object' 
                        ? article.author.avatar 
                        : `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author)}&background=6366f1&color=fff&size=32`}
                      alt={typeof article.author === 'object' ? article.author.name : article.author}
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          typeof article.author === 'object' ? article.author.name : article.author
                        )}&background=6366f1&color=fff&size=32`;
                      }}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {typeof article.author === 'object' ? article.author.name : article.author}
                      </p>
                      <p className="text-xs text-gray-500">
                        {article.publishedAt ? formatTimeAgo(article.publishedAt) : 'Recently'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span>{article.likes}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <span>{article.comments}</span>
                    </span>
                    <span>{article.readTime} min read</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {loadingMore && (
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-3 text-gray-500">
              <svg className="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Loading more articles...</span>
            </div>
          </div>
        )}

        {!hasMore && articles.length > 0 && (
          <div className="flex justify-center mt-12">
            <div className="text-gray-500 text-sm">
              🎉 You've reached the end! No more articles to load.
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
