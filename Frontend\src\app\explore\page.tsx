'use client';

import { useState, useEffect, useCallback } from 'react';
import Header from '@/components/Header';
import Link from 'next/link';

interface Article {
  id: number;
  title: string;
  excerpt: string;
  author: {
    name: string;
    avatar: string;
  };
  category: string;
  publishedAt: string;
  readTime: number;
  image?: string;
  tags: string[];
  likes: number;
  comments: number;
}

interface Category {
  id: string;
  name: string;
  count: number;
  color: string;
}

const categories: Category[] = [
  { id: 'all', name: 'All', count: 156, color: 'bg-gray-100 text-gray-800' },
  { id: 'technology', name: 'Technology', count: 42, color: 'bg-blue-100 text-blue-800' },
  { id: 'business', name: 'Business', count: 38, color: 'bg-green-100 text-green-800' },
  { id: 'science', name: 'Science', count: 29, color: 'bg-purple-100 text-purple-800' },
  { id: 'health', name: 'Health', count: 24, color: 'bg-red-100 text-red-800' },
  { id: 'lifestyle', name: 'Lifestyle', count: 23, color: 'bg-yellow-100 text-yellow-800' },
];

const mockArticles: Article[] = [
  {
    id: 1,
    title: "The Future of Artificial Intelligence in Healthcare",
    excerpt: "Exploring how AI is revolutionizing medical diagnosis and treatment, from machine learning algorithms to robotic surgery.",
    author: {
      name: "Dr. Sarah Chen",
      avatar: "https://ui-avatars.com/api/?name=Sarah+Chen&background=6366f1&color=fff&size=40"
    },
    category: "technology",
    publishedAt: "2024-01-15T10:30:00Z",
    readTime: 8,
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=250&fit=crop",
    tags: ["AI", "Healthcare", "Technology"],
    likes: 124,
    comments: 18
  },
  {
    id: 2,
    title: "Sustainable Business Practices for the Modern Era",
    excerpt: "How companies are adapting to environmental challenges while maintaining profitability and growth.",
    author: {
      name: "Michael Rodriguez",
      avatar: "https://ui-avatars.com/api/?name=Michael+Rodriguez&background=10b981&color=fff&size=40"
    },
    category: "business",
    publishedAt: "2024-01-14T14:20:00Z",
    readTime: 6,
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=250&fit=crop",
    tags: ["Sustainability", "Business", "Environment"],
    likes: 89,
    comments: 12
  },
  {
    id: 3,
    title: "Breakthrough in Quantum Computing Research",
    excerpt: "Scientists achieve new milestone in quantum computing that could revolutionize data processing and encryption.",
    author: {
      name: "Prof. Emily Watson",
      avatar: "https://ui-avatars.com/api/?name=Emily+Watson&background=8b5cf6&color=fff&size=40"
    },
    category: "science",
    publishedAt: "2024-01-13T09:15:00Z",
    readTime: 10,
    image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop",
    tags: ["Quantum Computing", "Science", "Research"],
    likes: 156,
    comments: 24
  },
  {
    id: 4,
    title: "Mental Health in the Digital Age",
    excerpt: "Understanding the impact of social media and digital technology on mental wellbeing and strategies for balance.",
    author: {
      name: "Dr. James Park",
      avatar: "https://ui-avatars.com/api/?name=James+Park&background=ef4444&color=fff&size=40"
    },
    category: "health",
    publishedAt: "2024-01-12T16:45:00Z",
    readTime: 7,
    image: "https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=250&fit=crop",
    tags: ["Mental Health", "Digital Wellness", "Psychology"],
    likes: 203,
    comments: 31
  },
  {
    id: 5,
    title: "The Art of Minimalist Living",
    excerpt: "Discovering how simplifying your lifestyle can lead to greater happiness and productivity.",
    author: {
      name: "Anna Thompson",
      avatar: "https://ui-avatars.com/api/?name=Anna+Thompson&background=f59e0b&color=fff&size=40"
    },
    category: "lifestyle",
    publishedAt: "2024-01-11T11:30:00Z",
    readTime: 5,
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=250&fit=crop",
    tags: ["Minimalism", "Lifestyle", "Wellness"],
    likes: 167,
    comments: 19
  },
  {
    id: 6,
    title: "Cryptocurrency Market Trends 2024",
    excerpt: "Analysis of current cryptocurrency trends and predictions for the digital currency market this year.",
    author: {
      name: "David Kim",
      avatar: "https://ui-avatars.com/api/?name=David+Kim&background=06b6d4&color=fff&size=40"
    },
    category: "business",
    publishedAt: "2024-01-10T13:20:00Z",
    readTime: 9,
    image: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400&h=250&fit=crop",
    tags: ["Cryptocurrency", "Finance", "Investment"],
    likes: 142,
    comments: 27
  }
];

export default function ExplorePage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // 模拟API调用获取文章
  const fetchArticles = async (page = 1, category = 'all', append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
        setCurrentPage(1);
      } else {
        setLoadingMore(true);
      }

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      // 模拟分页数据
      const itemsPerPage = 6;
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;

      // 根据类别过滤文章
      const filteredMockArticles = category === 'all'
        ? mockArticles
        : mockArticles.filter(article => article.category === category);

      // 为了演示无限滚动，我们复制数据来创建更多内容
      const extendedArticles = [];
      for (let i = 0; i < 5; i++) {
        extendedArticles.push(...filteredMockArticles.map(article => ({
          ...article,
          id: article.id + (i * 1000),
          title: `${article.title} (Page ${Math.floor((article.id + i * 1000) / itemsPerPage) + 1})`
        })));
      }

      const paginatedArticles = extendedArticles.slice(startIndex, endIndex);
      const totalItems = extendedArticles.length;
      const calculatedTotalPages = Math.ceil(totalItems / itemsPerPage);

      if (append) {
        setArticles(prev => [...prev, ...paginatedArticles]);
      } else {
        setArticles(paginatedArticles);
      }

      setTotalPages(calculatedTotalPages);
      setCurrentPage(page);
      setHasMore(page < calculatedTotalPages);

    } catch (error) {
      console.error('Error fetching articles:', error);
      // 使用原始mock数据作为fallback
      if (!append) {
        const filteredArticles = category === 'all'
          ? mockArticles
          : mockArticles.filter(article => article.category === category);
        setArticles(filteredArticles);
        setTotalPages(1);
        setCurrentPage(1);
        setHasMore(false);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 初始加载和类别变化时重新加载
  useEffect(() => {
    fetchArticles(1, selectedCategory, false);
  }, [selectedCategory]);

  // 无限滚动逻辑
  const handleScroll = useCallback(() => {
    if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loadingMore) {
      return;
    }

    // 当滚动到页面底部时加载更多
    if (hasMore && currentPage < totalPages) {
      fetchArticles(currentPage + 1, selectedCategory, true);
    }
  }, [currentPage, totalPages, loadingMore, hasMore, selectedCategory]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const filteredArticles = articles;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Explore</h1>
          <p className="text-lg text-gray-600">Discover trending articles and stories from our community</p>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : `${category.color} hover:shadow-md`
                }`}
              >
                {category.name}
                <span className="ml-2 text-xs opacity-75">({category.count})</span>
              </button>
            ))}
          </div>
        </div>

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredArticles.map((article) => (
            <Link
              key={article.id}
              href={`/article/${article.id}`}
              className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden"
            >
              {/* Article Image */}
              {article.image && (
                <div className="aspect-video overflow-hidden">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x250`;
                    }}
                  />
                </div>
              )}

              <div className="p-6">
                {/* Category Badge */}
                <div className="mb-3">
                  <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                    categories.find(c => c.id === article.category)?.color || 'bg-gray-100 text-gray-800'
                  }`}>
                    {categories.find(c => c.id === article.category)?.name || article.category}
                  </span>
                </div>

                {/* Article Title */}
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                  {article.title}
                </h3>

                {/* Article Excerpt */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {article.excerpt}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Author and Meta */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={article.author.avatar}
                      alt={article.author.name}
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author.name)}&background=6366f1&color=fff&size=32`;
                      }}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{article.author.name}</p>
                      <p className="text-xs text-gray-500">{formatTimeAgo(article.publishedAt)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span>{article.likes}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <span>{article.comments}</span>
                    </span>
                    <span>{article.readTime} min read</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Infinite Scroll Loading Indicator */}
        {loadingMore && (
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-3 text-gray-500">
              <svg className="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Loading more articles...</span>
            </div>
          </div>
        )}

        {/* End of content indicator */}
        {!hasMore && articles.length > 0 && (
          <div className="flex justify-center mt-12">
            <div className="text-gray-500 text-sm">
              🎉 You've reached the end! No more articles to load.
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
