'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';
import Link from 'next/link';

export default function FinalDiagnosis() {
  const router = useRouter();

  const testArticle = (articleId: string) => {
    console.log('=== 最终测试 ===');
    console.log('文章ID:', articleId);
    console.log('mockArticles:', mockArticles.map(a => ({ id: a.id, title: a.title })));
    
    const found = mockArticles.find(article => article.id === articleId);
    console.log('查找结果:', found ? `找到: ${found.title}` : '未找到');
    
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🔧 最终诊断和解决</h1>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-red-800">🚨 发现的问题</h2>
          <div className="text-sm text-red-700 space-y-2">
            <p><strong>根本原因</strong>: 存在多个冲突的文章详情页面路由</p>
            <p><strong>问题1</strong>: `/article-detail/[id]/page.tsx` 文件仍然存在</p>
            <p><strong>问题2</strong>: Next.js路由优先级导致错误的页面被加载</p>
            <p><strong>问题3</strong>: 一些测试页面还在跳转到错误的路径</p>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 已执行的修复</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ 删除冲突文件</strong>: 移除了 `/article-detail/[id]/page.tsx`</p>
            <p><strong>✅ 统一路由</strong>: 所有链接现在都指向 `/article/[id]`</p>
            <p><strong>✅ 修复测试页面</strong>: 更新了错误的跳转路径</p>
            <p><strong>✅ 简化ID处理</strong>: 使用简单的字符串匹配</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 最终测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockArticles.slice(0, 4).map((article) => (
              <button
                key={article.id}
                onClick={() => testArticle(article.id)}
                className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-left"
              >
                <h3 className="font-semibold text-gray-900 mb-2">
                  {article.title.substring(0, 40)}...
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-blue-600">点击测试 →</p>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🔗 直接链接测试</h2>
          <div className="space-y-3">
            {mockArticles.slice(0, 3).map((article) => (
              <div key={article.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded">
                <span className="text-sm font-mono text-gray-600">ID: {article.id}</span>
                <Link 
                  href={`/article/${article.id}`}
                  className="text-blue-600 hover:text-blue-800 underline font-mono"
                >
                  /article/{article.id}
                </Link>
                <span className="text-sm text-gray-500 flex-1">{article.title}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">📋 如果问题仍然存在</h2>
          <div className="text-sm text-blue-700 space-y-3">
            <p><strong>立即执行以下步骤:</strong></p>
            <ol className="list-decimal list-inside space-y-2 ml-4">
              <li><strong>重启开发服务器</strong>: 停止并重新启动 `npm run dev`</li>
              <li><strong>清除浏览器缓存</strong>: 按 Ctrl+Shift+R 硬刷新</li>
              <li><strong>检查控制台</strong>: 打开开发者工具查看错误信息</li>
              <li><strong>验证路由</strong>: 确认只有 `/article/[id]` 路径存在</li>
            </ol>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">🎯 预期结果</h2>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>✅ 点击任意文章</strong>: 应该跳转到正确的文章详情页面</p>
            <p><strong>✅ 显示文章内容</strong>: 标题、作者、内容等信息正确显示</p>
            <p><strong>✅ 控制台日志</strong>: 显示"✅ 找到文章"而不是"❌ 未找到文章"</p>
            <p><strong>✅ 不再出现</strong>: "Article Not Found"错误页面</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🏠 测试首页
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              🔍 测试探索页面
            </button>
            <button
              onClick={() => testArticle('1')}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              🧪 测试文章1
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
