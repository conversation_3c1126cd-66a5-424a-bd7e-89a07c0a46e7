import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import LoginPage from '../page'
import { SupabaseAuthProvider } from '@/contexts/SupabaseAuthContext'

// Mock the SupabaseAuthContext
const mockSignIn = jest.fn()
const mockAuthContext = {
  user: null,
  isAuthenticated: false,
  signIn: mockSignIn,
  signUp: jest.fn(),
  signOut: jest.fn(),
  error: '',
  setError: jest.fn(),
  loading: false,
}

jest.mock('@/contexts/SupabaseAuthContext', () => ({
  ...jest.requireActual('@/contexts/SupabaseAuthContext'),
  useSupabaseAuth: () => mockAuthContext,
}))

const renderLoginPage = () => {
  return render(
    <SupabaseAuthProvider>
      <LoginPage />
    </SupabaseAuthProvider>
  )
}

describe('LoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockAuthContext.error = ''
  })

  describe('Rendering', () => {
    it('should render login form with all required fields', () => {
      renderLoginPage()
      
      expect(screen.getByRole('heading', { name: /sign in to your account/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument()
    })

    it('should render Newzora branding', () => {
      renderLoginPage()

      expect(screen.getByText('Newzora')).toBeInTheDocument()
      expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument()
    })

    it('should have proper form structure', () => {
      renderLoginPage()
      
      const form = screen.getByRole('form') || screen.getByTestId('login-form')
      expect(form).toBeInTheDocument()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      
      expect(emailInput).toHaveAttribute('type', 'email')
      expect(passwordInput).toHaveAttribute('type', 'password')
      expect(emailInput).toBeRequired()
      expect(passwordInput).toBeRequired()
    })
  })

  describe('Form Validation', () => {
    it('should show validation error for empty email', async () => {
      const user = userEvent.setup()
      renderLoginPage()
      
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.click(submitButton)
      })
      
      // Check for HTML5 validation or custom validation message
      const emailInput = screen.getByLabelText(/email/i)
      expect(emailInput).toBeInvalid()
    })

    it('should show validation error for empty password', async () => {
      const user = userEvent.setup()
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
        await user.click(submitButton)
      })
      
      const passwordInput = screen.getByLabelText(/password/i)
      expect(passwordInput).toBeInvalid()
    })

    it('should accept valid email format', async () => {
      const user = userEvent.setup()
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
      })
      
      expect(emailInput).toHaveValue('<EMAIL>')
      expect(emailInput).toBeValid()
    })
  })

  describe('Form Submission', () => {
    it('should call login function with correct credentials', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValueOnce({ success: true })
      
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
        await user.type(passwordInput, 'password123')
        await user.click(submitButton)
      })
      
      expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })

    it('should show loading state during login', async () => {
      const user = userEvent.setup()
      let resolveLogin
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      mockSignIn.mockReturnValueOnce(loginPromise)
      
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
        await user.type(passwordInput, 'password123')
        await user.click(submitButton)
      })
      
      // Check for loading state
      expect(screen.getByText(/signing in/i) || screen.getByTestId('loading-spinner')).toBeInTheDocument()
      expect(submitButton).toBeDisabled()
      
      // Resolve the login
      act(() => {
        resolveLogin(true)
      })
    })

    it('should redirect on successful login', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValueOnce({ success: true })
      
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
        await user.type(passwordInput, 'password123')
        await user.click(submitButton)
      })
      
      await waitFor(() => {
        expect(window.location.href).toBe('/')
      })
    })
  })

  describe('Error Handling', () => {
    it('should display error message from AuthContext', () => {
      mockAuthContext.error = 'Invalid credentials'
      
      renderLoginPage()
      
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
    })

    it('should handle login failure', async () => {
      const user = userEvent.setup()
      mockSignIn.mockResolvedValueOnce({ success: false })
      
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      await act(async () => {
        await user.type(emailInput, '<EMAIL>')
        await user.type(passwordInput, 'wrongpassword')
        await user.click(submitButton)
      })
      
      // Should not redirect on failed login
      expect(window.location.href).toBe('http://localhost:3000')
    })
  })

  describe('Navigation Links', () => {
    it('should have link to registration page', () => {
      renderLoginPage()
      
      const registerLink = screen.getByRole('link', { name: /sign up/i })
      expect(registerLink).toBeInTheDocument()
      expect(registerLink).toHaveAttribute('href', '/register')
    })

    it('should have link to password reset', () => {
      renderLoginPage()
      
      const forgotPasswordLink = screen.getByRole('link', { name: /forgot.*password/i })
      expect(forgotPasswordLink).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      renderLoginPage()
      
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      
      expect(emailInput).toHaveAttribute('aria-required', 'true')
      expect(passwordInput).toHaveAttribute('aria-required', 'true')
    })

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup()
      renderLoginPage()
      
      // Tab through form elements
      await user.tab()
      expect(screen.getByLabelText(/email/i)).toHaveFocus()
      
      await user.tab()
      expect(screen.getByLabelText(/password/i)).toHaveFocus()
      
      await user.tab()
      expect(screen.getByRole('button', { name: /sign in/i })).toHaveFocus()
    })
  })
})
