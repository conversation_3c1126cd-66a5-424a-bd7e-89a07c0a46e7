'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface ReviewStats {
  totalPending: number;
  totalReviewed: number;
  approvedToday: number;
  rejectedToday: number;
  averageReviewTime: number;
  myReviewsToday: number;
}

interface PendingReview {
  id: string;
  articleId: string;
  title: string;
  author: {
    id: string;
    username: string;
    firstName?: string;
    lastName?: string;
  };
  submittedAt: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category: string;
  wordCount: number;
  flags: string[];
  aiScore?: number;
}

interface RecentActivity {
  id: string;
  type: 'approved' | 'rejected' | 'flagged';
  articleTitle: string;
  moderator: string;
  timestamp: string;
  reason?: string;
}

export default function ModeratorDashboard() {
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [pendingReviews, setPendingReviews] = useState<PendingReview[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (!['moderator', 'admin', 'super_admin'].includes(user?.role || '')) {
      router.push('/unauthorized');
      return;
    }

    fetchDashboardData();
  }, [isAuthenticated, user, router]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch review statistics
      const statsResponse = await fetch('/api/moderation/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Fetch pending reviews
      const reviewsResponse = await fetch('/api/moderation/pending?limit=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (reviewsResponse.ok) {
        const reviewsData = await reviewsResponse.json();
        setPendingReviews(reviewsData.data.reviews || []);
      }

      // Fetch recent activity
      const activityResponse = await fetch('/api/moderation/activity?limit=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(activityData.data.activities || []);
      }

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'approved': return '✅';
      case 'rejected': return '❌';
      case 'flagged': return '🚩';
      default: return '📝';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'flagged': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading moderator dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Moderator Dashboard</h1>
                <p className="mt-2 text-gray-600">Content review and moderation center</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => router.push('/moderator/queue')}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  Review Queue
                </button>
                <button
                  onClick={() => router.push('/moderator/reports')}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Reports
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">⏳</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPending}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Approved Today</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.approvedToday}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">✗</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Rejected Today</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.rejectedToday}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">📊</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Reviewed</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalReviewed}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">⏱️</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Avg Review Time</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.averageReviewTime}m</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">👤</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">My Reviews Today</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.myReviewsToday}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Pending Reviews */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">Pending Reviews</h2>
                <button
                  onClick={() => router.push('/moderator/queue')}
                  className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                >
                  View All
                </button>
              </div>
            </div>
            <div className="divide-y divide-gray-200">
              {pendingReviews.length > 0 ? (
                pendingReviews.map((review) => (
                  <div key={review.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {review.title}
                          </h3>
                          <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getPriorityColor(review.priority)}`}>
                            {review.priority}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                          by {review.author.firstName && review.author.lastName 
                            ? `${review.author.firstName} ${review.author.lastName}` 
                            : review.author.username}
                        </p>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <span>{review.category}</span>
                          <span className="mx-2">•</span>
                          <span>{review.wordCount} words</span>
                          <span className="mx-2">•</span>
                          <span>{new Date(review.submittedAt).toLocaleDateString()}</span>
                        </div>
                        {review.flags.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {review.flags.map((flag, index) => (
                              <span key={index} className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                                {flag}
                              </span>
                            ))}
                          </div>
                        )}
                        {review.aiScore && (
                          <div className="mt-2">
                            <span className="text-xs text-gray-500">
                              AI Score: <span className={`font-medium ${review.aiScore >= 80 ? 'text-green-600' : review.aiScore >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                                {review.aiScore}%
                              </span>
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <button
                          onClick={() => router.push(`/moderator/review/${review.id}`)}
                          className="bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700"
                        >
                          Review
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-6 text-center text-gray-500">
                  <div className="text-4xl mb-2">🎉</div>
                  <p>No pending reviews!</p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
                <button
                  onClick={() => router.push('/moderator/activity')}
                  className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                >
                  View All
                </button>
              </div>
            </div>
            <div className="divide-y divide-gray-200">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity) => (
                  <div key={activity.id} className="p-6">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <span className="text-lg">{getActivityIcon(activity.type)}</span>
                      </div>
                      <div className="ml-3 flex-1">
                        <p className="text-sm text-gray-900">
                          <span className="font-medium">{activity.moderator}</span>
                          <span className={`ml-1 ${getActivityColor(activity.type)}`}>
                            {activity.type}
                          </span>
                          <span className="ml-1">"{activity.articleTitle}"</span>
                        </p>
                        {activity.reason && (
                          <p className="mt-1 text-sm text-gray-500">{activity.reason}</p>
                        )}
                        <p className="mt-1 text-xs text-gray-400">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-6 text-center text-gray-500">
                  <div className="text-4xl mb-2">📝</div>
                  <p>No recent activity</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
