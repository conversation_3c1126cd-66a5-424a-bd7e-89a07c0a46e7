'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import Header from '@/components/Header';
import ArticleCard from '@/components/ArticleCard';
import { Article } from '@/types';
import { mockArticles } from '@/data/mockArticles';
import Link from 'next/link';

export default function Home() {
  const { user, isAuthenticated, signOut } = useSupabaseAuth();
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('Trending');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    fetchArticles();
  }, [selectedCategory, searchQuery]);

  // 无限滚动逻辑
  const handleScroll = useCallback(() => {
    if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loadingMore) {
      return;
    }

    // 当滚动到页面底部时加载更多
    if (currentPage < totalPages) {
      fetchArticles(currentPage + 1, true);
    }
  }, [currentPage, totalPages, loadingMore]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const fetchArticles = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
        setCurrentPage(1);
      } else {
        setLoadingMore(true);
      }

      const params = new URLSearchParams();

      if (selectedCategory !== 'Trending') {
        params.append('category', selectedCategory);
      }

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      params.append('featured', 'true');
      params.append('limit', '6');
      params.append('page', page.toString());

      const response = await fetch(`http://localhost:5000/api/articles?${params}`);
      const data = await response.json();

      if (append) {
        setArticles(prev => [...prev, ...data.articles]);
      } else {
        setArticles(data.articles || []);
      }

      setTotalPages(data.totalPages || 1);
      setCurrentPage(data.currentPage || page);
    } catch (error) {
      console.error('Error fetching articles:', error);
      // Use mock data as fallback
      if (!append) {
        const filteredArticles = selectedCategory === 'Trending'
          ? mockArticles
          : mockArticles.filter(article => article.category === selectedCategory);
        setArticles(filteredArticles);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        {/* Main Search Section */}
        <div className="mb-8">
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
              <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search for articles or videos"
              className="w-full pl-16 pr-6 py-5 text-lg bg-white border border-gray-200 rounded-2xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 transition-all duration-200 shadow-sm hover:shadow-lg focus:shadow-lg transform hover:scale-[1.02] focus:scale-[1.02]"
            />
          </div>
        </div>

        {/* Category Tabs */}
        <div className="mb-12">
          <div className="flex justify-center">
            <div className="flex gap-2">
              {['Trending', 'Technology', 'Lifestyle', 'Travel', 'Food'].map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-3 text-sm font-medium rounded-full transition-all duration-200 whitespace-nowrap transform hover:scale-105 ${
                    selectedCategory === category
                      ? 'text-white bg-blue-600 shadow-lg shadow-blue-600/25'
                      : 'text-gray-600 bg-white hover:text-gray-900 hover:bg-gray-50 hover:shadow-md border border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-10">
            Recommended for you
          </h2>

          {loading ? (
            <div className="space-y-8">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse flex gap-8 py-6">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-24 mb-4"></div>
                    <div className="h-7 bg-gray-300 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-4/5"></div>
                  </div>
                  <div className="w-64 h-40 bg-gray-300 rounded-2xl flex-shrink-0"></div>
                </div>
              ))}
            </div>
          ) : (
            <>
              <div className="space-y-8">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>

              {/* Infinite Scroll Loading Indicator */}
              {loadingMore && (
                <div className="flex justify-center mt-12">
                  <div className="flex items-center space-x-3 text-gray-500">
                    <svg className="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Loading more articles...</span>
                  </div>
                </div>
              )}

              {/* End of content indicator */}
              {currentPage >= totalPages && articles.length > 0 && (
                <div className="flex justify-center mt-12">
                  <div className="text-gray-500 text-sm">
                    🎉 You've reached the end! No more articles to load.
                  </div>
                </div>
              )}
            </>
          )}
        </section>
      </main>
    </div>
  );
}
