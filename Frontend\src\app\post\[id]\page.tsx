'use client';

import { useParams } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function PostPage() {
  const params = useParams();
  const id = params.id as string;
  
  // 直接查找文章
  const article = mockArticles.find(a => a.id === id);
  
  // 如果没找到文章
  if (!article) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h1>文章未找到</h1>
        <p>ID: {id}</p>
        <a href="/">返回首页</a>
      </div>
    );
  }
  
  // 显示文章
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>{article.title}</h1>
      <p><strong>作者:</strong> {typeof article.author === 'string' ? article.author : article.author.name}</p>
      <p><strong>分类:</strong> {article.category}</p>
      <p><strong>阅读时间:</strong> {article.readTime} 分钟</p>
      
      {article.image && (
        <img 
          src={article.image} 
          alt={article.title}
          style={{ width: '100%', height: '300px', objectFit: 'cover', margin: '20px 0' }}
        />
      )}
      
      <div style={{ lineHeight: '1.6', marginBottom: '20px' }}>
        <p>{article.description}</p>
        <p>{article.content}</p>
      </div>
      
      <div style={{ borderTop: '1px solid #ccc', paddingTop: '20px' }}>
        <p>点赞: {article.likes} | 评论: {article.comments || 0}</p>
        <a href="/" style={{ marginRight: '10px' }}>返回首页</a>
        <a href="/explore">浏览更多</a>
      </div>
    </div>
  );
}
