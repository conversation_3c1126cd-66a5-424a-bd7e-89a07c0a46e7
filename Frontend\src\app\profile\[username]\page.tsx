'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
// import Image from 'next/image'; // Temporarily disabled due to config issues
import Header from '@/components/Header';

interface UserProfile {
  id: number;
  username: string;
  displayName: string;
  bio: string;
  avatar: string;
  joinedDate: string;
  followersCount: number;
  followingCount: number;
  postsCount: number;
}

interface Post {
  id: number;
  title: string;
  image: string;
  createdAt: string;
}

export default function ProfilePage() {
  const params = useParams();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [activeTab, setActiveTab] = useState<'posts' | 'about'>('posts');
  const [loading, setLoading] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setUser({
        id: 1,
        username: 'sophia-carter',
        displayName: '<PERSON>',
        bio: 'Content Creator | Tech Enthusiast',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face',
        joinedDate: '2021',
        followersCount: 1250,
        followingCount: 340,
        postsCount: 89
      });

      setPosts([
        {
          id: 1,
          title: 'Abstract Art 1',
          image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=300&fit=crop',
          createdAt: '2024-01-15'
        },
        {
          id: 2,
          title: 'Minimalist Design',
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          createdAt: '2024-01-14'
        },
        {
          id: 3,
          title: 'Geometric Shapes',
          image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
          createdAt: '2024-01-13'
        },
        {
          id: 4,
          title: 'Nature Abstract',
          image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=300&h=300&fit=crop',
          createdAt: '2024-01-12'
        },
        {
          id: 5,
          title: 'Modern Art',
          image: 'https://images.unsplash.com/photo-1549887534-1541e9326642?w=300&h=300&fit=crop',
          createdAt: '2024-01-11'
        },
        {
          id: 6,
          title: 'Botanical Study',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',
          createdAt: '2024-01-10'
        },
        {
          id: 7,
          title: 'Color Theory',
          image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=300&fit=crop',
          createdAt: '2024-01-09'
        },
        {
          id: 8,
          title: 'Texture Study',
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          createdAt: '2024-01-08'
        },
        {
          id: 9,
          title: 'Form & Function',
          image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
          createdAt: '2024-01-07'
        },
        {
          id: 10,
          title: 'Digital Art',
          image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=300&h=300&fit=crop',
          createdAt: '2024-01-06'
        }
      ]);

      setLoading(false);
    }, 1000);
  }, [params.username]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="flex flex-col items-center mb-8">
              <div className="w-32 h-32 bg-gray-300 rounded-full mb-4"></div>
              <div className="h-6 bg-gray-300 rounded w-48 mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-64"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">User Not Found</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* User Profile Header */}
        <div className="flex flex-col items-center mb-12">
          {/* Avatar */}
          <div className="w-32 h-32 rounded-full overflow-hidden mb-6">
            <img
              src={user.avatar}
              alt={user.displayName}
              className="object-cover w-full h-full"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.displayName)}&background=6366f1&color=fff&size=128`;
              }}
            />
          </div>

          {/* User Info */}
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{user.displayName}</h1>
          <p className="text-blue-500 text-lg mb-2">{user.bio}</p>
          <p className="text-gray-500 mb-8">Joined in {user.joinedDate}</p>

          {/* Follow Button */}
          <button
            onClick={() => setIsFollowing(!isFollowing)}
            className={`px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 ${
              isFollowing
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                : 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg'
            }`}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </button>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-8 border-b border-gray-200">
            <button
              onClick={() => setActiveTab('posts')}
              className={`pb-4 px-2 text-lg font-medium transition-colors duration-200 ${
                activeTab === 'posts'
                  ? 'text-gray-900 border-b-2 border-gray-900'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Posts
            </button>
            <button
              onClick={() => setActiveTab('about')}
              className={`pb-4 px-2 text-lg font-medium transition-colors duration-200 ${
                activeTab === 'about'
                  ? 'text-gray-900 border-b-2 border-gray-900'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              About
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'posts' && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {posts.slice(0, 8).map((post, index) => {
              // Abstract art styles matching the mockup exactly
              const artStyles = [
                // Card 1: Large organic shape (top-left)
                <div key="art1" className="w-full h-full bg-gradient-to-br from-orange-200 to-orange-300 rounded-2xl relative overflow-hidden">
                  <div className="absolute top-4 left-4 w-24 h-32 bg-orange-400 rounded-full transform rotate-12 opacity-90"></div>
                </div>,

                // Card 2: Curved shapes (top-second)
                <div key="art2" className="w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl relative overflow-hidden">
                  <div className="absolute top-6 right-6 w-16 h-24 bg-orange-300 rounded-full transform -rotate-45"></div>
                  <div className="absolute bottom-4 left-4 w-12 h-20 bg-orange-400 rounded-full transform rotate-12"></div>
                </div>,

                // Card 3: Framed oval (top-third)
                <div key="art3" className="w-full h-full bg-white rounded-2xl relative overflow-hidden border border-gray-200">
                  <div className="absolute inset-4 border-2 border-orange-300 rounded-lg flex items-center justify-center">
                    <div className="w-12 h-20 bg-orange-200 rounded-full"></div>
                  </div>
                </div>,

                // Card 4: Abstract figure (top-right)
                <div key="art4" className="w-full h-full bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl relative overflow-hidden">
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-20 bg-orange-200 rounded-t-full"></div>
                  <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-20 h-12 bg-orange-300 rounded-full"></div>
                </div>,

                // Card 5: Framed curved shape (bottom-left)
                <div key="art5" className="w-full h-full bg-white rounded-2xl relative overflow-hidden border-4 border-orange-200">
                  <div className="absolute inset-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
                    <div className="w-10 h-16 bg-orange-400 rounded-full transform rotate-12"></div>
                  </div>
                </div>,

                // Card 6: Botanical with stem (bottom-second)
                <div key="art6" className="w-full h-full bg-white rounded-2xl relative overflow-hidden border border-gray-200">
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-1 h-16 bg-green-400"></div>
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-12 bg-green-300 rounded-full"></div>
                  <div className="absolute inset-x-4 bottom-4 h-8 bg-gradient-to-r from-orange-100 to-orange-200 rounded-b-2xl"></div>
                </div>,

                // Card 7: Two circles (bottom-third)
                <div key="art7" className="w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl relative overflow-hidden">
                  <div className="absolute top-6 left-6 w-10 h-10 bg-orange-300 rounded-full"></div>
                  <div className="absolute bottom-8 right-8 w-14 h-14 bg-orange-400 rounded-full opacity-80"></div>
                </div>,

                // Card 8: Striped oval (bottom-right)
                <div key="art8" className="w-full h-full bg-gradient-to-br from-orange-200 to-orange-300 rounded-2xl relative overflow-hidden">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-12 bg-orange-400 rounded-full">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-500 to-transparent opacity-60 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-500 to-transparent opacity-40 rounded-full transform translate-y-1"></div>
                  </div>
                </div>
              ];

              return (
                <div
                  key={post.id}
                  className="aspect-square rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group transform hover:scale-105"
                >
                  <div className="relative w-full h-full">
                    {artStyles[index]}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {activeTab === 'about' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <h3 className="text-xl font-bold text-gray-900 mb-4">About Sophia</h3>
              <p className="text-gray-700 leading-relaxed mb-6">
                Passionate content creator and tech enthusiast with a love for modern art and design.
                I create visual content that bridges the gap between technology and creativity,
                inspiring others to explore the intersection of art and innovation.
              </p>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-gray-900">{user.postsCount}</div>
                  <div className="text-gray-500 text-sm">Posts</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{user.followersCount}</div>
                  <div className="text-gray-500 text-sm">Followers</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{user.followingCount}</div>
                  <div className="text-gray-500 text-sm">Following</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
