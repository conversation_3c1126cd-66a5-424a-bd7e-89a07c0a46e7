'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function ProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('Posts');
  
  const username = params.username as string;
  
  // Mock user data
  const user = {
    name: '<PERSON>',
    title: 'Content Creator | Tech Enthusiast',
    joinDate: 'Joined in 2021',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    followers: 1250,
    following: 340,
    posts: mockArticles.length
  };

  // Mock portfolio items
  const portfolioItems = [
    { id: 1, type: 'image', color: 'bg-gradient-to-br from-orange-200 to-orange-400' },
    { id: 2, type: 'image', color: 'bg-gradient-to-br from-orange-300 to-orange-500' },
    { id: 3, type: 'image', color: 'bg-gradient-to-br from-yellow-200 to-orange-300' },
    { id: 4, type: 'image', color: 'bg-gradient-to-br from-orange-100 to-orange-300' },
    { id: 5, type: 'image', color: 'bg-gradient-to-br from-orange-200 to-orange-400' },
    { id: 6, type: 'image', color: 'bg-gradient-to-br from-yellow-100 to-orange-200' },
    { id: 7, type: 'image', color: 'bg-gradient-to-br from-orange-300 to-orange-500' },
    { id: 8, type: 'image', color: 'bg-gradient-to-br from-orange-200 to-yellow-300' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <button onClick={() => router.push('/')} className="text-xl font-bold text-gray-900">
                Newzora
              </button>
              <nav className="flex space-x-6">
                <button onClick={() => router.push('/')} className="text-gray-600 hover:text-gray-900">Home</button>
                <button onClick={() => router.push('/explore')} className="text-gray-600 hover:text-gray-900">Explore</button>
                <button onClick={() => router.push('/create')} className="text-gray-600 hover:text-gray-900">Create</button>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-64 px-4 py-2 bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <svg className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button className="p-2 text-gray-600 hover:text-gray-900">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
                </svg>
              </button>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </header>

      {/* Profile Content */}
      <main className="max-w-4xl mx-auto px-6 py-12">
        {/* Profile Header */}
        <div className="text-center mb-12">
          <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden bg-gray-300">
            <img
              src={user.avatar}
              alt={user.name}
              className="w-full h-full object-cover"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{user.name}</h1>
          <p className="text-blue-600 mb-2">{user.title}</p>
          <p className="text-gray-500 mb-6">{user.joinDate}</p>
          
          <button className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
            Follow
          </button>
        </div>

        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-8 border-b border-gray-200">
            {['Posts', 'About'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`pb-4 px-2 font-medium ${
                  activeTab === tab
                    ? 'text-gray-900 border-b-2 border-gray-900'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        {activeTab === 'Posts' && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {portfolioItems.map((item, index) => (
              <div
                key={item.id}
                className={`aspect-square rounded-lg ${item.color} cursor-pointer hover:scale-105 transition-transform`}
                onClick={() => router.push(`/article/${index + 1}`)}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <div className="relative w-full h-full p-4">
                    {index % 4 === 0 && (
                      <div className="absolute inset-4 bg-white/20 rounded-full"></div>
                    )}
                    {index % 4 === 1 && (
                      <div className="absolute inset-4 bg-white/20 rounded-lg transform rotate-12"></div>
                    )}
                    {index % 4 === 2 && (
                      <div className="absolute inset-4 bg-white/20 rounded-full border-4 border-white/30"></div>
                    )}
                    {index % 4 === 3 && (
                      <div className="absolute inset-4 bg-white/20 rounded-lg"></div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'About' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">About {user.name}</h3>
              <p className="text-gray-700 leading-relaxed">
                Passionate content creator and tech enthusiast with a love for exploring the intersection of art, technology, and human creativity.
              </p>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
