'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { mockArticles } from '@/data/mockArticles';
import { Article } from '@/types';

export default function ReadPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      const articleId = searchParams.get('id');
      console.log('📖 ReadPage: 开始加载文章, ID:', articleId);
      
      if (!articleId) {
        setError('缺少文章ID参数');
        setLoading(false);
        return;
      }

      // 直接查找文章，使用最简单的匹配方式
      const foundArticle = mockArticles.find(article => {
        const match = article.id === articleId || article.id.toString() === articleId.toString();
        console.log(`📖 比较文章 ${article.id} 与 ${articleId}: ${match ? '匹配' : '不匹配'}`);
        return match;
      });

      if (foundArticle) {
        console.log('📖 ✅ 成功找到文章:', foundArticle.title);
        setArticle(foundArticle);
        setError(null);
      } else {
        console.log('📖 ❌ 未找到文章，可用文章列表:', mockArticles.map(a => ({ id: a.id, title: a.title })));
        setError(`未找到ID为 "${articleId}" 的文章`);
      }
    } catch (err) {
      console.error('📖 ❌ 加载文章时出错:', err);
      setError('加载文章时发生错误');
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  // 加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded mb-4"></div>
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 rounded mb-6 w-3/4"></div>
              <div className="h-64 bg-gray-300 rounded mb-6"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error || !article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">文章加载失败</h1>
              <p className="text-gray-600 mb-6">{error}</p>
            </div>
            
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">调试信息:</h3>
                <p className="text-sm text-gray-600">请求的文章ID: {searchParams.get('id')}</p>
                <p className="text-sm text-gray-600">可用文章数量: {mockArticles.length}</p>
              </div>
              
              <div className="flex gap-4 justify-center">
                <button
                  onClick={() => router.push('/explore')}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  浏览所有文章
                </button>
                <button
                  onClick={() => router.push('/')}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  返回首页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 成功状态 - 显示文章内容
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* 面包屑导航 */}
      <nav className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <button 
              onClick={() => router.push('/')} 
              className="hover:text-blue-600 transition-colors"
            >
              首页
            </button>
            <span>/</span>
            <button 
              onClick={() => router.push('/explore')} 
              className="hover:text-blue-600 transition-colors"
            >
              文章
            </button>
            <span>/</span>
            <span className="text-gray-900 font-medium">{article.title}</span>
          </div>
        </div>
      </nav>

      {/* 文章内容 */}
      <article className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* 文章图片 */}
          {article.image && (
            <div className="aspect-video overflow-hidden">
              <img
                src={article.image}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=800x400`;
                }}
              />
            </div>
          )}

          <div className="p-8">
            {/* 文章头部 */}
            <header className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                  {article.category}
                </span>
                {article.tags?.map((tag) => (
                  <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                    #{tag}
                  </span>
                ))}
              </div>
              
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                {article.title}
              </h1>
              
              <div className="flex items-center justify-between text-sm text-gray-600 pb-6 border-b">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {typeof article.author === 'string' 
                          ? article.author.charAt(0).toUpperCase()
                          : article.author.name.charAt(0).toUpperCase()
                        }
                      </span>
                    </div>
                    <span className="font-medium">
                      {typeof article.author === 'string' ? article.author : article.author.name}
                    </span>
                  </div>
                  <span>{article.readTime} 分钟阅读</span>
                  <span>{article.views || 0} 次浏览</span>
                  <span>{new Date(article.publishedAt || article.createdAt).toLocaleDateString('zh-CN')}</span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <span>❤️</span>
                    <span>{article.likes}</span>
                  </span>
                  <span className="flex items-center gap-1">
                    <span>💬</span>
                    <span>{article.comments || 0}</span>
                  </span>
                </div>
              </div>
            </header>

            {/* 文章正文 */}
            <div className="prose prose-lg max-w-none mb-8">
              <div className="text-lg text-gray-700 leading-relaxed mb-8">
                {article.description}
              </div>
              
              <div className="space-y-6 text-gray-800 leading-relaxed">
                <p>{article.content}</p>
                
                <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">详细内容</h2>
                <p>
                  这里展示文章的完整内容。在实际应用中，这部分内容会从后端API获取，
                  包含完整的文章正文、图片、引用等富文本内容。
                </p>
                
                <blockquote className="border-l-4 border-blue-500 pl-6 py-2 bg-blue-50 rounded-r-lg">
                  <p className="italic text-blue-800">
                    "这是一个引用示例，展示文章内容的丰富性和多样性。"
                  </p>
                </blockquote>
                
                <p>
                  文章内容可以包含多个段落、列表、代码块等各种元素，
                  为读者提供丰富的阅读体验。
                </p>
              </div>
            </div>

            {/* 文章操作 */}
            <div className="border-t pt-6 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors">
                    <span>❤️</span>
                    <span>点赞 ({article.likes})</span>
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                    <span>💬</span>
                    <span>评论 ({article.comments || 0})</span>
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                    <span>📤</span>
                    <span>分享</span>
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 bg-yellow-50 text-yellow-600 rounded-lg hover:bg-yellow-100 transition-colors">
                    <span>🔖</span>
                    <span>收藏</span>
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  文章ID: {article.id}
                </div>
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="border-t pt-6">
              <div className="flex justify-between">
                <button
                  onClick={() => router.back()}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  ← 返回上一页
                </button>
                <div className="flex gap-3">
                  <button
                    onClick={() => router.push('/explore')}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    浏览更多文章
                  </button>
                  <button
                    onClick={() => router.push('/')}
                    className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    返回首页
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}
