'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';

export default function AccountSettingsPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nickname: '',
    bio: '',
    email: '<EMAIL>'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [hasChanges, setHasChanges] = useState(false);

  // 验证函数
  const validateNickname = (nickname: string) => {
    if (nickname.trim().length > 50) {
      return 'Nickname must be less than 50 characters';
    }
    return '';
  };

  const validateBio = (bio: string) => {
    if (bio.trim().length > 500) {
      return 'Bio must be less than 500 characters';
    }
    return '';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 标记有变更
    setHasChanges(true);

    // 实时验证
    let fieldError = '';
    if (name === 'nickname') {
      fieldError = validateNickname(value);
    } else if (name === 'bio') {
      fieldError = validateBio(value);
    }

    setErrors(prev => ({
      ...prev,
      [name]: fieldError
    }));

    // 如果之前保存成功，现在有变更则重置保存状态
    if (isSaved) {
      setIsSaved(false);
    }
  };

  const handleSaveProfile = async () => {
    // 验证所有字段
    const nicknameError = validateNickname(formData.nickname);
    const bioError = validateBio(formData.bio);

    const newErrors = {
      nickname: nicknameError,
      bio: bioError
    };

    setErrors(newErrors);

    // 如果有验证错误，不保存
    if (nicknameError || bioError) {
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsSaved(true);
      setHasChanges(false);

      // 3秒后自动恢复按钮状态
      setTimeout(() => setIsSaved(false), 3000);
    } catch (error) {
      console.error('Failed to save profile:', error);
      setErrors({ submit: 'Failed to save profile. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    // Handle logout logic
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-6">
        {/* Page Title */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
        </div>

        <div className="space-y-8">
          {/* Profile Section */}
          <section>
            <h2 className="text-lg font-bold text-gray-900 mb-4">Profile</h2>

            {/* Profile Photo */}
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 rounded-full overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-*************-2616b612b786?w=48&h=48&fit=crop&crop=face"
                  alt="Profile"
                  className="object-cover w-full h-full"
                />
              </div>
              <div>
                <h3 className="text-base font-semibold text-gray-900">Profile Photo</h3>
                <button className="text-blue-500 hover:text-blue-600 text-sm">
                  Change your profile photo
                </button>
              </div>
            </div>

            {/* Nickname */}
            <div className="mb-6">
              <label htmlFor="nickname" className="block text-sm font-medium text-gray-900 mb-2">
                Nickname
              </label>
              <input
                type="text"
                id="nickname"
                name="nickname"
                value={formData.nickname}
                onChange={handleInputChange}
                maxLength={50}
                placeholder="Enter your nickname"
                className={`w-full px-3 py-2 border rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.nickname ? 'border-red-300' : 'border-gray-200'
                }`}
              />
              {errors.nickname && (
                <p className="mt-1 text-sm text-red-600">{errors.nickname}</p>
              )}
            </div>

            {/* Bio */}
            <div className="mb-6">
              <label htmlFor="bio" className="block text-sm font-medium text-gray-900 mb-2">
                Bio
              </label>
              <textarea
                id="bio"
                name="bio"
                rows={4}
                value={formData.bio}
                onChange={handleInputChange}
                maxLength={500}
                placeholder="Tell us about yourself..."
                className={`w-full px-3 py-2 border rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                  errors.bio ? 'border-red-300' : 'border-gray-200'
                }`}
              />
              {errors.bio && (
                <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
              )}
            </div>

            {/* Error Message */}
            {errors.submit && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{errors.submit}</p>
                </div>
              </div>
            )}

            {/* Save Profile Button */}
            <div className="flex justify-between items-center">
              {hasChanges && !isSaved && (
                <p className="text-sm text-yellow-600">You have unsaved changes</p>
              )}
              <div className="ml-auto">
                <button
                  onClick={handleSaveProfile}
                  disabled={isLoading || !hasChanges}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    isSaved
                      ? 'bg-green-500 text-white'
                      : isLoading
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : !hasChanges
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700 transform hover:scale-105'
                  }`}
                >
                  {isSaved ? (
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>Saved!</span>
                    </div>
                  ) : isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </div>
          </section>

          {/* Account Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Account</h2>

            {/* Email */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Email</h3>
                <p className="text-blue-500">Linked to your email</p>
              </div>
              <span className="text-gray-900 font-medium">{formData.email}</span>
            </div>

            {/* Password */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Password</h3>
                <p className="text-blue-500">Change your password</p>
              </div>
              <button
                onClick={() => alert('Password change functionality would be implemented here')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Change
              </button>
            </div>
          </section>

          {/* Privacy Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Privacy</h2>

            {/* Content Visibility */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Content Visibility</h3>
                <p className="text-blue-500">Control who can see your content</p>
              </div>
              <button
                onClick={() => alert('Content visibility management would be implemented here')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Manage
              </button>
            </div>

            {/* Blocked Users */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Blocked Users</h3>
                <p className="text-blue-500">Manage your blocked users</p>
              </div>
              <button
                onClick={() => alert('Blocked users management would be implemented here')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                View
              </button>
            </div>
          </section>

          {/* Notifications Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Notifications</h2>

            {/* Notification Settings */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
                <p className="text-blue-500">Customize your notification preferences</p>
              </div>
              <button
                onClick={() => router.push('/notifications')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Edit
              </button>
            </div>
          </section>

          {/* Earnings Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Earnings</h2>

            {/* Earnings Overview */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Earnings Overview</h3>
                <p className="text-blue-500">View your earnings and payment details</p>
              </div>
              <button
                onClick={() => alert('Earnings overview would be implemented here')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                View
              </button>
            </div>
          </section>

          {/* Other Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Other</h2>

            {/* Log Out */}
            <div className="flex items-center justify-between py-4">
              <h3 className="text-lg font-semibold text-gray-900">Log Out</h3>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </button>
            </div>
          </section>
        </div>
      </main>
    </div>
  );
}
