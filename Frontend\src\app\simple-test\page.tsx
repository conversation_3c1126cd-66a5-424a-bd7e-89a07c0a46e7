'use client';

import { mockArticles } from '@/data/mockArticles';

export default function SimpleTest() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>最简单的文章测试</h1>
      
      <h2>可用文章列表:</h2>
      {mockArticles.map((article) => (
        <div key={article.id} style={{ border: '1px solid #ccc', margin: '10px 0', padding: '10px' }}>
          <h3>{article.title}</h3>
          <p>ID: {article.id}</p>
          <p>作者: {typeof article.author === 'string' ? article.author : article.author.name}</p>
          <a href={`/post/${article.id}`} style={{ color: 'blue', textDecoration: 'underline' }}>
            查看文章 →
          </a>
        </div>
      ))}
      
      <h2>直接链接测试:</h2>
      <ul>
        <li><a href="/post/1">文章1</a></li>
        <li><a href="/post/2">文章2</a></li>
        <li><a href="/post/3">文章3</a></li>
        <li><a href="/post/999">不存在的文章</a></li>
      </ul>
      
      <p><a href="/">返回首页</a></p>
    </div>
  );
}
