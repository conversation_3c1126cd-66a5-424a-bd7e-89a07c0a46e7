'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import ProtectedRoute from '@/components/ProtectedRoute';
import {
  FollowButton,
  FollowList,
  ShareButton,
  ActivityTimeline,
  MessageInterface,
  UserTags
} from '@/components/social';
import { useAuth } from '@/contexts/AuthContext';
import {
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  TagIcon
} from '@heroicons/react/24/outline';

export default function SocialPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'timeline' | 'followers' | 'following' | 'messages' | 'tags'>('timeline');
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // Mock data for demonstration
  const mockUsers = [
    { id: 1, username: 'john_doe', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face' },
    { id: 2, username: 'jane_smith', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face' },
    { id: 3, username: 'alex_wilson', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face' }
  ];

  const mockArticle = {
    id: 1,
    title: 'OneNews - Social Features Demo',
    description: 'Experience OneNews complete social features including follow, share, messaging and interest tags.'
  };

  const tabs = [
    { id: 'timeline', name: 'Activity Timeline', icon: ClockIcon },
    { id: 'followers', name: 'Followers', icon: UserGroupIcon },
    { id: 'following', name: 'Following', icon: UserGroupIcon },
    { id: 'messages', name: 'Messages', icon: ChatBubbleLeftRightIcon },
    { id: 'tags', name: 'Interest Tags', icon: TagIcon },
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Social Center</h1>
            <p className="mt-2 text-gray-600">Manage your social relationships and interactions</p>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`
                      flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {activeTab === 'timeline' && (
                <div>
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Activity Timeline</h2>
                    <p className="text-gray-600 text-sm mb-4">View the latest activities of users you follow</p>
                  </div>
                  <ActivityTimeline />
                </div>
              )}

              {activeTab === 'followers' && user && (
                <div>
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Followers</h2>
                    <p className="text-gray-600 text-sm mb-4">View users who follow you</p>
                  </div>
                  <FollowList userId={user.id} type="followers" />
                </div>
              )}

              {activeTab === 'following' && user && (
                <div>
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Following</h2>
                    <p className="text-gray-600 text-sm mb-4">View users you follow</p>
                  </div>
                  <FollowList userId={user.id} type="following" />
                </div>
              )}

              {activeTab === 'messages' && (
                <div>
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Messages</h2>
                    <p className="text-gray-600 text-sm mb-4">Have private conversations with other users</p>
                  </div>

                  {selectedUserId ? (
                    <MessageInterface
                      recipientId={selectedUserId}
                      recipientName={`User${selectedUserId}`}
                      onClose={() => setSelectedUserId(null)}
                    />
                  ) : (
                    <div className="bg-white rounded-lg border p-8 text-center">
                      <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Select Conversation</h3>
                      <p className="text-gray-600 mb-4">Select a user to start a private conversation</p>
                      <div className="space-y-2">
                        {mockUsers.map((mockUser) => (
                          <button
                            key={mockUser.id}
                            onClick={() => setSelectedUserId(mockUser.id)}
                            className="block w-full text-left px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                          >
                            Chat with {mockUser.username}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'tags' && (
                <div>
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Interest Tags</h2>
                    <p className="text-gray-600 text-sm mb-4">Manage your interest tags to get personalized recommendations</p>
                  </div>
                  <div className="bg-white rounded-lg border p-6">
                    <UserTags editable={true} />
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* User Profile Card */}
              {user && (
                <div className="bg-white rounded-lg border p-6">
                  <div className="text-center">
                    <div className="relative w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500">
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-white font-medium text-xl">
                          {user.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900">{user.username}</h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Follow Button Demo</span>
                    <FollowButton userId={2} username="Demo User" size="sm" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Share Feature Demo</span>
                    <ShareButton
                      articleId={mockArticle.id}
                      title={mockArticle.title}
                      description={mockArticle.description}
                      variant="icon"
                    />
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Followers</span>
                    <span className="text-sm font-medium text-gray-900">-</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Following</span>
                    <span className="text-sm font-medium text-gray-900">-</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Interest Tags</span>
                    <span className="text-sm font-medium text-gray-900">-</span>
                  </div>
                </div>
              </div>

              {/* Help */}
              <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
                <h3 className="text-lg font-medium text-blue-900 mb-2">Usage Tips</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• View latest activities of followed users in the activity timeline</li>
                  <li>• Use messaging feature to communicate with other users</li>
                  <li>• Set interest tags to get personalized recommendations</li>
                  <li>• Share interesting content to social platforms</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
