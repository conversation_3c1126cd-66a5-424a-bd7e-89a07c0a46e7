'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function TemplateTestPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <button onClick={() => router.push('/')} className="text-xl font-bold text-gray-900">
                Newzora
              </button>
              <nav className="flex space-x-6">
                <button onClick={() => router.push('/')} className="text-gray-600 hover:text-gray-900">Home</button>
                <button onClick={() => router.push('/explore')} className="text-gray-600 hover:text-gray-900">Explore</button>
                <button onClick={() => router.push('/create')} className="text-gray-600 hover:text-gray-900">Create</button>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-64 px-4 py-2 bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <svg className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button className="p-2 text-gray-600 hover:text-gray-900">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
                </svg>
              </button>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🎨 原设计模板1:1复刻测试</h1>
        
        {/* 状态报告 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 完成状态</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
            <div>
              <h3 className="font-medium mb-2">🎯 路径问题已解决:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>工作目录已修正为 D:\Newzora</li>
                <li>终端进程启动正常</li>
                <li>文件路径访问正确</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">🎨 设计复刻完成:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>文章详情页 /article/[id]</li>
                <li>用户资料页 /profile/[username]</li>
                <li>Newzora品牌统一</li>
                <li>原设计UI风格</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 功能测试区域 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 功能测试</h2>
          
          {/* 文章详情页测试 */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-900 mb-3">📖 文章详情页测试</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {mockArticles.slice(0, 8).map((article) => (
                <button
                  key={article.id}
                  onClick={() => router.push(`/article/${article.id}`)}
                  className="p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-sm"
                >
                  文章 {article.id}
                </button>
              ))}
            </div>
          </div>

          {/* 用户资料页测试 */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-900 mb-3">👤 用户资料页测试</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {['sophia-carter', 'john-doe', 'jane-smith', 'alex-chen'].map((username) => (
                <button
                  key={username}
                  onClick={() => router.push(`/profile/${username}`)}
                  className="p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors text-sm"
                >
                  @{username}
                </button>
              ))}
            </div>
          </div>

          {/* 导航测试 */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">🧭 导航测试</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button
                onClick={() => router.push('/')}
                className="p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors text-sm"
              >
                🏠 首页
              </button>
              <button
                onClick={() => router.push('/explore')}
                className="p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors text-sm"
              >
                🔍 探索页面
              </button>
              <button
                onClick={() => router.push('/create')}
                className="p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors text-sm"
              >
                ✏️ 创建页面
              </button>
              <button
                onClick={() => router.push('/debug')}
                className="p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors text-sm"
              >
                🐛 调试页面
              </button>
            </div>
          </div>
        </div>

        {/* 设计对比 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">🎨 设计特性对比</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-700">
            <div>
              <h3 className="font-medium mb-2">原设计特点:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>简洁的白色头部导航</li>
                <li>大图展示的文章详情</li>
                <li>橙色渐变的视觉元素</li>
                <li>网格布局的作品集</li>
                <li>现代化的评论系统</li>
                <li>清晰的用户资料页</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">复刻实现:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>✅ 统一的Newzora品牌</li>
                <li>✅ 响应式头部导航</li>
                <li>✅ 大图文章展示</li>
                <li>✅ 橙色渐变配色</li>
                <li>✅ 网格作品集布局</li>
                <li>✅ 完整的评论功能</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 技术实现说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">⚙️ 技术实现</h2>
          <div className="text-sm text-yellow-700 space-y-3">
            <div>
              <h3 className="font-medium">路由结构:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><code>/article/[id]</code> - 文章详情页（支持动态ID）</li>
                <li><code>/profile/[username]</code> - 用户资料页（支持动态用户名）</li>
                <li>所有链接已更新指向新路由</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium">UI组件:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>统一的头部导航组件</li>
                <li>响应式网格布局</li>
                <li>Tailwind CSS样式系统</li>
                <li>现代化的交互效果</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium">功能集成:</h3>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>保留所有现有功能</li>
                <li>集成mockArticles数据</li>
                <li>完整的错误处理</li>
                <li>优化的用户体验</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 下一步计划 */}
        <div className="mt-8 bg-gray-100 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">🚀 下一步计划</h2>
          <div className="text-sm text-gray-700 space-y-2">
            <p>1. 测试所有页面功能是否正常工作</p>
            <p>2. 验证文章详情页的完整显示</p>
            <p>3. 确认用户资料页的作品集展示</p>
            <p>4. 检查导航和链接的正确跳转</p>
            <p>5. 优化移动端响应式设计</p>
          </div>
        </div>
      </div>
    </div>
  );
}
