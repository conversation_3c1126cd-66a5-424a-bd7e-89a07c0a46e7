'use client';

import { useState } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';

const testAccounts = [
  { 
    email: '<EMAIL>', 
    password: 'Admin123!', 
    role: 'admin', 
    username: 'admin_test',
    description: '管理员账户 - 完整系统访问权限'
  },
  { 
    email: '<EMAIL>', 
    password: 'Moderator123!', 
    role: 'moderator', 
    username: 'moderator_test',
    description: '内容管理员账户 - 内容管理权限'
  },
  { 
    email: '<EMAIL>', 
    password: 'Editor123!', 
    role: 'editor', 
    username: 'editor_test',
    description: '编辑账户 - 内容编辑权限'
  },
  { 
    email: '<EMAIL>', 
    password: 'User123!', 
    role: 'user', 
    username: 'user_test1',
    description: '普通用户账户 1 - 基础用户权限'
  },
  { 
    email: '<EMAIL>', 
    password: 'User123!', 
    role: 'user', 
    username: 'user_test2',
    description: '普通用户账户 2 - 基础用户权限'
  },
  { 
    email: '<EMAIL>', 
    password: 'Unverified123!', 
    role: 'user', 
    username: 'unverified_test',
    description: '未验证邮箱账户 - 邮箱未验证状态'
  },
  { 
    email: '<EMAIL>', 
    password: 'Inactive123!', 
    role: 'user', 
    username: 'inactive_test',
    description: '非活跃账户 - 账户已停用状态'
  }
];

export default function TestAccountsPage() {
  const [testingAccount, setTestingAccount] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<{[key: string]: boolean}>({});
  const { signIn, user, isAuthenticated, signOut } = useSupabaseAuth();
  const router = useRouter();

  const testAccount = async (account: typeof testAccounts[0]) => {
    setTestingAccount(account.email);
    
    try {
      const result = await signIn(account.email, account.password);
      
      if (result.success) {
        setTestResults(prev => ({ ...prev, [account.email]: true }));
        console.log(`✅ Test successful for ${account.email}`);
        
        // 自动登出以便测试下一个账户
        setTimeout(async () => {
          await signOut();
          setTestingAccount(null);
        }, 2000);
      } else {
        setTestResults(prev => ({ ...prev, [account.email]: false }));
        console.log(`❌ Test failed for ${account.email}`);
        setTestingAccount(null);
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, [account.email]: false }));
      console.error(`❌ Test error for ${account.email}:`, error);
      setTestingAccount(null);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Newzora 测试账户</h1>
          <p className="text-gray-600 mb-8">
            以下是可用于测试的账户列表。点击"测试登录"按钮可以自动测试账户是否正常工作。
          </p>

          {/* 当前登录状态 */}
          {isAuthenticated && user && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-green-900 mb-2">当前登录状态</h3>
              <p className="text-green-700">
                已登录为: <strong>{user.email}</strong> ({user.role})
              </p>
              <button
                onClick={signOut}
                className="mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                登出
              </button>
            </div>
          )}

          {/* 测试账户列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {testAccounts.map((account) => (
              <div key={account.email} className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{account.username}</h3>
                    <p className="text-sm text-gray-500">{account.description}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    account.role === 'admin' ? 'bg-red-100 text-red-800' :
                    account.role === 'moderator' ? 'bg-orange-100 text-orange-800' :
                    account.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {account.role}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">邮箱:</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">{account.email}</code>
                      <button
                        onClick={() => copyToClipboard(account.email)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        复制
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">密码:</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">{account.password}</code>
                      <button
                        onClick={() => copyToClipboard(account.password)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        复制
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <button
                    onClick={() => testAccount(account)}
                    disabled={testingAccount === account.email}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {testingAccount === account.email ? '测试中...' : '测试登录'}
                  </button>

                  {testResults[account.email] !== undefined && (
                    <span className={`text-sm font-medium ${
                      testResults[account.email] ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {testResults[account.email] ? '✅ 测试成功' : '❌ 测试失败'}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 使用说明 */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">使用说明</h3>
            <ul className="text-blue-800 space-y-2">
              <li>• 点击"测试登录"按钮可以自动测试账户登录功能</li>
              <li>• 点击"复制"按钮可以复制邮箱或密码到剪贴板</li>
              <li>• 测试成功后会自动登出，以便测试其他账户</li>
              <li>• 所有账户都支持邮箱和用户名两种登录方式</li>
              <li>• 这些是开发环境的测试账户，生产环境请使用真实账户</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
