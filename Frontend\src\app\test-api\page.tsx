'use client';

import { useState } from 'react';
import Header from '@/components/Header';

export default function TestAPIPage() {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testAPIs = async () => {
    setLoading(true);
    setResults([]);
    
    addResult('🧪 Starting API tests...');

    // 1. Test Health Check
    try {
      addResult('1️⃣ Testing Health Check...');
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      addResult(`✅ Health Check: ${data.status}`);
    } catch (error) {
      addResult(`❌ Health Check failed: ${error}`);
    }

    // 2. Test Articles List
    try {
      addResult('2️⃣ Testing Articles List...');
      const response = await fetch('http://localhost:5000/api/articles');
      const data = await response.json();
      addResult(`✅ Articles List: ${data.data?.length || 0} articles`);
    } catch (error) {
      addResult(`❌ Articles List failed: ${error}`);
    }

    // 3. Test Single Article
    try {
      addResult('3️⃣ Testing Single Article...');
      const response = await fetch('http://localhost:5000/api/articles/1');
      const data = await response.json();
      addResult(`✅ Single Article: ${data.title || 'No title'}`);
    } catch (error) {
      addResult(`❌ Single Article failed: ${error}`);
    }

    // 4. Test Login (Enhanced Auth)
    try {
      addResult('4️⃣ Testing Login (Enhanced Auth)...');
      const response = await fetch('http://localhost:5000/api/auth-enhanced/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'password123'
        })
      });
      const data = await response.json();
      if (data.success) {
        addResult(`✅ Login successful: ${data.user?.username || 'Unknown user'}`);
        addResult(`🎫 Token received: ${data.token ? 'Yes' : 'No'}`);
      } else {
        addResult(`❌ Login failed: ${data.message}`);
      }
    } catch (error) {
      addResult(`❌ Login failed: ${error}`);
    }

    setLoading(false);
    addResult('🏁 API tests completed!');
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">API Connection Test</h1>
          <p className="text-gray-600 mb-4">
            This page tests the connection between frontend and backend APIs.
          </p>
          
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Test Credentials:</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> password123</p>
              <p><strong>Username:</strong> testuser</p>
            </div>
          </div>
          
          <button
            onClick={testAPIs}
            disabled={loading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {loading ? 'Testing APIs...' : 'Run API Tests'}
          </button>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Results:</h2>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {results.length === 0 ? (
              <p className="text-gray-500">Click "Run API Tests" to start testing...</p>
            ) : (
              results.map((result, index) => (
                <div key={index} className="text-sm font-mono bg-white p-2 rounded border">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
