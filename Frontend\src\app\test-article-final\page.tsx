'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function TestArticleFinal() {
  const router = useRouter();

  const testArticleNavigation = (articleId: string) => {
    console.log(`🧪 Testing navigation to article: ${articleId}`);
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">文章详情页面最终测试</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 问题已修复</h2>
          <div className="space-y-2 text-gray-700">
            <p><strong>✅ 路由统一</strong>: ArticleCard现在跳转到 /article/[id]</p>
            <p><strong>✅ 逻辑简化</strong>: 移除了复杂的ID分割逻辑</p>
            <p><strong>✅ 直接匹配</strong>: 直接使用article.id进行查找</p>
            <p><strong>✅ 调试信息</strong>: 添加了详细的控制台日志</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 测试所有文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testArticleNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                  <span>{article.readTime} min read</span>
                </div>
                <div className="mt-2 text-center">
                  <span className="text-sm text-blue-600">点击测试导航 →</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">📋 测试步骤</h2>
          <div className="space-y-3 text-gray-700">
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">1</span>
              <div>
                <p className="font-medium">点击上方任意文章卡片</p>
                <p className="text-sm text-gray-600">应该正常跳转到文章详情页面</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">2</span>
              <div>
                <p className="font-medium">检查控制台日志</p>
                <p className="text-sm text-gray-600">应该看到"✅ Found article"消息</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">3</span>
              <div>
                <p className="font-medium">验证文章内容显示</p>
                <p className="text-sm text-gray-600">标题、作者、内容等应该正确显示</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">4</span>
              <div>
                <p className="font-medium">测试首页和探索页面</p>
                <p className="text-sm text-gray-600">从其他页面点击文章也应该正常工作</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">🎯 预期结果</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ 不再显示"文章未找到"</strong></p>
            <p><strong>✅ 所有文章都能正常显示内容</strong></p>
            <p><strong>✅ 路由导航完全正常</strong></p>
            <p><strong>✅ 控制台显示成功日志</strong></p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🏠 测试首页
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              🔍 测试探索页面
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
