'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function TestArticleFix() {
  const router = useRouter();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const testArticleNavigation = () => {
    addResult('🧪 Testing Article Navigation...');
    
    // Test 1: Check mockArticles structure
    addResult(`📋 Found ${mockArticles.length} mock articles`);
    
    mockArticles.forEach((article, index) => {
      addResult(`📄 Article ${index + 1}: ID="${article.id}" (${typeof article.id}), Title="${article.title}"`);
    });

    // Test 2: Test navigation to first article
    const firstArticle = mockArticles[0];
    if (firstArticle) {
      addResult(`🔗 Testing navigation to article: ${firstArticle.id}`);
      addResult(`📍 URL will be: /article/${firstArticle.id}`);
    }

    addResult('✅ Test completed - check console for any errors');
  };

  const navigateToArticle = (articleId: string) => {
    addResult(`🚀 Navigating to article: ${articleId}`);
    router.push(`/article/${articleId}`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Article Navigation Test</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          <div className="flex gap-4 mb-4">
            <button
              onClick={testArticleNavigation}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Run Navigation Test
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Clear Results
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.slice(0, 6).map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => navigateToArticle(article.id)}
              >
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: {article.id} ({typeof article.id})
                </p>
                <p className="text-sm text-gray-500 line-clamp-2">
                  {article.description}
                </p>
                <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                  <span>{article.author}</span>
                  <span>{article.readTime} min read</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">No test results yet. Click "Run Navigation Test" to start.</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Back to Home
          </button>
        </div>
      </div>
    </div>
  );
}
