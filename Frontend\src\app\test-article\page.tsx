'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function TestArticle() {
  const router = useRouter();
  const [selectedArticle, setSelectedArticle] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  useEffect(() => {
    addResult('🔄 TestArticle component mounted');
    addResult(`📋 Found ${mockArticles.length} mock articles`);
    mockArticles.forEach((article, index) => {
      addResult(`📄 Article ${index + 1}: ID="${article.id}" Title="${article.title}"`);
    });
  }, []);

  const testDirectNavigation = (articleId: string) => {
    addResult(`🚀 Testing direct navigation to article: ${articleId}`);
    
    // 查找文章
    const article = mockArticles.find(a => a.id === articleId);
    if (article) {
      addResult(`✅ Article found: "${article.title}"`);
      addResult(`🔗 Navigating to /article/${articleId}`);
      router.push(`/article/${articleId}`);
    } else {
      addResult(`❌ Article not found with ID: ${articleId}`);
    }
  };

  const testInlineDisplay = (articleId: string) => {
    addResult(`🧪 Testing inline display for article: ${articleId}`);
    
    const article = mockArticles.find(a => a.id === articleId);
    if (article) {
      addResult(`✅ Article found: "${article.title}"`);
      setSelectedArticle(article);
    } else {
      addResult(`❌ Article not found with ID: ${articleId}`);
      setSelectedArticle(null);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Article Navigation Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-y-4">
              <button
                onClick={clearResults}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Clear Test Results
              </button>
              
              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">Quick Navigation Tests</h3>
                <div className="space-y-2">
                  {mockArticles.slice(0, 3).map((article) => (
                    <div key={article.id} className="flex gap-2">
                      <button
                        onClick={() => testDirectNavigation(article.id)}
                        className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                      >
                        Navigate to "{article.title.substring(0, 20)}..."
                      </button>
                      <button
                        onClick={() => testInlineDisplay(article.id)}
                        className="px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                      >
                        Show
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No test results yet.</p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-xs font-mono text-gray-800">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Article Cards */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">All Test Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex gap-2">
                  <button
                    onClick={() => testDirectNavigation(article.id)}
                    className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    Navigate
                  </button>
                  <button
                    onClick={() => testInlineDisplay(article.id)}
                    className="px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    Show
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Inline Article Display */}
        {selectedArticle && (
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Selected Article Preview</h2>
            <div className="border border-gray-200 rounded-lg p-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{selectedArticle.title}</h3>
              <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                <span>ID: {selectedArticle.id}</span>
                <span>Author: {typeof selectedArticle.author === 'string' ? selectedArticle.author : selectedArticle.author.name}</span>
                <span>Category: {selectedArticle.category}</span>
                <span>Read Time: {selectedArticle.readTime} min</span>
              </div>
              <p className="text-gray-700 mb-4">{selectedArticle.description}</p>
              <div className="flex gap-2">
                <button
                  onClick={() => testDirectNavigation(selectedArticle.id)}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Go to Full Article
                </button>
                <button
                  onClick={() => setSelectedArticle(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Close Preview
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">Debug Information</h2>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>Total Mock Articles:</strong> {mockArticles.length}</p>
            <p><strong>Article IDs:</strong> {mockArticles.map(a => a.id).join(', ')}</p>
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
            <p><strong>Selected Article:</strong> {selectedArticle ? selectedArticle.title : 'None'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
