'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function TestFixFinal() {
  const router = useRouter();

  const testNavigation = (articleId: string) => {
    console.log(`Testing navigation to article: ${articleId}`);
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Final Article Fix Test</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
          <div className="space-y-2 text-gray-700">
            <p>1. Click any article card below to test navigation</p>
            <p>2. Check that you reach the article detail page without "Article Not Found"</p>
            <p>3. Verify that the article content displays correctly</p>
            <p>4. Test the back navigation buttons</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Quick Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockArticles.slice(0, 3).map((article) => (
              <button
                key={article.id}
                onClick={() => testNavigation(article.id)}
                className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-left"
              >
                <h3 className="font-semibold text-gray-900 mb-2">
                  {article.title.substring(0, 50)}...
                </h3>
                <p className="text-sm text-gray-600 mb-2">ID: {article.id}</p>
                <p className="text-sm text-blue-600">Click to test →</p>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">All Test Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                  <span>{article.readTime} min read</span>
                </div>
                <div className="mt-2 text-center">
                  <span className="text-sm text-blue-600">Click to test navigation →</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">Fix Summary</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ Created new article detail page:</strong> /article-detail/[id]</p>
            <p><strong>✅ Updated ArticleCard component:</strong> Now routes to /article-detail/[id]</p>
            <p><strong>✅ Updated explore page links:</strong> Now routes to /article-detail/[id]</p>
            <p><strong>✅ Simplified article loading:</strong> Direct mockArticles lookup with fallback</p>
            <p><strong>✅ Removed complex API dependencies:</strong> Works without backend server</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Test Home Page
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Test Explore Page
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
