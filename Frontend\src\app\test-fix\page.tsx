'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function TestFix() {
  const router = useRouter();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
    console.log(message);
  };

  const testArticleData = () => {
    addResult('🧪 Testing Article Data Structure...');
    
    addResult(`📋 Total articles: ${mockArticles.length}`);
    
    mockArticles.forEach((article, index) => {
      addResult(`📄 Article ${index + 1}:`);
      addResult(`   ID: "${article.id}" (${typeof article.id})`);
      addResult(`   Title: "${article.title}"`);
      addResult(`   Author: ${typeof article.author === 'string' ? article.author : article.author.name}`);
      addResult(`   Category: "${article.category}"`);
      addResult(`   Has Content: ${!!article.content}`);
    });

    addResult('✅ Data structure test completed');
  };

  const testNavigation = (articleId: string) => {
    addResult(`🚀 Testing navigation to article: ${articleId}`);
    addResult(`📍 URL: /article/${articleId}`);
    
    // 模拟检查文章是否存在
    const article = mockArticles.find(a => a.id === articleId);
    if (article) {
      addResult(`✅ Article found: "${article.title}"`);
      addResult(`🔗 Navigating...`);
      router.push(`/article/${articleId}`);
    } else {
      addResult(`❌ Article not found with ID: ${articleId}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Article Fix Test Page</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-y-4">
              <button
                onClick={testArticleData}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Test Article Data Structure
              </button>
              <button
                onClick={clearResults}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Clear Results
              </button>
              <div className="pt-4 border-t">
                <button
                  onClick={() => router.push('/')}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  Go to Home Page
                </button>
                <button
                  onClick={() => router.push('/explore')}
                  className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 mt-2"
                >
                  Go to Explore Page
                </button>
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No test results yet.</p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono text-gray-800">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Article Cards for Testing */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Test Articles (Click to Navigate)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.slice(0, 6).map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code> ({typeof article.id})
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                  <span>{article.readTime} min read</span>
                </div>
                <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {article.category}
                  </span>
                  <span>👁️ {article.views} • ❤️ {article.likes}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">Debug Information</h2>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>Total Mock Articles:</strong> {mockArticles.length}</p>
            <p><strong>Article IDs:</strong> {mockArticles.map(a => a.id).join(', ')}</p>
            <p><strong>Article ID Types:</strong> {mockArticles.map(a => typeof a.id).join(', ')}</p>
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
