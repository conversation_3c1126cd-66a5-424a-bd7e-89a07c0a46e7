'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';

export default function TestHydration() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testArticleNavigation = (articleId: string) => {
    addResult(`Testing navigation to article: ${articleId}`);
    
    // 查找文章
    const article = mockArticles.find(a => a.id === articleId);
    if (article) {
      addResult(`✅ Article found: "${article.title}"`);
      addResult(`🔗 Navigating to /article/${articleId}`);
      router.push(`/article/${articleId}`);
    } else {
      addResult(`❌ Article not found with ID: ${articleId}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Hydration Fix Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Navigation Test */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Navigation Test</h2>
            <div className="space-y-4">
              <div className="flex gap-2">
                <button
                  onClick={() => router.push('/')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Go to Home
                </button>
                <button
                  onClick={() => router.push('/explore')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  Go to Explore
                </button>
              </div>
              <button
                onClick={clearResults}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Clear Test Results
              </button>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No test results yet.</p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono text-gray-800">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Article Test Cards */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Test Articles (Click to Navigate)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.slice(0, 6).map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testArticleNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                  <span>{article.readTime} min read</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Status Info */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">Hydration Status</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ Component Mounted:</strong> {mounted ? 'Yes' : 'No'}</p>
            <p><strong>✅ Client-side Rendering:</strong> {typeof window !== 'undefined' ? 'Active' : 'Inactive'}</p>
            <p><strong>✅ Mock Articles Loaded:</strong> {mockArticles.length} articles</p>
            <p><strong>✅ Navigation Ready:</strong> Router available</p>
          </div>
        </div>
      </div>
    </div>
  );
}
