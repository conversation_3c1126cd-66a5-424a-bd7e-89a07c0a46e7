'use client';

import { useState } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

export default function TestLoginPage() {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { signIn, user, isAuthenticated } = useSupabaseAuth();

  const testLogin = async () => {
    setIsLoading(true);
    setTestResult('Testing login...');

    try {
      console.log('🧪 Starting login test...');
      
      const result = await signIn('<EMAIL>', 'admin123');
      
      if (result.success) {
        setTestResult('✅ Login test successful!');
        console.log('✅ Login test passed');
      } else {
        setTestResult(`❌ Login test failed: ${result.error}`);
        console.log('❌ Login test failed:', result.error);
      }
    } catch (error) {
      setTestResult(`❌ Login test error: ${error}`);
      console.error('❌ Login test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Login Test Page
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Current Status</h3>
              <p className="mt-2 text-sm text-gray-600">
                Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}
              </p>
              {user && (
                <div className="mt-2 text-sm text-gray-600">
                  <p>User: {user.email}</p>
                  <p>Role: {user.role}</p>
                </div>
              )}
            </div>

            <div>
              <button
                onClick={testLogin}
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isLoading ? 'Testing...' : 'Test Login'}
              </button>
            </div>

            {testResult && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-900">{testResult}</p>
              </div>
            )}

            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900">Test Credentials</h3>
              <div className="mt-2 text-sm text-gray-600">
                <p>Email: <EMAIL></p>
                <p>Password: admin123</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
