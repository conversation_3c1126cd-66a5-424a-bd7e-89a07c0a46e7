'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function TestRouteFix() {
  const router = useRouter();

  const testDirectNavigation = (articleId: string) => {
    console.log(`🧪 Testing direct navigation to article: ${articleId}`);
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Article Route Fix Test</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🔧 Fix Applied</h2>
          <div className="space-y-2 text-gray-700">
            <p>✅ Simplified /article/[id]/page.tsx to use only local mockArticles data</p>
            <p>✅ Removed API dependency that was causing failures</p>
            <p>✅ Added fallback article creation for any ID</p>
            <p>✅ Updated ArticleCard and explore page to use /article/[id] route</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 Quick Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockArticles.slice(0, 3).map((article) => (
              <button
                key={article.id}
                onClick={() => testDirectNavigation(article.id)}
                className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-left"
              >
                <h3 className="font-semibold text-gray-900 mb-2">
                  {article.title.substring(0, 40)}...
                </h3>
                <p className="text-sm text-gray-600 mb-2">ID: {article.id}</p>
                <p className="text-sm text-blue-600">Click to test →</p>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">📋 Test Instructions</h2>
          <div className="space-y-3 text-gray-700">
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">1</span>
              <div>
                <p className="font-medium">Test Home Page Navigation</p>
                <p className="text-sm text-gray-600">Go to home page and click any article card</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">2</span>
              <div>
                <p className="font-medium">Test Explore Page Navigation</p>
                <p className="text-sm text-gray-600">Go to explore page and click any article</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">3</span>
              <div>
                <p className="font-medium">Test Direct URL Access</p>
                <p className="text-sm text-gray-600">Try accessing /article/1, /article/2, etc. directly</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">4</span>
              <div>
                <p className="font-medium">Test Non-existent Articles</p>
                <p className="text-sm text-gray-600">Try /article/999 - should show default article</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">🎯 Available Test Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testDirectNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="text-center">
                  <span className="text-sm text-blue-600">Click to test navigation →</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🏠 Test Home Page
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              🔍 Test Explore Page
            </button>
            <button
              onClick={() => testDirectNavigation('999')}
              className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
            >
              🧪 Test Non-existent Article
            </button>
          </div>
        </div>

        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ Expected Results</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ All article clicks should work:</strong> No more "Article Not Found" errors</p>
            <p><strong>✅ Content should display:</strong> Either real article content or generated default content</p>
            <p><strong>✅ Navigation should be smooth:</strong> No loading errors or blank pages</p>
            <p><strong>✅ Console should show success:</strong> Check browser console for "✅ Found article" messages</p>
          </div>
        </div>
      </div>
    </div>
  );
}
