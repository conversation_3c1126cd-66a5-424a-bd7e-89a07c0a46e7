'use client';

import { useRouter } from 'next/navigation';
import { mockArticles } from '@/data/mockArticles';
import Header from '@/components/Header';

export default function TestSimpleFix() {
  const router = useRouter();

  const testArticleNavigation = (articleId: string) => {
    console.log(`🧪 测试跳转到文章: ${articleId}`);
    router.push(`/article/${articleId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">最简单的解决方案测试</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-800">✅ 最终解决方案</h2>
          <div className="space-y-2 text-gray-700">
            <p><strong>✅ 恢复简单ID</strong>: 使用 "1", "2", "3" 等简单字符串ID</p>
            <p><strong>✅ 统一路由</strong>: 所有链接都指向 /article/[id]</p>
            <p><strong>✅ 移除复杂逻辑</strong>: 不再有slug、split等复杂处理</p>
            <p><strong>✅ 直接匹配</strong>: article.id === articleId 简单比较</p>
            <p><strong>✅ 删除冗余</strong>: 只保留一个文章详情页面</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">🧪 测试所有文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockArticles.map((article) => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => testArticleNavigation(article.id)}
              >
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 overflow-hidden">
                  {article.image && (
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=400x200`;
                      }}
                    />
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  ID: <code className="bg-gray-100 px-1 rounded">{article.id}</code>
                </p>
                <p className="text-sm text-gray-500 line-clamp-2 mb-3">
                  {article.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{typeof article.author === 'string' ? article.author : article.author.name}</span>
                  <span>{article.readTime} min read</span>
                </div>
                <div className="mt-2 text-center">
                  <span className="text-sm text-blue-600">点击测试导航 →</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">🔧 问题根源分析</h2>
          <div className="text-sm text-blue-700 space-y-2">
            <p><strong>原问题</strong>: 路由不匹配 + ID处理过于复杂</p>
            <p><strong>之前</strong>: ArticleCard → /article-detail/ + explore → /article/ + 复杂slug逻辑</p>
            <p><strong>现在</strong>: 所有链接 → /article/ + 简单ID匹配</p>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-green-800">🎯 预期结果</h2>
          <div className="text-sm text-green-700 space-y-2">
            <p><strong>✅ 100%成功率</strong>: 所有文章链接都能正常工作</p>
            <p><strong>✅ 无"未找到"</strong>: 不再显示文章未找到错误</p>
            <p><strong>✅ 简单可靠</strong>: 逻辑简单，不易出错</p>
            <p><strong>✅ 统一体验</strong>: 首页、探索页面行为一致</p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🏠 测试首页
            </button>
            <button
              onClick={() => router.push('/explore')}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              🔍 测试探索页面
            </button>
            <button
              onClick={() => testArticleNavigation('1')}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              🧪 测试文章1
            </button>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">💡 为什么这次一定能成功</h2>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>1. 消除了所有复杂性</strong>: 不再有slug、split、复杂ID处理</p>
            <p><strong>2. 统一了路由路径</strong>: 所有地方都使用 /article/[id]</p>
            <p><strong>3. 简化了数据查找</strong>: 直接 article.id === articleId</p>
            <p><strong>4. 移除了冗余代码</strong>: 只保留一个文章详情页面</p>
            <p><strong>5. 使用了最简单的ID</strong>: "1", "2", "3" 等字符串</p>
          </div>
        </div>
      </div>
    </div>
  );
}
