export default function TestSimplePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🎉 Newzora 测试页面
        </h1>
        <p className="text-gray-600 mb-6">
          如果您能看到这个页面，说明 Next.js 应用正在正常运行！
        </p>
        <div className="space-y-4">
          <a 
            href="/" 
            className="block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回首页
          </a>
          <a 
            href="/template-test" 
            className="block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            模板测试页面
          </a>
          <a 
            href="/article/1" 
            className="block px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            文章详情页
          </a>
          <a 
            href="/profile/sophia-carter" 
            className="block px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            用户资料页
          </a>
        </div>
        <div className="mt-8 p-4 bg-white rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-2">系统状态</h2>
          <div className="text-sm text-gray-600 space-y-1">
            <div>✅ Next.js 应用运行正常</div>
            <div>✅ Tailwind CSS 样式加载</div>
            <div>✅ 路由系统工作正常</div>
          </div>
        </div>
      </div>
    </div>
  );
}
