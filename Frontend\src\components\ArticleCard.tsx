'use client';

// import Image from 'next/image'; // Temporarily disabled due to config issues
import { useRouter } from 'next/navigation';
import { Article } from '@/types';

interface ArticleCardProps {
  article: Article;
}

const getCategoryColor = (category: string) => {
  const colors = {
    'Technology': 'text-blue-600',
    'Travel': 'text-purple-600',
    'Lifestyle': 'text-green-600',
    'Food': 'text-orange-600',
    'Trending': 'text-red-600',
  };
  return colors[category as keyof typeof colors] || 'text-gray-600';
};

export default function ArticleCard({ article }: ArticleCardProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/article/${article.id}`);
  };

  return (
    <div
      onClick={handleClick}
      className="group cursor-pointer py-6 px-4 hover:bg-white/80 transition-all duration-300 rounded-2xl hover:shadow-lg hover:shadow-gray-200/50 transform hover:scale-[1.02] border border-transparent hover:border-gray-100"
    >
      <div className="flex gap-8">
        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Category */}
          <div className="mb-3">
            <span className={`text-sm font-medium ${getCategoryColor(article.category)}`}>
              {article.category}
            </span>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors leading-tight line-clamp-2">
            {article.title}
          </h2>

          {/* Description */}
          <p className="text-gray-600 text-base leading-relaxed line-clamp-3">
            {article.description}
          </p>
        </div>

        {/* Image */}
        <div className="w-64 h-40 flex-shrink-0">
          <div className="relative w-full h-full rounded-2xl overflow-hidden bg-gray-100">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=256x160`;
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
