'use client';

// import Image from 'next/image'; // Temporarily disabled due to config issues
import { useRouter } from 'next/navigation';
import { Article } from '@/types';

interface ArticleCardProps {
  article: Article;
}

const getCategoryColor = (category: string) => {
  const colors = {
    'Technology': 'text-blue-600',
    'Travel': 'text-purple-600',
    'Lifestyle': 'text-green-600',
    'Food': 'text-orange-600',
    'Trending': 'text-red-600',
  };
  return colors[category as keyof typeof colors] || 'text-gray-600';
};

export default function ArticleCard({ article }: ArticleCardProps) {
  const router = useRouter();

  const handleClick = () => {
    console.log('🔗 ArticleCard: 跳转到文章', article.id);
    router.push(`/read?id=${article.id}`);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Recently';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Recently';
    }
  };

  return (
    <div
      onClick={handleClick}
      className="group cursor-pointer py-6 hover:bg-gray-50 transition-colors"
    >
      <div className="flex gap-6">
        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Category */}
          <div className="mb-2">
            <span className={`text-sm font-medium ${getCategoryColor(article.category)}`}>
              {article.category}
            </span>
          </div>

          {/* Title */}
          <h2 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors leading-tight line-clamp-2">
            {article.title}
          </h2>

          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 mb-3">
            {article.description}
          </p>

          {/* Author and Meta */}
          <div className="flex items-center text-sm text-gray-500">
            <span className="font-medium text-gray-700">
              {typeof article.author === 'object' && article.author?.username
                ? article.author.username
                : typeof article.author === 'string'
                ? article.author
                : 'Anonymous'}
            </span>
            <span className="mx-2">•</span>
            <span>{formatDate(article.publishedAt)}</span>
            <span className="mx-2">•</span>
            <span>{article.readTime || '5'} min read</span>
          </div>
        </div>

        {/* Image */}
        <div className="w-48 h-32 flex-shrink-0">
          <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-200">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=192x128`;
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
