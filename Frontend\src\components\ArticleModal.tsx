'use client';

import { useState, useEffect } from 'react';
import { Article } from '@/types';
import { mockArticles } from '@/data/mockArticles';

interface ArticleModalProps {
  articleId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function ArticleModal({ articleId, isOpen, onClose }: ArticleModalProps) {
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && articleId) {
      setLoading(true);
      const foundArticle = mockArticles.find(a => a.id === articleId);
      setArticle(foundArticle || null);
      setLoading(false);
    }
  }, [isOpen, articleId]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      ></div>
      
      {/* 模态框内容 */}
      <div className="relative min-h-screen flex items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* 关闭按钮 */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 bg-white rounded-full shadow-lg hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {loading ? (
            <div className="p-8">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-300 rounded mb-4"></div>
                <div className="h-64 bg-gray-300 rounded mb-6"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ) : article ? (
            <div className="p-8">
              {/* 文章图片 */}
              {article.image && (
                <div className="mb-6">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
              )}

              {/* 文章头部 */}
              <header className="mb-6">
                <div className="flex items-center gap-2 mb-4">
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    {article.category}
                  </span>
                  {article.tags?.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      #{tag}
                    </span>
                  ))}
                </div>
                
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  {article.title}
                </h1>
                
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-4">
                    <span>作者：{typeof article.author === 'string' ? article.author : article.author.name}</span>
                    <span>{article.readTime} 分钟阅读</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span>❤️ {article.likes}</span>
                    <span>💬 {article.comments}</span>
                  </div>
                </div>
              </header>

              {/* 文章内容 */}
              <div className="prose max-w-none mb-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  {article.description}
                </p>
                
                <div className="space-y-4">
                  <p>{article.content}</p>
                  
                  <h3>更多内容</h3>
                  <p>
                    这里显示文章的完整内容。在弹窗中查看文章是一种很好的用户体验，
                    用户可以快速浏览文章内容而不需要离开当前页面。
                  </p>
                </div>
              </div>

              {/* 文章操作 */}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button className="flex items-center gap-2 px-3 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 text-sm">
                      ❤️ 点赞 ({article.likes})
                    </button>
                    <button className="flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 text-sm">
                      💬 评论 ({article.comments})
                    </button>
                    <button className="flex items-center gap-2 px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 text-sm">
                      📤 分享
                    </button>
                  </div>
                  <div className="text-sm text-gray-500">
                    ID: {article.id}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center">
              <h2 className="text-xl font-bold text-gray-900 mb-4">文章未找到</h2>
              <p className="text-gray-600 mb-4">无法找到指定的文章</p>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                关闭
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
