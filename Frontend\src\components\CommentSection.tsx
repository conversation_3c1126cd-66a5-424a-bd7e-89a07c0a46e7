'use client';

import { useState, useEffect } from 'react';
// import Image from 'next/image'; // Temporarily disabled due to config issues

interface Comment {
  id: number;
  author: {
    name: string;
    avatar: string;
    username?: string;
  };
  content: string;
  createdAt: string;
  likes: number;
  isLiked?: boolean;
}

interface CommentSectionProps {
  articleId: number;
  initialComments?: Comment[];
  currentUser?: {
    name: string;
    avatar: string;
  };
}

export default function CommentSection({
  articleId,
  initialComments = [],
  currentUser = {
    name: "You",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face"
  }
}: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchComments();
  }, [articleId]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:5000/api/comments/article/${articleId}`);
      if (response.ok) {
        const data = await response.json();
        // Transform backend comment format to frontend format
        const transformedComments = data.map((comment: any) => ({
          id: comment.id,
          author: {
            name: typeof comment.author === 'object' ? comment.author.name : (comment.author || 'Anonymous'),
            avatar: typeof comment.author === 'object' ? comment.author.avatar : (comment.avatar || "https://ui-avatars.com/api/?name=Anonymous&background=6366f1&color=fff&size=48")
          },
          content: comment.content,
          createdAt: formatDate(comment.createdAt),
          likes: comment.likes || 0,
          isLiked: false
        }));
        setComments(transformedComments);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      // Fallback to demo comments if API fails
      setComments([
        {
          id: 1,
          author: {
            name: "Liam Harper",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face"
          },
          content: "This is a fascinating exploration of modern art. The visual journey you've created is truly captivating, and I appreciate the insights you've shared. It's inspired me to look at art in a new light.",
          createdAt: "2d ago",
          likes: 12,
          isLiked: false
        },
        {
          id: 2,
          author: {
            name: "Ava Bennett",
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face"
          },
          content: "I've always been curious about modern art but found it challenging to understand. Your video has demystified it for me, and I now have a greater appreciation for the creativity and thought behind these pieces. Thank you for making art accessible!",
          createdAt: "1d ago",
          likes: 8,
          isLiked: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1d ago';
    if (diffDays < 7) return `${diffDays}d ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)}w ago`;
    return `${Math.ceil(diffDays / 30)}m ago`;
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    setIsSubmitting(true);

    try {
      const response = await fetch('http://localhost:5000/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: articleId,
          author: currentUser.name,
          content: newComment.trim(),
        }),
      });

      if (response.ok) {
        const newCommentData = await response.json();
        const transformedComment: Comment = {
          id: newCommentData.id,
          author: {
            name: newCommentData.author,
            avatar: currentUser.avatar
          },
          content: newCommentData.content,
          createdAt: "Just now",
          likes: 0,
          isLiked: false
        };

        setComments(prev => [transformedComment, ...prev]);
        setNewComment('');
      }
    } catch (error) {
      console.error('Error posting comment:', error);
      // Fallback to local state update
      const comment: Comment = {
        id: Date.now(),
        author: currentUser,
        content: newComment.trim(),
        createdAt: "Just now",
        likes: 0,
        isLiked: false
      };

      setComments(prev => [comment, ...prev]);
      setNewComment('');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: number) => {
    try {
      const response = await fetch(`http://localhost:5000/api/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const responseData = await response.json();
        const data = responseData.success ? responseData.data : responseData;
        setComments(prev => prev.map(comment =>
          comment.id === commentId
            ? {
                ...comment,
                isLiked: !comment.isLiked,
                likes: data.likes
              }
            : comment
        ));
      }
    } catch (error) {
      console.error('Error liking comment:', error);
      // Fallback to local state update
      setComments(prev => prev.map(comment =>
        comment.id === commentId
          ? {
              ...comment,
              isLiked: !comment.isLiked,
              likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
            }
          : comment
      ));
    }
  };

  return (
    <section className="mt-12">
      <h2 className="text-2xl font-bold text-gray-900 mb-8">Comments</h2>

      {/* Comment Input */}
      <div className="mb-8">
        <form onSubmit={handleSubmitComment} className="flex space-x-4">
          <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
            <Image
              src={currentUser.avatar}
              alt={currentUser.name}
              width={48}
              height={48}
              className="object-cover w-full h-full"
            />
          </div>
          <div className="flex-1">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Add a comment..."
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 focus:bg-white transition-all duration-200 hover:bg-gray-100/50 resize-none"
              rows={3}
            />
            <div className="flex justify-end mt-3">
              <button
                type="submit"
                disabled={!newComment.trim() || isSubmitting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isSubmitting ? 'Posting...' : 'Post Comment'}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Comments List */}
      <div className="space-y-8">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading comments...</p>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <p>No comments yet. Be the first to share your thoughts!</p>
          </div>
        ) : (
          comments.map((comment) => (
          <div key={comment.id} className="flex space-x-4">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
              <img
                src={comment.author.avatar}
                alt={comment.author.name}
                className="object-cover w-full h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.name)}&background=6366f1&color=fff&size=48`;
                }}
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4 className="font-semibold text-gray-900">{comment.author.name}</h4>
                <span className="text-gray-500 text-sm">{comment.createdAt}</span>
              </div>
              <p className="text-gray-700 leading-relaxed mb-3">
                {comment.content}
              </p>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleLikeComment(comment.id)}
                  className={`flex items-center space-x-1 text-sm transition-colors duration-200 ${
                    comment.isLiked 
                      ? 'text-red-500' 
                      : 'text-gray-500 hover:text-red-500'
                  }`}
                >
                  <svg 
                    className="w-4 h-4" 
                    fill={comment.isLiked ? 'currentColor' : 'none'} 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
                    />
                  </svg>
                  <span>{comment.likes}</span>
                </button>
                <button className="text-sm text-gray-500 hover:text-blue-500 transition-colors duration-200">
                  Reply
                </button>
              </div>
            </div>
          </div>
          ))
        )}
      </div>
    </section>
  );
}
