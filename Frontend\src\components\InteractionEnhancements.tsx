'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from '@/contexts/InternationalizationContext';

// 加载动画组件
export function LoadingSpinner({ size = 'medium', className = '' }: { size?: 'small' | 'medium' | 'large', className?: string }) {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-6 h-6',
    large: 'w-8 h-8'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`} />
  );
}

// 成功/错误提示组件
export function Toast({ 
  message, 
  type = 'info', 
  duration = 3000, 
  onClose 
}: { 
  message: string, 
  type?: 'success' | 'error' | 'warning' | 'info', 
  duration?: number, 
  onClose?: () => void 
}) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!isVisible) return null;

  const typeStyles = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-black',
    info: 'bg-blue-500 text-white'
  };

  const icons = {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ'
  };

  return (
    <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 ${typeStyles[type]} animate-slide-in-right`}>
      <span className="font-bold">{icons[type]}</span>
      <span>{message}</span>
      <button 
        onClick={() => {
          setIsVisible(false);
          onClose?.();
        }}
        className="ml-2 hover:opacity-75"
      >
        ×
      </button>
    </div>
  );
}

// 确认对话框组件
export function ConfirmDialog({ 
  isOpen, 
  title, 
  message, 
  confirmText = 'Confirm', 
  cancelText = 'Cancel', 
  onConfirm, 
  onCancel,
  type = 'default'
}: {
  isOpen: boolean,
  title: string,
  message: string,
  confirmText?: string,
  cancelText?: string,
  onConfirm: () => void,
  onCancel: () => void,
  type?: 'default' | 'danger'
}) {
  const { t } = useTranslation();

  if (!isOpen) return null;

  const confirmButtonClass = type === 'danger' 
    ? 'bg-red-600 hover:bg-red-700 text-white'
    : 'bg-blue-600 hover:bg-blue-700 text-white';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 animate-scale-in">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-6">{message}</p>
          
          <div className="flex space-x-3 justify-end">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors"
            >
              {cancelText}
            </button>
            <button
              onClick={onConfirm}
              className={`px-4 py-2 rounded-lg transition-colors ${confirmButtonClass}`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// 工具提示组件
export function Tooltip({ 
  children, 
  content, 
  position = 'top' 
}: { 
  children: React.ReactNode, 
  content: string, 
  position?: 'top' | 'bottom' | 'left' | 'right' 
}) {
  const [isVisible, setIsVisible] = useState(false);

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div className={`absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded whitespace-nowrap ${positionClasses[position]}`}>
          {content}
          <div className="absolute w-2 h-2 bg-gray-900 transform rotate-45" 
               style={{
                 [position === 'top' ? 'top' : position === 'bottom' ? 'bottom' : position === 'left' ? 'left' : 'right']: '100%',
                 [position === 'top' || position === 'bottom' ? 'left' : 'top']: '50%',
                 transform: 'translate(-50%, -50%) rotate(45deg)'
               }} />
        </div>
      )}
    </div>
  );
}

// 进度条组件
export function ProgressBar({ 
  progress, 
  className = '', 
  showPercentage = true,
  color = 'blue'
}: { 
  progress: number, 
  className?: string, 
  showPercentage?: boolean,
  color?: 'blue' | 'green' | 'red' | 'yellow'
}) {
  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600'
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-1">
        {showPercentage && (
          <span className="text-sm font-medium text-gray-700">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${colorClasses[color]}`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
}

// 骨架屏组件
export function SkeletonLoader({ 
  lines = 3, 
  className = '' 
}: { 
  lines?: number, 
  className?: string 
}) {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index}
          className={`bg-gray-200 rounded h-4 mb-2 ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
}

// 无限滚动组件
export function InfiniteScroll({ 
  children, 
  hasMore, 
  loadMore, 
  loading = false,
  threshold = 100
}: {
  children: React.ReactNode,
  hasMore: boolean,
  loadMore: () => void,
  loading?: boolean,
  threshold?: number
}) {
  useEffect(() => {
    const handleScroll = () => {
      if (loading || !hasMore) return;

      const scrollTop = document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        loadMore();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loading, hasMore, loadMore, threshold]);

  return (
    <div>
      {children}
      {loading && (
        <div className="flex justify-center py-4">
          <LoadingSpinner />
        </div>
      )}
      {!hasMore && (
        <div className="text-center py-4 text-gray-500">
          No more content to load
        </div>
      )}
    </div>
  );
}

// 搜索高亮组件
export function HighlightText({ 
  text, 
  highlight, 
  className = '' 
}: { 
  text: string, 
  highlight: string, 
  className?: string 
}) {
  if (!highlight) return <span className={className}>{text}</span>;

  const parts = text.split(new RegExp(`(${highlight})`, 'gi'));
  
  return (
    <span className={className}>
      {parts.map((part, index) => 
        part.toLowerCase() === highlight.toLowerCase() ? (
          <mark key={index} className="bg-yellow-200 px-1 rounded">
            {part}
          </mark>
        ) : (
          part
        )
      )}
    </span>
  );
}

// 响应式图片组件
export function ResponsiveImage({ 
  src, 
  alt, 
  className = '',
  fallback = '/images/placeholder.jpg'
}: {
  src: string,
  alt: string,
  className?: string,
  fallback?: string
}) {
  const [imageSrc, setImageSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setImageSrc(fallback);
    setIsLoading(false);
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      <img
        src={imageSrc}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        className={`w-full h-full object-cover rounded transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
      />
    </div>
  );
}

// 复制到剪贴板组件
export function CopyToClipboard({ 
  text, 
  children, 
  onCopy 
}: {
  text: string,
  children: React.ReactNode,
  onCopy?: () => void
}) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      onCopy?.();
      
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className="relative inline-flex items-center"
      title={copied ? 'Copied!' : 'Copy to clipboard'}
    >
      {children}
      {copied && (
        <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded">
          Copied!
        </span>
      )}
    </button>
  );
}

// 自动保存指示器
export function AutoSaveIndicator({ 
  status 
}: { 
  status: 'saving' | 'saved' | 'error' | 'idle' 
}) {
  const { t } = useTranslation();

  const statusConfig = {
    saving: { text: 'Saving...', color: 'text-blue-600', icon: '⏳' },
    saved: { text: 'Saved', color: 'text-green-600', icon: '✓' },
    error: { text: 'Save failed', color: 'text-red-600', icon: '✗' },
    idle: { text: '', color: 'text-gray-400', icon: '' }
  };

  const config = statusConfig[status];

  if (status === 'idle') return null;

  return (
    <div className={`flex items-center space-x-1 text-sm ${config.color}`}>
      <span>{config.icon}</span>
      <span>{config.text}</span>
    </div>
  );
}

// 键盘快捷键提示
export function KeyboardShortcut({ 
  keys, 
  description 
}: { 
  keys: string[], 
  description: string 
}) {
  return (
    <div className="flex items-center justify-between py-1">
      <span className="text-sm text-gray-600">{description}</span>
      <div className="flex space-x-1">
        {keys.map((key, index) => (
          <kbd 
            key={index}
            className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded"
          >
            {key}
          </kbd>
        ))}
      </div>
    </div>
  );
}
