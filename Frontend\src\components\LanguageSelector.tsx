'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useInternationalization } from '@/contexts/InternationalizationContext';

interface LanguageSelectorProps {
  className?: string;
  showFlag?: boolean;
  showNativeName?: boolean;
  compact?: boolean;
}

export default function LanguageSelector({
  className = '',
  showFlag = true,
  showNativeName = true,
  compact = false
}: LanguageSelectorProps) {
  const {
    currentLanguage,
    languages,
    changeLanguage,
    isLoading,
    t
  } = useInternationalization();

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 关闭下拉菜单当点击外部时
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentLang = languages.find(lang => lang.code === currentLanguage);

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await changeLanguage(languageCode);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-8 w-20 bg-gray-200 rounded"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 当前语言按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg border border-gray-300 
          bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 
          transition-colors duration-200
          ${compact ? 'text-sm' : 'text-base'}
        `}
        aria-label={t('settings.language')}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        {showFlag && currentLang && (
          <span className="text-lg" role="img" aria-label={currentLang.name}>
            {currentLang.flag}
          </span>
        )}
        
        <span className="font-medium">
          {currentLang ? (
            showNativeName ? currentLang.nativeName : currentLang.name
          ) : (
            currentLanguage.toUpperCase()
          )}
        </span>
        
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 语言下拉菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
          <div className="py-1" role="listbox">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`
                  w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 
                  focus:outline-none focus:bg-gray-50 transition-colors duration-150
                  ${currentLanguage === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}
                `}
                role="option"
                aria-selected={currentLanguage === language.code}
              >
                {showFlag && (
                  <span className="text-lg" role="img" aria-label={language.name}>
                    {language.flag}
                  </span>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">
                    {language.nativeName}
                  </div>
                  {!compact && (
                    <div className="text-sm text-gray-500 truncate">
                      {language.name}
                    </div>
                  )}
                </div>
                
                {language.rtl && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    RTL
                  </span>
                )}
                
                {currentLanguage === language.code && (
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
          
          {/* 语言统计信息 */}
          <div className="border-t border-gray-200 px-4 py-2 bg-gray-50">
            <div className="text-xs text-gray-500">
              {t('settings.language')}: {languages.length} {t('common.available')}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 紧凑版语言选择器
export function CompactLanguageSelector({ className = '' }: { className?: string }) {
  return (
    <LanguageSelector
      className={className}
      showFlag={true}
      showNativeName={false}
      compact={true}
    />
  );
}

// 仅图标版语言选择器
export function IconLanguageSelector({ className = '' }: { className?: string }) {
  const { currentLanguage, languages, changeLanguage, isOpen, setIsOpen } = useInternationalization();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentLang = languages.find(lang => lang.code === currentLanguage);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        aria-label="Select Language"
        title={currentLang?.nativeName || 'Language'}
      >
        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
        </svg>
      </button>

      {isDropdownOpen && (
        <div className="absolute top-full right-0 mt-1 w-48 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
          <div className="py-1">
            {languages.slice(0, 8).map((language) => (
              <button
                key={language.code}
                onClick={() => {
                  changeLanguage(language.code);
                  setIsDropdownOpen(false);
                }}
                className={`
                  w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 
                  ${currentLanguage === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}
                `}
              >
                <span className="text-base">{language.flag}</span>
                <span className="text-sm">{language.nativeName}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
