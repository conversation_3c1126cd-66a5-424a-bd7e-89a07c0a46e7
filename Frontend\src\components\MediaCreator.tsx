'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useTranslation } from '@/contexts/InternationalizationContext';

interface MediaCreatorProps {
  onMediaCreated?: (media: any) => void;
  allowedTypes?: ('video' | 'audio' | 'image')[];
  maxSize?: number;
  className?: string;
}

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function MediaCreator({
  onMediaCreated,
  allowedTypes = ['video', 'audio', 'image'],
  maxSize = 500 * 1024 * 1024, // 500MB
  className = ''
}: MediaCreatorProps) {
  const { t } = useTranslation();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [mediaInfo, setMediaInfo] = useState<any>(null);
  const [processingOptions, setProcessingOptions] = useState({
    quality: 'medium',
    format: 'auto',
    compress: false,
    extractAudio: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 获取接受的文件类型
  const getAcceptedTypes = () => {
    const typeMap = {
      video: 'video/*',
      audio: 'audio/*',
      image: 'image/*'
    };
    return allowedTypes.map(type => typeMap[type]).join(',');
  };

  // 处理文件选择
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件大小
    if (file.size > maxSize) {
      alert(t('media.error.fileTooLarge', { maxSize: Math.round(maxSize / 1024 / 1024) }));
      return;
    }

    // 验证文件类型
    const fileType = file.type.split('/')[0];
    if (!allowedTypes.includes(fileType as any)) {
      alert(t('media.error.unsupportedType'));
      return;
    }

    setSelectedFile(file);
    
    // 创建预览
    const url = URL.createObjectURL(file);
    setPreview(url);

    // 获取媒体信息
    if (fileType === 'video' && videoRef.current) {
      videoRef.current.src = url;
      videoRef.current.onloadedmetadata = () => {
        setMediaInfo({
          duration: videoRef.current?.duration,
          width: videoRef.current?.videoWidth,
          height: videoRef.current?.videoHeight
        });
      };
    } else if (fileType === 'audio' && audioRef.current) {
      audioRef.current.src = url;
      audioRef.current.onloadedmetadata = () => {
        setMediaInfo({
          duration: audioRef.current?.duration
        });
      };
    }
  }, [allowedTypes, maxSize, t]);

  // 上传文件
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('title', selectedFile.name);
      formData.append('description', '');
      formData.append('tags', JSON.stringify([]));

      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      // 处理响应
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            setMediaInfo(response.data.media);
            onMediaCreated?.(response.data.media);
            alert(t('media.success.uploaded'));
          } else {
            alert(t('media.error.uploadFailed', { error: response.message }));
          }
        } else {
          alert(t('media.error.uploadFailed', { error: 'Server error' }));
        }
        setIsUploading(false);
      });

      xhr.addEventListener('error', () => {
        alert(t('media.error.uploadFailed', { error: 'Network error' }));
        setIsUploading(false);
      });

      xhr.open('POST', `${API_BASE}/media/upload`);
      
      // 添加认证头
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      xhr.send(formData);

    } catch (error) {
      console.error('Upload error:', error);
      alert(t('media.error.uploadFailed', { error: 'Unknown error' }));
      setIsUploading(false);
    }
  };

  // 处理媒体文件
  const handleProcessMedia = async (action: string) => {
    if (!mediaInfo?.id) return;

    try {
      let endpoint = '';
      let body = {};

      switch (action) {
        case 'convert':
          endpoint = `/media/convert/${mediaInfo.id}`;
          body = {
            outputFormat: processingOptions.format,
            quality: processingOptions.quality
          };
          break;
        case 'compress':
          endpoint = `/media/compress/${mediaInfo.id}`;
          body = {
            compressionLevel: processingOptions.quality
          };
          break;
        case 'extractAudio':
          endpoint = `/media/extract-audio/${mediaInfo.id}`;
          body = {
            format: 'mp3',
            quality: processingOptions.quality
          };
          break;
        default:
          return;
      }

      const response = await fetch(`${API_BASE}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(body)
      });

      const data = await response.json();

      if (data.success) {
        alert(t('media.success.processed'));
        // 可以在这里更新UI或触发回调
      } else {
        alert(t('media.error.processingFailed', { error: data.message }));
      }

    } catch (error) {
      console.error('Processing error:', error);
      alert(t('media.error.processingFailed', { error: 'Unknown error' }));
    }
  };

  // 清除选择
  const handleClear = () => {
    setSelectedFile(null);
    setPreview(null);
    setMediaInfo(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (preview) {
      URL.revokeObjectURL(preview);
    }
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return (
          <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        );
      case 'audio':
        return (
          <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
          </svg>
        );
      case 'image':
        return (
          <svg className="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('media.creator.title')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('media.creator.description')}
        </p>
      </div>

      {/* 文件选择区域 */}
      {!selectedFile && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
          <div className="flex justify-center mb-4">
            {allowedTypes.map(type => (
              <div key={type} className="mx-2">
                {getFileTypeIcon(type)}
              </div>
            ))}
          </div>
          
          <p className="text-gray-600 mb-4">
            {t('media.creator.dragDrop')}
          </p>
          
          <button
            onClick={() => fileInputRef.current?.click()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('media.creator.selectFile')}
          </button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept={getAcceptedTypes()}
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <p className="text-xs text-gray-500 mt-2">
            {t('media.creator.maxSize', { size: Math.round(maxSize / 1024 / 1024) })}
          </p>
        </div>
      )}

      {/* 文件预览和信息 */}
      {selectedFile && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileTypeIcon(selectedFile.type.split('/')[0])}
              <div>
                <p className="font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 媒体预览 */}
          {preview && (
            <div className="bg-gray-100 rounded-lg p-4">
              {selectedFile.type.startsWith('video/') && (
                <video
                  ref={videoRef}
                  src={preview}
                  controls
                  className="w-full max-h-64 rounded"
                />
              )}
              
              {selectedFile.type.startsWith('audio/') && (
                <audio
                  ref={audioRef}
                  src={preview}
                  controls
                  className="w-full"
                />
              )}
              
              {selectedFile.type.startsWith('image/') && (
                <img
                  src={preview}
                  alt="Preview"
                  className="w-full max-h-64 object-contain rounded"
                />
              )}
            </div>
          )}

          {/* 媒体信息 */}
          {mediaInfo && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">{t('media.info.title')}</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                {mediaInfo.duration && (
                  <div>
                    <span className="text-gray-600">{t('media.info.duration')}:</span>
                    <span className="ml-2 font-medium">{Math.round(mediaInfo.duration)}s</span>
                  </div>
                )}
                {mediaInfo.width && mediaInfo.height && (
                  <div>
                    <span className="text-gray-600">{t('media.info.dimensions')}:</span>
                    <span className="ml-2 font-medium">{mediaInfo.width}x{mediaInfo.height}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 上传进度 */}
          {isUploading && (
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-900">
                  {t('media.uploading')}
                </span>
                <span className="text-sm text-blue-700">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          {!isUploading && (
            <div className="flex space-x-3">
              <button
                onClick={handleUpload}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                {t('media.upload')}
              </button>
              
              {mediaInfo?.id && (
                <>
                  <button
                    onClick={() => handleProcessMedia('compress')}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    {t('media.compress')}
                  </button>
                  
                  {selectedFile.type.startsWith('video/') && (
                    <button
                      onClick={() => handleProcessMedia('extractAudio')}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      {t('media.extractAudio')}
                    </button>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      )}

      {/* 隐藏的媒体元素用于获取信息 */}
      <video ref={videoRef} className="hidden" />
      <audio ref={audioRef} className="hidden" />
    </div>
  );
}
