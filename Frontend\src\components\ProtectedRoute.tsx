'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: 'user' | 'admin' | 'moderator';
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  requireRole,
  redirectTo = '/auth/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If authentication is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    // If specific role is required
    if (requireRole && user) {
      const hasRequiredRole = checkUserRole(user.role, requireRole);
      if (!hasRequiredRole) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [isAuthenticated, user, requireAuth, requireRole, router, redirectTo]);



  // If authentication is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // If specific role is required but user doesn't have it, don't render children
  if (requireRole && user && !checkUserRole(user.role, requireRole)) {
    return null;
  }

  return <>{children}</>;
}

// Helper function to check if user has required role
function checkUserRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'user': 1,
    'moderator': 2,
    'admin': 3
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

// Component for routes that should only be accessible to non-authenticated users
export function GuestRoute({ children, redirectTo = '/' }: { children: React.ReactNode; redirectTo?: string }) {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, router, redirectTo]);



  if (isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}


