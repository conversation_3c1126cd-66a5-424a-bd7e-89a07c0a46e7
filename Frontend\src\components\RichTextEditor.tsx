'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { apiService } from '@/lib/api';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  maxLength?: number;
  enableImageUpload?: boolean;
  enablePreview?: boolean;
}

interface ToolbarButton {
  icon: string;
  title: string;
  command: string;
  value?: string;
}

const toolbarButtons: ToolbarButton[] = [
  { icon: '𝐁', title: 'Bold', command: 'bold' },
  { icon: '𝐼', title: 'Italic', command: 'italic' },
  { icon: '𝐔', title: 'Underline', command: 'underline' },
  { icon: '𝐒', title: 'Strikethrough', command: 'strikeThrough' },
  { icon: '|', title: 'Separator', command: '' },
  { icon: 'H1', title: 'Heading 1', command: 'formatBlock', value: 'h1' },
  { icon: 'H2', title: 'Heading 2', command: 'formatBlock', value: 'h2' },
  { icon: 'H3', title: 'Heading 3', command: 'formatBlock', value: 'h3' },
  { icon: '¶', title: 'Paragraph', command: 'formatBlock', value: 'p' },
  { icon: '|', title: 'Separator', command: '' },
  { icon: '≡', title: 'Unordered List', command: 'insertUnorderedList' },
  { icon: '≣', title: 'Ordered List', command: 'insertOrderedList' },
  { icon: '⇥', title: 'Indent', command: 'indent' },
  { icon: '⇤', title: 'Outdent', command: 'outdent' },
  { icon: '|', title: 'Separator', command: '' },
  { icon: '🔗', title: 'Insert Link', command: 'createLink' },
  { icon: '🖼️', title: 'Insert Image', command: 'insertImage' },
  { icon: '|', title: 'Separator', command: '' },
  { icon: '↶', title: 'Undo', command: 'undo' },
  { icon: '↷', title: 'Redo', command: 'redo' }
];

export default function RichTextEditor({
  value,
  onChange,
  placeholder = 'Start writing...',
  className = '',
  maxLength,
  enableImageUpload = true,
  enablePreview = true
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isPreview, setIsPreview] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [uploading, setUploading] = useState(false);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
      updateWordCount();
    }
  }, [value]);

  const updateWordCount = useCallback(() => {
    if (editorRef.current) {
      const text = editorRef.current.textContent || '';
      const words = text.trim().split(/\s+/).filter(word => word.length > 0);
      setWordCount(words.length);
    }
  }, []);

  const handleInput = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
      updateWordCount();
    }
  }, [onChange, updateWordCount]);

  const executeCommand = useCallback((command: string, value?: string) => {
    if (command === 'createLink') {
      const url = prompt('Enter the URL:');
      if (url) {
        document.execCommand(command, false, url);
      }
    } else if (command === 'insertImage') {
      if (enableImageUpload) {
        fileInputRef.current?.click();
      } else {
        const url = prompt('Enter the image URL:');
        if (url) {
          document.execCommand(command, false, url);
        }
      }
    } else if (command) {
      document.execCommand(command, false, value);
    }
    
    // Focus back to editor
    editorRef.current?.focus();
    handleInput();
  }, [enableImageUpload, handleInput]);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB');
      return;
    }

    try {
      setUploading(true);
      const response = await apiService.uploadFile(file, 'image');
      
      if (response.success && response.data?.url) {
        // Insert image into editor
        const img = `<img src="${response.data.url}" alt="${file.name}" style="max-width: 100%; height: auto;" />`;
        document.execCommand('insertHTML', false, img);
        handleInput();
      } else {
        alert('Failed to upload image');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      alert('Failed to upload image');
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'b':
          event.preventDefault();
          executeCommand('bold');
          break;
        case 'i':
          event.preventDefault();
          executeCommand('italic');
          break;
        case 'u':
          event.preventDefault();
          executeCommand('underline');
          break;
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            executeCommand('redo');
          } else {
            executeCommand('undo');
          }
          break;
      }
    }

    // Check max length
    if (maxLength && editorRef.current) {
      const text = editorRef.current.textContent || '';
      if (text.length >= maxLength && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
        event.preventDefault();
      }
    }
  }, [executeCommand, maxLength]);

  const togglePreview = () => {
    setIsPreview(!isPreview);
  };

  const getPreviewContent = () => {
    return { __html: value };
  };

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap items-center gap-1">
        {toolbarButtons.map((button, index) => {
          if (button.command === '') {
            return (
              <div key={index} className="w-px h-6 bg-gray-300 mx-1"></div>
            );
          }

          return (
            <button
              key={index}
              type="button"
              onClick={() => executeCommand(button.command, button.value)}
              className="p-2 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded transition-colors"
              title={button.title}
              disabled={uploading}
            >
              {button.icon}
            </button>
          );
        })}

        {/* Preview Toggle */}
        {enablePreview && (
          <>
            <div className="w-px h-6 bg-gray-300 mx-1"></div>
            <button
              type="button"
              onClick={togglePreview}
              className={`p-2 text-sm font-medium rounded transition-colors ${
                isPreview 
                  ? 'bg-indigo-100 text-indigo-700' 
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
              title={isPreview ? 'Edit Mode' : 'Preview Mode'}
            >
              {isPreview ? '✏️' : '👁️'}
            </button>
          </>
        )}

        {/* Upload Status */}
        {uploading && (
          <div className="ml-auto flex items-center text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600 mr-2"></div>
            Uploading...
          </div>
        )}
      </div>

      {/* Editor Content */}
      <div className="relative">
        {isPreview ? (
          <div 
            className="p-4 min-h-[200px] prose prose-sm max-w-none"
            dangerouslySetInnerHTML={getPreviewContent()}
          />
        ) : (
          <div
            ref={editorRef}
            contentEditable
            onInput={handleInput}
            onKeyDown={handleKeyDown}
            className="p-4 min-h-[200px] focus:outline-none"
            style={{ minHeight: '200px' }}
            data-placeholder={placeholder}
            suppressContentEditableWarning={true}
          />
        )}

        {/* Placeholder */}
        {!isPreview && !value && (
          <div className="absolute top-4 left-4 text-gray-400 pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2 flex justify-between items-center text-sm text-gray-600">
        <div>
          {wordCount} words
          {maxLength && (
            <span className="ml-2">
              ({editorRef.current?.textContent?.length || 0}/{maxLength} characters)
            </span>
          )}
        </div>
        <div className="text-xs text-gray-500">
          Use Ctrl+B for bold, Ctrl+I for italic, Ctrl+U for underline
        </div>
      </div>

      {/* Hidden file input for image upload */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  );
}
