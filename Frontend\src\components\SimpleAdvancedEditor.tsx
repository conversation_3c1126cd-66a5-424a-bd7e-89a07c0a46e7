'use client';

import React, { useCallback, useRef, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import { useDropzone } from 'react-dropzone';
import {
  BoldIcon,
  ItalicIcon,
  StrikethroughIcon,
  LinkIcon,
  PhotoIcon,
  TableCellsIcon,
  ListBulletIcon,
  NumberedListIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  EyeIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

interface SimpleAdvancedEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  showWordCount?: boolean;
  maxWords?: number;
  autoSave?: boolean;
  autoSaveInterval?: number;
}

const SimpleAdvancedEditor: React.FC<SimpleAdvancedEditorProps> = ({
  content = '',
  onChange,
  onSave,
  placeholder = 'Start writing...',
  height = 500,
  disabled = false,
  showWordCount = true,
  maxWords,
  autoSave = true,
  autoSaveInterval = 30000
}) => {
  const [isPreview, setIsPreview] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 配置编辑器
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg shadow-sm my-4',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-gray-300 w-full my-4',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border border-gray-300',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 bg-gray-50 font-semibold p-2',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 p-2',
        },
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const newContent = editor.getHTML();
      const text = editor.getText();
      const words = text.trim().split(/\s+/).filter(word => word.length > 0);
      setWordCount(words.length);
      
      onChange?.(newContent);
    },
    editable: !disabled,
  });

  // 自动保存功能
  React.useEffect(() => {
    if (!autoSave || !editor || !onSave) return;

    const interval = setInterval(() => {
      const content = editor.getHTML();
      if (content && content !== '<p></p>') {
        setIsSaving(true);
        onSave(content);
        setLastSaved(new Date());
        setTimeout(() => setIsSaving(false), 1000);
      }
    }, autoSaveInterval);

    return () => clearInterval(interval);
  }, [editor, onSave, autoSave, autoSaveInterval]);

  // 文件拖拽上传
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!editor) return;

    for (const file of acceptedFiles) {
      if (file.type.startsWith('image/')) {
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await fetch('/api/media/upload', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            },
            body: formData,
          });

          if (response.ok) {
            const result = await response.json();
            editor.chain().focus().setImage({ src: result.data.url }).run();
          }
        } catch (error) {
          console.error('Image upload failed:', error);
        }
      }
    }
  }, [editor]);

  const { getRootProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp']
    },
    noClick: true,
  });

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }> = ({ onClick, isActive, disabled, children, title }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`p-2 rounded-md transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-700'
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      {children}
    </button>
  );

  // Insert link
  const insertLink = () => {
    const url = window.prompt('Enter link URL:');
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  };

  // 插入图片
  const insertImage = () => {
    fileInputRef.current?.click();
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !editor) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        editor.chain().focus().setImage({ src: result.data.url }).run();
      }
    } catch (error) {
      console.error('Image upload failed:', error);
    }
  };

  // 插入表格
  const insertTable = () => {
    editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  };

  if (!editor) {
    return <div className="animate-pulse bg-gray-200 h-96 rounded-lg"></div>;
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
      {/* 工具栏 */}
      <div className="border-b border-gray-300 bg-gray-50 p-2">
        <div className="flex flex-wrap gap-1">
          {/* 文本格式化 */}
          <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Bold"
            >
              <BoldIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="Italic"
            >
              <ItalicIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleStrike().run()}
              isActive={editor.isActive('strike')}
              title="Strikethrough"
            >
              <StrikethroughIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              isActive={editor.isActive('code')}
              title="Inline Code"
            >
              <CodeBracketIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* 标题 */}
          <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
            <select
              onChange={(e) => {
                const level = parseInt(e.target.value);
                if (level === 0) {
                  editor.chain().focus().setParagraph().run();
                } else {
                  editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run();
                }
              }}
              className="px-2 py-1 text-sm border border-gray-300 rounded"
            >
              <option value="0">Paragraph</option>
              <option value="1">Heading 1</option>
              <option value="2">Heading 2</option>
              <option value="3">Heading 3</option>
              <option value="4">Heading 4</option>
              <option value="5">Heading 5</option>
              <option value="6">Heading 6</option>
            </select>
          </div>

          {/* 列表和引用 */}
          <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              title="Bullet List"
            >
              <ListBulletIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              title="Numbered List"
            >
              <NumberedListIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              isActive={editor.isActive('blockquote')}
              title="Quote"
            >
              <ChatBubbleLeftRightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Media and Links */}
          <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
            <ToolbarButton onClick={insertLink} title="Insert Link">
              <LinkIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton onClick={insertImage} title="Insert Image">
              <PhotoIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton onClick={insertTable} title="Insert Table">
              <TableCellsIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Undo/Redo */}
          <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              title="Undo"
            >
              <ArrowUturnLeftIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              title="Redo"
            >
              <ArrowUturnRightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Preview Mode */}
          <div className="flex gap-1">
            <ToolbarButton
              onClick={() => setIsPreview(!isPreview)}
              isActive={isPreview}
              title={isPreview ? "Edit Mode" : "Preview Mode"}
            >
              {isPreview ? <DocumentTextIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* 编辑器内容区域 */}
      <div
        {...getRootProps()}
        className={`relative ${isDragActive ? 'bg-blue-50 border-blue-300' : ''}`}
        style={{ minHeight: height }}
      >
        {isDragActive && (
          <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center z-10">
            <div className="text-blue-600 text-lg font-medium">
              Drop images here to upload
            </div>
          </div>
        )}

        {isPreview ? (
          <div
            className="p-4 prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: editor.getHTML() }}
          />
        ) : (
          <EditorContent
            editor={editor}
            className="p-4 focus:outline-none"
            style={{ minHeight: height - 60 }}
          />
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t border-gray-300 bg-gray-50 px-4 py-2 flex justify-between items-center text-sm text-gray-600">
        <div className="flex items-center gap-4">
          {showWordCount && (
            <span className={maxWords && wordCount > maxWords ? 'text-red-600' : ''}>
              Words: {wordCount}{maxWords && ` / ${maxWords}`}
            </span>
          )}
          {isSaving && <span className="text-blue-600">Saving...</span>}
          {lastSaved && !isSaving && (
            <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {onSave && (
            <button
              onClick={() => onSave(editor.getHTML())}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  );
};

export default SimpleAdvancedEditor;
