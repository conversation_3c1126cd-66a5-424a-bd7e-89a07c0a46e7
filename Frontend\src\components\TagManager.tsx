'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color: string;
  usageCount: number;
  interestLevel?: 'low' | 'medium' | 'high';
  addedAt?: string;
}

interface TagManagerProps {
  userId?: number;
  mode?: 'manage' | 'display';
  maxTags?: number;
  onTagsChange?: (tags: Tag[]) => void;
}

export default function TagManager({
  userId,
  mode = 'display',
  maxTags = 10,
  onTagsChange
}: TagManagerProps) {
  const { user, token } = useAuth();
  const [userTags, setUserTags] = useState<Tag[]>([]);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);

  const targetUserId = userId || user?.id;
  const canManage = mode === 'manage' && user && user.id === targetUserId;

  useEffect(() => {
    if (targetUserId) {
      fetchUserTags();
    }
    if (canManage) {
      fetchAvailableTags();
    }
  }, [targetUserId, canManage]);

  const fetchUserTags = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/tags/user/${targetUserId}`, {
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUserTags(data.data.tags);
        onTagsChange?.(data.data.tags);
      }
    } catch (error) {
      console.error('Error fetching user tags:', error);
    }
  };

  const fetchAvailableTags = async () => {
    try {
      const url = searchQuery 
        ? `http://localhost:5000/api/tags?search=${encodeURIComponent(searchQuery)}&limit=20`
        : 'http://localhost:5000/api/tags?limit=50';
      
      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        setAvailableTags(data.data.tags);
      }
    } catch (error) {
      console.error('Error fetching available tags:', error);
    }
  };

  const addTag = async (tag: Tag, interestLevel: 'low' | 'medium' | 'high' = 'medium') => {
    if (!token || !user) return;

    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/tags/user', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tagId: tag.id,
          interestLevel
        })
      });

      if (response.ok) {
        const newTag = { ...tag, interestLevel, addedAt: new Date().toISOString() };
        const updatedTags = [...userTags, newTag];
        setUserTags(updatedTags);
        onTagsChange?.(updatedTags);
        setShowAddModal(false);
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Failed to add tag');
      }
    } catch (error) {
      console.error('Error adding tag:', error);
      alert('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const removeTag = async (tagId: number) => {
    if (!token || !user) return;

    setIsLoading(true);
    try {
      const response = await fetch(`http://localhost:5000/api/tags/user/${tagId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const updatedTags = userTags.filter(tag => tag.id !== tagId);
        setUserTags(updatedTags);
        onTagsChange?.(updatedTags);
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Failed to remove tag');
      }
    } catch (error) {
      console.error('Error removing tag:', error);
      alert('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getInterestLevelColor = (level?: string) => {
    switch (level) {
      case 'high': return 'ring-2 ring-green-500';
      case 'medium': return 'ring-2 ring-blue-500';
      case 'low': return 'ring-2 ring-gray-400';
      default: return '';
    }
  };

  const getInterestLevelText = (level?: string) => {
    switch (level) {
      case 'high': return 'High Interest';
      case 'medium': return 'Medium Interest';
      case 'low': return 'Low Interest';
      default: return '';
    }
  };

  useEffect(() => {
    if (canManage && searchQuery) {
      const debounceTimer = setTimeout(() => {
        fetchAvailableTags();
      }, 300);
      return () => clearTimeout(debounceTimer);
    }
  }, [searchQuery, canManage]);

  return (
    <div className="space-y-4">
      {/* User Tags Display */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-gray-900">
            {canManage ? 'Your Interests' : 'Interests'}
          </h3>
          {canManage && userTags.length < maxTags && (
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Tag
            </button>
          )}
        </div>

        {userTags.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {userTags.map((tag) => (
              <div
                key={tag.id}
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getInterestLevelColor(tag.interestLevel)}`}
                style={{ backgroundColor: tag.color + '20', color: tag.color }}
                title={canManage ? getInterestLevelText(tag.interestLevel) : tag.description}
              >
                <span>{tag.name}</span>
                {canManage && (
                  <button
                    onClick={() => removeTag(tag.id)}
                    className="ml-2 text-gray-400 hover:text-red-500"
                    disabled={isLoading}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">
            {canManage ? 'No interests added yet. Add some tags to personalize your experience!' : 'No interests to display.'}
          </p>
        )}
      </div>

      {/* Add Tag Modal */}
      {showAddModal && canManage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Add Interest Tag</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-4">
              <input
                type="text"
                placeholder="Search tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="max-h-64 overflow-y-auto space-y-2">
              {availableTags
                .filter(tag => !userTags.some(userTag => userTag.id === tag.id))
                .map((tag) => (
                  <div
                    key={tag.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span
                          className="inline-block w-3 h-3 rounded-full"
                          style={{ backgroundColor: tag.color }}
                        ></span>
                        <span className="font-medium text-gray-900">{tag.name}</span>
                      </div>
                      {tag.description && (
                        <p className="text-sm text-gray-500 mt-1">{tag.description}</p>
                      )}
                      <p className="text-xs text-gray-400 mt-1">{tag.usageCount} users</p>
                    </div>
                    <div className="flex space-x-1">
                      {(['low', 'medium', 'high'] as const).map((level) => (
                        <button
                          key={level}
                          onClick={() => addTag(tag, level)}
                          disabled={isLoading}
                          className={`px-2 py-1 text-xs rounded ${
                            level === 'high' ? 'bg-green-100 text-green-700 hover:bg-green-200' :
                            level === 'medium' ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' :
                            'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={`Add with ${level} interest`}
                        >
                          {level}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
            </div>

            {availableTags.filter(tag => !userTags.some(userTag => userTag.id === tag.id)).length === 0 && (
              <p className="text-center text-gray-500 py-4">
                {searchQuery ? 'No tags found matching your search.' : 'No more tags available.'}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
