'use client';

import { useState, useRef } from 'react';

interface VideoPlayerProps {
  videoUrl?: string;
  title: string;
  thumbnail?: string;
  autoPlay?: boolean;
  controls?: boolean;
  className?: string;
}

export default function VideoPlayer({
  videoUrl,
  title,
  thumbnail,
  autoPlay = false,
  controls = true,
  className = ""
}: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideoClick = () => {
    handlePlayPause();
  };

  // For demo purposes, we'll show a beautiful abstract art background
  // In a real app, this would be replaced with actual video content
  return (
    <div className={`relative w-full h-96 rounded-2xl overflow-hidden bg-gradient-to-br from-orange-100 via-orange-200 to-orange-300 ${className}`}>
      {/* Abstract Art Background - matching the mockup */}
      <div className="absolute inset-0">
        {/* Abstract shapes */}
        <div className="absolute top-20 left-20 w-16 h-16 bg-orange-300 rounded-full opacity-70"></div>
        <div className="absolute top-12 right-32 w-48 h-48 bg-orange-400 rounded-full opacity-50"></div>
        <div className="absolute bottom-8 right-8 w-64 h-64 bg-orange-600 rounded-full opacity-40"></div>
        <div className="absolute bottom-16 left-16 w-32 h-32 bg-orange-500 rounded-full opacity-60"></div>
      </div>

      {/* Video Element (hidden for demo) */}
      {videoUrl && (
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover opacity-0"
          autoPlay={autoPlay}
          controls={false}
          poster={thumbnail}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => setShowControls(false)}
        >
          <source src={videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      )}

      {/* Play Button Overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <button
          onClick={handlePlayPause}
          className="w-20 h-20 bg-black bg-opacity-60 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-all duration-300 transform hover:scale-105 group"
          aria-label={isPlaying ? 'Pause video' : 'Play video'}
        >
          {isPlaying ? (
            // Pause Icon
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          ) : (
            // Play Icon
            <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          )}
        </button>
      </div>

      {/* Video Controls (if enabled) */}
      {controls && showControls && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            {/* Play/Pause Button */}
            <button
              onClick={handlePlayPause}
              className="flex items-center space-x-2 hover:text-orange-300 transition-colors"
            >
              {isPlaying ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </button>

            {/* Progress Bar */}
            <div className="flex-1 mx-4">
              <div className="w-full bg-white/30 rounded-full h-1">
                <div className="bg-orange-400 h-1 rounded-full w-1/3"></div>
              </div>
            </div>

            {/* Volume and Fullscreen */}
            <div className="flex items-center space-x-3">
              {/* Volume Button */}
              <button className="hover:text-orange-300 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12H5a1 1 0 01-1-1V9a1 1 0 011-1h4l5-5v16l-5-5z" />
                </svg>
              </button>

              {/* Fullscreen Button */}
              <button className="hover:text-orange-300 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {isPlaying && (
        <div className="absolute top-4 right-4">
          <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
        </div>
      )}

      {/* Video Title Overlay */}
      <div className="absolute top-4 left-4 right-4">
        <h3 className="text-white text-lg font-semibold drop-shadow-lg">
          {title}
        </h3>
      </div>
    </div>
  );
}
