'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { apiService } from '../../services/apiService';
import { toast } from 'react-hot-toast';
import {
  FaUsers,
  FaNewspaper,
  FaEye,
  FaHeart,
  FaComment,
  FaShare,
  FaTrendingUp,
  FaTrendingDown,
  FaCalendar,
  FaDownload,
  FaRefresh
} from 'react-icons/fa';

interface AnalyticsDashboardProps {
  timeRange?: 'today' | 'week' | 'month' | 'year';
  showRealtime?: boolean;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  timeRange = 'week',
  showRealtime = true
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [realtimeStats, setRealtimeStats] = useState<any>(null);
  const [userAnalytics, setUserAnalytics] = useState<any>(null);
  const [contentAnalytics, setContentAnalytics] = useState<any>(null);
  const [trafficAnalytics, setTrafficAnalytics] = useState<any>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setIsLoading(true);
      
      const days = {
        today: 1,
        week: 7,
        month: 30,
        year: 365
      }[selectedTimeRange];

      const [userResponse, contentResponse, trafficResponse, performanceResponse] = await Promise.all([
        apiService.getAnalytics('user', { days }),
        apiService.getAnalytics('content', { days }),
        apiService.getAnalytics('traffic', { days }),
        apiService.getAnalytics('performance', { days })
      ]);

      if (userResponse.success) setUserAnalytics(userResponse.data);
      if (contentResponse.success) setContentAnalytics(contentResponse.data);
      if (trafficResponse.success) setTrafficAnalytics(trafficResponse.data);
      if (performanceResponse.success) setPerformanceMetrics(performanceResponse.data);

    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeRange]);

  const fetchRealtimeStats = useCallback(async () => {
    if (!showRealtime) return;

    try {
      const response = await apiService.getRealtimeStats();
      if (response.success) {
        setRealtimeStats(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch realtime stats:', error);
    }
  }, [showRealtime]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  useEffect(() => {
    fetchRealtimeStats();
    
    if (showRealtime) {
      const interval = setInterval(fetchRealtimeStats, 30000); // Update every 30 seconds
      return () => clearInterval(interval);
    }
  }, [fetchRealtimeStats, showRealtime]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    previousValue?: number;
    icon: React.ReactNode;
    color: string;
    suffix?: string;
  }> = ({ title, value, previousValue, icon, color, suffix = '' }) => {
    const change = previousValue ? calculatePercentageChange(value, previousValue) : 0;
    const isPositive = change >= 0;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">
              {formatNumber(value)}{suffix}
            </p>
            {previousValue !== undefined && (
              <div className="flex items-center mt-2">
                {isPositive ? (
                  <FaTrendingUp className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <FaTrendingDown className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  {Math.abs(change).toFixed(1)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">
                  vs previous period
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            {icon}
          </div>
        </div>
      </div>
    );
  };

  const ChartCard: React.FC<{
    title: string;
    data: any[];
    type: 'line' | 'bar' | 'pie';
  }> = ({ title, data, type }) => {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button className="text-gray-400 hover:text-gray-600">
            <FaDownload className="w-4 h-4" />
          </button>
        </div>
        
        {/* Placeholder for chart - you would integrate with a charting library like Chart.js or Recharts */}
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Chart placeholder - {type} chart</p>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Monitor your platform's performance and user engagement</p>
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="today">Today</option>
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
            <option value="year">Last year</option>
          </select>
          
          <button
            onClick={fetchAnalytics}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
          >
            <FaRefresh className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Realtime Stats */}
      {showRealtime && realtimeStats && (
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Live Statistics</h2>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Live</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{realtimeStats.onlineUsers || 0}</p>
              <p className="text-sm opacity-90">Online Users</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{realtimeStats.activeReaders || 0}</p>
              <p className="text-sm opacity-90">Active Readers</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{realtimeStats.pageViews || 0}</p>
              <p className="text-sm opacity-90">Page Views (1h)</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{realtimeStats.newComments || 0}</p>
              <p className="text-sm opacity-90">New Comments (1h)</p>
            </div>
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={userAnalytics?.totalUsers || 0}
          previousValue={userAnalytics?.previousTotalUsers}
          icon={<FaUsers className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        
        <StatCard
          title="Total Articles"
          value={contentAnalytics?.totalArticles || 0}
          previousValue={contentAnalytics?.previousTotalArticles}
          icon={<FaNewspaper className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        
        <StatCard
          title="Page Views"
          value={trafficAnalytics?.pageViews || 0}
          previousValue={trafficAnalytics?.previousPageViews}
          icon={<FaEye className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
        
        <StatCard
          title="Engagement Rate"
          value={contentAnalytics?.engagementRate || 0}
          previousValue={contentAnalytics?.previousEngagementRate}
          icon={<FaHeart className="w-6 h-6 text-white" />}
          color="bg-red-500"
          suffix="%"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard
          title="User Growth"
          data={userAnalytics?.growthData || []}
          type="line"
        />
        
        <ChartCard
          title="Content Performance"
          data={contentAnalytics?.performanceData || []}
          type="bar"
        />
        
        <ChartCard
          title="Traffic Sources"
          data={trafficAnalytics?.sourcesData || []}
          type="pie"
        />
        
        <ChartCard
          title="Popular Content"
          data={contentAnalytics?.popularContent || []}
          type="bar"
        />
      </div>

      {/* Detailed Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Articles */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Articles</h3>
          <div className="space-y-3">
            {contentAnalytics?.topArticles?.slice(0, 5).map((article: any, index: number) => (
              <div key={article.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900 truncate">{article.title}</p>
                  <p className="text-sm text-gray-600">{article.author}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{formatNumber(article.views)}</p>
                  <p className="text-sm text-gray-600">views</p>
                </div>
              </div>
            )) || (
              <p className="text-gray-500 text-center py-4">No data available</p>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {userAnalytics?.recentActivity?.slice(0, 5).map((activity: any, index: number) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {activity.type === 'user_register' && <FaUsers className="w-4 h-4 text-green-500" />}
                  {activity.type === 'article_publish' && <FaNewspaper className="w-4 h-4 text-blue-500" />}
                  {activity.type === 'comment_create' && <FaComment className="w-4 h-4 text-purple-500" />}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-600">{activity.timestamp}</p>
                </div>
              </div>
            )) || (
              <p className="text-gray-500 text-center py-4">No recent activity</p>
            )}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {performanceMetrics && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {performanceMetrics.averageLoadTime || 0}ms
              </p>
              <p className="text-sm text-gray-600">Average Load Time</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {performanceMetrics.bounceRate || 0}%
              </p>
              <p className="text-sm text-gray-600">Bounce Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {performanceMetrics.averageSessionDuration || 0}s
              </p>
              <p className="text-sm text-gray-600">Avg Session Duration</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
