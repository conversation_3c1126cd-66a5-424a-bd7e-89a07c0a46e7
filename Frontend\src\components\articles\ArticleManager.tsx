'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { apiService, Article } from '../../services/apiService';
import { useEnhancedAuth } from '../../contexts/EnhancedAuthContext';
import { toast } from 'react-hot-toast';
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaEye, 
  FaHeart, 
  FaComment, 
  FaShare,
  FaSearch,
  FaFilter,
  FaSort
} from 'react-icons/fa';

interface ArticleManagerProps {
  showDrafts?: boolean;
  showPublished?: boolean;
  authorId?: number;
  category?: string;
  onArticleSelect?: (article: Article) => void;
}

const ArticleManager: React.FC<ArticleManagerProps> = ({
  showDrafts = true,
  showPublished = true,
  authorId,
  category,
  onArticleSelect
}) => {
  const { user, isAuthenticated } = useEnhancedAuth();
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || '');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [categories, setCategories] = useState<any[]>([]);

  const fetchArticles = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      const params: any = {
        page: currentPage,
        limit: 20,
        sortBy,
        sortOrder
      };

      if (searchQuery) params.search = searchQuery;
      if (selectedCategory) params.category = selectedCategory;
      if (authorId) params.authorId = authorId;
      if (showPublished && !showDrafts) params.published = true;
      if (showDrafts && !showPublished) params.published = false;

      const response = await apiService.getArticles(params);

      if (response.success && response.data) {
        setArticles(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.pages);
        }
      }
    } catch (error) {
      console.error('Failed to fetch articles:', error);
      toast.error('Failed to load articles');
    } finally {
      setIsLoading(false);
    }
  }, [
    isAuthenticated,
    currentPage,
    searchQuery,
    selectedCategory,
    sortBy,
    sortOrder,
    authorId,
    showPublished,
    showDrafts
  ]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await apiService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  }, []);

  const handleDeleteArticle = async (id: number) => {
    if (!confirm('Are you sure you want to delete this article?')) {
      return;
    }

    try {
      const response = await apiService.deleteArticle(id);
      if (response.success) {
        setArticles(prev => prev.filter(article => article.id !== id));
        toast.success('Article deleted successfully');
      }
    } catch (error) {
      console.error('Failed to delete article:', error);
      toast.error('Failed to delete article');
    }
  };

  const handleLikeArticle = async (id: number, isLiked: boolean) => {
    try {
      if (isLiked) {
        await apiService.unlikeArticle(id);
      } else {
        await apiService.likeArticle(id);
      }

      setArticles(prev => 
        prev.map(article => 
          article.id === id 
            ? { 
                ...article, 
                likes: isLiked ? article.likes - 1 : article.likes + 1 
              }
            : article
        )
      );
    } catch (error) {
      console.error('Failed to like/unlike article:', error);
      toast.error('Failed to update like status');
    }
  };

  const handleShareArticle = async (id: number, platform: string) => {
    try {
      await apiService.shareArticle(id, platform);
      toast.success(`Article shared to ${platform}`);
    } catch (error) {
      console.error('Failed to share article:', error);
      toast.error('Failed to share article');
    }
  };

  useEffect(() => {
    fetchArticles();
  }, [fetchArticles]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchArticles();
  };

  const handleSortChange = (newSortBy: string) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortBy(newSortBy);
      setSortOrder('DESC');
    }
    setCurrentPage(1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Article Manager
          </h2>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => window.location.href = '/articles/new'}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FaPlus className="w-4 h-4 mr-2" />
              New Article
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mt-4 flex flex-col sm:flex-row gap-4">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </form>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.name}>
                {category.name}
              </option>
            ))}
          </select>

          <div className="flex items-center gap-2">
            <FaSort className="text-gray-400 w-4 h-4" />
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field);
                setSortOrder(order as 'ASC' | 'DESC');
                setCurrentPage(1);
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="createdAt-DESC">Newest First</option>
              <option value="createdAt-ASC">Oldest First</option>
              <option value="title-ASC">Title A-Z</option>
              <option value="title-DESC">Title Z-A</option>
              <option value="views-DESC">Most Viewed</option>
              <option value="likes-DESC">Most Liked</option>
            </select>
          </div>
        </div>
      </div>

      {/* Articles List */}
      <div className="p-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No articles found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {articles.map(article => (
              <div
                key={article.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onArticleSelect?.(article)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600">
                        {article.title}
                      </h3>
                      {!article.published && (
                        <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          Draft
                        </span>
                      )}
                    </div>
                    
                    {article.excerpt && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {article.excerpt}
                      </p>
                    )}

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>By {article.author?.username || 'Unknown'}</span>
                      <span>{formatDate(article.createdAt)}</span>
                      <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                        {article.category}
                      </span>
                    </div>

                    <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <FaEye className="w-4 h-4" />
                        <span>{article.views}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FaHeart className="w-4 h-4" />
                        <span>{article.likes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FaComment className="w-4 h-4" />
                        <span>{article.comments}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.location.href = `/articles/${article.id}/edit`;
                      }}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                      title="Edit"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLikeArticle(article.id, false); // Simplified - should check if already liked
                      }}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md"
                      title="Like"
                    >
                      <FaHeart className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleShareArticle(article.id, 'twitter');
                      }}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md"
                      title="Share"
                    >
                      <FaShare className="w-4 h-4" />
                    </button>

                    {(user?.id === article.authorId || user?.role === 'admin') && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteArticle(article.id);
                        }}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md"
                        title="Delete"
                      >
                        <FaTrash className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-6">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="px-3 py-2 text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArticleManager;
