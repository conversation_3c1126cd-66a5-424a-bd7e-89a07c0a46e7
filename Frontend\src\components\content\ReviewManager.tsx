'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import {
  ShieldCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  DocumentTextIcon,
  PhotoIcon,
  UserIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface ContentReview {
  id: number;
  contentType: 'article' | 'draft' | 'comment' | 'media_file' | 'user_profile';
  contentId: number;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  autoApproved: boolean;
  aiFlags: string[];
  reviewNotes?: string;
  reviewerId?: number;
  submitterId: number;
  submittedAt: string;
  reviewStartedAt?: string;
  reviewCompletedAt?: string;
  createdAt: string;
  updatedAt: string;
  // Associated data
  submitter?: {
    id: number;
    username: string;
    email: string;
  };
  reviewer?: {
    id: number;
    username: string;
    email: string;
  };
  content?: {
    title?: string;
    excerpt?: string;
    url?: string;
  };
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

interface ReviewManagerProps {
  className?: string;
  userRole?: 'user' | 'admin';
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

const statusConfig = {
  pending: { label: 'Pending Review', color: 'bg-yellow-100 text-yellow-800', icon: ClockIcon },
  in_review: { label: 'In Review', color: 'bg-blue-100 text-blue-800', icon: ShieldCheckIcon },
  approved: { label: 'Approved', color: 'bg-green-100 text-green-800', icon: CheckCircleIcon },
  rejected: { label: 'Rejected', color: 'bg-red-100 text-red-800', icon: XCircleIcon },
  escalated: { label: 'Escalated', color: 'bg-purple-100 text-purple-800', icon: ExclamationTriangleIcon },
};

const priorityConfig = {
  low: { label: 'Low', color: 'text-gray-600' },
  medium: { label: 'Medium', color: 'text-yellow-600' },
  high: { label: 'High', color: 'text-orange-600' },
  urgent: { label: 'Urgent', color: 'text-red-600' },
};

const riskLevelConfig = {
  low: { label: 'Low Risk', color: 'text-green-600' },
  medium: { label: 'Medium Risk', color: 'text-yellow-600' },
  high: { label: 'High Risk', color: 'text-orange-600' },
  critical: { label: 'Critical Risk', color: 'text-red-600' },
};

const contentTypeConfig = {
  article: { label: 'Article', icon: DocumentTextIcon },
  draft: { label: 'Draft', icon: DocumentTextIcon },
  comment: { label: 'Comment', icon: DocumentTextIcon },
  media_file: { label: 'Media File', icon: PhotoIcon },
  user_profile: { label: 'User Profile', icon: UserIcon },
};

export default function ReviewManager({ className = '', userRole = 'user' }: ReviewManagerProps) {
  const { user, token, isAuthenticated } = useAuth();
  const toast = useToast();
  
  const [reviews, setReviews] = useState<ContentReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  
  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [contentTypeFilter, setContentTypeFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const fetchReviews = useCallback(async (page: number = 1) => {
    if (!isAuthenticated || !token) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (searchQuery) params.append('search', searchQuery);
      if (statusFilter) params.append('status', statusFilter);
      if (contentTypeFilter) params.append('contentType', contentTypeFilter);
      if (priorityFilter) params.append('priority', priorityFilter);

      // Choose different API endpoints based on user role
      const endpoint = userRole === 'admin' ? '/reviews' : '/reviews/my-reviews';
      
      const response = await fetch(`${API_BASE_URL}${endpoint}?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setReviews(data.data.reviews);
          setPagination(data.data.pagination);
        } else {
          toast.error('Load Failed', data.message || 'Unable to get review list');
        }
      } else if (response.status === 401) {
        toast.error('Authentication Failed', 'Please log in again');
      } else {
        toast.error('Load Failed', 'Server error, please try again later');
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Load Failed', 'Network error, please check connection');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, token, searchQuery, statusFilter, contentTypeFilter, priorityFilter, pagination.limit, userRole, toast]);

  useEffect(() => {
    fetchReviews(1);
  }, [fetchReviews]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchReviews(1);
  };

  const handleReviewAction = async (reviewId: number, action: 'approve' | 'reject', notes?: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes }),
      });

      if (response.ok) {
        const actionText = action === 'approve' ? 'approved' : 'rejected';
        toast.success('Operation Successful', `Review has been ${actionText}`);
        fetchReviews(pagination.page);
      } else {
        const data = await response.json();
        toast.error('Operation Failed', data.message || 'Unable to complete review action');
      }
    } catch (error) {
      console.error('Error handling review action:', error);
      toast.error('Operation Failed', 'Network error, please check connection');
    }
  };

  const handleClaimReview = async (reviewId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}/claim`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.success('Claim Successful', 'Review task has been claimed');
        fetchReviews(pagination.page);
      } else {
        const data = await response.json();
        toast.error('Claim Failed', data.message || 'Unable to claim review task');
      }
    } catch (error) {
      console.error('Error claiming review:', error);
      toast.error('Claim Failed', 'Network error, please check connection');
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchReviews(newPage);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAuthenticated) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Please log in first</h3>
        <p className="mt-2 text-sm text-gray-500">Log in to view review status</p>
        <Link
          href="/auth/login"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Log in now
        </Link>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {userRole === 'admin' ? 'Content Review Management' : 'My Review Records'}
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {userRole === 'admin'
              ? 'Manage platform content review and security checks'
              : 'View the review status of your submitted content'
            }
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border p-4">
        <form onSubmit={handleSearch} className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search review records..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filter
          </button>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Search
          </button>
        </form>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Status</option>
                  {Object.entries(statusConfig).map(([value, config]) => (
                    <option key={value} value={value}>{config.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                <select
                  value={contentTypeFilter}
                  onChange={(e) => setContentTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Types</option>
                  {Object.entries(contentTypeConfig).map(([value, config]) => (
                    <option key={value} value={value}>{config.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Priorities</option>
                  {Object.entries(priorityConfig).map(([value, config]) => (
                    <option key={value} value={value}>{config.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg border">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading...</p>
          </div>
        ) : reviews.length === 0 ? (
          <div className="p-8 text-center">
            <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No Review Records</h3>
            <p className="mt-2 text-sm text-gray-500">
              {searchQuery || statusFilter || contentTypeFilter || priorityFilter
                ? 'No review records found matching the criteria'
                : userRole === 'admin'
                  ? 'No content currently needs review'
                  : 'You have not submitted any content that requires review'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {reviews.map((review) => {
              const StatusIcon = statusConfig[review.status].icon;
              const ContentTypeIcon = contentTypeConfig[review.contentType].icon;

              return (
                <div key={review.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center space-x-2">
                          <ContentTypeIcon className="h-5 w-5 text-gray-400" />
                          <span className="text-sm font-medium text-gray-900">
                            {contentTypeConfig[review.contentType].label} #{review.contentId}
                          </span>
                        </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[review.status].color}`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig[review.status].label}
                        </span>
                        <span className={`text-xs font-medium ${priorityConfig[review.priority].color}`}>
                          {priorityConfig[review.priority].label} Priority
                        </span>
                        <span className={`text-xs font-medium ${riskLevelConfig[review.riskLevel].color}`}>
                          {riskLevelConfig[review.riskLevel].label}
                        </span>
                        {review.autoApproved && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            Auto Approved
                          </span>
                        )}
                      </div>

                      {review.content?.title && (
                        <h4 className="text-lg font-medium text-gray-900 mb-1">
                          {review.content.title}
                        </h4>
                      )}

                      {review.content?.excerpt && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {review.content.excerpt}
                        </p>
                      )}

                      <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                        <span>Submitted: {formatDate(review.submittedAt)}</span>
                        {review.reviewStartedAt && (
                          <span>Review Started: {formatDate(review.reviewStartedAt)}</span>
                        )}
                        {review.reviewCompletedAt && (
                          <span>Review Completed: {formatDate(review.reviewCompletedAt)}</span>
                        )}
                      </div>

                      {review.submitter && (
                        <div className="text-xs text-gray-500 mb-2">
                          Submitter: {review.submitter.username} ({review.submitter.email})
                        </div>
                      )}

                      {review.reviewer && (
                        <div className="text-xs text-gray-500 mb-2">
                          Reviewer: {review.reviewer.username} ({review.reviewer.email})
                        </div>
                      )}

                      {review.aiFlags && review.aiFlags.length > 0 && (
                        <div className="mb-2">
                          <span className="text-xs font-medium text-gray-700">AI Flags: </span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {review.aiFlags.map((flag, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800"
                              >
                                {flag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {review.reviewNotes && (
                        <div className="mt-2 p-3 bg-gray-50 rounded-md">
                          <span className="text-xs font-medium text-gray-700">Review Notes: </span>
                          <p className="text-sm text-gray-600 mt-1">{review.reviewNotes}</p>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {review.content?.url && (
                        <a
                          href={review.content.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          title="View Content"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </a>
                      )}

                      {userRole === 'admin' && review.status === 'pending' && (
                        <button
                          onClick={() => handleClaimReview(review.id)}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          Claim Review
                        </button>
                      )}

                      {userRole === 'admin' && review.status === 'in_review' && review.reviewerId === user?.id && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleReviewAction(review.id, 'approve')}
                            className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => {
                              const notes = prompt('Please enter rejection reason:');
                              if (notes) handleReviewAction(review.id, 'reject', notes);
                            }}
                            className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            Reject
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg border">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(pagination.page * pagination.limit, pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(pagination.pages - 4, pagination.page - 2)) + i;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === pagination.page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
