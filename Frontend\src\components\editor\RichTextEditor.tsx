'use client';

import React, { useState, useRef, useCallback } from 'react';
import { apiService } from '../../services/apiService';
import { toast } from 'react-hot-toast';
import {
  FaBold,
  FaItalic,
  FaUnderline,
  FaStrikethrough,
  FaListUl,
  FaListOl,
  FaQuoteLeft,
  FaCode,
  FaLink,
  FaImage,
  FaVideo,
  FaTable,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
  FaUndo,
  FaRedo,
  FaEye,
  FaSave
} from 'react-icons/fa';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  onSave?: () => void;
  autoSave?: boolean;
  autoSaveInterval?: number;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Start writing your article...',
  height = '400px',
  onSave,
  autoSave = false,
  autoSaveInterval = 30000
}) => {
  const [isPreview, setIsPreview] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-save functionality
  React.useEffect(() => {
    if (!autoSave) return;

    const interval = setInterval(() => {
      if (onSave && value.trim()) {
        onSave();
      }
    }, autoSaveInterval);

    return () => clearInterval(interval);
  }, [autoSave, autoSaveInterval, onSave, value]);

  // Update word count
  React.useEffect(() => {
    const text = value.replace(/<[^>]*>/g, '').trim();
    const words = text ? text.split(/\s+/).length : 0;
    setWordCount(words);
  }, [value]);

  const execCommand = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [onChange]);

  const handleInput = useCallback(() => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [onChange]);

  const handleImageUpload = async (file: File) => {
    try {
      setIsUploading(true);
      const response = await apiService.uploadFile(file, 'image');
      
      if (response.success && response.data) {
        const imageUrl = response.data.url;
        execCommand('insertImage', imageUrl);
        toast.success('Image uploaded successfully');
      }
    } catch (error) {
      console.error('Failed to upload image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        handleImageUpload(file);
      } else {
        toast.error('Please select an image file');
      }
    }
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const insertTable = () => {
    const rows = prompt('Number of rows:');
    const cols = prompt('Number of columns:');
    
    if (rows && cols) {
      const rowCount = parseInt(rows);
      const colCount = parseInt(cols);
      
      let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
      for (let i = 0; i < rowCount; i++) {
        tableHTML += '<tr>';
        for (let j = 0; j < colCount; j++) {
          tableHTML += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
        }
        tableHTML += '</tr>';
      }
      tableHTML += '</table>';
      
      execCommand('insertHTML', tableHTML);
    }
  };

  const toolbarButtons = [
    { icon: FaBold, command: 'bold', title: 'Bold' },
    { icon: FaItalic, command: 'italic', title: 'Italic' },
    { icon: FaUnderline, command: 'underline', title: 'Underline' },
    { icon: FaStrikethrough, command: 'strikeThrough', title: 'Strikethrough' },
    { type: 'separator' },
    { icon: FaListUl, command: 'insertUnorderedList', title: 'Bullet List' },
    { icon: FaListOl, command: 'insertOrderedList', title: 'Numbered List' },
    { icon: FaQuoteLeft, command: 'formatBlock', value: 'blockquote', title: 'Quote' },
    { icon: FaCode, command: 'formatBlock', value: 'pre', title: 'Code Block' },
    { type: 'separator' },
    { icon: FaAlignLeft, command: 'justifyLeft', title: 'Align Left' },
    { icon: FaAlignCenter, command: 'justifyCenter', title: 'Align Center' },
    { icon: FaAlignRight, command: 'justifyRight', title: 'Align Right' },
    { type: 'separator' },
    { icon: FaLink, action: insertLink, title: 'Insert Link' },
    { icon: FaImage, action: () => fileInputRef.current?.click(), title: 'Insert Image' },
    { icon: FaTable, action: insertTable, title: 'Insert Table' },
    { type: 'separator' },
    { icon: FaUndo, command: 'undo', title: 'Undo' },
    { icon: FaRedo, command: 'redo', title: 'Redo' }
  ];

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap items-center gap-1">
        {/* Format Dropdown */}
        <select
          onChange={(e) => execCommand('formatBlock', e.target.value)}
          className="px-2 py-1 text-sm border border-gray-300 rounded mr-2"
          defaultValue=""
        >
          <option value="">Format</option>
          <option value="h1">Heading 1</option>
          <option value="h2">Heading 2</option>
          <option value="h3">Heading 3</option>
          <option value="h4">Heading 4</option>
          <option value="h5">Heading 5</option>
          <option value="h6">Heading 6</option>
          <option value="p">Paragraph</option>
        </select>

        {/* Font Size */}
        <select
          onChange={(e) => execCommand('fontSize', e.target.value)}
          className="px-2 py-1 text-sm border border-gray-300 rounded mr-2"
          defaultValue="3"
        >
          <option value="1">8pt</option>
          <option value="2">10pt</option>
          <option value="3">12pt</option>
          <option value="4">14pt</option>
          <option value="5">18pt</option>
          <option value="6">24pt</option>
          <option value="7">36pt</option>
        </select>

        {/* Toolbar Buttons */}
        {toolbarButtons.map((button, index) => {
          if (button.type === 'separator') {
            return <div key={index} className="w-px h-6 bg-gray-300 mx-1" />;
          }

          const Icon = button.icon!;
          return (
            <button
              key={index}
              onClick={() => {
                if (button.action) {
                  button.action();
                } else if (button.command) {
                  execCommand(button.command, button.value);
                }
              }}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title={button.title}
              disabled={isUploading}
            >
              <Icon className="w-4 h-4" />
            </button>
          );
        })}

        {/* Preview Toggle */}
        <div className="ml-auto flex items-center gap-2">
          <button
            onClick={() => setIsPreview(!isPreview)}
            className={`p-2 rounded transition-colors ${
              isPreview 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
            title="Toggle Preview"
          >
            <FaEye className="w-4 h-4" />
          </button>

          {onSave && (
            <button
              onClick={onSave}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title="Save"
            >
              <FaSave className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Editor/Preview Area */}
      <div style={{ height }}>
        {isPreview ? (
          <div 
            className="p-4 prose max-w-none overflow-auto h-full"
            dangerouslySetInnerHTML={{ __html: value }}
          />
        ) : (
          <div
            ref={editorRef}
            contentEditable
            onInput={handleInput}
            className="p-4 outline-none h-full overflow-auto"
            style={{ minHeight: height }}
            dangerouslySetInnerHTML={{ __html: value }}
            data-placeholder={placeholder}
          />
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2 flex justify-between items-center text-sm text-gray-600">
        <div className="flex items-center gap-4">
          <span>{wordCount} words</span>
          {autoSave && (
            <span className="text-green-600">Auto-save enabled</span>
          )}
          {isUploading && (
            <span className="text-blue-600">Uploading...</span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <span>Rich Text Editor</span>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Custom Styles */}
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        .prose h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }
        .prose h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }
        .prose h3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }
        .prose h4 { font-size: 1em; font-weight: bold; margin: 1.12em 0; }
        .prose h5 { font-size: 0.83em; font-weight: bold; margin: 1.5em 0; }
        .prose h6 { font-size: 0.75em; font-weight: bold; margin: 1.67em 0; }
        .prose p { margin: 1em 0; }
        .prose blockquote { 
          margin: 1em 0; 
          padding-left: 1em; 
          border-left: 4px solid #e5e7eb; 
          color: #6b7280; 
        }
        .prose pre { 
          background: #f3f4f6; 
          padding: 1em; 
          border-radius: 0.375rem; 
          overflow-x: auto; 
        }
        .prose ul, .prose ol { margin: 1em 0; padding-left: 2em; }
        .prose li { margin: 0.5em 0; }
        .prose img { max-width: 100%; height: auto; }
        .prose table { border-collapse: collapse; width: 100%; margin: 1em 0; }
        .prose td, .prose th { border: 1px solid #d1d5db; padding: 0.5em; }
        .prose th { background: #f9fafb; font-weight: bold; }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
