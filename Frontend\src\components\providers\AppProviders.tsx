'use client';

import React from 'react';
import { Toaster } from 'react-hot-toast';
import { EnhancedAuthProvider } from '../../contexts/EnhancedAuthContext';
import { WebSocketProvider } from '../../contexts/WebSocketContext';
import { EnhancedNotificationProvider } from '../../contexts/EnhancedNotificationContext';

interface AppProvidersProps {
  children: React.ReactNode;
}

const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <EnhancedAuthProvider>
      <WebSocketProvider>
        <EnhancedNotificationProvider>
          {children}
          
          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                style: {
                  background: '#10b981',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />
        </EnhancedNotificationProvider>
      </WebSocketProvider>
    </EnhancedAuthProvider>
  );
};

export default AppProviders;
