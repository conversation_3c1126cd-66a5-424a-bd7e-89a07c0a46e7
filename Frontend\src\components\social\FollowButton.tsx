'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import { UserPlusIcon, UserMinusIcon } from '@heroicons/react/24/outline';

interface FollowButtonProps {
  userId: number;
  username?: string;
  initialFollowState?: boolean;
  onFollowChange?: (isFollowing: boolean, followerCount: number) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  showIcon?: boolean;
  className?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function FollowButton({
  userId,
  username,
  initialFollowState = false,
  onFollowChange,
  size = 'md',
  variant = 'primary',
  showIcon = true,
  className = ''
}: FollowButtonProps) {
  const { user, token, isAuthenticated } = useAuth();
  const toast = useToast();
  
  const [isFollowing, setIsFollowing] = useState(initialFollowState);
  const [isLoading, setIsLoading] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);

  // Check if user is following on component mount
  const checkFollowStatus = useCallback(async () => {
    if (!isAuthenticated || !token || !userId || userId === user?.id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/follows/check/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setIsFollowing(data.data.isFollowing);
        }
      }
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  }, [isAuthenticated, token, userId, user?.id]);

  // Get follower count
  const getFollowerCount = useCallback(async () => {
    if (!userId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/follows/${userId}/followers?limit=1`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setFollowerCount(data.data.pagination.total);
        }
      }
    } catch (error) {
      console.error('Error fetching follower count:', error);
    }
  }, [userId]);

  useEffect(() => {
    checkFollowStatus();
    getFollowerCount();
  }, [checkFollowStatus, getFollowerCount]);

  const handleFollow = async () => {
    if (!isAuthenticated) {
      toast.warning('Please log in first', 'You need to log in to follow users');
      return;
    }

    if (!token || userId === user?.id) return;

    setIsLoading(true);

    try {
      const url = isFollowing 
        ? `${API_BASE_URL}/follows/${userId}`
        : `${API_BASE_URL}/follows`;
      
      const method = isFollowing ? 'DELETE' : 'POST';
      const body = isFollowing ? undefined : JSON.stringify({ followingId: userId });

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body,
      });

      const data = await response.json();

      if (response.ok && data.success) {
        const newFollowState = !isFollowing;
        const newFollowerCount = followerCount + (newFollowState ? 1 : -1);
        
        setIsFollowing(newFollowState);
        setFollowerCount(newFollowerCount);
        
        // Notify parent component
        onFollowChange?.(newFollowState, newFollowerCount);

        // Show success message
        const action = newFollowState ? 'Follow' : 'Unfollow';
        const displayName = username || 'User';
        toast.success(
          `${action} Successful`,
          `You have ${newFollowState ? 'followed' : 'unfollowed'} ${displayName}`
        );

        // Record activity if following
        if (newFollowState) {
          try {
            await fetch(`${API_BASE_URL}/activities/user-follow`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ followedUserId: userId }),
            });
          } catch (error) {
            console.error('Error recording follow activity:', error);
          }
        }
      } else {
        toast.error('Operation Failed', data.message || 'Please try again later');
      }
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error('Operation Failed', 'Network error, please check connection and try again');
    } finally {
      setIsLoading(false);
    }
  };

  // Don't show button for own profile
  if (!isAuthenticated || userId === user?.id) {
    return null;
  }

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // Variant classes
  const getVariantClasses = () => {
    if (isFollowing) {
      return 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-red-50 hover:text-red-600 hover:border-red-300';
    }
    
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 text-white hover:bg-blue-700 border border-blue-600';
      case 'secondary':
        return 'bg-gray-600 text-white hover:bg-gray-700 border border-gray-600';
      case 'outline':
        return 'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50';
      default:
        return 'bg-blue-600 text-white hover:bg-blue-700 border border-blue-600';
    }
  };

  const buttonText = isFollowing ? 'Following' : 'Follow';
  const Icon = isFollowing ? UserMinusIcon : UserPlusIcon;

  return (
    <button
      onClick={handleFollow}
      disabled={isLoading}
      className={`
        inline-flex items-center justify-center font-medium rounded-md
        transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
        disabled:opacity-50 disabled:cursor-not-allowed
        ${sizeClasses[size]}
        ${getVariantClasses()}
        ${className}
      `}
    >
      {isLoading ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          Processing...
        </div>
      ) : (
        <div className="flex items-center">
          {showIcon && <Icon className="h-4 w-4 mr-2" />}
          {buttonText}
        </div>
      )}
    </button>
  );
}
