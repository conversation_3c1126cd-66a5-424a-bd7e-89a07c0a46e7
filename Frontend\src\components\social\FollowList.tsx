'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
// import Image from 'next/image'; // Temporarily disabled due to config issues
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import FollowButton from './FollowButton';
import { UserIcon } from '@heroicons/react/24/outline';

interface User {
  id: number;
  username: string;
  avatar?: string;
  createdAt: string;
}

interface FollowListProps {
  userId: number;
  type: 'followers' | 'following';
  className?: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function FollowList({ userId, type, className = '' }: FollowListProps) {
  const { token } = useAuth();
  const toast = useToast();
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [loadingMore, setLoadingMore] = useState(false);

  const fetchUsers = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) setLoading(true);
      else setLoadingMore(true);

      const endpoint = type === 'followers' ? 'followers' : 'following';
      const response = await fetch(
        `${API_BASE_URL}/follows/${userId}/${endpoint}?page=${page}&limit=20`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const newUsers = data.data[type];
          setUsers(prev => append ? [...prev, ...newUsers] : newUsers);
          setPagination(data.data.pagination);
        } else {
          toast.error('Load Failed', data.message || 'Unable to get user list');
        }
      } else {
        toast.error('Load Failed', 'Server error, please try again later');
      }
    } catch (error) {
      console.error(`Error fetching ${type}:`, error);
      toast.error('Load Failed', 'Network error, please check connection');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [userId, type, toast]);

  useEffect(() => {
    fetchUsers(1, false);
  }, [fetchUsers]);

  const handleLoadMore = () => {
    if (pagination.page < pagination.pages && !loadingMore) {
      fetchUsers(pagination.page + 1, true);
    }
  };

  const handleFollowChange = (targetUserId: number, isFollowing: boolean) => {
    // Update the user list if needed
    if (type === 'following' && !isFollowing) {
      // Remove user from following list if unfollowed
      setUsers(prev => prev.filter(user => user.id !== targetUserId));
      setPagination(prev => ({ ...prev, total: prev.total - 1 }));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {[...Array(5)].map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 bg-white rounded-lg border animate-pulse">
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (users.length === 0) {
    const emptyMessage = type === 'followers' ? 'No followers' : 'No following users';
    return (
      <div className={`text-center py-12 ${className}`}>
        <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">{emptyMessage}</h3>
        <p className="mt-2 text-sm text-gray-500">
          {type === 'followers'
            ? 'No one is following this user yet'
            : 'This user is not following anyone yet'
          }
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {users.map((user) => (
        <div key={user.id} className="flex items-center space-x-4 p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
          <Link href={`/profile/${user.id}`} className="flex-shrink-0">
            <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-100">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.username}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.username)}&background=6366f1&color=fff&size=48`;
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                  <span className="text-white font-medium text-lg">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
          </Link>

          <div className="flex-1 min-w-0">
            <Link href={`/profile/${user.id}`} className="block">
              <h3 className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors">
                {user.username}
              </h3>
              <p className="text-xs text-gray-500 mt-1">
                Joined {formatDate(user.createdAt)}
              </p>
            </Link>
          </div>

          <div className="flex-shrink-0">
            <FollowButton
              userId={user.id}
              username={user.username}
              size="sm"
              variant="outline"
              onFollowChange={(isFollowing) => handleFollowChange(user.id, isFollowing)}
            />
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.page < pagination.pages && (
        <div className="text-center pt-4">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loadingMore ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                Loading...
              </>
            ) : (
              `Load more (${pagination.total - users.length} remaining)`
            )}
          </button>
        </div>
      )}

      {/* Pagination Info */}
      <div className="text-center text-sm text-gray-500 pt-2">
        Showing {users.length} / {pagination.total} users
      </div>
    </div>
  );
}
