'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
}

// 认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

// 认证操作
interface AuthActions {
  login: (email: string, password: string) => Promise<boolean>;
  register: (username: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  setError: (error: string) => void;
  error: string;
}

// Context类型
type AuthContextType = AuthState & AuthActions;

// 创建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API URL
const API_URL = 'http://localhost:5000/api';

// AuthProvider组件
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string>('');

  // 计算认证状态
  const isAuthenticated = !!(user && token);

  // 登录函数
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setError('');
      console.log('🔐 开始登录:', email);

      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();
      console.log('📡 登录响应:', data);

      if (data.success && data.user && data.token) {
        // 设置状态
        setUser(data.user);
        setToken(data.token);

        // 保存到localStorage
        localStorage.setItem('auth_token', data.token);
        localStorage.setItem('auth_user', JSON.stringify(data.user));

        console.log('✅ 登录成功');
        return true;
      } else {
        setError(data.message || '登录失败');
        return false;
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('网络错误，请重试');
      return false;
    }
  };

  // Register function
  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setError('');
      console.log('📝 Starting registration:', email);

      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, email, password }),
      });

      const data = await response.json();
      console.log('📡 Registration response:', data);

      if (data.success && data.user && data.session) {
        // 设置认证状态
        setUser(data.user);
        setToken(data.session.access_token);

        // 保存到localStorage
        localStorage.setItem('auth_token', data.session.access_token);
        localStorage.setItem('auth_user', JSON.stringify(data.user));

        console.log('✅ Registration successful');
        return true;
      } else {
        setError(data.message || 'Registration failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Registration error:', error);
      setError('网络错误，请重试');
      return false;
    }
  };

  // 登出函数
  const logout = () => {
    console.log('🚪 用户登出');
    setUser(null);
    setToken(null);
    setError('');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
  };

  // 验证token有效性
  const verifyToken = async (token: string): Promise<boolean> => {
    try {
      const response = await fetch(`${API_URL}/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user) {
          setUser(data.user);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ Token验证失败:', error);
      return false;
    }
  };

  // 初始化认证状态（从localStorage恢复）
  React.useEffect(() => {
    const savedToken = localStorage.getItem('auth_token');
    const savedUser = localStorage.getItem('auth_user');

    if (savedToken && savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setToken(savedToken);
        console.log('🔄 从localStorage恢复认证状态');

        // 验证token是否仍然有效
        verifyToken(savedToken).then((isValid) => {
          if (isValid) {
            console.log('✅ Token验证成功');
          } else {
            console.log('❌ Token已失效，清除认证状态');
            logout();
          }
        });
      } catch (error) {
        console.error('❌ 恢复认证状态失败:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      }
    }
  }, []);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    login,
    register,
    logout,
    error,
    setError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// useAuth hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}