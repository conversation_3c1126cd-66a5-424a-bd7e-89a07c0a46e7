'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { apiService, setTokens, clearTokens, getToken, User } from '../services/apiService';
import { toast } from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { identifier: string; password: string; rememberMe?: boolean }) => Promise<void>;
  register: (userData: {
    username: string;
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  resendVerification: (email: string) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  checkPasswordStrength: (password: string) => Promise<any>;
}

const EnhancedAuthContext = createContext<AuthContextType | undefined>(undefined);

export const useEnhancedAuth = () => {
  const context = useContext(EnhancedAuthContext);
  if (context === undefined) {
    throw new Error('useEnhancedAuth must be used within an EnhancedAuthProvider');
  }
  return context;
};

export const EnhancedAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      const token = getToken();
      if (token) {
        try {
          const response = await apiService.getMe();
          if (response.success && response.data) {
            setUser(response.data.user);
          } else {
            clearTokens();
          }
        } catch (error) {
          console.error('Failed to get user info:', error);
          clearTokens();
        }
      }
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (credentials: { identifier: string; password: string; rememberMe?: boolean }) => {
    try {
      setIsLoading(true);
      const response = await apiService.login(credentials);
      
      if (response.success && response.data) {
        const { user, tokens } = response.data;
        setTokens(tokens.accessToken, tokens.refreshToken);
        setUser(user);
        
        // Store user data
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(user));
        }
        
        toast.success('Login successful!');
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.response?.data?.message || error.message || 'Login failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: {
    username: string;
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) => {
    try {
      setIsLoading(true);
      const response = await apiService.register(userData);
      
      if (response.success && response.data) {
        const { user, tokens } = response.data;
        setTokens(tokens.accessToken, tokens.refreshToken);
        setUser(user);
        
        // Store user data
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(user));
        }
        
        toast.success('Registration successful! Please check your email to verify your account.');
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.response?.data?.message || error.message || 'Registration failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearTokens();
      setUser(null);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user');
      }
      toast.success('Logged out successfully');
    }
  }, []);

  const updateUser = useCallback(async (userData: Partial<User>) => {
    try {
      const response = await apiService.updateProfile(userData);
      
      if (response.success && response.data) {
        const updatedUser = response.data.user;
        setUser(updatedUser);
        
        // Update stored user data
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(updatedUser));
        }
        
        toast.success('Profile updated successfully');
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (error: any) {
      console.error('Update user error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to update profile');
      throw error;
    }
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      const response = await apiService.getMe();
      
      if (response.success && response.data) {
        const updatedUser = response.data.user;
        setUser(updatedUser);
        
        // Update stored user data
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(updatedUser));
        }
      }
    } catch (error) {
      console.error('Refresh user error:', error);
    }
  }, []);

  const forgotPassword = useCallback(async (email: string) => {
    try {
      const response = await apiService.forgotPassword(email);
      
      if (response.success) {
        toast.success('Password reset instructions sent to your email');
      } else {
        throw new Error(response.message || 'Failed to send reset email');
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to send reset email');
      throw error;
    }
  }, []);

  const resetPassword = useCallback(async (token: string, newPassword: string) => {
    try {
      const response = await apiService.resetPassword(token, newPassword);
      
      if (response.success) {
        toast.success('Password reset successfully');
      } else {
        throw new Error(response.message || 'Failed to reset password');
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to reset password');
      throw error;
    }
  }, []);

  const verifyEmail = useCallback(async (token: string) => {
    try {
      const response = await apiService.verifyEmail(token);
      
      if (response.success) {
        toast.success('Email verified successfully');
        // Refresh user data to update verification status
        await refreshUser();
      } else {
        throw new Error(response.message || 'Failed to verify email');
      }
    } catch (error: any) {
      console.error('Verify email error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to verify email');
      throw error;
    }
  }, [refreshUser]);

  const resendVerification = useCallback(async (email: string) => {
    try {
      const response = await apiService.resendVerification(email);
      
      if (response.success) {
        toast.success('Verification email sent successfully');
      } else {
        throw new Error(response.message || 'Failed to send verification email');
      }
    } catch (error: any) {
      console.error('Resend verification error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to send verification email');
      throw error;
    }
  }, []);

  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    try {
      const response = await apiService.changePassword(currentPassword, newPassword);
      
      if (response.success) {
        toast.success('Password changed successfully');
      } else {
        throw new Error(response.message || 'Failed to change password');
      }
    } catch (error: any) {
      console.error('Change password error:', error);
      toast.error(error.response?.data?.message || error.message || 'Failed to change password');
      throw error;
    }
  }, []);

  const checkPasswordStrength = useCallback(async (password: string) => {
    try {
      const response = await apiService.checkPasswordStrength(password);
      return response.data;
    } catch (error: any) {
      console.error('Check password strength error:', error);
      throw error;
    }
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
    changePassword,
    checkPasswordStrength,
  };

  return <EnhancedAuthContext.Provider value={value}>{children}</EnhancedAuthContext.Provider>;
};
