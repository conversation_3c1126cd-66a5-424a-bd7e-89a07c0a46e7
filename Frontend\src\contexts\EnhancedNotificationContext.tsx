'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { apiService, Notification } from '../services/apiService';
import { useEnhancedAuth } from './EnhancedAuthContext';
import { useWebSocket } from './WebSocketContext';
import { toast } from 'react-hot-toast';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  settings: any;
  fetchNotifications: (params?: any) => Promise<void>;
  markAsRead: (id: number) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: number) => Promise<void>;
  updateSettings: (settings: any) => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

const EnhancedNotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useEnhancedNotifications = () => {
  const context = useContext(EnhancedNotificationContext);
  if (context === undefined) {
    throw new Error('useEnhancedNotifications must be used within an EnhancedNotificationProvider');
  }
  return context;
};

export const EnhancedNotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useEnhancedAuth();
  const { subscribe } = useWebSocket();
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState<any>({});

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  const fetchNotifications = useCallback(async (params: any = {}) => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      const response = await apiService.getNotifications({
        page: 1,
        limit: 50,
        ...params
      });

      if (response.success && response.data) {
        setNotifications(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  const markAsRead = useCallback(async (id: number) => {
    try {
      const response = await apiService.markNotificationAsRead(id);
      
      if (response.success) {
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: true }
              : notification
          )
        );
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await apiService.markAllNotificationsAsRead();
      
      if (response.success) {
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, read: true }))
        );
        toast.success('All notifications marked as read');
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  }, []);

  const deleteNotification = useCallback(async (id: number) => {
    try {
      const response = await apiService.deleteNotification(id);
      
      if (response.success) {
        setNotifications(prev => prev.filter(notification => notification.id !== id));
        toast.success('Notification deleted');
      }
    } catch (error) {
      console.error('Failed to delete notification:', error);
      toast.error('Failed to delete notification');
    }
  }, []);

  const fetchSettings = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const response = await apiService.getNotificationSettings();
      
      if (response.success && response.data) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch notification settings:', error);
    }
  }, [isAuthenticated]);

  const updateSettings = useCallback(async (newSettings: any) => {
    try {
      const response = await apiService.updateNotificationSettings(newSettings);
      
      if (response.success) {
        setSettings(newSettings);
        toast.success('Notification settings updated');
      }
    } catch (error) {
      console.error('Failed to update notification settings:', error);
      toast.error('Failed to update notification settings');
    }
  }, []);

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications();
  }, [fetchNotifications]);

  // Handle real-time notifications from WebSocket
  useEffect(() => {
    const unsubscribe = subscribe('notification', (data: any) => {
      // Add new notification to the list
      setNotifications(prev => [data, ...prev]);
      
      // Show browser notification if permission granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(data.title, {
          body: data.message,
          icon: '/favicon.ico',
          tag: `notification-${data.id}`
        });
      }
    });

    return unsubscribe;
  }, [subscribe]);

  // Handle notification updates from WebSocket
  useEffect(() => {
    const unsubscribe = subscribe('notification_update', (data: any) => {
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === data.id 
            ? { ...notification, ...data }
            : notification
        )
      );
    });

    return unsubscribe;
  }, [subscribe]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          toast.success('Browser notifications enabled');
        }
      });
    }
  }, []);

  // Fetch notifications and settings on mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchNotifications();
      fetchSettings();
    } else {
      setNotifications([]);
      setSettings({});
    }
  }, [isAuthenticated, fetchNotifications, fetchSettings]);

  // Periodic refresh of notifications
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      fetchNotifications();
    }, 60000); // Refresh every minute

    return () => clearInterval(interval);
  }, [isAuthenticated, fetchNotifications]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isLoading,
    settings,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updateSettings,
    refreshNotifications,
  };

  return <EnhancedNotificationContext.Provider value={value}>{children}</EnhancedNotificationContext.Provider>;
};
