'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 类型定义
interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
}

interface TranslationParams {
  [key: string]: string | number;
}

interface InternationalizationContextType {
  currentLanguage: string;
  languages: Language[];
  translations: Record<string, string>;
  isRTL: boolean;
  isLoading: boolean;
  changeLanguage: (languageCode: string) => Promise<void>;
  t: (key: string, params?: TranslationParams) => string;
  detectUserLanguage: () => Promise<string>;
  translateText: (text: string, targetLanguage?: string) => Promise<string>;
  formatDateTime: (date: Date | string, options?: Intl.DateTimeFormatOptions) => string;
}

const InternationalizationContext = createContext<InternationalizationContextType | undefined>(undefined);

// API基础URL
const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface InternationalizationProviderProps {
  children: ReactNode;
  defaultLanguage?: string;
}

export function InternationalizationProvider({ 
  children, 
  defaultLanguage = 'en' 
}: InternationalizationProviderProps) {
  const [currentLanguage, setCurrentLanguage] = useState<string>(defaultLanguage);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isRTL, setIsRTL] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 初始化
  useEffect(() => {
    initializeI18n();
  }, []);

  // 语言变化时更新翻译
  useEffect(() => {
    if (currentLanguage) {
      loadTranslations(currentLanguage);
    }
  }, [currentLanguage]);

  // 初始化国际化
  const initializeI18n = async () => {
    try {
      setIsLoading(true);
      
      // 获取支持的语言列表
      await loadSupportedLanguages();
      
      // 检测用户语言或使用保存的语言
      const savedLanguage = localStorage.getItem('newzora_language');
      const detectedLanguage = savedLanguage || await detectUserLanguage();
      
      // 设置语言
      await changeLanguage(detectedLanguage);
      
    } catch (error) {
      console.error('Failed to initialize i18n:', error);
      // 使用默认语言
      await changeLanguage(defaultLanguage);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载支持的语言
  const loadSupportedLanguages = async () => {
    try {
      const response = await fetch(`${API_BASE}/i18n/languages`);
      const data = await response.json();
      
      if (data.success) {
        setLanguages(data.data.languages);
      }
    } catch (error) {
      console.error('Failed to load supported languages:', error);
      // 使用默认语言列表
      setLanguages([
        { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸', rtl: false },
        { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳', rtl: false },
        { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸', rtl: false },
        { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷', rtl: false },
        { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', rtl: false },
        { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵', rtl: false },
        { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷', rtl: false },
        { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true }
      ]);
    }
  };

  // 检测用户语言
  const detectUserLanguage = async (): Promise<string> => {
    try {
      const response = await fetch(`${API_BASE}/i18n/detect-language`);
      const data = await response.json();
      
      if (data.success) {
        return data.data.detectedLanguage;
      }
    } catch (error) {
      console.error('Failed to detect user language:', error);
    }
    
    return defaultLanguage;
  };

  // 加载翻译
  const loadTranslations = async (languageCode: string) => {
    try {
      const response = await fetch(`${API_BASE}/i18n/translations/${languageCode}`);
      const data = await response.json();
      
      if (data.success) {
        setTranslations(data.data.translations);
        setIsRTL(data.data.isRTL);
        
        // 更新HTML方向
        document.documentElement.dir = data.data.isRTL ? 'rtl' : 'ltr';
        document.documentElement.lang = languageCode;
      }
    } catch (error) {
      console.error('Failed to load translations:', error);
      // 使用默认翻译
      setTranslations(getDefaultTranslations(languageCode));
    }
  };

  // 获取默认翻译
  const getDefaultTranslations = (languageCode: string): Record<string, string> => {
    const defaultTranslations: Record<string, Record<string, string>> = {
      en: {
        'app.name': 'Newzora',
        'nav.home': 'Home',
        'nav.explore': 'Explore',
        'nav.create': 'Create',
        'nav.profile': 'Profile',
        'nav.settings': 'Settings',
        'nav.login': 'Login',
        'nav.logout': 'Logout',
        'nav.signup': 'Sign Up',
        'common.loading': 'Loading...',
        'common.error': 'Error',
        'common.success': 'Success',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.save': 'Save',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.share': 'Share',
        'common.like': 'Like',
        'common.comment': 'Comment',
        'common.follow': 'Follow',
        'common.unfollow': 'Unfollow'
      },
      zh: {
        'app.name': 'Newzora',
        'nav.home': '首页',
        'nav.explore': '探索',
        'nav.create': '创作',
        'nav.profile': '个人资料',
        'nav.settings': '设置',
        'nav.login': '登录',
        'nav.logout': '退出',
        'nav.signup': '注册',
        'common.loading': '加载中...',
        'common.error': '错误',
        'common.success': '成功',
        'common.cancel': '取消',
        'common.confirm': '确认',
        'common.save': '保存',
        'common.delete': '删除',
        'common.edit': '编辑',
        'common.share': '分享',
        'common.like': '点赞',
        'common.comment': '评论',
        'common.follow': '关注',
        'common.unfollow': '取消关注'
      }
    };

    return defaultTranslations[languageCode] || defaultTranslations.en;
  };

  // 更改语言
  const changeLanguage = async (languageCode: string) => {
    try {
      setCurrentLanguage(languageCode);
      
      // 保存到本地存储
      localStorage.setItem('newzora_language', languageCode);
      
      // 加载翻译
      await loadTranslations(languageCode);
      
      console.log(`Language changed to: ${languageCode}`);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  // 翻译函数
  const t = (key: string, params?: TranslationParams): string => {
    let translation = translations[key] || key;
    
    // 参数替换
    if (params) {
      Object.keys(params).forEach(param => {
        translation = translation.replace(`{${param}}`, String(params[param]));
      });
    }
    
    return translation;
  };

  // 翻译文本
  const translateText = async (text: string, targetLanguage?: string): Promise<string> => {
    try {
      const target = targetLanguage || currentLanguage;
      
      const response = await fetch(`${API_BASE}/i18n/translate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          targetLanguage: target,
          sourceLanguage: 'auto'
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        return data.data.translatedText;
      }
    } catch (error) {
      console.error('Failed to translate text:', error);
    }
    
    return text;
  };

  // 格式化日期时间
  const formatDateTime = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      const language = languages.find(l => l.code === currentLanguage);
      const locale = getLocaleFromLanguage(currentLanguage);
      
      const defaultOptions: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      
      return dateObj.toLocaleDateString(locale, { ...defaultOptions, ...options });
    } catch (error) {
      console.error('Failed to format date:', error);
      return date.toString();
    }
  };

  // 获取语言对应的区域设置
  const getLocaleFromLanguage = (languageCode: string): string => {
    const localeMap: Record<string, string> = {
      'en': 'en-US',
      'zh': 'zh-CN',
      'zh-TW': 'zh-TW',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN',
      'pt': 'pt-PT',
      'it': 'it-IT'
    };
    
    return localeMap[languageCode] || 'en-US';
  };

  const value: InternationalizationContextType = {
    currentLanguage,
    languages,
    translations,
    isRTL,
    isLoading,
    changeLanguage,
    t,
    detectUserLanguage,
    translateText,
    formatDateTime
  };

  return (
    <InternationalizationContext.Provider value={value}>
      {children}
    </InternationalizationContext.Provider>
  );
}

// Hook for using internationalization
export function useInternationalization() {
  const context = useContext(InternationalizationContext);
  if (context === undefined) {
    throw new Error('useInternationalization must be used within an InternationalizationProvider');
  }
  return context;
}

// Convenience hook for translation
export function useTranslation() {
  const { t, currentLanguage, isRTL } = useInternationalization();
  return { t, currentLanguage, isRTL };
}
