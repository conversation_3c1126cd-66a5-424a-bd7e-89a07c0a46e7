'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { useEnhancedAuth } from './EnhancedAuthContext';
import { toast } from 'react-hot-toast';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

interface WebSocketContextType {
  isConnected: boolean;
  sendMessage: (type: string, data: any) => void;
  subscribe: (type: string, callback: (data: any) => void) => () => void;
  unsubscribe: (type: string, callback: (data: any) => void) => void;
  onlineUsers: number;
  notifications: any[];
  markNotificationAsRead: (id: number) => void;
  clearNotifications: () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useEnhancedAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(0);
  const [notifications, setNotifications] = useState<any[]>([]);
  
  const wsRef = useRef<WebSocket | null>(null);
  const subscribersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:5000';

  const connect = useCallback(() => {
    if (!isAuthenticated || !user) {
      return;
    }

    try {
      const token = localStorage.getItem('accessToken');
      const wsUrl = `${WS_URL}?token=${token}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        reconnectAttempts.current = 0;
        
        // Send user identification
        if (wsRef.current) {
          wsRef.current.send(JSON.stringify({
            type: 'user_connect',
            data: { userId: user.id }
          }));
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000; // Exponential backoff
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`);
            connect();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }, [isAuthenticated, user, WS_URL]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'User logout');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setOnlineUsers(0);
    setNotifications([]);
  }, []);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    const { type, data } = message;

    switch (type) {
      case 'notification':
        setNotifications(prev => [data, ...prev]);
        
        // Show toast notification
        if (data.type === 'message') {
          toast.success(`New message from ${data.senderName}`);
        } else if (data.type === 'like') {
          toast.success('Someone liked your article!');
        } else if (data.type === 'comment') {
          toast.success('New comment on your article!');
        } else if (data.type === 'follow') {
          toast.success(`${data.senderName} started following you!`);
        }
        break;

      case 'online_users_count':
        setOnlineUsers(data.count);
        break;

      case 'user_status_update':
        // Handle user status updates (online/offline)
        break;

      case 'article_update':
        // Handle real-time article updates
        break;

      case 'comment_update':
        // Handle real-time comment updates
        break;

      case 'system_announcement':
        toast.info(data.message);
        break;

      default:
        // Handle custom message types
        const subscribers = subscribersRef.current.get(type);
        if (subscribers) {
          subscribers.forEach(callback => callback(data));
        }
        break;
    }
  }, []);

  const sendMessage = useCallback((type: string, data: any) => {
    if (wsRef.current && isConnected) {
      const message = {
        type,
        data,
        timestamp: new Date().toISOString()
      };
      
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, [isConnected]);

  const subscribe = useCallback((type: string, callback: (data: any) => void) => {
    if (!subscribersRef.current.has(type)) {
      subscribersRef.current.set(type, new Set());
    }
    
    subscribersRef.current.get(type)!.add(callback);

    // Return unsubscribe function
    return () => {
      const subscribers = subscribersRef.current.get(type);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          subscribersRef.current.delete(type);
        }
      }
    };
  }, []);

  const unsubscribe = useCallback((type: string, callback: (data: any) => void) => {
    const subscribers = subscribersRef.current.get(type);
    if (subscribers) {
      subscribers.delete(callback);
      if (subscribers.size === 0) {
        subscribersRef.current.delete(type);
      }
    }
  }, []);

  const markNotificationAsRead = useCallback((id: number) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );

    // Send read status to server
    sendMessage('mark_notification_read', { notificationId: id });
  }, [sendMessage]);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
    sendMessage('clear_notifications', {});
  }, [sendMessage]);

  // Connect/disconnect based on authentication status
  useEffect(() => {
    if (isAuthenticated && user) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, user, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Ping/pong to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      if (wsRef.current && isConnected) {
        sendMessage('ping', {});
      }
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected, sendMessage]);

  const value: WebSocketContextType = {
    isConnected,
    sendMessage,
    subscribe,
    unsubscribe,
    onlineUsers,
    notifications,
    markNotificationAsRead,
    clearNotifications,
  };

  return <WebSocketContext.Provider value={value}>{children}</WebSocketContext.Provider>;
};
