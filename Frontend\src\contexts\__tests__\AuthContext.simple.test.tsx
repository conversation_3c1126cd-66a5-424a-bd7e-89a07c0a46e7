import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider, useAuth } from '../AuthContext'

// Simple test component
const TestComponent = () => {
  const { user, isAuthenticated, login, logout, error } = useAuth()
  
  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user-info">
        {user ? `User: ${user.username}` : 'No user'}
      </div>
      <div data-testid="error-message">{error}</div>
      <button 
        data-testid="login-button" 
        onClick={() => login('<EMAIL>', 'password123')}
      >
        Login
      </button>
      <button data-testid="logout-button" onClick={logout}>
        Logout
      </button>
    </div>
  )
}

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  )
}

describe('AuthContext - Simple Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    window.localStorage.clear()
    // Reset fetch mock
    global.fetch = jest.fn()
  })

  it('should render with initial unauthenticated state', () => {
    renderWithAuthProvider()
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
    expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
    expect(screen.getByTestId('error-message')).toHaveTextContent('')
  })

  it('should handle successful login', async () => {
    const user = userEvent.setup()
    const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' }
    const mockToken = 'mock-jwt-token'
    
    // Mock successful API response
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        user: mockUser,
        token: mockToken
      })
    })
    
    renderWithAuthProvider()
    
    await act(async () => {
      await user.click(screen.getByTestId('login-button'))
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: testuser')
    })
  })

  it('should handle login failure', async () => {
    const user = userEvent.setup()
    
    // Mock failed API response
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: false,
        message: 'Invalid credentials'
      })
    })
    
    renderWithAuthProvider()
    
    await act(async () => {
      await user.click(screen.getByTestId('login-button'))
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid credentials')
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
    })
  })

  it('should handle network error', async () => {
    const user = userEvent.setup()
    
    // Mock network error
    global.fetch.mockRejectedValueOnce(new Error('Network error'))
    
    renderWithAuthProvider()
    
    await act(async () => {
      await user.click(screen.getByTestId('login-button'))
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('网络错误，请重试')
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
    })
  })

  it('should logout successfully', async () => {
    const user = userEvent.setup()
    
    // Setup initial authenticated state
    const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' }
    const mockToken = 'mock-jwt-token'
    
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        user: mockUser,
        token: mockToken
      })
    })
    
    renderWithAuthProvider()
    
    // Login first
    await act(async () => {
      await user.click(screen.getByTestId('login-button'))
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
    })
    
    // Then logout
    await act(async () => {
      await user.click(screen.getByTestId('logout-button'))
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
      expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
    })
  })

  it('should make correct API call for login', async () => {
    const user = userEvent.setup()
    
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, user: {}, token: 'token' })
    })
    
    renderWithAuthProvider()
    
    await act(async () => {
      await user.click(screen.getByTestId('login-button'))
    })
    
    expect(global.fetch).toHaveBeenCalledWith('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>', password: 'password123' })
    })
  })
})
