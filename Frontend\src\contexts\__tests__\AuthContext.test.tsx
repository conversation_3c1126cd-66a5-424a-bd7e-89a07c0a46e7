import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider, useAuth } from '../AuthContext'

// Test component to access AuthContext
const TestComponent = () => {
  const { user, isAuthenticated, login, logout, error } = useAuth()
  
  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user-info">
        {user ? `User: ${user.username}` : 'No user'}
      </div>
      <div data-testid="error-message">{error}</div>
      <button 
        data-testid="login-button" 
        onClick={() => login('<EMAIL>', 'password123')}
      >
        Login
      </button>
      <button data-testid="logout-button" onClick={logout}>
        Logout
      </button>
    </div>
  )
}

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
    // Reset fetch mock
    fetch.mockClear()
  })

  describe('Initial State', () => {
    it('should render with initial unauthenticated state', () => {
      renderWithAuthProvider()
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
      expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
      expect(screen.getByTestId('error-message')).toHaveTextContent('')
    })

    it('should restore user from localStorage on mount', () => {
      // Setup localStorage with user data
      const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' }
      const mockToken = 'mock-jwt-token'
      
      localStorage.setItem('auth_token', mockToken)
      localStorage.setItem('auth_user', JSON.stringify(mockUser))
      
      renderWithAuthProvider()
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: testuser')
    })
  })

  describe('Login Functionality', () => {
    it('should login successfully with valid credentials', async () => {
      const user = userEvent.setup()
      const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' }
      const mockToken = 'mock-jwt-token'
      
      // Mock successful API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          user: mockUser,
          token: mockToken
        })
      })
      
      renderWithAuthProvider()
      
      await act(async () => {
        await user.click(screen.getByTestId('login-button'))
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
        expect(screen.getByTestId('user-info')).toHaveTextContent('User: testuser')
      })
      
      // Verify localStorage was updated
      expect(localStorage.setItem).toHaveBeenCalledWith('auth_token', mockToken)
      expect(localStorage.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(mockUser))
    })

    it('should handle login failure with error message', async () => {
      const user = userEvent.setup()
      
      // Mock failed API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: false,
          message: 'Invalid credentials'
        })
      })
      
      renderWithAuthProvider()
      
      await act(async () => {
        await user.click(screen.getByTestId('login-button'))
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid credentials')
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
      })
    })

    it('should handle network error during login', async () => {
      const user = userEvent.setup()
      
      // Mock network error
      fetch.mockRejectedValueOnce(new Error('Network error'))
      
      renderWithAuthProvider()
      
      await act(async () => {
        await user.click(screen.getByTestId('login-button'))
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('网络错误，请重试')
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
      })
    })
  })

  describe('Logout Functionality', () => {
    it('should logout successfully and clear user data', async () => {
      const user = userEvent.setup()
      const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' }
      const mockToken = 'mock-jwt-token'
      
      // Setup initial authenticated state
      localStorage.setItem('auth_token', mockToken)
      localStorage.setItem('auth_user', JSON.stringify(mockUser))
      
      renderWithAuthProvider()
      
      // Verify initial authenticated state
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
      
      await act(async () => {
        await user.click(screen.getByTestId('logout-button'))
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')
        expect(screen.getByTestId('user-info')).toHaveTextContent('No user')
      })
      
      // Verify localStorage was cleared
      expect(localStorage.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorage.removeItem).toHaveBeenCalledWith('auth_user')
    })
  })

  describe('API Integration', () => {
    it('should make correct API call for login', async () => {
      const user = userEvent.setup()
      
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, user: {}, token: 'token' })
      })
      
      renderWithAuthProvider()
      
      await act(async () => {
        await user.click(screen.getByTestId('login-button'))
      })
      
      expect(fetch).toHaveBeenCalledWith('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: '<EMAIL>', password: 'password123' })
      })
    })
  })

  describe('Error Handling', () => {
    it('should throw error when useAuth is used outside AuthProvider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      expect(() => {
        render(<TestComponent />)
      }).toThrow('useAuth must be used within an AuthProvider')
      
      consoleSpy.mockRestore()
    })
  })
})
