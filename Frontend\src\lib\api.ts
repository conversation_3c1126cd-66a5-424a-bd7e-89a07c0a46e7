// API service for Newzora admin interface
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      const response = await fetch(url, {
        headers: this.getAuthHeaders(),
        ...options
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Dashboard APIs
  async getDashboardStats() {
    return this.request<{
      users: { total: number; active: number; newToday: number; verified: number };
      content: { articles: number; drafts: number; comments: number; pendingReview: number };
      system: { emailsSent: number; activeConnections: number; serverUptime: string; databaseStatus: string };
    }>('/admin/dashboard/stats');
  }

  async getDashboardActivity() {
    return this.request<{
      recentUsers: any[];
      recentArticles: any[];
      recentComments: any[];
    }>('/admin/dashboard/activity');
  }

  async getSystemHealth() {
    return this.request<{
      status: string;
      timestamp: string;
      database: { status: string; latency: number };
      memory: any;
      cpu: any;
      uptime: number;
    }>('/admin/dashboard/health');
  }

  // User Management APIs
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    return this.request<any[]>(`/admin/users?${queryParams.toString()}`);
  }

  async getUser(userId: string) {
    return this.request<any>(`/admin/users/${userId}`);
  }

  async updateUser(userId: string, data: any) {
    return this.request<any>(`/admin/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async banUser(userId: string, reason: string) {
    return this.request<any>(`/admin/users/${userId}/ban`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    });
  }

  async unbanUser(userId: string) {
    return this.request<any>(`/admin/users/${userId}/unban`, {
      method: 'POST'
    });
  }

  async deleteUser(userId: string) {
    return this.request<any>(`/admin/users/${userId}`, {
      method: 'DELETE'
    });
  }

  // Role and Permission APIs
  async getRoles() {
    return this.request<any[]>('/roles');
  }

  async getRole(roleId: string) {
    return this.request<any>(`/roles/${roleId}`);
  }

  async createRole(data: any) {
    return this.request<any>('/roles', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async updateRole(roleId: string, data: any) {
    return this.request<any>(`/roles/${roleId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteRole(roleId: string) {
    return this.request<any>(`/roles/${roleId}`, {
      method: 'DELETE'
    });
  }

  async getPermissions() {
    return this.request<any[]>('/permissions');
  }

  async getRolePermissions(roleId: string) {
    return this.request<any[]>(`/roles/${roleId}/permissions`);
  }

  async updateRolePermissions(roleId: string, permissionIds: string[]) {
    return this.request<any>(`/roles/${roleId}/permissions`, {
      method: 'PUT',
      body: JSON.stringify({ permissionIds })
    });
  }

  // Content Management APIs
  async getArticles(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    authorId?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    return this.request<any[]>(`/articles?${queryParams.toString()}`);
  }

  async getArticle(articleId: string) {
    return this.request<any>(`/articles/${articleId}`);
  }

  async updateArticle(articleId: string, data: any) {
    return this.request<any>(`/articles/${articleId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteArticle(articleId: string) {
    return this.request<any>(`/articles/${articleId}`, {
      method: 'DELETE'
    });
  }

  async approveArticle(articleId: string) {
    return this.request<any>(`/admin/articles/${articleId}/approve`, {
      method: 'POST'
    });
  }

  async rejectArticle(articleId: string, reason: string) {
    return this.request<any>(`/admin/articles/${articleId}/reject`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    });
  }

  // Email Service APIs
  async getEmailStats() {
    return this.request<any>('/email/stats');
  }

  async sendTestEmail(email: string, template: string) {
    return this.request<any>('/email/test', {
      method: 'POST',
      body: JSON.stringify({ email, template })
    });
  }

  // File Upload APIs
  async uploadFile(file: File, type: 'image' | 'document' = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    
    return fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData
    }).then(response => response.json());
  }

  // Analytics APIs
  async getUserAnalytics(days: number = 30) {
    return this.request<any>(`/admin/dashboard/analytics/users?days=${days}`);
  }

  async getContentAnalytics(days: number = 30) {
    return this.request<any>(`/admin/dashboard/analytics/content?days=${days}`);
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Export types for use in components
export type { ApiResponse };

// Utility functions for error handling
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unexpected error occurred';
};

export const isApiError = (error: any): boolean => {
  return error && (error.message || typeof error === 'string');
};
