import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wdpprzemflzlardkmncfk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndkcHByemVmbHpsYXJka21uY2ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDk4MjgsImV4cCI6MjA2NzY4NTgyOH0.yp_k9Sv6AMFZmUs_EWa_-rPZGyTxNNFTZOM4RaU668s';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// 认证相关的辅助函数
export const auth = {
  // 注册用户
  async signUp(email: string, password: string, userData?: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    });
    return { data, error };
  },

  // 登录用户
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  },

  // 登出用户
  async signOut() {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // 获取当前用户
  async getUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  // 获取当前会话
  async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession();
    return { session, error };
  },

  // 重置密码
  async resetPassword(email: string) {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    });
    return { data, error };
  },

  // 更新密码
  async updatePassword(password: string) {
    const { data, error } = await supabase.auth.updateUser({
      password
    });
    return { data, error };
  },

  // 监听认证状态变化
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
};

// 数据库操作辅助函数
export const db = {
  // 用户相关操作
  users: {
    async getProfile(userId: string) {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      return { data, error };
    },

    async updateProfile(userId: string, updates: any) {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();
      return { data, error };
    },

    async checkUsername(username: string) {
      const { data, error } = await supabase
        .from('users')
        .select('username')
        .eq('username', username)
        .single();
      return { exists: !!data && !error, error };
    },

    async search(query: string, limit = 10) {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, display_name, avatar_url, is_verified')
        .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
        .eq('is_active', true)
        .order('followers_count', { ascending: false })
        .limit(limit);
      return { data, error };
    }
  },

  // 文章相关操作
  articles: {
    async getAll(limit = 20, offset = 0) {
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          author:users(id, username, display_name, avatar_url, is_verified)
        `)
        .eq('published', true)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      return { data, error };
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          author:users(id, username, display_name, avatar_url, is_verified)
        `)
        .eq('id', id)
        .eq('published', true)
        .single();
      return { data, error };
    },

    async getBySlug(slug: string) {
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          author:users(id, username, display_name, avatar_url, is_verified)
        `)
        .eq('slug', slug)
        .eq('published', true)
        .single();
      return { data, error };
    },

    async create(article: any) {
      const { data, error } = await supabase
        .from('articles')
        .insert(article)
        .select()
        .single();
      return { data, error };
    },

    async update(id: string, updates: any) {
      const { data, error } = await supabase
        .from('articles')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('articles')
        .delete()
        .eq('id', id);
      return { error };
    },

    async incrementViews(id: string) {
      const { error } = await supabase.rpc('increment_article_views', {
        article_id: id
      });
      return { error };
    }
  },

  // 评论相关操作
  comments: {
    async getByArticle(articleId: string) {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          author:users(id, username, display_name, avatar_url, is_verified)
        `)
        .eq('article_id', articleId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true });
      return { data, error };
    },

    async create(comment: any) {
      const { data, error } = await supabase
        .from('comments')
        .insert(comment)
        .select(`
          *,
          author:users(id, username, display_name, avatar_url, is_verified)
        `)
        .single();
      return { data, error };
    },

    async update(id: string, updates: any) {
      const { data, error } = await supabase
        .from('comments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('comments')
        .update({ is_deleted: true, deleted_at: new Date().toISOString() })
        .eq('id', id);
      return { error };
    }
  }
};

export default supabase;
