'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { apiService, Article } from '../services/apiService';
import { useEnhancedAuth } from '../contexts/EnhancedAuthContext';
import AppLayout from '../components/layout/AppLayout';
import RichTextEditor from '../components/editor/RichTextEditor';
import { toast } from 'react-hot-toast';
import {
  FaSave,
  FaEye,
  FaUpload,
  FaTags,
  FaFolder,
  FaCalendar,
  FaGlobe,
  FaLock,
  FaTrash
} from 'react-icons/fa';

const ArticleEditor: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useEnhancedAuth();
  
  const [article, setArticle] = useState<Partial<Article>>({
    title: '',
    content: '',
    excerpt: '',
    featuredImage: '',
    category: '',
    tags: [],
    published: false
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDraft, setIsDraft] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);
  const [availableTags, setAvailableTags] = useState<any[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  const isEditing = !!id;

  // Fetch article data if editing
  useEffect(() => {
    if (isEditing) {
      fetchArticle();
    }
  }, [id, isEditing]);

  // Fetch categories and tags
  useEffect(() => {
    fetchCategories();
    fetchTags();
  }, []);

  const fetchArticle = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const response = await apiService.getArticle(parseInt(id));
      
      if (response.success && response.data) {
        setArticle(response.data);
        setIsDraft(!response.data.published);
      } else {
        toast.error('Article not found');
        navigate('/articles');
      }
    } catch (error) {
      console.error('Failed to fetch article:', error);
      toast.error('Failed to load article');
      navigate('/articles');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await apiService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const fetchTags = async () => {
    try {
      const response = await apiService.getTags({ limit: 100 });
      if (response.success && response.data) {
        setAvailableTags(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch tags:', error);
    }
  };

  const handleSave = async (publish = false) => {
    if (!article.title?.trim()) {
      toast.error('Please enter a title');
      return;
    }

    if (!article.content?.trim()) {
      toast.error('Please enter content');
      return;
    }

    try {
      setIsSaving(true);
      
      const articleData = {
        ...article,
        published: publish,
        excerpt: article.excerpt || generateExcerpt(article.content || '')
      };

      let response;
      
      if (isEditing) {
        response = await apiService.updateArticle(parseInt(id!), articleData);
      } else {
        response = await apiService.createArticle(articleData);
      }

      if (response.success && response.data) {
        setArticle(response.data);
        setIsDraft(!publish);
        
        toast.success(
          publish 
            ? 'Article published successfully!' 
            : 'Article saved as draft'
        );

        if (!isEditing) {
          navigate(`/articles/${response.data.id}/edit`);
        }
      }
    } catch (error) {
      console.error('Failed to save article:', error);
      toast.error('Failed to save article');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAutoSave = useCallback(async () => {
    if (!autoSaveEnabled || !article.title?.trim() || !article.content?.trim()) {
      return;
    }

    try {
      const articleData = {
        ...article,
        published: false
      };

      if (isEditing) {
        await apiService.updateArticle(parseInt(id!), articleData);
      } else {
        const response = await apiService.createDraft(articleData);
        if (response.success && response.data) {
          navigate(`/articles/${response.data.id}/edit`, { replace: true });
        }
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [article, autoSaveEnabled, isEditing, id, navigate]);

  const handleDelete = async () => {
    if (!isEditing) return;

    if (!confirm('Are you sure you want to delete this article? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await apiService.deleteArticle(parseInt(id!));
      if (response.success) {
        toast.success('Article deleted successfully');
        navigate('/articles');
      }
    } catch (error) {
      console.error('Failed to delete article:', error);
      toast.error('Failed to delete article');
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      const response = await apiService.uploadFile(file, 'image');
      if (response.success && response.data) {
        setArticle(prev => ({ ...prev, featuredImage: response.data.url }));
        toast.success('Featured image uploaded successfully');
      }
    } catch (error) {
      console.error('Failed to upload image:', error);
      toast.error('Failed to upload image');
    }
  };

  const addTag = (tagName: string) => {
    if (!tagName.trim() || article.tags?.includes(tagName)) return;

    setArticle(prev => ({
      ...prev,
      tags: [...(prev.tags || []), tagName.trim()]
    }));
    setTagInput('');
  };

  const removeTag = (tagToRemove: string) => {
    setArticle(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const generateExcerpt = (content: string) => {
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.length > 160 
      ? textContent.substring(0, 160) + '...'
      : textContent;
  };

  if (isLoading) {
    return (
      <AppLayout title="Loading...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title={isEditing ? 'Edit Article' : 'New Article'}>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? 'Edit Article' : 'Create New Article'}
              </h1>
              {isDraft && (
                <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                  Draft
                </span>
              )}
            </div>

            <div className="flex items-center gap-2">
              <label className="flex items-center gap-2 text-sm text-gray-600">
                <input
                  type="checkbox"
                  checked={autoSaveEnabled}
                  onChange={(e) => setAutoSaveEnabled(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                Auto-save
              </label>

              <button
                onClick={() => setShowPreview(!showPreview)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <FaEye className="w-4 h-4 mr-2 inline" />
                {showPreview ? 'Edit' : 'Preview'}
              </button>

              <button
                onClick={() => handleSave(false)}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                <FaSave className="w-4 h-4 mr-2 inline" />
                Save Draft
              </button>

              <button
                onClick={() => handleSave(true)}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <FaGlobe className="w-4 h-4 mr-2 inline" />
                {article.published ? 'Update' : 'Publish'}
              </button>

              {isEditing && (
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
                >
                  <FaTrash className="w-4 h-4 mr-2 inline" />
                  Delete
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={article.title || ''}
                onChange={(e) => setArticle(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter article title..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Content Editor */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {showPreview ? (
                <div className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Preview</h3>
                  <div 
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{ __html: article.content || '' }}
                  />
                </div>
              ) : (
                <div className="p-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content *
                  </label>
                  <RichTextEditor
                    value={article.content || ''}
                    onChange={(content) => setArticle(prev => ({ ...prev, content }))}
                    onSave={handleAutoSave}
                    autoSave={autoSaveEnabled}
                    height="500px"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Featured Image */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              
              {article.featuredImage ? (
                <div className="relative">
                  <img
                    src={article.featuredImage}
                    alt="Featured"
                    className="w-full h-32 object-cover rounded-md"
                  />
                  <button
                    onClick={() => setArticle(prev => ({ ...prev, featuredImage: '' }))}
                    className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                  >
                    <FaTrash className="w-3 h-3" />
                  </button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                  <FaUpload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Upload featured image</p>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file);
                    }}
                    className="mt-2 text-sm"
                  />
                </div>
              )}
            </div>

            {/* Category */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaFolder className="w-4 h-4 inline mr-1" />
                Category
              </label>
              <select
                value={article.category || ''}
                onChange={(e) => setArticle(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaTags className="w-4 h-4 inline mr-1" />
                Tags
              </label>
              
              <div className="flex flex-wrap gap-2 mb-3">
                {article.tags?.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                  >
                    {tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>

              <div className="flex gap-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag(tagInput);
                    }
                  }}
                  placeholder="Add tag..."
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={() => addTag(tagInput)}
                  className="px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                >
                  Add
                </button>
              </div>

              {/* Suggested Tags */}
              {availableTags.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-gray-500 mb-2">Suggested:</p>
                  <div className="flex flex-wrap gap-1">
                    {availableTags.slice(0, 10).map(tag => (
                      <button
                        key={tag.id}
                        onClick={() => addTag(tag.name)}
                        className="px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Excerpt */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Excerpt
              </label>
              <textarea
                value={article.excerpt || ''}
                onChange={(e) => setArticle(prev => ({ ...prev, excerpt: e.target.value }))}
                placeholder="Brief description of the article..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                {article.excerpt?.length || 0}/160 characters
              </p>
            </div>

            {/* Publishing Options */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                <FaCalendar className="w-4 h-4 inline mr-1" />
                Publishing
              </h3>
              
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={article.published || false}
                    onChange={(e) => setArticle(prev => ({ ...prev, published: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Publish immediately
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default ArticleEditor;
