'use client';

import React, { useState } from 'react';
import { useEnhancedAuth } from '../contexts/EnhancedAuthContext';
import { toast } from 'react-hot-toast';
import { <PERSON>aU<PERSON>, FaLock, FaEnvelope, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';

// 测试账户数据
const testAccounts = [
  {
    username: 'admin_test',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    description: 'Administrator account with full permissions'
  },
  {
    username: 'moderator_test',
    email: '<EMAIL>',
    password: 'Moderator123!',
    role: 'moderator',
    description: 'Moderator account with content management permissions'
  },
  {
    username: 'editor_test',
    email: '<EMAIL>',
    password: 'Editor123!',
    role: 'user',
    description: 'Content editor account'
  },
  {
    username: 'user_test1',
    email: '<EMAIL>',
    password: 'User123!',
    role: 'user',
    description: 'Regular user account'
  },
  {
    username: 'user_test2',
    email: '<EMAIL>',
    password: 'User123!',
    role: 'user',
    description: 'Another regular user account'
  }
];

const TestAuth: React.FC = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    forgotPassword,
    checkPasswordStrength
  } = useEnhancedAuth();

  const [activeTab, setActiveTab] = useState<'login' | 'register' | 'accounts'>('accounts');
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<any>(null);

  // Login form state
  const [loginForm, setLoginForm] = useState({
    identifier: '',
    password: '',
    rememberMe: false
  });

  // Register form state
  const [registerForm, setRegisterForm] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  });

  const handleQuickLogin = async (account: any) => {
    try {
      await login({
        identifier: account.email,
        password: account.password,
        rememberMe: false
      });
      toast.success(`Logged in as ${account.username} (${account.role})`);
    } catch (error: any) {
      toast.error(`Login failed: ${error.message}`);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(loginForm);
      toast.success('Login successful!');
    } catch (error: any) {
      toast.error(`Login failed: ${error.message}`);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (registerForm.password !== registerForm.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      await register({
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password,
        firstName: registerForm.firstName,
        lastName: registerForm.lastName
      });
      toast.success('Registration successful!');
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error: any) {
      toast.error(`Logout failed: ${error.message}`);
    }
  };

  const handleForgotPassword = async () => {
    if (!loginForm.identifier) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      await forgotPassword(loginForm.identifier);
      toast.success('Password reset instructions sent to your email');
    } catch (error: any) {
      toast.error(`Failed to send reset email: ${error.message}`);
    }
  };

  const handlePasswordChange = async (password: string) => {
    if (password.length > 0) {
      try {
        const strength = await checkPasswordStrength(password);
        setPasswordStrength(strength);
      } catch (error) {
        console.error('Password strength check failed:', error);
      }
    } else {
      setPasswordStrength(null);
    }
  };

  const getPasswordStrengthColor = (strength: string) => {
    switch (strength) {
      case 'weak': return 'text-red-500';
      case 'medium': return 'text-yellow-500';
      case 'strong': return 'text-green-500';
      case 'very_strong': return 'text-green-600';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Newzora Authentication Test</h1>
          <p className="mt-2 text-gray-600">Test login, registration, and authentication features</p>
          
          {isAuthenticated && user && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800">
                ✅ Logged in as: <strong>{user.username}</strong> ({user.email}) - Role: <strong>{user.role}</strong>
              </p>
              <button
                onClick={handleLogout}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('accounts')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'accounts'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Test Accounts
            </button>
            <button
              onClick={() => setActiveTab('login')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'login'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Login Test
            </button>
            <button
              onClick={() => setActiveTab('register')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'register'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Register Test
            </button>
          </div>
        </div>

        {/* Test Accounts Tab */}
        {activeTab === 'accounts' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Test Accounts</h2>
            <p className="text-gray-600 mb-6">
              Click on any account below to quickly log in and test the authentication system.
            </p>
            
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {testAccounts.map((account, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{account.username}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      account.role === 'admin' 
                        ? 'bg-red-100 text-red-800'
                        : account.role === 'moderator'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {account.role}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-3">
                    <p><strong>Email:</strong> {account.email}</p>
                    <p><strong>Password:</strong> {account.password}</p>
                    <p className="mt-1">{account.description}</p>
                  </div>
                  
                  <button
                    onClick={() => handleQuickLogin(account)}
                    disabled={isLoading}
                    className="w-full px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? 'Logging in...' : 'Quick Login'}
                  </button>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Testing Instructions:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Admin account has full system access</li>
                <li>• Moderator account can manage content</li>
                <li>• User accounts have standard permissions</li>
                <li>• Test different roles to verify permission systems</li>
                <li>• Check navigation and feature availability per role</li>
              </ul>
            </div>
          </div>
        )}

        {/* Login Test Tab */}
        {activeTab === 'login' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Login Test</h2>
            
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email or Username
                </label>
                <div className="relative">
                  <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={loginForm.identifier}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, identifier: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter email or username"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="rememberMe"
                  checked={loginForm.rememberMe}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, rememberMe: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div className="flex space-x-3">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 py-2 px-4 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Logging in...' : 'Login'}
                </button>
                
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className="px-4 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50"
                >
                  Forgot Password
                </button>
              </div>
            </form>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Quick Fill Options:</h4>
              <div className="flex flex-wrap gap-2">
                {testAccounts.slice(0, 3).map((account, index) => (
                  <button
                    key={index}
                    onClick={() => setLoginForm(prev => ({
                      ...prev,
                      identifier: account.email,
                      password: account.password
                    }))}
                    className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                  >
                    Fill {account.role}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Register Test Tab */}
        {activeTab === 'register' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Registration Test</h2>
            
            <form onSubmit={handleRegister} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={registerForm.firstName}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, firstName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="First name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={registerForm.lastName}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, lastName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Last name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Username
                </label>
                <div className="relative">
                  <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={registerForm.username}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Choose a username"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <div className="relative">
                  <FaEnvelope className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="email"
                    value={registerForm.email}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={registerForm.password}
                    onChange={(e) => {
                      setRegisterForm(prev => ({ ...prev, password: e.target.value }));
                      handlePasswordChange(e.target.value);
                    }}
                    className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Create a password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
                  </button>
                </div>
                
                {passwordStrength && (
                  <div className="mt-2">
                    <div className={`text-sm ${getPasswordStrengthColor(passwordStrength.strength)}`}>
                      Password Strength: {passwordStrength.strength}
                    </div>
                    {passwordStrength.errors.length > 0 && (
                      <ul className="mt-1 text-sm text-red-600">
                        {passwordStrength.errors.map((error: string, index: number) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm Password
                </label>
                <div className="relative">
                  <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={registerForm.confirmPassword}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Confirm your password"
                  />
                </div>
                {registerForm.password && registerForm.confirmPassword && registerForm.password !== registerForm.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">Passwords do not match</p>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2 px-4 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </button>
            </form>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2">Registration Testing Notes:</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Password must be at least 8 characters</li>
                <li>• Must contain uppercase, lowercase, number, and special character</li>
                <li>• Username must be unique and 3-30 characters</li>
                <li>• Email verification will be required</li>
                <li>• Check email templates and verification flow</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestAuth;
