/**
 * Newzora API 服务配置
 * 统一管理所有API调用
 */

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// API 响应类型定义
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

// 请求配置类型
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
}

// 基础API类
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  // 获取认证头
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }

  // 构建URL
  private buildUrl(endpoint: string, params?: Record<string, string>): string {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }
    return url.toString();
  }

  // 通用请求方法
  async request<T = any>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      params
    } = config;

    const url = this.buildUrl(endpoint, params);
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...this.getAuthHeaders(),
      ...headers
    };

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // GET 请求
  async get<T = any>(endpoint: string, params?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', params });
  }

  // POST 请求
  async post<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body });
  }

  // PUT 请求
  async put<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body });
  }

  // DELETE 请求
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // 文件上传
  async upload<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: formData
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }
}

// 创建API服务实例
const api = new ApiService(API_BASE);

// 认证相关API
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    api.post('/auth/login', credentials),
  
  register: (userData: { email: string; password: string; name: string }) =>
    api.post('/auth/register', userData),
  
  logout: () => api.post('/auth/logout'),
  
  getProfile: () => api.get('/auth/profile'),
  
  updateProfile: (data: any) => api.put('/auth/profile', data),
  
  refreshToken: () => api.post('/auth/refresh')
};

// 文章相关API
export const articlesApi = {
  getList: (params?: Record<string, string>) => api.get('/articles', params),
  
  getById: (id: string) => api.get(`/articles/${id}`),
  
  create: (data: any) => api.post('/articles', data),
  
  update: (id: string, data: any) => api.put(`/articles/${id}`, data),
  
  delete: (id: string) => api.delete(`/articles/${id}`),
  
  like: (id: string) => api.post(`/articles/${id}/like`),
  
  bookmark: (id: string) => api.post(`/articles/${id}/bookmark`)
};

// 推荐系统API
export const recommendationsApi = {
  getPersonalized: (params?: Record<string, string>) =>
    api.get('/recommendations/personalized', params),
  
  getRealtime: (params?: Record<string, string>) =>
    api.get('/recommendations/realtime', params),
  
  getContextual: (params?: Record<string, string>) =>
    api.get('/recommendations/contextual', params),
  
  getDeepLearning: (params?: Record<string, string>) =>
    api.get('/recommendations/deep-learning', params),
  
  getMultiArmedBandit: (params?: Record<string, string>) =>
    api.get('/recommendations/multi-armed-bandit', params),
  
  recordFeedback: (data: { contentId: string; action: string; algorithm?: string }) =>
    api.post('/recommendations/feedback', data),
  
  getStats: (params?: Record<string, string>) =>
    api.get('/recommendations/stats', params),
  
  getPopular: (params?: Record<string, string>) =>
    api.get('/recommendations/popular', params),
  
  getTrending: (params?: Record<string, string>) =>
    api.get('/recommendations/trending', params)
};

// AI内容审核API
export const aiModerationApi = {
  moderate: (data: { contentType: string; content: string; options?: any }) =>
    api.post('/ai-moderation/moderate', data),
  
  moderateBatch: (data: { contents: any[] }) =>
    api.post('/ai-moderation/moderate-batch', data),
  
  detectSensitiveWords: (data: { content: string }) =>
    api.post('/ai-moderation/detect-sensitive-words', data),
  
  detectToxicity: (data: { content: string }) =>
    api.post('/ai-moderation/detect-toxicity', data),
  
  semanticAnalysis: (data: { content: string }) =>
    api.post('/ai-moderation/semantic-analysis', data),
  
  detectImageContent: (data: { content: string }) =>
    api.post('/ai-moderation/detect-image-content', data),
  
  detectMaliciousUrls: (data: { content: string }) =>
    api.post('/ai-moderation/detect-malicious-urls', data),
  
  getStats: (params?: Record<string, string>) =>
    api.get('/ai-moderation/stats', params),
  
  test: () => api.get('/ai-moderation/test')
};

// 国际化API
export const i18nApi = {
  detectLanguage: () => api.get('/i18n/detect-language'),
  
  getLanguages: () => api.get('/i18n/languages'),
  
  getTranslations: (language: string, params?: Record<string, string>) =>
    api.get(`/i18n/translations/${language}`, params),
  
  translate: (data: { text: string; targetLanguage: string; sourceLanguage?: string }) =>
    api.post('/i18n/translate', data),
  
  translateBatch: (data: { texts: string[]; targetLanguage: string; sourceLanguage?: string }) =>
    api.post('/i18n/translate-batch', data),
  
  detectTextLanguage: (data: { text: string }) =>
    api.post('/i18n/detect-text-language', data),
  
  formatDateTime: (data: { date: string; language?: string; options?: any }) =>
    api.post('/i18n/format-datetime', data),
  
  getStats: () => api.get('/i18n/stats'),
  
  clearCache: () => api.post('/i18n/clear-cache')
};

// 创作者生态系统API
export const creatorsApi = {
  getLevels: () => api.get('/creators/levels'),
  
  evaluateLevel: () => api.get('/creators/evaluate-level'),
  
  getVerificationTypes: () => api.get('/creators/verification-types'),
  
  applyVerification: (data: { verificationType: string; documents: any }) =>
    api.post('/creators/apply-verification', data),
  
  getIncentivePrograms: () => api.get('/creators/incentive-programs'),
  
  calculateRewards: () => api.get('/creators/calculate-rewards'),
  
  getRecommendations: (params?: Record<string, string>) =>
    api.get('/creators/recommendations', params),
  
  getStats: () => api.get('/creators/stats'),
  
  getGlobalSupport: () => api.get('/creators/global-support'),
  
  search: (params?: Record<string, string>) =>
    api.get('/creators/search', params),
  
  follow: (creatorId: string) => api.post(`/creators/follow/${creatorId}`),
  
  unfollow: (creatorId: string) => api.delete(`/creators/follow/${creatorId}`)
};

// 评论系统API
export const commentsApi = {
  getList: (targetType: string, targetId: string, params?: Record<string, string>) =>
    api.get(`/comments/${targetType}/${targetId}`, params),
  
  getEnhanced: (targetType: string, targetId: string, params?: Record<string, string>) =>
    api.get(`/comments/enhanced/${targetType}/${targetId}`, params),
  
  create: (data: any) => api.post('/comments', data),
  
  createEnhanced: (data: any) => api.post('/comments/enhanced', data),
  
  update: (id: string, data: any) => api.put(`/comments/${id}`, data),
  
  delete: (id: string) => api.delete(`/comments/${id}`),
  
  interact: (commentId: string, data: { type: string }) =>
    api.post(`/comments/${commentId}/interact`, data),
  
  report: (commentId: string, data: { type: string; reason?: string; additionalInfo?: string }) =>
    api.post(`/comments/${commentId}/report`, data),
  
  getStats: (targetType: string, targetId: string) =>
    api.get(`/comments/stats/${targetType}/${targetId}`),
  
  getInteractionTypes: () => api.get('/comments/interaction-types'),
  
  getReportTypes: () => api.get('/comments/report-types')
};

// 媒体管理API
export const mediaApi = {
  upload: (file: File, additionalData?: Record<string, any>) =>
    api.upload('/media/upload', file, additionalData),
  
  getList: (params?: Record<string, string>) => api.get('/media', params),
  
  getById: (id: string) => api.get(`/media/${id}`),
  
  delete: (id: string) => api.delete(`/media/${id}`),
  
  convert: (id: string, data: { outputFormat: string; quality?: string; options?: any }) =>
    api.post(`/media/convert/${id}`, data),
  
  compress: (id: string, data: { compressionLevel?: string }) =>
    api.post(`/media/compress/${id}`, data),
  
  extractAudio: (id: string, data: { format?: string; quality?: string }) =>
    api.post(`/media/extract-audio/${id}`, data)
};

// 搜索API
export const searchApi = {
  global: (params?: Record<string, string>) => api.get('/search', params),
  
  articles: (params?: Record<string, string>) => api.get('/search/articles', params),
  
  users: (params?: Record<string, string>) => api.get('/search/users', params),
  
  suggestions: (params?: Record<string, string>) => api.get('/search/suggestions', params)
};

// 用户管理API
export const usersApi = {
  getList: (params?: Record<string, string>) => api.get('/users', params),
  
  getById: (id: string) => api.get(`/users/${id}`),
  
  update: (id: string, data: any) => api.put(`/users/${id}`, data),
  
  follow: (id: string) => api.post(`/users/${id}/follow`),
  
  unfollow: (id: string) => api.delete(`/users/${id}/follow`)
};

// 导出默认API实例
export default api;
