import axios, { AxiosResponse, AxiosError } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface User {
  id: number;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: string;
  isEmailVerified: boolean;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
}

export interface Article {
  id: number;
  title: string;
  content: string;
  excerpt?: string;
  featuredImage?: string;
  category: string;
  tags: string[];
  authorId: number;
  author?: User;
  published: boolean;
  publishedAt?: string;
  views: number;
  likes: number;
  comments: number;
  createdAt: string;
  updatedAt: string;
}

export interface Comment {
  id: number;
  content: string;
  articleId: number;
  authorId: number;
  author?: User;
  parentId?: number;
  replies?: Comment[];
  likes: number;
  createdAt: string;
  updatedAt: string;
}

export interface Notification {
  id: number;
  userId: number;
  type: string;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const getToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('accessToken');
  }
  return null;
};

const getRefreshToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refreshToken');
  }
  return null;
};

const setTokens = (accessToken: string, refreshToken: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }
};

const clearTokens = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  }
};

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = getRefreshToken();
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth-enhanced/refresh-token`, {
            refreshToken
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data.data;
          setTokens(accessToken, newRefreshToken);

          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
          return Promise.reject(refreshError);
        }
      } else {
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    return Promise.reject(error);
  }
);

// API Service Class
class ApiService {
  // Authentication APIs
  async login(credentials: { identifier: string; password: string; rememberMe?: boolean }) {
    const response = await api.post<ApiResponse<{ user: User; tokens: any }>>('/auth-enhanced/login', credentials);
    return response.data;
  }

  async register(userData: {
    username: string;
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) {
    const response = await api.post<ApiResponse<{ user: User; tokens: any }>>('/auth-enhanced/register', userData);
    return response.data;
  }

  async logout() {
    const response = await api.post<ApiResponse>('/auth-enhanced/logout');
    clearTokens();
    return response.data;
  }

  async forgotPassword(email: string) {
    const response = await api.post<ApiResponse>('/auth-enhanced/forgot-password', { email });
    return response.data;
  }

  async resetPassword(token: string, newPassword: string) {
    const response = await api.post<ApiResponse>('/auth-enhanced/reset-password', { token, newPassword });
    return response.data;
  }

  async verifyEmail(token: string) {
    const response = await api.post<ApiResponse>('/auth-enhanced/verify-email', { token });
    return response.data;
  }

  async resendVerification(email: string) {
    const response = await api.post<ApiResponse>('/auth-enhanced/resend-verification', { email });
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string) {
    const response = await api.post<ApiResponse>('/auth-enhanced/change-password', {
      currentPassword,
      newPassword
    });
    return response.data;
  }

  async getMe() {
    const response = await api.get<ApiResponse<{ user: User }>>('/auth-enhanced/me');
    return response.data;
  }

  async updateProfile(data: Partial<User>) {
    const response = await api.put<ApiResponse<{ user: User }>>('/auth-enhanced/profile', data);
    return response.data;
  }

  async checkPasswordStrength(password: string) {
    const response = await api.post<ApiResponse<{
      isValid: boolean;
      strength: string;
      errors: string[];
    }>>('/auth-enhanced/check-password-strength', { password });
    return response.data;
  }

  // Article APIs
  async getArticles(params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    authorId?: number;
    published?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<Article[]>>(`/articles?${queryParams.toString()}`);
    return response.data;
  }

  async getArticle(id: number) {
    const response = await api.get<ApiResponse<Article>>(`/articles/${id}`);
    return response.data;
  }

  async createArticle(articleData: {
    title: string;
    content: string;
    excerpt?: string;
    featuredImage?: string;
    category: string;
    tags: string[];
    published?: boolean;
  }) {
    const response = await api.post<ApiResponse<Article>>('/articles', articleData);
    return response.data;
  }

  async updateArticle(id: number, articleData: Partial<Article>) {
    const response = await api.put<ApiResponse<Article>>(`/articles/${id}`, articleData);
    return response.data;
  }

  async deleteArticle(id: number) {
    const response = await api.delete<ApiResponse>(`/articles/${id}`);
    return response.data;
  }

  async likeArticle(id: number) {
    const response = await api.post<ApiResponse>(`/articles/${id}/like`);
    return response.data;
  }

  async unlikeArticle(id: number) {
    const response = await api.delete<ApiResponse>(`/articles/${id}/like`);
    return response.data;
  }

  async shareArticle(id: number, platform: string) {
    const response = await api.post<ApiResponse>(`/articles/${id}/share`, { platform });
    return response.data;
  }

  // Comment APIs
  async getComments(articleId: number, params: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<Comment[]>>(`/articles/${articleId}/comments?${queryParams.toString()}`);
    return response.data;
  }

  async createComment(articleId: number, content: string, parentId?: number) {
    const response = await api.post<ApiResponse<Comment>>(`/articles/${articleId}/comments`, {
      content,
      parentId
    });
    return response.data;
  }

  async updateComment(id: number, content: string) {
    const response = await api.put<ApiResponse<Comment>>(`/comments/${id}`, { content });
    return response.data;
  }

  async deleteComment(id: number) {
    const response = await api.delete<ApiResponse>(`/comments/${id}`);
    return response.data;
  }

  async likeComment(id: number) {
    const response = await api.post<ApiResponse>(`/comments/${id}/like`);
    return response.data;
  }

  async unlikeComment(id: number) {
    const response = await api.delete<ApiResponse>(`/comments/${id}/like`);
    return response.data;
  }

  // User Management APIs
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<User[]>>(`/users?${queryParams.toString()}`);
    return response.data;
  }

  async getUser(id: number) {
    const response = await api.get<ApiResponse<User>>(`/users/${id}`);
    return response.data;
  }

  async followUser(id: number) {
    const response = await api.post<ApiResponse>(`/follows`, { followingId: id });
    return response.data;
  }

  async unfollowUser(id: number) {
    const response = await api.delete<ApiResponse>(`/follows/${id}`);
    return response.data;
  }

  async getFollowers(id: number, params: { page?: number; limit?: number } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<User[]>>(`/users/${id}/followers?${queryParams.toString()}`);
    return response.data;
  }

  async getFollowing(id: number, params: { page?: number; limit?: number } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<User[]>>(`/users/${id}/following?${queryParams.toString()}`);
    return response.data;
  }

  // Notification APIs
  async getNotifications(params: {
    page?: number;
    limit?: number;
    read?: boolean;
    type?: string;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<Notification[]>>(`/notifications?${queryParams.toString()}`);
    return response.data;
  }

  async markNotificationAsRead(id: number) {
    const response = await api.put<ApiResponse>(`/notifications/${id}/read`);
    return response.data;
  }

  async markAllNotificationsAsRead() {
    const response = await api.put<ApiResponse>('/notifications/read-all');
    return response.data;
  }

  async deleteNotification(id: number) {
    const response = await api.delete<ApiResponse>(`/notifications/${id}`);
    return response.data;
  }

  async getNotificationSettings() {
    const response = await api.get<ApiResponse>('/notifications/settings');
    return response.data;
  }

  async updateNotificationSettings(settings: any) {
    const response = await api.put<ApiResponse>('/notifications/settings', settings);
    return response.data;
  }

  // Search APIs
  async search(query: string, params: {
    type?: 'articles' | 'users' | 'all';
    page?: number;
    limit?: number;
    filters?: any;
  } = {}) {
    const queryParams = new URLSearchParams();
    queryParams.append('q', query);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        if (key === 'filters') {
          queryParams.append(key, JSON.stringify(value));
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    const response = await api.get<ApiResponse>(`/search?${queryParams.toString()}`);
    return response.data;
  }

  async getSearchSuggestions(query: string) {
    const response = await api.get<ApiResponse<string[]>>(`/search/suggestions?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  async getPopularSearches() {
    const response = await api.get<ApiResponse<string[]>>('/search/popular');
    return response.data;
  }

  // Category and Tag APIs
  async getCategories() {
    const response = await api.get<ApiResponse<{ id: number; name: string; description?: string; articleCount: number }[]>>('/categories');
    return response.data;
  }

  async getTags(params: { search?: string; limit?: number } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<{ id: number; name: string; articleCount: number }[]>>(`/tags?${queryParams.toString()}`);
    return response.data;
  }

  async createTag(name: string) {
    const response = await api.post<ApiResponse>('/tags', { name });
    return response.data;
  }

  // Draft APIs
  async getDrafts(params: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse<Article[]>>(`/drafts?${queryParams.toString()}`);
    return response.data;
  }

  async getDraft(id: number) {
    const response = await api.get<ApiResponse<Article>>(`/drafts/${id}`);
    return response.data;
  }

  async createDraft(draftData: {
    title: string;
    content: string;
    excerpt?: string;
    featuredImage?: string;
    category?: string;
    tags?: string[];
  }) {
    const response = await api.post<ApiResponse<Article>>('/drafts', draftData);
    return response.data;
  }

  async updateDraft(id: number, draftData: Partial<Article>) {
    const response = await api.put<ApiResponse<Article>>(`/drafts/${id}`, draftData);
    return response.data;
  }

  async deleteDraft(id: number) {
    const response = await api.delete<ApiResponse>(`/drafts/${id}`);
    return response.data;
  }

  async publishDraft(id: number) {
    const response = await api.post<ApiResponse<Article>>(`/drafts/${id}/publish`);
    return response.data;
  }

  // Media/Upload APIs
  async uploadFile(file: File, type: 'image' | 'document' | 'video' = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const token = getToken();
    const response = await fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: formData
    });

    return response.json();
  }

  async getMediaFiles(params: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/media?${queryParams.toString()}`);
    return response.data;
  }

  async deleteMediaFile(id: number) {
    const response = await api.delete<ApiResponse>(`/media/${id}`);
    return response.data;
  }

  // Message/Chat APIs
  async getConversations(params: { page?: number; limit?: number } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/messages/conversations?${queryParams.toString()}`);
    return response.data;
  }

  async getMessages(conversationId: number, params: { page?: number; limit?: number } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/messages/conversations/${conversationId}?${queryParams.toString()}`);
    return response.data;
  }

  async sendMessage(recipientId: number, content: string, type: 'text' | 'image' | 'file' = 'text') {
    const response = await api.post<ApiResponse>('/messages', {
      recipientId,
      content,
      type
    });
    return response.data;
  }

  async markMessageAsRead(messageId: number) {
    const response = await api.put<ApiResponse>(`/messages/${messageId}/read`);
    return response.data;
  }

  async deleteMessage(messageId: number) {
    const response = await api.delete<ApiResponse>(`/messages/${messageId}`);
    return response.data;
  }

  // Analytics APIs
  async getAnalytics(type: 'user' | 'content' | 'traffic' | 'performance', params: {
    days?: number;
    startDate?: string;
    endDate?: string;
    groupBy?: 'hour' | 'day' | 'week' | 'month';
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/analytics/${type}?${queryParams.toString()}`);
    return response.data;
  }

  async trackEvent(eventType: string, data: any = {}) {
    const response = await api.post<ApiResponse>('/analytics/track', {
      eventType,
      ...data,
      timestamp: new Date().toISOString()
    });
    return response.data;
  }

  async getRealtimeStats() {
    const response = await api.get<ApiResponse>('/analytics/realtime');
    return response.data;
  }

  // Report APIs
  async getReports(params: {
    type?: string;
    category?: string;
    isPublic?: boolean;
    page?: number;
    limit?: number;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/reports?${queryParams.toString()}`);
    return response.data;
  }

  async getReport(id: number) {
    const response = await api.get<ApiResponse>(`/reports/${id}`);
    return response.data;
  }

  async generateReport(id: number, params: any = {}) {
    const response = await api.post<ApiResponse>(`/reports/${id}/generate`, params);
    return response.data;
  }

  async createReport(reportData: {
    name: string;
    description?: string;
    type: string;
    category?: string;
    config: any;
    isPublic?: boolean;
  }) {
    const response = await api.post<ApiResponse>('/reports', reportData);
    return response.data;
  }

  // Admin APIs
  async getDashboardStats() {
    const response = await api.get<ApiResponse>('/admin/dashboard/stats');
    return response.data;
  }

  async getDashboardActivity() {
    const response = await api.get<ApiResponse>('/admin/dashboard/activity');
    return response.data;
  }

  async getSystemHealth() {
    const response = await api.get<ApiResponse>('/admin/dashboard/health');
    return response.data;
  }

  async moderateContent(contentId: number, action: 'approve' | 'reject' | 'flag', reason?: string) {
    const response = await api.post<ApiResponse>(`/moderation/content/${contentId}`, {
      action,
      reason
    });
    return response.data;
  }

  async getModerationQueue(params: {
    type?: string;
    status?: string;
    page?: number;
    limit?: number;
  } = {}) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get<ApiResponse>(`/moderation/queue?${queryParams.toString()}`);
    return response.data;
  }

  // Email APIs
  async sendEmail(data: {
    to: string;
    subject: string;
    template?: string;
    content?: string;
    data?: any;
  }) {
    const response = await api.post<ApiResponse>('/email/send', data);
    return response.data;
  }

  async getEmailStats() {
    const response = await api.get<ApiResponse>('/email/stats');
    return response.data;
  }

  async sendTestEmail(email: string, template: string) {
    const response = await api.post<ApiResponse>('/email/test', { email, template });
    return response.data;
  }
}

// Create and export singleton instance
export const apiService = new ApiService();
export { setTokens, clearTokens, getToken };
export default apiService;
