'use client';

import { io, Socket } from 'socket.io-client';

export interface NotificationData {
  id: number;
  type: string;
  title: string;
  content: string;
  data?: any;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  actionUrl?: string;
  imageUrl?: string;
  isRead?: boolean;
  readAt?: string;
  createdAt: string;
  timestamp: string;
}

export interface UnreadCountData {
  count: number;
  timestamp: string;
}

export interface SystemMessageData {
  type: 'system' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  timestamp: string;
  action?: {
    label: string;
    url: string;
  };
}

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Function[]> = new Map();

  constructor() {
    this.setupEventListeners();
  }

  // Connect to Socket.IO server
  connect(token: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        const serverUrl = process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5000';
        
        this.socket = io(serverUrl, {
          auth: {
            token: token
          },
          transports: ['websocket', 'polling'],
          timeout: 10000,
          forceNew: true
        });

        this.socket.on('connect', () => {
          console.log('🔌 Connected to notification service');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve(true);
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 Disconnected from notification service:', reason);
          this.isConnected = false;
          
          // Auto reconnect
          if (reason === 'io server disconnect') {
            // Server actively disconnected, don't reconnect
            return;
          }

          this.attemptReconnect(token);
        });

        // Set up event listeners
        this.setupSocketEventListeners();

      } catch (error) {
        console.error('Socket connection failed:', error);
        reject(error);
      }
    });
  }

  // Disconnect
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('🔌 Socket disconnected manually');
    }
  }

  // Attempt reconnection
  private attemptReconnect(token: string) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      this.connect(token).catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  // Set up Socket event listeners
  private setupSocketEventListeners() {
    if (!this.socket) return;

    // Connection success message
    this.socket.on('connected', (data) => {
      console.log('✅ Socket connection confirmed:', data);
      this.emitLocal('connected', data);
    });

    // Authentication error handling
    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket authentication error:', error.message);

      // If it's a user authentication error, trigger logout
      if (error.message.includes('User not found or inactive') ||
          error.message.includes('Invalid authentication token') ||
          error.message.includes('Authentication token required')) {
        console.warn('🔐 Authentication failed, triggering logout...');
        this.emitLocal('auth_error', { message: error.message });

        // Clear local authentication information
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
        }

        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }
    });

    // New notification
    this.socket.on('new_notification', (notification: NotificationData) => {
      console.log('🔔 New notification received:', notification);
      this.emitLocal('new_notification', notification);
    });

    // Unread count update
    this.socket.on('unread_count_updated', (data: UnreadCountData) => {
      console.log('📊 Unread count updated:', data);
      this.emitLocal('unread_count_updated', data);
    });

    // System message
    this.socket.on('system_message', (message: SystemMessageData) => {
      console.log('📢 System message received:', message);
      this.emitLocal('system_message', message);
    });

    // Notification read confirmation
    this.socket.on('notification_read', (data) => {
      console.log('✅ Notification read confirmed:', data);
      this.emitLocal('notification_read', data);
    });

    // Error handling
    this.socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
      this.emitLocal('socket_error', { message: error.message || error });
    });
  }

  // Set up general event listeners
  private setupEventListeners() {
    // Reconnect when page visibility changes
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && !this.isConnected) {
          // Attempt reconnection when page becomes visible and not connected
          const token = localStorage.getItem('auth_token');
          if (token) {
            this.connect(token).catch(console.error);
          }
        }
      });
    }

    // Reconnect when network status changes
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        if (!this.isConnected) {
          const token = localStorage.getItem('auth_token');
          if (token) {
            this.connect(token).catch(console.error);
          }
        }
      });
    }
  }

  // Send event to server
  emit(event: string, data?: any) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  }

  // Listen to events
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  // Remove event listener
  off(event: string, callback?: Function) {
    if (!this.listeners.has(event)) return;

    if (callback) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(event);
    }
  }

  // Trigger local event
  private emitLocal(event: string, data?: any) {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in socket event callback:', error);
        }
      });
    }
  }

  // Mark notification as read
  markNotificationAsRead(notificationId: number) {
    this.emit('mark_notification_read', { notificationId });
  }

  // Mark all notifications as read
  markAllNotificationsAsRead() {
    this.emit('mark_all_notifications_read');
  }

  // Get connection status
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Get Socket instance
  getSocket(): Socket | null {
    return this.socket;
  }
}

// Create singleton instance
const socketService = new SocketService();
export default socketService;
