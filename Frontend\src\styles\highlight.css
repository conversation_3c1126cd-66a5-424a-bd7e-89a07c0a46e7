/* 代码高亮样式 - 基于 GitHub Dark 主题 */

.ProseMirror pre {
  background: #0d1117;
  color: #c9d1d9;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
  border: 1px solid #30363d;
}

.ProseMirror pre code {
  background: none;
  color: inherit;
  font-size: 0.875rem;
  padding: 0;
  border-radius: 0;
}

/* 语法高亮颜色 */
.hljs-comment,
.hljs-quote {
  color: #8b949e;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #ff7b72;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #79c0ff;
}

.hljs-string,
.hljs-doctag {
  color: #a5d6ff;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #d2a8ff;
  font-weight: bold;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-type,
.hljs-class .hljs-title,
.hljs-tag,
.hljs-regexp,
.hljs-link {
  color: #ffa657;
}

.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-builtin-name {
  color: #79c0ff;
}

.hljs-meta,
.hljs-deletion {
  color: #8b949e;
}

.hljs-addition {
  color: #56d364;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* 特定语言的样式 */
.hljs-json .hljs-attr {
  color: #79c0ff;
}

.hljs-javascript .hljs-function,
.hljs-typescript .hljs-function {
  color: #d2a8ff;
}

.hljs-python .hljs-function {
  color: #ffa657;
}

.hljs-css .hljs-selector-class,
.hljs-css .hljs-selector-id {
  color: #79c0ff;
}

.hljs-css .hljs-property {
  color: #79c0ff;
}

.hljs-css .hljs-value {
  color: #a5d6ff;
}

/* 行号样式 */
.hljs-ln-numbers {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-align: center;
  color: #6e7681;
  border-right: 1px solid #30363d;
  vertical-align: top;
  padding-right: 8px;
  margin-right: 8px;
}

.hljs-ln-code {
  padding-left: 8px;
}

/* 编辑器特定样式 */
.ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 300px;
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin: 1rem 0 0.5rem 0;
  font-weight: bold;
  line-height: 1.2;
}

.ProseMirror h1 {
  font-size: 2rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
}

.ProseMirror h4 {
  font-size: 1.125rem;
}

.ProseMirror h5 {
  font-size: 1rem;
}

.ProseMirror h6 {
  font-size: 0.875rem;
  color: #6b7280;
}

.ProseMirror blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
  font-style: italic;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.ProseMirror table th {
  background-color: #f9fafb;
  font-weight: bold;
}

.ProseMirror code {
  background-color: #f3f4f6;
  color: #ef4444;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* 拖拽高亮 */
.ProseMirror.drag-over {
  background-color: #eff6ff;
  border: 2px dashed #3b82f6;
}

/* 选中状态 */
.ProseMirror .selectedCell {
  background-color: #dbeafe;
}

/* 占位符样式 */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 焦点样式 */
.ProseMirror:focus {
  outline: none;
}

/* 表格调整大小 */
.tableWrapper {
  overflow-x: auto;
  margin: 1rem 0;
}

.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ProseMirror {
    padding: 0.75rem;
  }
  
  .ProseMirror pre {
    padding: 0.75rem;
    font-size: 0.75rem;
  }
  
  .ProseMirror h1 {
    font-size: 1.5rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.25rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.125rem;
  }
}
