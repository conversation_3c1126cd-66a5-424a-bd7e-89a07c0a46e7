export interface Article {
  id: number;
  title: string;
  description?: string;
  excerpt?: string;
  content: string;
  category: 'Technology' | 'Travel' | 'Lifestyle' | 'Food' | 'Trending' | string;
  image?: string;
  author: {
    id: number;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
  } | string;
  readTime: number;
  tags: string[];
  views: number;
  likes: number;
  comments?: number;
  shares?: number;
  featured?: boolean;
  published: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  _id: string;
  username: string;
  email: string;
  avatar: string;
  role: 'user' | 'admin';
  preferences: {
    categories: string[];
  };
  bookmarks: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  name: string;
  count: number;
}

export interface Comment {
  id: number;
  articleId?: number;
  author: {
    id: number;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
  } | string;
  avatar?: string; // 保持向后兼容
  content: string;
  likes: number;
  parentId?: number;
  replies?: Comment[];
  createdAt: string;
  updatedAt: string;
}
