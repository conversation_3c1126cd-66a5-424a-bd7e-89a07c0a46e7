/**
 * 将文本转换为 URL 友好的 slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-') // 将非字母数字字符和中文替换为连字符
    .replace(/^-+|-+$/g, '') // 删除开头和结尾的连字符
    .replace(/[\u4e00-\u9fa5]/g, (char) => encodeURIComponent(char)); // 对中文字符进行编码
}

/**
 * 通过 slug 查找文章
 */
export function findArticleBySlug(slug: string, articles: any[]): any {
  return articles.find(article => 
    generateSlug(article.title) === slug || 
    article.slug === slug || 
    article.id === slug
  );
}
