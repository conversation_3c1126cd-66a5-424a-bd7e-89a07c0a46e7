// 社交登录工具函数

export interface SocialProvider {
  name: string;
  displayName: string;
  color: string;
  icon: string;
}

export const socialProviders: Record<string, SocialProvider> = {
  google: {
    name: 'google',
    displayName: 'Google',
    color: '#4285F4',
    icon: 'google'
  },
  facebook: {
    name: 'facebook',
    displayName: 'Facebook',
    color: '#1877F2',
    icon: 'facebook'
  },
  twitter: {
    name: 'twitter',
    displayName: 'X',
    color: '#000000',
    icon: 'twitter'
  },
  github: {
    name: 'github',
    displayName: 'GitHub',
    color: '#181717',
    icon: 'github'
  },
  apple: {
    name: 'apple',
    displayName: 'Apple',
    color: '#000000',
    icon: 'apple'
  }
};

// 模拟社交登录函数
export const initiateSocialLogin = async (provider: string): Promise<boolean> => {
  console.log(`🔐 Initiating ${provider} login...`);
  
  try {
    // 模拟OAuth流程
    const authWindow = window.open(
      `http://localhost:5000/auth/${provider}`,
      'socialAuth',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    if (!authWindow) {
      throw new Error('Popup blocked. Please allow popups for this site.');
    }

    // 监听消息从OAuth窗口
    return new Promise((resolve, reject) => {
      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) {
          return;
        }

        if (event.data.type === 'SOCIAL_AUTH_SUCCESS') {
          window.removeEventListener('message', messageListener);
          authWindow.close();
          console.log(`✅ ${provider} login successful:`, event.data.user);
          resolve(true);
        } else if (event.data.type === 'SOCIAL_AUTH_ERROR') {
          window.removeEventListener('message', messageListener);
          authWindow.close();
          console.error(`❌ ${provider} login failed:`, event.data.error);
          reject(new Error(event.data.error));
        }
      };

      window.addEventListener('message', messageListener);

      // 检查窗口是否被关闭
      const checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageListener);
          reject(new Error('Authentication cancelled'));
        }
      }, 1000);

      // 超时处理
      setTimeout(() => {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageListener);
        if (!authWindow.closed) {
          authWindow.close();
        }
        reject(new Error('Authentication timeout'));
      }, 60000); // 60秒超时
    });

  } catch (error) {
    console.error(`❌ ${provider} login error:`, error);
    throw error;
  }
};

// 模拟社交登录成功后的用户数据处理
export const handleSocialAuthSuccess = (provider: string, userData: any) => {
  const user = {
    id: userData.id || `${provider}_${Date.now()}`,
    email: userData.email || `user@${provider}.com`,
    name: userData.name || `${provider} User`,
    avatar: userData.avatar || `https://ui-avatars.com/api/?name=${provider}+User&background=6366f1&color=fff&size=64`,
    provider: provider,
    isVerified: true,
    createdAt: new Date().toISOString()
  };

  // 存储到localStorage (在实际应用中应该使用更安全的方式)
  localStorage.setItem('user', JSON.stringify(user));
  localStorage.setItem('isAuthenticated', 'true');
  localStorage.setItem('authProvider', provider);

  return user;
};

// 获取社交登录的重定向URL
export const getSocialAuthUrl = (provider: string, redirectUri?: string): string => {
  const baseUrl = 'http://localhost:5000/auth';
  const redirect = redirectUri || `${window.location.origin}/auth/callback`;
  
  const urls: Record<string, string> = {
    google: `${baseUrl}/google?redirect_uri=${encodeURIComponent(redirect)}`,
    facebook: `${baseUrl}/facebook?redirect_uri=${encodeURIComponent(redirect)}`,
    twitter: `${baseUrl}/twitter?redirect_uri=${encodeURIComponent(redirect)}`,
    github: `${baseUrl}/github?redirect_uri=${encodeURIComponent(redirect)}`,
    apple: `${baseUrl}/apple?redirect_uri=${encodeURIComponent(redirect)}`
  };

  return urls[provider] || `${baseUrl}/${provider}`;
};

// 检查是否支持某个社交登录提供商
export const isSocialProviderSupported = (provider: string): boolean => {
  return provider in socialProviders;
};

// 获取社交登录提供商的显示信息
export const getSocialProviderInfo = (provider: string): SocialProvider | null => {
  return socialProviders[provider] || null;
};

// 模拟检查社交登录状态
export const checkSocialAuthStatus = (): { isAuthenticated: boolean; provider?: string; user?: any } => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const provider = localStorage.getItem('authProvider');
  const userStr = localStorage.getItem('user');
  
  if (isAuthenticated && provider && userStr) {
    try {
      const user = JSON.parse(userStr);
      return { isAuthenticated: true, provider, user };
    } catch (error) {
      console.error('Error parsing user data:', error);
      return { isAuthenticated: false };
    }
  }
  
  return { isAuthenticated: false };
};

// 社交登录登出
export const socialLogout = (): void => {
  localStorage.removeItem('user');
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('authProvider');
  console.log('🔓 Social auth logout completed');
};
