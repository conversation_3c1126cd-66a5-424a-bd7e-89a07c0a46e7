# Newzora 手动测试检查清单

## 🎯 测试目标
在自动化测试的基础上，通过手动测试验证用户体验和界面交互，确保系统在真实使用场景下的稳定性和可用性。

## 📋 测试前准备

### 环境准备
- [ ] 后端服务器运行正常 (http://localhost:5000)
- [ ] 前端服务器运行正常 (http://localhost:3000)
- [ ] 数据库连接正常
- [ ] 测试账户准备完毕

### 测试工具
- [ ] 浏览器开发者工具 (F12)
- [ ] 多个浏览器 (Chrome, Firefox, Safari)
- [ ] 移动设备或模拟器
- [ ] 网络监控工具

### 测试账户
- **管理员**: <EMAIL> / Admin123!
- **用户1**: <EMAIL> / User123!
- **用户2**: <EMAIL> / User123!
- **内容管理员**: <EMAIL> / Moderator123!

## 🔐 用户认证系统测试

### 1. 用户注册流程
**测试页面**: http://localhost:3000/register

- [ ] **页面加载**
  - [ ] 注册页面正常显示
  - [ ] 表单字段完整显示
  - [ ] 页面样式正确

- [ ] **表单验证**
  - [ ] 用户名格式验证 (3-20字符)
  - [ ] 邮箱格式验证
  - [ ] 密码强度验证 (至少8位，包含大小写字母、数字、特殊字符)
  - [ ] 确认密码匹配验证
  - [ ] 必填字段验证

- [ ] **注册提交**
  - [ ] 有效信息注册成功
  - [ ] 重复邮箱注册被拒绝
  - [ ] 重复用户名注册被拒绝
  - [ ] 成功提示信息显示
  - [ ] 错误提示信息清晰

- [ ] **邮箱验证**
  - [ ] 注册后收到验证邮件
  - [ ] 验证链接有效
  - [ ] 点击验证后账户激活
  - [ ] 验证成功页面显示

### 2. 用户登录流程
**测试页面**: http://localhost:3000/login

- [ ] **登录界面**
  - [ ] 登录表单正常显示
  - [ ] 用户名/邮箱输入框
  - [ ] 密码输入框
  - [ ] "记住我"选项
  - [ ] "忘记密码"链接

- [ ] **登录验证**
  - [ ] 邮箱登录成功
  - [ ] 用户名登录成功
  - [ ] 错误密码被拒绝
  - [ ] 不存在用户被拒绝
  - [ ] 未验证邮箱处理

- [ ] **登录状态**
  - [ ] 登录成功跳转到首页
  - [ ] 用户信息正确显示
  - [ ] 导航栏显示用户菜单
  - [ ] "记住我"功能正常

### 3. 密码管理
**测试页面**: http://localhost:3000/forgot-password

- [ ] **忘记密码**
  - [ ] 忘记密码页面正常
  - [ ] 输入邮箱发送重置邮件
  - [ ] 收到密码重置邮件
  - [ ] 重置链接有效

- [ ] **密码重置**
  - [ ] 重置页面正常显示
  - [ ] 新密码强度验证
  - [ ] 密码重置成功
  - [ ] 新密码可以登录

- [ ] **密码修改**
  - [ ] 个人设置中修改密码
  - [ ] 旧密码验证
  - [ ] 新密码强度验证
  - [ ] 修改成功提示

## 📝 文章管理系统测试

### 4. 文章创建和编辑
**测试页面**: http://localhost:3000/create

- [ ] **文章创建界面**
  - [ ] 创建页面正常加载
  - [ ] 富文本编辑器显示正常
  - [ ] 标题输入框
  - [ ] 分类选择器
  - [ ] 标签输入框

- [ ] **富文本编辑器**
  - [ ] 文本格式化功能 (粗体、斜体、下划线)
  - [ ] 标题样式 (H1-H6)
  - [ ] 列表功能 (有序、无序)
  - [ ] 链接插入功能
  - [ ] 图片上传和插入
  - [ ] 代码块功能

- [ ] **图片上传**
  - [ ] 点击上传图片
  - [ ] 拖拽上传图片
  - [ ] 图片格式验证 (jpg, png, gif)
  - [ ] 图片大小限制
  - [ ] 上传进度显示
  - [ ] 图片预览正常

- [ ] **文章保存**
  - [ ] 保存草稿功能
  - [ ] 发布文章功能
  - [ ] 定时发布功能
  - [ ] 保存成功提示

### 5. 文章管理
**测试页面**: http://localhost:3000/dashboard/articles

- [ ] **文章列表**
  - [ ] 文章列表正常显示
  - [ ] 文章状态显示 (草稿、已发布)
  - [ ] 创建时间显示
  - [ ] 阅读量统计
  - [ ] 点赞数统计

- [ ] **文章操作**
  - [ ] 编辑文章功能
  - [ ] 删除文章功能
  - [ ] 文章状态切换
  - [ ] 批量操作功能

- [ ] **文章详情页**
  - [ ] 文章内容正确显示
  - [ ] 图片正常加载
  - [ ] 格式保持正确
  - [ ] 分享功能
  - [ ] 打印功能

## 👥 用户管理系统测试

### 6. 个人资料管理
**测试页面**: http://localhost:3000/profile

- [ ] **个人资料页面**
  - [ ] 个人信息显示正确
  - [ ] 头像显示正常
  - [ ] 个人简介显示
  - [ ] 文章统计信息

- [ ] **资料编辑**
  - [ ] 头像上传功能
  - [ ] 昵称修改
  - [ ] 个人简介编辑
  - [ ] 联系信息编辑
  - [ ] 保存成功提示

- [ ] **隐私设置**
  - [ ] 个人信息可见性设置
  - [ ] 邮箱通知设置
  - [ ] 隐私选项保存

### 7. 用户关注系统
- [ ] **关注功能**
  - [ ] 关注其他用户
  - [ ] 取消关注功能
  - [ ] 关注列表显示
  - [ ] 粉丝列表显示

- [ ] **用户搜索**
  - [ ] 用户名搜索
  - [ ] 搜索结果显示
  - [ ] 用户资料预览

## 💬 社交功能测试

### 8. 文章互动
- [ ] **点赞功能**
  - [ ] 点赞文章
  - [ ] 取消点赞
  - [ ] 点赞数实时更新
  - [ ] 点赞状态保存

- [ ] **评论功能**
  - [ ] 发表评论
  - [ ] 评论显示
  - [ ] 评论回复
  - [ ] 评论点赞
  - [ ] 评论删除 (自己的评论)

- [ ] **分享功能**
  - [ ] 社交媒体分享
  - [ ] 链接复制
  - [ ] 分享统计

### 9. 私信系统
**测试页面**: http://localhost:3000/messages

- [ ] **私信界面**
  - [ ] 私信列表显示
  - [ ] 对话界面
  - [ ] 新消息提示

- [ ] **发送私信**
  - [ ] 发送文本消息
  - [ ] 发送图片
  - [ ] 消息状态显示 (已发送、已读)

### 10. 通知系统
**测试页面**: http://localhost:3000/notifications

- [ ] **通知显示**
  - [ ] 通知列表正常显示
  - [ ] 通知类型区分 (点赞、评论、关注)
  - [ ] 未读通知标记
  - [ ] 通知时间显示

- [ ] **通知操作**
  - [ ] 标记为已读
  - [ ] 批量标记已读
  - [ ] 删除通知
  - [ ] 通知设置

## 🛡️ 管理员功能测试

### 11. 管理员面板
**测试页面**: http://localhost:3000/admin

- [ ] **面板访问**
  - [ ] 管理员账户可以访问
  - [ ] 普通用户被拒绝访问
  - [ ] 权限验证正确

- [ ] **仪表板**
  - [ ] 用户统计显示
  - [ ] 文章统计显示
  - [ ] 系统状态显示
  - [ ] 图表数据正确

### 12. 用户管理
**测试页面**: http://localhost:3000/admin/users

- [ ] **用户列表**
  - [ ] 用户列表正常显示
  - [ ] 用户信息完整
  - [ ] 搜索功能
  - [ ] 筛选功能

- [ ] **用户操作**
  - [ ] 查看用户详情
  - [ ] 编辑用户信息
  - [ ] 停用/激活用户
  - [ ] 角色权限设置

### 13. 内容管理
**测试页面**: http://localhost:3000/admin/content

- [ ] **内容审核**
  - [ ] 待审核内容列表
  - [ ] 内容预览
  - [ ] 审核通过/拒绝
  - [ ] 审核理由记录

- [ ] **内容管理**
  - [ ] 所有文章列表
  - [ ] 批量操作
  - [ ] 内容分类管理
  - [ ] 标签管理

## 🌐 界面和用户体验测试

### 14. 响应式设计
- [ ] **桌面端** (1920x1080)
  - [ ] 布局正常显示
  - [ ] 导航菜单正常
  - [ ] 内容区域适配

- [ ] **平板端** (768x1024)
  - [ ] 响应式布局
  - [ ] 触摸操作友好
  - [ ] 菜单适配

- [ ] **手机端** (375x667)
  - [ ] 移动端布局
  - [ ] 导航菜单折叠
  - [ ] 触摸操作优化

### 15. 浏览器兼容性
- [ ] **Chrome浏览器**
  - [ ] 所有功能正常
  - [ ] 样式显示正确
  - [ ] 性能良好

- [ ] **Firefox浏览器**
  - [ ] 功能兼容性
  - [ ] 样式兼容性
  - [ ] 性能表现

- [ ] **Safari浏览器**
  - [ ] 功能兼容性
  - [ ] 样式兼容性
  - [ ] 性能表现

### 16. 性能和加载
- [ ] **页面加载速度**
  - [ ] 首页加载时间 < 3秒
  - [ ] 文章页面加载快速
  - [ ] 图片懒加载正常

- [ ] **交互响应**
  - [ ] 按钮点击响应快速
  - [ ] 表单提交响应及时
  - [ ] 页面切换流畅

## 🔒 安全性测试

### 17. 输入验证
- [ ] **XSS防护**
  - [ ] 在评论中输入脚本代码
  - [ ] 在文章中插入恶意代码
  - [ ] 脚本被正确转义

- [ ] **SQL注入防护**
  - [ ] 在搜索框输入SQL语句
  - [ ] 在登录表单输入SQL代码
  - [ ] 系统正确过滤

### 18. 权限控制
- [ ] **页面访问控制**
  - [ ] 未登录用户访问限制
  - [ ] 普通用户权限限制
  - [ ] 管理员权限验证

- [ ] **数据访问控制**
  - [ ] 用户只能编辑自己的内容
  - [ ] 管理员可以管理所有内容
  - [ ] 敏感信息保护

## 📊 测试记录

### 测试执行记录
| 测试项目 | 执行时间 | 测试人员 | 结果 | 备注 |
|----------|----------|----------|------|------|
| 用户注册 | | | | |
| 用户登录 | | | | |
| 文章创建 | | | | |
| 文章管理 | | | | |
| 社交功能 | | | | |
| 管理员功能 | | | | |
| 响应式设计 | | | | |
| 浏览器兼容性 | | | | |

### 问题记录
| 问题描述 | 严重程度 | 发现时间 | 负责人 | 状态 |
|----------|----------|----------|--------|------|
| | | | | |
| | | | | |
| | | | | |

### 测试总结
- **总测试项**: ___
- **通过项**: ___
- **失败项**: ___
- **阻塞问题**: ___
- **建议**: ___

## 🎯 测试完成标准

### 必须通过的测试
- [ ] 所有核心功能正常工作
- [ ] 用户认证流程完整
- [ ] 文章管理功能稳定
- [ ] 管理员权限正确
- [ ] 安全测试无严重问题

### 建议通过的测试
- [ ] 响应式设计良好
- [ ] 浏览器兼容性好
- [ ] 性能表现优秀
- [ ] 用户体验友好

---

**测试负责人**: ___________  
**测试日期**: ___________  
**测试环境**: 开发环境  
**下次测试**: ___________
