# Newzora 稳定服务器架构

## 🎯 概述

全新设计的Newzora稳定服务器架构，专为长期稳定运行而优化，支持多种网络环境包括翻墙网络，保留所有原有功能。

## 🚀 快速启动

### 一键启动（推荐）
```bash
# 运行稳定启动脚本
start-stable.bat
```

### 手动启动
```bash
# 后端服务器
cd Backend
npm run stable

# 前端服务器（新终端）
cd Frontend
npm run stable
```

## 📁 新架构文件结构

```
Newzora/
├── Backend/
│   ├── stable-server.js          # 新的稳定后端服务器
│   ├── server-launcher.js        # 服务器启动器（错误恢复）
│   ├── legacy/                   # 旧服务器文件备份
│   ├── .env.stable               # 稳定环境配置
│   └── ...（所有原有功能文件）
├── Frontend/
│   ├── stable-server.js          # 新的稳定前端服务器
│   ├── .env.stable               # 前端稳定配置
│   └── ...（所有原有组件）
├── start-stable.bat              # 一键启动脚本
├── cleanup-old-servers.bat       # 清理旧服务器
├── network-doctor.bat            # 网络诊断工具
├── verify-features.bat           # 功能验证脚本
└── backup/                       # 自动备份目录
```

## 🌐 网络支持特性

### 多网络环境兼容
- ✅ **本地网络**: localhost, 127.0.0.1
- ✅ **局域网**: 自动检测本机IP
- ✅ **翻墙网络**: 支持代理和VPN环境
- ✅ **多网卡**: 自动绑定所有网络接口
- ✅ **动态IP**: 自适应IP变化

### 网络配置
```javascript
// 自动支持的访问地址
http://localhost:5000        // 本地访问
http://127.0.0.1:5000       // 回环地址
http://[本机IP]:5000        // 局域网访问
```

### CORS配置
- 动态白名单管理
- 开发环境宽松策略
- 生产环境严格控制
- 支持多端口访问

## 🔧 服务器特性

### 稳定性保障
- **自动错误恢复**: 服务器崩溃自动重启
- **优雅关闭**: 正确处理进程终止
- **健康监控**: 实时状态检查
- **连接管理**: 超时和连接池优化

### 性能优化
- **压缩传输**: Gzip压缩减少带宽
- **缓存策略**: 静态资源缓存
- **连接复用**: HTTP Keep-Alive
- **内存管理**: 自动垃圾回收

### 安全增强
- **Helmet安全头**: 防XSS、CSRF等攻击
- **速率限制**: 防止API滥用
- **CORS保护**: 跨域请求控制
- **输入验证**: 严格的数据验证

## 📊 功能完整性

### 核心功能（100%保留）
- ✅ **用户认证系统**: 注册、登录、JWT、社交登录
- ✅ **文章管理系统**: CRUD、富文本编辑、分类标签
- ✅ **用户管理系统**: 权限控制、角色管理
- ✅ **管理员面板**: 仪表板、用户管理、内容审核
- ✅ **社交功能**: 关注、点赞、评论、私信
- ✅ **通知系统**: 实时通知、邮件通知
- ✅ **搜索功能**: 全文搜索、筛选排序
- ✅ **文件上传**: 图片、文档上传处理
- ✅ **邮件服务**: SMTP、模板邮件
- ✅ **数据分析**: 统计报表、用户行为分析

### 新增特性
- 🆕 **网络自适应**: 自动适配各种网络环境
- 🆕 **错误恢复**: 自动重启和故障转移
- 🆕 **健康监控**: 实时状态监控和报警
- 🆕 **性能监控**: 内存、CPU、响应时间监控
- 🆕 **日志增强**: 结构化日志和错误追踪

## 🛠️ 管理工具

### 启动和管理
```bash
start-stable.bat           # 一键启动所有服务
network-doctor.bat         # 网络问题诊断
verify-features.bat        # 功能完整性验证
cleanup-old-servers.bat    # 清理旧服务器文件
```

### NPM脚本
```bash
# 后端
npm run stable            # 启动稳定服务器
npm run health            # 健康检查
npm run verify            # 验证配置
npm run network-test      # 网络测试

# 前端
npm run stable            # 启动稳定前端服务器
npm run serve             # 同上（别名）
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 自动解决
start-stable.bat          # 脚本会自动释放端口

# 手动解决
netstat -ano | findstr :5000
taskkill /PID [进程ID] /F
```

#### 2. 网络连接问题
```bash
# 运行网络诊断
network-doctor.bat

# 常见解决方案
ipconfig /flushdns         # 刷新DNS缓存
netsh winsock reset        # 重置网络栈
```

#### 3. 数据库连接失败
```bash
# 检查PostgreSQL服务
services.msc               # 确保PostgreSQL正在运行

# 测试连接
cd Backend
npm run health
```

#### 4. 防火墙阻止
```bash
# 添加防火墙例外
netsh advfirewall firewall add rule name="Newzora" dir=in action=allow protocol=TCP localport=5000
```

### 日志查看
```bash
# 后端日志
Backend/logs/app.log       # 应用日志
Backend/logs/error.log     # 错误日志

# 实时日志
tail -f Backend/logs/app.log
```

## 📈 监控和维护

### 健康检查端点
```bash
# 后端健康检查
GET http://localhost:5000/api/health

# 前端健康检查
GET http://localhost:3000/health
```

### 性能监控
- **内存使用**: 自动监控和报警
- **响应时间**: API响应时间统计
- **错误率**: 错误统计和分析
- **并发连接**: 连接数监控

### 自动维护
- **日志轮转**: 自动清理旧日志
- **缓存清理**: 定期清理过期缓存
- **数据库优化**: 自动索引优化
- **备份管理**: 自动备份重要数据

## 🔄 升级和迁移

### 从旧服务器迁移
1. 运行 `cleanup-old-servers.bat` 清理旧文件
2. 所有功能自动迁移到新架构
3. 旧文件备份到 `backup/` 目录
4. 验证功能完整性

### 配置迁移
- 环境变量自动迁移
- 数据库配置保持不变
- 所有API端点保持兼容

## 🌍 部署建议

### 开发环境
```bash
# 使用稳定启动脚本
start-stable.bat
```

### 生产环境
```bash
# 设置生产环境变量
NODE_ENV=production

# 启用HTTPS
ENABLE_HTTPS=true
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# 启动服务器
npm run stable
```

### Docker部署
```dockerfile
# 后续可提供Docker配置
FROM node:18-alpine
COPY . /app
WORKDIR /app
RUN npm install
CMD ["npm", "run", "stable"]
```

## 📞 技术支持

### 自助诊断
1. 运行 `network-doctor.bat` 诊断网络问题
2. 运行 `verify-features.bat` 验证功能
3. 查看日志文件获取详细错误信息

### 常用命令
```bash
# 完整重启
taskkill /F /IM node.exe    # 结束所有Node进程
start-stable.bat            # 重新启动

# 重置网络
ipconfig /flushdns
netsh winsock reset
```

## 🎉 总结

新的稳定服务器架构提供了：
- 🚀 **更好的稳定性**: 自动错误恢复和重启
- 🌐 **更强的网络兼容性**: 支持各种网络环境
- 🔧 **更简单的管理**: 一键启动和自动化工具
- 📊 **更完整的功能**: 保留所有原有功能
- 🛡️ **更高的安全性**: 增强的安全防护

立即使用 `start-stable.bat` 体验新的稳定服务器！
