# Newzora 测试账户和功能测试指南

## 🧪 测试账户列表

### 管理员账户
| 角色 | 用户名 | 邮箱 | 密码 | 状态 | 用途 |
|------|--------|------|------|------|------|
| **超级管理员** | `admin_test` | `<EMAIL>` | `Admin123!` | ✅ 已验证 | 完整系统权限测试 |
| **系统管理员** | `sysadmin_test` | `<EMAIL>` | `SysAdmin123!` | ✅ 已验证 | 系统配置管理测试 |

### 内容管理员账户
| 角色 | 用户名 | 邮箱 | 密码 | 状态 | 用途 |
|------|--------|------|------|------|------|
| **内容管理员** | `moderator_test` | `<EMAIL>` | `Moderator123!` | ✅ 已验证 | 内容审核和管理 |
| **编辑主管** | `editor_chief` | `<EMAIL>` | `Editor123!` | ✅ 已验证 | 文章编辑和发布 |

### 普通用户账户
| 角色 | 用户名 | 邮箱 | 密码 | 状态 | 用途 |
|------|--------|------|------|------|------|
| **活跃用户** | `user_test1` | `<EMAIL>` | `User123!` | ✅ 已验证 | 基础功能测试 |
| **普通用户** | `user_test2` | `<EMAIL>` | `User123!` | ✅ 已验证 | 社交功能测试 |
| **作者用户** | `author_test` | `<EMAIL>` | `Author123!` | ✅ 已验证 | 文章创作测试 |
| **读者用户** | `reader_test` | `<EMAIL>` | `Reader123!` | ✅ 已验证 | 阅读和评论测试 |

### 特殊状态账户
| 角色 | 用户名 | 邮箱 | 密码 | 状态 | 用途 |
|------|--------|------|------|------|------|
| **未验证用户** | `unverified_test` | `<EMAIL>` | `Unverified123!` | ❌ 未验证 | 邮箱验证流程测试 |
| **停用账户** | `inactive_test` | `<EMAIL>` | `Inactive123!` | 🚫 已停用 | 账户状态测试 |
| **锁定账户** | `locked_test` | `<EMAIL>` | `Locked123!` | 🔒 已锁定 | 安全机制测试 |

## 🔐 认证功能测试

### 1. 登录功能测试

#### 正常登录测试
```bash
# 测试邮箱登录
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "Admin123!",
  "rememberMe": false
}

# 测试用户名登录
POST /api/auth-enhanced/login
{
  "identifier": "user_test1",
  "password": "User123!",
  "rememberMe": true
}
```

#### 异常情况测试
```bash
# 错误密码
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "WrongPassword123!",
  "rememberMe": false
}

# 不存在的用户
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "Password123!",
  "rememberMe": false
}

# 未验证邮箱的用户
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "Unverified123!",
  "rememberMe": false
}

# 已停用的账户
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "Inactive123!",
  "rememberMe": false
}
```

### 2. 注册功能测试

#### 正常注册测试
```bash
POST /api/auth-enhanced/register
{
  "username": "newuser_test",
  "email": "<EMAIL>",
  "password": "NewUser123!",
  "firstName": "New",
  "lastName": "User",
  "acceptTerms": true
}
```

#### 注册验证测试
```bash
# 用户名已存在
POST /api/auth-enhanced/register
{
  "username": "admin_test",
  "email": "<EMAIL>",
  "password": "Password123!",
  "firstName": "Test",
  "lastName": "User"
}

# 邮箱已存在
POST /api/auth-enhanced/register
{
  "username": "newusername",
  "email": "<EMAIL>",
  "password": "Password123!",
  "firstName": "Test",
  "lastName": "User"
}

# 弱密码
POST /api/auth-enhanced/register
{
  "username": "weakpass_user",
  "email": "<EMAIL>",
  "password": "123456",
  "firstName": "Weak",
  "lastName": "Password"
}
```

### 3. 密码找回测试

#### 发送重置邮件
```bash
POST /api/auth-enhanced/forgot-password
{
  "email": "<EMAIL>"
}
```

#### 重置密码
```bash
POST /api/auth-enhanced/reset-password
{
  "token": "reset_token_from_email",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

### 4. 邮箱验证测试

#### 重发验证邮件
```bash
POST /api/auth-enhanced/resend-verification
{
  "email": "<EMAIL>"
}
```

#### 验证邮箱
```bash
POST /api/auth-enhanced/verify-email
{
  "token": "verification_token_from_email"
}
```

## 🧪 自动化测试脚本

### 创建测试账户脚本
```bash
# 运行账户创建脚本
node Backend/scripts/create-test-accounts.js
```

### 认证功能测试脚本
```bash
# 运行认证测试
node Backend/scripts/test-authentication.js
```

### 前端界面测试
```bash
# 访问测试页面
http://localhost:3000/test-auth
```

## 📊 测试场景和预期结果

### 场景1: 管理员完整流程
1. **登录**: `<EMAIL>` / `Admin123!`
   - ✅ 预期: 登录成功，获得管理员权限
   - ✅ 验证: 可访问管理员面板
   
2. **权限测试**: 访问用户管理页面
   - ✅ 预期: 可以查看、编辑、删除用户
   - ✅ 验证: 管理员功能正常

### 场景2: 普通用户注册流程
1. **注册**: 新用户注册
   - ✅ 预期: 注册成功，发送验证邮件
   - ✅ 验证: 用户创建但未激活
   
2. **邮箱验证**: 点击验证链接
   - ✅ 预期: 账户激活成功
   - ✅ 验证: 可以正常登录

### 场景3: 密码找回流程
1. **忘记密码**: 输入邮箱地址
   - ✅ 预期: 发送重置邮件
   - ✅ 验证: 邮件包含重置链接
   
2. **重置密码**: 设置新密码
   - ✅ 预期: 密码更新成功
   - ✅ 验证: 可用新密码登录

### 场景4: 安全机制测试
1. **多次错误登录**: 连续输入错误密码
   - ✅ 预期: 账户临时锁定
   - ✅ 验证: 显示锁定提示
   
2. **速率限制**: 快速连续请求
   - ✅ 预期: 触发速率限制
   - ✅ 验证: 返回429状态码

## 🔧 测试工具和命令

### 快速测试命令
```bash
# 启动服务器
start-stable.bat

# 验证所有功能
verify-features.bat

# 网络诊断
network-doctor.bat

# 创建测试数据
node Backend/scripts/create-test-accounts.js

# 运行认证测试
node Backend/scripts/simple-auth-test.js
```

### API测试工具
- **Postman**: 导入API集合进行测试
- **curl**: 命令行API测试
- **浏览器**: 前端界面测试
- **自动化脚本**: 批量功能验证

### 测试数据重置
```bash
# 重置测试账户
node Backend/scripts/reset-test-data.js

# 清理测试数据
node Backend/scripts/cleanup-test-data.js
```

## 📋 测试检查清单

### 基础功能 ✅
- [ ] 管理员登录成功
- [ ] 普通用户登录成功
- [ ] 用户名登录成功
- [ ] 邮箱登录成功
- [ ] 错误密码被拒绝
- [ ] 不存在用户被拒绝

### 注册功能 ✅
- [ ] 新用户注册成功
- [ ] 重复用户名被拒绝
- [ ] 重复邮箱被拒绝
- [ ] 弱密码被拒绝
- [ ] 验证邮件发送成功
- [ ] 邮箱验证成功

### 密码管理 ✅
- [ ] 忘记密码邮件发送
- [ ] 密码重置成功
- [ ] 密码强度验证
- [ ] 新密码登录成功

### 安全机制 ✅
- [ ] 速率限制生效
- [ ] 账户锁定机制
- [ ] JWT Token验证
- [ ] Token刷新机制
- [ ] 权限控制正常

### 用户状态 ✅
- [ ] 未验证用户限制
- [ ] 停用账户拒绝
- [ ] 锁定账户处理
- [ ] 角色权限区分

## 🎯 测试重点

### 1. 安全性测试
- 密码加密存储
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 会话管理

### 2. 性能测试
- 并发登录处理
- 大量用户注册
- 数据库连接池
- 响应时间监控

### 3. 兼容性测试
- 不同浏览器
- 移动设备
- 网络环境
- 代理服务器

### 4. 用户体验测试
- 错误提示清晰
- 加载状态显示
- 表单验证友好
- 响应式设计

## 🚀 开始测试

1. **启动服务器**:
   ```bash
   start-stable.bat
   ```

2. **访问测试页面**:
   ```
   http://localhost:3000/test-auth
   ```

3. **使用测试账户**:
   - 管理员: `<EMAIL>` / `Admin123!`
   - 用户: `<EMAIL>` / `User123!`

4. **验证功能**:
   ```bash
   verify-features.bat
   ```

立即开始测试，验证Newzora认证系统的完整功能！
