# JSON解析错误解决方案

## 🚨 问题描述
```
Unexpected token '<', "<!DOCTYPE "... is not valid JSON
```

这个错误表示前端期望收到JSON响应，但实际收到了HTML页面。

## 🔍 问题原因

### 1. 后端服务器未启动
- API请求被重定向到默认的HTML页面
- 前端尝试解析HTML作为JSON导致错误

### 2. API路由配置错误
- 请求的API端点不存在
- 路由配置有误导致返回404页面

### 3. CORS或代理配置问题
- 跨域请求被阻止
- 前端代理配置错误

## 🔧 解决步骤

### 步骤1: 确保后端服务器运行
```bash
# 停止所有Node进程
taskkill /F /IM node.exe

# 启动后端服务器
cd Backend
npm run stable
# 或
node server-launcher.js
```

### 步骤2: 验证后端API
打开浏览器访问：
```
http://localhost:5000/api/health
```

应该看到JSON响应：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": {
    "status": "connected"
  }
}
```

### 步骤3: 测试认证端点
使用Postman或浏览器开发者工具测试：
```
POST http://localhost:5000/api/auth-enhanced/login
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "Admin123!",
  "rememberMe": false
}
```

### 步骤4: 检查前端配置
确认前端API配置正确：
```javascript
// Frontend/src/services/apiService.ts
const API_BASE_URL = 'http://localhost:5000/api';
```

### 步骤5: 启动前端服务器
```bash
cd Frontend
npm run stable
# 或
node stable-server.js
```

### 步骤6: 清除浏览器缓存
- 按 `Ctrl + Shift + R` 强制刷新
- 或清除浏览器缓存和Cookie

## 🧪 快速测试

### 1. 手动测试API
```bash
# Windows PowerShell
Invoke-WebRequest -Uri "http://localhost:5000/api/health" -UseBasicParsing

# 或使用curl (如果安装了)
curl http://localhost:5000/api/health
```

### 2. 测试认证API
```bash
# PowerShell
$body = @{
    identifier = "<EMAIL>"
    password = "Admin123!"
    rememberMe = $false
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/auth-enhanced/login" -Method POST -Body $body -ContentType "application/json" -UseBasicParsing
```

## 🔍 调试技巧

### 1. 浏览器开发者工具
- 按 `F12` 打开开发者工具
- 切换到 `Network` 标签
- 尝试登录，查看API请求详情
- 检查请求URL、状态码、响应内容

### 2. 检查响应头
确认API响应包含正确的Content-Type：
```
Content-Type: application/json
```

### 3. 查看完整错误信息
在浏览器控制台查看完整的错误堆栈：
```javascript
// 在控制台运行
console.error('API Error:', error);
```

## 🚀 一键修复脚本

创建 `quick-fix.bat` 文件：
```batch
@echo off
echo 修复JSON解析错误...

:: 停止所有Node进程
taskkill /F /IM node.exe

:: 等待3秒
timeout /t 3 /nobreak

:: 启动后端
cd Backend
start "Backend" cmd /k "node server-launcher.js"

:: 等待10秒让后端启动
timeout /t 10 /nobreak

:: 启动前端
cd ..\Frontend
start "Frontend" cmd /k "node stable-server.js"

:: 等待5秒
timeout /t 5 /nobreak

:: 打开测试页面
start http://localhost:3000/test-auth

echo 修复完成！
pause
```

## 📋 检查清单

### 后端检查 ✅
- [ ] 后端服务器在端口5000运行
- [ ] `/api/health` 返回JSON响应
- [ ] `/api/auth-enhanced/login` 端点可访问
- [ ] 数据库连接正常
- [ ] 没有启动错误

### 前端检查 ✅
- [ ] 前端服务器在端口3000运行
- [ ] API_BASE_URL配置正确
- [ ] 代理配置正常（如果使用）
- [ ] 浏览器缓存已清除

### 网络检查 ✅
- [ ] 端口5000和3000没有被其他程序占用
- [ ] 防火墙允许本地连接
- [ ] CORS配置正确

## 🆘 如果问题仍然存在

### 1. 检查服务器日志
查看后端服务器窗口的错误信息

### 2. 使用最小化测试
创建简单的测试文件：
```javascript
// test-api.js
const axios = require('axios');

axios.get('http://localhost:5000/api/health')
  .then(response => {
    console.log('✅ API正常:', response.data);
  })
  .catch(error => {
    console.log('❌ API错误:', error.message);
  });
```

### 3. 重置环境
```bash
# 完全重置
npm install  # 重新安装依赖
rm -rf node_modules package-lock.json  # 删除并重新安装
npm install
```

### 4. 检查端口占用
```bash
netstat -ano | findstr :5000
netstat -ano | findstr :3000
```

## 💡 预防措施

1. **始终先启动后端再启动前端**
2. **定期检查API端点是否正常**
3. **使用健康检查端点监控服务器状态**
4. **保持浏览器开发者工具打开以便调试**
5. **定期清除浏览器缓存**

## 🎯 成功标志

当问题解决后，您应该能够：
- ✅ 访问 `http://localhost:5000/api/health` 看到JSON响应
- ✅ 在前端登录页面成功登录
- ✅ 浏览器开发者工具显示API请求返回JSON
- ✅ 没有 "Unexpected token '<'" 错误

按照以上步骤，JSON解析错误应该能够得到解决！
