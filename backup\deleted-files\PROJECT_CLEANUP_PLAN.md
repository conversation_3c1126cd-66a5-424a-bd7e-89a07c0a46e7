# 🗂️ Newzora 项目结构优化计划

## 📋 当前问题分析

### 根目录问题
- ❌ 多个临时测试文件 (test-*.js, test-*.html)
- ❌ 重复的文档文件 (多个 .md 状态报告)
- ❌ 不必要的 node_modules (应该只在子项目中)
- ❌ 调试文件散落在根目录

### Backend 目录问题
- ❌ 测试和调试文件混在主目录
- ❌ 临时脚本文件
- ❌ 重复的配置文件

### Frontend 目录问题  
- ❌ 调试HTML文件
- ❌ 测试文件位置不当

## 🎯 优化目标

1. **清晰的目录结构**
2. **移除所有无用文件**
3. **保持功能完整性**
4. **统一文档管理**

## 📁 理想的项目结构

```
Newzora/
├── README.md                 # 主要文档
├── LICENSE                   # 许可证
├── package.json             # 根项目配置
├── package-lock.json        # 锁定文件
├── .gitignore              # Git忽略规则
├── 
├── Backend/                 # 后端应用
│   ├── src/                # 源代码
│   ├── config/             # 配置文件
│   ├── tests/              # 测试文件
│   └── package.json        # 后端依赖
├── 
├── Frontend/               # 前端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   ├── tests/             # 测试文件
│   └── package.json       # 前端依赖
├── 
├── docs/                  # 项目文档
│   ├── setup/            # 安装指南
│   ├── development/      # 开发文档
│   ├── deployment/       # 部署文档
│   └── reports/          # 状态报告
├── 
├── deployment/           # 部署配置
│   ├── docker/          # Docker配置
│   ├── nginx/           # Nginx配置
│   └── scripts/         # 部署脚本
└── 
└── tools/               # 开发工具
    ├── scripts/         # 实用脚本
    └── utilities/       # 工具程序
```

## 🗑️ 需要清理的文件列表

### 根目录清理
- `test-*.js` - 移动到 tools/scripts/
- `test-*.html` - 移动到 tools/scripts/
- `article-debug-test.html` - 移动到 tools/scripts/
- `comprehensive-navigation-test.html` - 移动到 tools/scripts/
- `ARTICLE_DETAIL_PAGE_FUNCTIONALITY_REPORT.md` - 移动到 docs/reports/
- `GITHUB_SYNC_COMPLETE.md` - 移动到 docs/reports/
- `NAVIGATION_ISSUES_FIXED.md` - 移动到 docs/reports/
- `NEWZORA_DEVELOPMENT_STATUS.md` - 移动到 docs/reports/
- `NEXT_DEVELOPMENT_STEPS.md` - 移动到 docs/development/
- `NOTIFICATIONS_IMPLEMENTATION.md` - 移动到 docs/reports/
- `REACT_ERROR_FIX_SUMMARY.md` - 移动到 docs/reports/
- `SUPABASE_INTEGRATION_COMPLETE.md` - 移动到 docs/reports/

### Backend 清理
- `create-admin.js` - 移动到 scripts/
- `debug-server.js` - 删除或移动到 tools/
- `test-article-creation.js` - 移动到 tests/
- `test-server.js` - 移动到 tests/

### Frontend 清理
- `debug-auth.html` - 移动到 tools/
- `test-frontend.html` - 移动到 tools/

## ✅ 执行步骤

1. 创建标准目录结构
2. 移动文件到正确位置
3. 删除真正无用的文件
4. 更新引用路径
5. 验证功能完整性
