@echo off
chcp 65001 >nul
echo 🔍 Newzora API服务器连接检查
echo ============================
echo.

echo 📋 检查项目:
echo 1. 端口监听状态
echo 2. 服务器进程状态  
echo 3. API健康检查
echo 4. 认证端点测试
echo 5. 网络连通性
echo.

:: 检查端口监听状态
echo 🚪 1. 检查端口监听状态
echo ======================
echo 检查后端端口 5000:
netstat -an | findstr :5000
if %errorlevel% == 0 (
    echo ✅ 端口 5000 正在监听
    
    echo 查找监听进程:
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        echo 进程ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo ❌ 端口 5000 没有进程监听
    echo 💡 后端服务器可能未启动
)

echo.
echo 检查前端端口 3000:
netstat -an | findstr :3000
if %errorlevel% == 0 (
    echo ✅ 端口 3000 正在监听
) else (
    echo ❌ 端口 3000 没有进程监听
    echo 💡 前端服务器可能未启动
)

:: 检查Node.js进程
echo.
echo 🔄 2. 检查Node.js进程状态
echo =========================
tasklist | findstr node.exe
if %errorlevel% == 0 (
    echo ✅ 发现Node.js进程
) else (
    echo ❌ 没有发现Node.js进程
    echo 💡 服务器可能未启动
)

:: 测试API连接
echo.
echo 🌐 3. API健康检查
echo ================
echo 测试后端健康检查端点...
powershell -Command "
try {
    Write-Host '正在连接 http://localhost:5000/api/health ...'
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10
    
    Write-Host '✅ 后端API连接成功!'
    Write-Host '   状态码:' $response.StatusCode
    Write-Host '   内容类型:' $response.Headers.'Content-Type'
    Write-Host '   响应大小:' $response.Content.Length '字节'
    
    # 解析JSON响应
    try {
        $data = $response.Content | ConvertFrom-Json
        Write-Host '   服务器状态:' $data.status
        if ($data.database) {
            Write-Host '   数据库状态:' $data.database.status
        }
        if ($data.uptime) {
            Write-Host '   运行时间:' ([math]::Round($data.uptime)) '秒'
        }
        Write-Host '✅ JSON解析成功 - API正常工作'
    } catch {
        Write-Host '❌ JSON解析失败 - 可能返回了HTML'
        Write-Host '   响应内容预览:' $response.Content.Substring(0, [Math]::Min(100, $response.Content.Length))
    }
} catch {
    Write-Host '❌ 后端API连接失败'
    Write-Host '   错误类型:' $_.Exception.GetType().Name
    Write-Host '   错误信息:' $_.Exception.Message
    
    if ($_.Exception.Message -like '*ConnectFailure*' -or $_.Exception.Message -like '*refused*') {
        Write-Host '💡 连接被拒绝 - 服务器可能未启动'
    } elseif ($_.Exception.Message -like '*timeout*') {
        Write-Host '💡 连接超时 - 服务器响应慢或网络问题'
    } elseif ($_.Exception.Message -like '*resolve*') {
        Write-Host '💡 域名解析失败 - 检查localhost配置'
    }
}
" 2>nul

:: 测试认证端点
echo.
echo 🔐 4. 认证端点测试
echo ================
echo 测试登录API端点...
powershell -Command "
try {
    Write-Host '正在测试 POST /api/auth-enhanced/login ...'
    
    $body = @{
        identifier = '<EMAIL>'
        password = 'Admin123!'
        rememberMe = $false
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10
    
    Write-Host '✅ 认证端点连接成功!'
    Write-Host '   状态码:' $response.StatusCode
    Write-Host '   内容类型:' $response.Headers.'Content-Type'
    
    # 检查响应内容
    if ($response.Headers.'Content-Type' -like '*application/json*') {
        Write-Host '✅ 返回正确的JSON格式'
        try {
            $data = $response.Content | ConvertFrom-Json
            if ($data.success) {
                Write-Host '✅ 测试登录成功'
                Write-Host '   用户:' $data.data.user.username
                Write-Host '   角色:' $data.data.user.role
            } else {
                Write-Host '⚠️ 登录失败:' $data.message
            }
        } catch {
            Write-Host '❌ JSON解析失败'
        }
    } else {
        Write-Host '❌ 返回HTML而不是JSON - 这是问题所在!'
        Write-Host '   内容类型:' $response.Headers.'Content-Type'
    }
} catch {
    Write-Host '❌ 认证端点连接失败'
    Write-Host '   状态码:' $_.Exception.Response.StatusCode
    Write-Host '   错误信息:' $_.Exception.Message
    
    # 检查错误响应
    if ($_.Exception.Response) {
        $contentType = $_.Exception.Response.Headers.'Content-Type'
        Write-Host '   响应类型:' $contentType
        
        if ($contentType -like '*text/html*') {
            Write-Host '❌ 确认: 返回HTML页面而不是JSON'
            Write-Host '💡 这是导致前端JSON解析错误的原因'
        }
    }
}
" 2>nul

:: 测试前端连接
echo.
echo 🎨 5. 前端服务器测试
echo ==================
echo 测试前端健康检查...
powershell -Command "
try {
    Write-Host '正在连接 http://localhost:3000/health ...'
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    
    Write-Host '✅ 前端服务器连接成功!'
    Write-Host '   状态码:' $response.StatusCode
    
    try {
        $data = $response.Content | ConvertFrom-Json
        Write-Host '   前端状态:' $data.status
        Write-Host '   后端URL:' $data.backend
    } catch {
        Write-Host '   响应内容:' $response.Content.Substring(0, [Math]::Min(50, $response.Content.Length))
    }
} catch {
    Write-Host '❌ 前端服务器连接失败'
    Write-Host '   错误信息:' $_.Exception.Message
}
" 2>nul

:: 网络连通性测试
echo.
echo 🌍 6. 网络连通性测试
echo ==================
echo 测试本地回环连接...
ping -n 2 127.0.0.1 | findstr "TTL"
if %errorlevel% == 0 (
    echo ✅ 本地回环网络正常
) else (
    echo ❌ 本地回环网络异常
)

echo.
echo 测试localhost解析...
nslookup localhost | findstr "127.0.0.1"
if %errorlevel% == 0 (
    echo ✅ localhost解析正常
) else (
    echo ❌ localhost解析异常
)

:: 生成诊断报告
echo.
echo 📊 诊断结果汇总
echo ===============

echo.
echo 🔧 问题诊断:
echo -----------
netstat -an | findstr :5000 >nul
if %errorlevel% neq 0 (
    echo ❌ 主要问题: 后端服务器未启动 (端口5000无监听)
    echo 💡 解决方案: 启动后端服务器
    echo    cd Backend
    echo    node server-launcher.js
    echo.
) else (
    echo ✅ 后端服务器正在运行
)

netstat -an | findstr :3000 >nul
if %errorlevel% neq 0 (
    echo ⚠️ 前端服务器未启动 (端口3000无监听)
    echo 💡 解决方案: 启动前端服务器
    echo    cd Frontend  
    echo    node stable-server.js
    echo.
) else (
    echo ✅ 前端服务器正在运行
)

echo 🚀 快速修复命令:
echo ================
echo 1. 停止所有服务器:
echo    taskkill /F /IM node.exe
echo.
echo 2. 启动后端服务器:
echo    cd Backend ^&^& start cmd /k "node server-launcher.js"
echo.
echo 3. 启动前端服务器:
echo    cd Frontend ^&^& start cmd /k "node stable-server.js"
echo.
echo 4. 等待30秒后测试:
echo    http://localhost:5000/api/health
echo    http://localhost:3000/test-auth
echo.

echo 📋 测试账户:
echo ===========
echo 管理员: <EMAIL> / Admin123!
echo 用户:   <EMAIL> / User123!
echo.

echo ✅ 服务器连接检查完成!
echo.
echo 按任意键退出...
pause >nul
