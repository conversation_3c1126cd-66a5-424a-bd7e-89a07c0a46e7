@echo off
echo 🔍 Newzora System Diagnosis
echo ===========================

echo.
echo 📋 System Information
echo =====================
echo Node.js Version:
node --version
echo.
echo NPM Version:
npm --version

echo.
echo 🌐 Port Status Check
echo ====================
echo Checking port 5000 (Backend):
netstat -ano | findstr :5000
if %errorlevel% == 0 (
    echo ✅ Port 5000 is in use
) else (
    echo ❌ Port 5000 is free
)

echo.
echo Checking port 3000 (Frontend):
netstat -ano | findstr :3000
if %errorlevel% == 0 (
    echo ✅ Port 3000 is in use
) else (
    echo ❌ Port 3000 is free
)

echo.
echo 🗄️ Database Connection Test
echo ===========================
cd Backend
node scripts/simple-db-test.js

echo.
echo 📦 Backend Dependencies Check
echo =============================
echo Checking if node_modules exists...
if exist node_modules (
    echo ✅ node_modules folder exists
) else (
    echo ❌ node_modules folder missing - run 'npm install'
)

echo.
echo Checking key dependencies...
node -e "try { require('express'); console.log('✅ Express installed'); } catch(e) { console.log('❌ Express missing'); }"
node -e "try { require('sequelize'); console.log('✅ Sequelize installed'); } catch(e) { console.log('❌ Sequelize missing'); }"
node -e "try { require('bcryptjs'); console.log('✅ bcryptjs installed'); } catch(e) { console.log('❌ bcryptjs missing'); }"
node -e "try { require('jsonwebtoken'); console.log('✅ jsonwebtoken installed'); } catch(e) { console.log('❌ jsonwebtoken missing'); }"

echo.
echo 📄 Configuration Files Check
echo ============================
if exist .env (
    echo ✅ .env file exists
) else (
    echo ❌ .env file missing
)

if exist config\database.js (
    echo ✅ Database config exists
) else (
    echo ❌ Database config missing
)

echo.
echo 🔧 Server Startup Test
echo ======================
echo Testing server startup (will timeout after 10 seconds)...
timeout 10 node simple-server.js
echo.
echo If server started successfully, you should see startup messages above.

echo.
echo 📊 Diagnosis Complete
echo ====================
echo.
echo 💡 Common Solutions:
echo ===================
echo 1. If dependencies missing: npm install
echo 2. If database fails: Check PostgreSQL is running
echo 3. If ports in use: Kill existing processes
echo 4. If .env missing: Copy from .env.example
echo.
echo 🚀 Quick Fix Commands:
echo =====================
echo npm install                    # Install dependencies
echo node scripts/simple-db-test.js # Test database
echo node simple-server.js         # Start server manually
echo.
echo Press any key to exit...
pause >nul
