@echo off
chcp 65001 >nul
echo 🔧 修复 JSON 解析错误
echo ====================
echo.

echo 问题: "Unexpected token '<', '<!DOCTYPE '... is not valid JSON"
echo 原因: API请求返回了HTML而不是JSON数据
echo.

echo 🚀 开始修复...
echo ==============

:: 步骤1: 停止所有Node进程
echo 步骤1: 停止现有服务器进程
echo -------------------------
taskkill /F /IM node.exe >nul 2>&1
echo ✅ 已停止所有Node进程

:: 等待进程完全停止
timeout /t 3 /nobreak >nul

:: 步骤2: 检查端口释放
echo.
echo 步骤2: 检查端口状态
echo ------------------
netstat -an | findstr :5000 >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口5000仍被占用，尝试强制释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
        taskkill /PID %%a /F >nul 2>&1
    )
) else (
    echo ✅ 端口5000已释放
)

netstat -an | findstr :3000 >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口3000仍被占用，尝试强制释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        taskkill /PID %%a /F >nul 2>&1
    )
) else (
    echo ✅ 端口3000已释放
)

:: 步骤3: 启动后端服务器
echo.
echo 步骤3: 启动后端服务器
echo --------------------
cd Backend

echo 检查后端依赖...
if not exist node_modules (
    echo 安装后端依赖...
    npm install
)

echo 启动稳定后端服务器...
start "Newzora Backend (Fixed)" cmd /k "echo 🚀 启动后端服务器... && node server-launcher.js"

:: 步骤4: 等待后端启动
echo.
echo 步骤4: 等待后端服务器启动
echo -------------------------
echo 等待15秒让服务器完全启动...
timeout /t 15 /nobreak >nul

:: 步骤5: 测试后端连接
echo.
echo 步骤5: 测试后端API连接
echo ----------------------
powershell -Command "
$maxAttempts = 5
$attempt = 1

while ($attempt -le $maxAttempts) {
    try {
        Write-Host '尝试连接后端服务器 (第' $attempt '次)...'
        $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10
        
        Write-Host '✅ 后端服务器连接成功!'
        Write-Host '   状态码:' $response.StatusCode
        Write-Host '   内容类型:' $response.Headers.'Content-Type'
        
        # 检查是否返回JSON
        $contentType = $response.Headers.'Content-Type'
        if ($contentType -like '*application/json*') {
            Write-Host '✅ 返回正确的JSON格式'
            $data = $response.Content | ConvertFrom-Json
            Write-Host '   服务器状态:' $data.status
            Write-Host '   数据库状态:' $data.database.status
        } else {
            Write-Host '⚠️ 返回的不是JSON格式:' $contentType
        }
        break
    } catch {
        Write-Host '❌ 连接失败:' $_.Exception.Message
        if ($attempt -eq $maxAttempts) {
            Write-Host '❌ 后端服务器启动失败，请检查服务器窗口的错误信息'
            exit 1
        }
        Start-Sleep -Seconds 3
        $attempt++
    }
}
" 2>nul

if %errorlevel% neq 0 (
    echo ❌ 后端服务器启动失败
    echo 💡 请检查后端服务器窗口的错误信息
    pause
    exit /b 1
)

:: 步骤6: 测试认证端点
echo.
echo 步骤6: 测试认证API端点
echo ---------------------
powershell -Command "
try {
    Write-Host '测试认证端点...'
    $body = @{
        identifier = '<EMAIL>'
        password = 'Admin123!'
        rememberMe = $false
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10
    
    Write-Host '✅ 认证端点工作正常'
    Write-Host '   状态码:' $response.StatusCode
    
    # 检查响应内容
    $contentType = $response.Headers.'Content-Type'
    if ($contentType -like '*application/json*') {
        Write-Host '✅ 认证端点返回正确的JSON格式'
        $data = $response.Content | ConvertFrom-Json
        if ($data.success) {
            Write-Host '✅ 测试登录成功'
        } else {
            Write-Host '⚠️ 登录失败:' $data.message
        }
    } else {
        Write-Host '❌ 认证端点返回HTML而不是JSON'
        Write-Host '   内容类型:' $contentType
        Write-Host '   这是问题的根源!'
    }
} catch {
    Write-Host '❌ 认证端点测试失败'
    Write-Host '   状态码:' $_.Exception.Response.StatusCode
    Write-Host '   错误:' $_.Exception.Message
    
    # 检查错误响应内容
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        if ($responseBody -like '*<!DOCTYPE*' -or $responseBody -like '*<html*') {
            Write-Host '❌ 确认: 认证端点返回HTML页面'
            Write-Host '   这说明路由配置有问题'
        }
    }
}
" 2>nul

:: 步骤7: 启动前端服务器
echo.
echo 步骤7: 启动前端服务器
echo --------------------
cd ..\Frontend

echo 检查前端依赖...
if not exist node_modules (
    echo 安装前端依赖...
    npm install
)

echo 启动前端服务器...
start "Newzora Frontend (Fixed)" cmd /k "echo 🎨 启动前端服务器... && node stable-server.js"

:: 步骤8: 等待前端启动
echo.
echo 步骤8: 等待前端服务器启动
echo -------------------------
echo 等待10秒让前端服务器启动...
timeout /t 10 /nobreak >nul

:: 步骤9: 测试前端连接
echo.
echo 步骤9: 测试前端服务器
echo --------------------
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 前端服务器运行正常'
    Write-Host '   状态码:' $response.StatusCode
} catch {
    Write-Host '❌ 前端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

cd ..

:: 步骤10: 显示修复结果
echo.
echo 🎉 修复完成!
echo ============
echo.
echo 📍 服务器状态:
echo    后端: http://localhost:5000
echo    前端: http://localhost:3000
echo    测试页面: http://localhost:3000/test-auth
echo.
echo 🧪 测试建议:
echo    1. 打开浏览器访问: http://localhost:3000/test-auth
echo    2. 按F12打开开发者工具，查看Network标签
echo    3. 尝试登录测试账户: <EMAIL> / Admin123!
echo    4. 检查API请求是否返回正确的JSON响应
echo.
echo 💡 如果问题仍然存在:
echo    1. 检查后端服务器窗口是否有错误信息
echo    2. 检查前端服务器窗口是否有错误信息
echo    3. 清除浏览器缓存 (Ctrl+Shift+R)
echo    4. 检查浏览器控制台的错误信息
echo.

echo 🌍 自动打开测试页面...
timeout /t 3 /nobreak >nul
start http://localhost:3000/test-auth

echo.
echo ✅ 修复脚本执行完成!
echo 如果问题仍然存在，请查看服务器窗口的详细错误信息。
echo.
echo 按任意键退出...
pause >nul
