// 网络安全的服务器配置，解决网络连接问题
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const { sequelize } = require('./config/database');
const os = require('os');

const app = express();
const PORT = process.env.PORT || 5000;

// 获取本机IP地址
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return '127.0.0.1';
}

const localIP = getLocalIP();

// 更宽松的CORS配置
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    `http://${localIP}:3000`,
    'http://localhost:3001',
    'http://127.0.0.1:3001'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 基础中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// 健康检查端点 - 多个版本
app.get('/api/health', async (req, res) => {
  try {
    await sequelize.authenticate();
    const healthData = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: 'connected',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      network: {
        localIP: localIP,
        hostname: os.hostname(),
        platform: os.platform()
      },
      server: {
        port: PORT,
        nodeVersion: process.version,
        environment: process.env.NODE_ENV || 'development'
      }
    };
    
    res.json(healthData);
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// 简化的健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'Newzora Backend Server',
    status: 'running',
    endpoints: {
      health: '/api/health',
      auth: '/api/auth-enhanced',
      docs: '/api/docs'
    },
    network: {
      localIP: localIP,
      accessUrls: [
        `http://localhost:${PORT}`,
        `http://127.0.0.1:${PORT}`,
        `http://${localIP}:${PORT}`
      ]
    }
  });
});

// 认证路由
try {
  app.use('/api/auth-enhanced', require('./routes/auth-enhanced'));
  console.log('✅ Auth routes loaded');
} catch (error) {
  console.error('❌ Failed to load auth routes:', error.message);
}

// 网络测试端点
app.get('/api/network-test', (req, res) => {
  res.json({
    success: true,
    message: 'Network connection successful',
    clientIP: req.ip,
    serverIP: localIP,
    headers: req.headers,
    timestamp: new Date().toISOString()
  });
});

// CORS预检请求处理
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

// 错误处理
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    availableRoutes: [
      'GET /',
      'GET /api/health',
      'GET /health',
      'POST /api/auth-enhanced/login',
      'POST /api/auth-enhanced/register'
    ]
  });
});

// 启动服务器函数
async function startServer() {
  try {
    console.log('🚀 Starting Newzora Network-Safe Server...');
    console.log('==========================================');
    
    // 网络信息
    console.log('🌐 Network Information:');
    console.log(`   Local IP: ${localIP}`);
    console.log(`   Hostname: ${os.hostname()}`);
    console.log(`   Platform: ${os.platform()}`);
    
    // 测试数据库连接
    console.log('\n📡 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // 尝试多种绑定方式
    const bindOptions = [
      { host: '0.0.0.0', description: 'All interfaces' },
      { host: 'localhost', description: 'Localhost only' },
      { host: '127.0.0.1', description: 'Loopback interface' },
      { host: localIP, description: 'Local network interface' }
    ];

    let serverStarted = false;
    
    for (const option of bindOptions) {
      if (serverStarted) break;
      
      try {
        console.log(`\n🔌 Attempting to bind to ${option.host} (${option.description})...`);
        
        const server = await new Promise((resolve, reject) => {
          const srv = app.listen(PORT, option.host, () => {
            resolve(srv);
          });
          
          srv.on('error', (err) => {
            reject(err);
          });
          
          // 超时处理
          setTimeout(() => {
            reject(new Error('Server start timeout'));
          }, 5000);
        });

        console.log(`✅ Server successfully started!`);
        console.log(`🌐 Server running on http://${option.host}:${PORT}`);
        
        // 显示所有可能的访问地址
        console.log('\n📍 Access URLs:');
        console.log(`   http://localhost:${PORT}`);
        console.log(`   http://127.0.0.1:${PORT}`);
        if (localIP !== '127.0.0.1') {
          console.log(`   http://${localIP}:${PORT}`);
        }
        
        console.log('\n🔗 API Endpoints:');
        console.log(`   Health Check: http://localhost:${PORT}/api/health`);
        console.log(`   Network Test: http://localhost:${PORT}/api/network-test`);
        console.log(`   Authentication: http://localhost:${PORT}/api/auth-enhanced`);
        
        serverStarted = true;

        // 优雅关闭处理
        const gracefulShutdown = () => {
          console.log('\n🛑 Shutting down gracefully...');
          server.close(() => {
            console.log('✅ Server closed');
            sequelize.close().then(() => {
              console.log('✅ Database connection closed');
              process.exit(0);
            });
          });
        };

        process.on('SIGTERM', gracefulShutdown);
        process.on('SIGINT', gracefulShutdown);
        
        // 定期网络健康检查
        setInterval(() => {
          console.log(`💓 Server heartbeat - Uptime: ${Math.floor(process.uptime())}s`);
        }, 30000);

      } catch (error) {
        console.log(`❌ Failed to bind to ${option.host}: ${error.message}`);
        continue;
      }
    }

    if (!serverStarted) {
      throw new Error('Failed to start server on any interface');
    }

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    console.error('\n🔧 Troubleshooting suggestions:');
    console.error('1. Check if port is already in use: netstat -ano | findstr :5000');
    console.error('2. Run as administrator');
    console.error('3. Check firewall settings');
    console.error('4. Try a different port: PORT=5001 node network-safe-server.js');
    console.error('5. Run network diagnosis: network-diagnosis.bat');
    process.exit(1);
  }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

startServer();
