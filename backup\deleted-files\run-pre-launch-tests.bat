@echo off
chcp 65001 >nul
echo 🚀 Newzora 上线前完整功能测试
echo =============================
echo.

echo 📋 测试准备检查
echo ==============
echo 1. 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或不可用
    pause
    exit /b 1
)
echo ✅ Node.js 环境正常

echo.
echo 2. 检查项目依赖...
cd Backend
if not exist node_modules (
    echo 安装后端依赖...
    npm install
)
echo ✅ 后端依赖完整

cd ..\Frontend
if not exist node_modules (
    echo 安装前端依赖...
    npm install
)
echo ✅ 前端依赖完整

cd ..

echo.
echo 3. 启动测试环境...
echo ==================

:: 停止现有进程
echo 停止现有Node进程...
taskkill /F /IM node.exe >nul 2>&1

:: 等待进程停止
timeout /t 3 /nobreak >nul

:: 启动后端服务器
echo 启动后端服务器...
cd Backend
start "Newzora Backend (Testing)" cmd /k "echo 🚀 后端测试服务器启动中... && node server-launcher.js"

:: 等待后端启动
echo 等待后端服务器启动 (20秒)...
timeout /t 20 /nobreak >nul

:: 启动前端服务器
echo 启动前端服务器...
cd ..\Frontend
start "Newzora Frontend (Testing)" cmd /k "echo 🎨 前端测试服务器启动中... && node simple-frontend.js"

:: 等待前端启动
echo 等待前端服务器启动 (10秒)...
timeout /t 10 /nobreak >nul

cd ..

echo.
echo 4. 验证服务器状态...
echo ==================
echo 测试后端连接...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 后端服务器运行正常'
    $data = $response.Content | ConvertFrom-Json
    Write-Host '   状态:' $data.status
} catch {
    Write-Host '❌ 后端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
    exit 1
}
" 2>nul

if %errorlevel% neq 0 (
    echo ❌ 后端服务器未正常启动，无法进行测试
    echo 💡 请检查后端服务器窗口的错误信息
    pause
    exit /b 1
)

echo 测试前端连接...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 前端服务器运行正常'
} catch {
    Write-Host '❌ 前端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

echo.
echo 🧪 开始执行自动化测试套件
echo =========================
cd Backend
node ../automated-testing-suite.js

set "test_result=%errorlevel%"

echo.
echo 📊 测试执行完成
echo ==============

if %test_result% equ 0 (
    echo ✅ 所有测试通过！
    echo 🎉 Newzora 已准备好上线！
    echo.
    echo 📋 上线前最终检查清单:
    echo ========================
    echo [ ] 生产环境服务器准备就绪
    echo [ ] 域名和DNS配置完成
    echo [ ] SSL证书安装和配置
    echo [ ] 数据库生产环境迁移
    echo [ ] 环境变量配置正确
    echo [ ] 备份策略实施
    echo [ ] 监控和日志系统配置
    echo [ ] 错误报告和通知设置
    echo [ ] 性能监控配置
    echo [ ] 安全策略实施
    echo.
    echo 🚀 建议上线步骤:
    echo ===============
    echo 1. 部署到预生产环境进行最终验证
    echo 2. 执行数据库迁移和初始化
    echo 3. 配置生产环境变量
    echo 4. 部署应用到生产服务器
    echo 5. 配置负载均衡和CDN
    echo 6. 执行生产环境冒烟测试
    echo 7. 监控系统启动和运行状态
    echo 8. 正式发布和公告
) else (
    echo ❌ 测试发现问题，需要修复后再上线
    echo.
    echo 🔧 问题修复建议:
    echo ===============
    echo 1. 查看详细测试报告: Backend/test-reports/
    echo 2. 修复失败的测试用例
    echo 3. 重新运行测试验证修复
    echo 4. 确保所有测试通过后再考虑上线
    echo.
    echo 💡 常见问题排查:
    echo ===============
    echo - 检查数据库连接和配置
    echo - 验证API端点和路由配置
    echo - 确认用户权限和认证流程
    echo - 检查第三方服务集成
    echo - 验证邮件服务配置
)

echo.
echo 📍 测试环境信息:
echo ===============
echo 后端服务器: http://localhost:5000
echo 前端应用:   http://localhost:3000
echo 测试页面:   http://localhost:3000/test-auth
echo 管理面板:   http://localhost:3000/admin
echo.
echo 🧪 手动测试建议:
echo ===============
echo 1. 访问前端应用，测试用户界面
echo 2. 使用测试账户验证登录流程
echo 3. 创建和编辑文章测试内容管理
echo 4. 测试社交功能（点赞、评论、关注）
echo 5. 验证管理员功能和权限控制
echo 6. 测试移动端响应式设计
echo 7. 验证邮件通知功能
echo 8. 测试搜索和筛选功能
echo.
echo 📧 测试账户信息:
echo ===============
echo 管理员: <EMAIL> / Admin123!
echo 用户:   <EMAIL> / User123!
echo 编辑:   <EMAIL> / Moderator123!
echo.

if %test_result% equ 0 (
    echo 🎯 恭喜！Newzora 已通过所有上线前测试！
    echo 🚀 系统已准备好正式上线！
) else (
    echo ⚠️ 请修复测试中发现的问题后重新测试
)

echo.
echo 按任意键退出测试程序...
pause >nul

:: 询问是否保持服务器运行
echo.
set /p keep_running="是否保持测试服务器运行以便手动测试? (y/n): "
if /i not "%keep_running%"=="y" (
    echo 停止测试服务器...
    taskkill /F /IM node.exe >nul 2>&1
    echo ✅ 测试服务器已停止
)
