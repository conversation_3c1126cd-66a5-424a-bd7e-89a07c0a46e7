#!/usr/bin/env node

/**
 * Newzora 前端稳定服务器
 * 支持多种网络环境的前端开发服务器
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');
const fs = require('fs');
const os = require('os');

const app = express();

// 配置
const config = {
  port: parseInt(process.env.FRONTEND_PORT) || 3000,
  host: process.env.FRONTEND_HOST || '0.0.0.0',
  backendUrl: process.env.BACKEND_URL || 'http://localhost:5000',
  environment: process.env.NODE_ENV || 'development',
  enableProxy: process.env.ENABLE_PROXY !== 'false',
  staticPath: process.env.STATIC_PATH || path.join(__dirname, 'build'),
  publicPath: process.env.PUBLIC_PATH || path.join(__dirname, 'public')
};

// 获取网络信息
function getNetworkInfo() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4') {
        addresses.push({
          name,
          address: interface.address,
          internal: interface.internal
        });
      }
    }
  }
  
  return {
    hostname: os.hostname(),
    platform: os.platform(),
    addresses
  };
}

const networkInfo = getNetworkInfo();

console.log('🎨 Newzora Frontend Server Starting...');
console.log('=====================================');
console.log(`Environment: ${config.environment}`);
console.log(`Backend URL: ${config.backendUrl}`);

// 1. 静态文件服务
if (fs.existsSync(config.staticPath)) {
  app.use(express.static(config.staticPath, {
    maxAge: config.environment === 'production' ? '1y' : '0',
    etag: true,
    lastModified: true
  }));
  console.log('✅ Static files served from:', config.staticPath);
}

if (fs.existsSync(config.publicPath)) {
  app.use('/public', express.static(config.publicPath));
  console.log('✅ Public files served from:', config.publicPath);
}

// 2. API代理 - 解决跨域问题
if (config.enableProxy) {
  const proxyOptions = {
    target: config.backendUrl,
    changeOrigin: true,
    secure: false, // 允许自签名证书
    timeout: 30000, // 30秒超时
    proxyTimeout: 30000,
    onError: (err, req, res) => {
      console.error('Proxy Error:', err.message);
      res.status(500).json({
        error: 'Proxy Error',
        message: 'Backend server is not available',
        backend: config.backendUrl
      });
    },
    onProxyReq: (proxyReq, req, res) => {
      // 添加原始主机头
      proxyReq.setHeader('X-Forwarded-Host', req.get('host'));
      proxyReq.setHeader('X-Forwarded-Proto', req.protocol);
      proxyReq.setHeader('X-Real-IP', req.ip);
    },
    onProxyRes: (proxyRes, req, res) => {
      // 添加CORS头
      proxyRes.headers['Access-Control-Allow-Origin'] = '*';
      proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,OPTIONS';
      proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization,X-Requested-With';
    },
    logLevel: config.environment === 'development' ? 'debug' : 'warn'
  };

  app.use('/api', createProxyMiddleware(proxyOptions));
  console.log('✅ API proxy enabled:', config.backendUrl);
}

// 3. 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: config.environment,
    backend: config.backendUrl,
    network: {
      hostname: networkInfo.hostname,
      addresses: networkInfo.addresses.map(addr => ({
        address: addr.address,
        internal: addr.internal
      }))
    }
  });
});

// 4. 开发环境热重载支持
if (config.environment === 'development') {
  // WebSocket代理用于热重载
  const { createProxyMiddleware: wsProxy } = require('http-proxy-middleware');
  
  app.use('/sockjs-node', wsProxy({
    target: 'http://localhost:3001', // React开发服务器的WebSocket
    ws: true,
    changeOrigin: true,
    logLevel: 'silent'
  }));
}

// 5. SPA路由支持 - 所有非API请求返回index.html
app.get('*', (req, res) => {
  const indexPath = path.join(config.staticPath, 'index.html');
  
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    // 开发环境返回简单的HTML
    res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Newzora</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
              margin: 0; padding: 40px; background: #f5f5f5; 
            }
            .container { 
              max-width: 800px; margin: 0 auto; background: white; 
              padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .status { color: #28a745; font-weight: bold; }
            .error { color: #dc3545; }
            .info { color: #007bff; }
            ul { list-style: none; padding: 0; }
            li { padding: 8px 0; border-bottom: 1px solid #eee; }
            .endpoint { font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🎨 Newzora Frontend Server</h1>
            <p class="status">✅ Server is running</p>
            
            <h2>📍 Access Information</h2>
            <ul>
              <li><strong>Environment:</strong> ${config.environment}</li>
              <li><strong>Frontend Port:</strong> ${config.port}</li>
              <li><strong>Backend URL:</strong> ${config.backendUrl}</li>
              <li><strong>Proxy Enabled:</strong> ${config.enableProxy ? 'Yes' : 'No'}</li>
            </ul>
            
            <h2>🔗 Available Endpoints</h2>
            <ul>
              <li><span class="endpoint">GET /health</span> - Frontend health check</li>
              <li><span class="endpoint">GET /api/*</span> - Proxied to backend</li>
              <li><span class="endpoint">GET /*</span> - SPA routing</li>
            </ul>
            
            <h2>🌐 Network Access</h2>
            <ul>
              <li><a href="http://localhost:${config.port}">http://localhost:${config.port}</a></li>
              <li><a href="http://127.0.0.1:${config.port}">http://127.0.0.1:${config.port}</a></li>
              ${networkInfo.addresses
                .filter(addr => !addr.internal)
                .map(addr => `<li><a href="http://${addr.address}:${config.port}">http://${addr.address}:${config.port}</a> (${addr.name})</li>`)
                .join('')}
            </ul>
            
            <h2>🧪 Test Links</h2>
            <ul>
              <li><a href="/api/health">Backend Health Check</a></li>
              <li><a href="/test-auth">Authentication Test Page</a></li>
              <li><a href="/admin">Admin Dashboard</a></li>
            </ul>
            
            <p class="info">
              💡 This is a development server. In production, build the React app and serve static files.
            </p>
          </div>
        </body>
      </html>
    `);
  }
});

// 错误处理
app.use((error, req, res, next) => {
  console.error('Frontend Server Error:', error);
  res.status(500).json({
    error: 'Frontend Server Error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
function startServer() {
  const server = app.listen(config.port, config.host, () => {
    const address = server.address();
    
    console.log(`\n🌐 Frontend Server running on ${config.host}:${address.port}`);
    
    // 显示访问地址
    console.log('\n📍 Access URLs:');
    console.log(`   http://localhost:${address.port}`);
    console.log(`   http://127.0.0.1:${address.port}`);
    
    networkInfo.addresses.forEach(addr => {
      if (!addr.internal) {
        console.log(`   http://${addr.address}:${address.port} (${addr.name})`);
      }
    });
    
    console.log('\n🔗 Important Links:');
    console.log(`   Health Check: http://localhost:${address.port}/health`);
    console.log(`   API Proxy: http://localhost:${address.port}/api/*`);
    console.log(`   Test Auth: http://localhost:${address.port}/test-auth`);
    
    console.log('\n✅ Frontend server started successfully!');
  });

  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`❌ Port ${config.port} is already in use`);
      console.log('💡 Try using a different port: FRONTEND_PORT=3001 node stable-server.js');
    } else {
      console.error('❌ Frontend server error:', error.message);
    }
    process.exit(1);
  });

  // 优雅关闭
  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down frontend server...');
    server.close(() => {
      console.log('✅ Frontend server closed');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down frontend server...');
    server.close(() => {
      console.log('✅ Frontend server closed');
      process.exit(0);
    });
  });
}

// 启动
if (require.main === module) {
  startServer();
}

module.exports = { app, config, networkInfo };
