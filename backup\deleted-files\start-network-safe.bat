@echo off
echo 🌐 Starting Newzora Network-Safe Server
echo =======================================

echo.
echo 🔍 Pre-flight Network Check
echo ===========================

echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)
echo ✅ Node.js is available

echo.
echo Checking network interfaces...
ipconfig | findstr /C:"IPv4"

echo.
echo Checking port availability...
netstat -an | findstr :5000 >nul
if %errorlevel% == 0 (
    echo ⚠️ Port 5000 is in use. Attempting to free it...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        echo Killing process %%a...
        taskkill /PID %%a /F >nul 2>&1
    )
) else (
    echo ✅ Port 5000 is available
)

echo.
echo 🚀 Starting Network-Safe Backend Server
echo =======================================
cd Backend

echo Starting server with enhanced network compatibility...
start "Newzora Backend (Network Safe)" cmd /k "node network-safe-server.js"

echo.
echo ⏳ Waiting for server to start...
timeout /t 8 /nobreak >nul

echo.
echo 🧪 Testing Server Connectivity
echo ==============================

echo Testing localhost connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ Localhost connection successful'; $data = $response.Content | ConvertFrom-Json; Write-Host 'Server Status:' $data.status; Write-Host 'Database:' $data.database } catch { Write-Host '❌ Localhost connection failed:' $_.Exception.Message }"

echo.
echo Testing 127.0.0.1 connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:5000/api/health' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ 127.0.0.1 connection successful' } catch { Write-Host '❌ 127.0.0.1 connection failed:' $_.Exception.Message }"

echo.
echo Testing network endpoint...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/network-test' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ Network test endpoint accessible' } catch { Write-Host '❌ Network test endpoint failed:' $_.Exception.Message }"

echo.
echo 🔐 Testing Authentication
echo =========================
echo Testing login with admin account...
powershell -Command "try { $body = @{ identifier='<EMAIL>'; password='Admin123!'; rememberMe=$false } | ConvertTo-Json; $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 15; $data = $response.Content | ConvertFrom-Json; if ($data.success) { Write-Host '✅ Authentication working - User:' $data.data.user.username } else { Write-Host '❌ Authentication failed:' $data.message } } catch { Write-Host '❌ Authentication test error:' $_.Exception.Message }"

echo.
echo 🎨 Starting Frontend Server
echo ===========================
cd ..\Frontend

echo Checking if frontend dependencies are installed...
if not exist node_modules (
    echo ⚠️ Frontend dependencies not found. Installing...
    npm install
)

echo Starting frontend development server...
start "Newzora Frontend" cmd /k "npm run dev"

echo.
echo 🎉 Servers Started!
echo ==================
echo.
echo 📍 Access Points:
echo ================
echo Backend Server:
echo   • http://localhost:5000
echo   • http://127.0.0.1:5000
echo.
echo Frontend Application:
echo   • http://localhost:3000
echo   • http://127.0.0.1:3000
echo.
echo 🔗 Important URLs:
echo =================
echo   • Health Check: http://localhost:5000/api/health
echo   • Network Test: http://localhost:5000/api/network-test
echo   • Auth Test Page: http://localhost:3000/test-auth
echo   • API Documentation: http://localhost:5000/
echo.
echo 👤 Test Accounts:
echo ================
echo   Admin:    <EMAIL>     / Admin123!
echo   User:     <EMAIL>     / User123!
echo   Moderator: <EMAIL> / Moderator123!
echo.
echo 🔧 Troubleshooting:
echo ==================
echo   • If connection fails, run: network-diagnosis.bat
echo   • If ports conflict, run: fix-network.bat
echo   • Check server windows for error messages
echo   • Ensure Windows Firewall allows Node.js
echo.
echo Press any key to open test page in browser...
pause >nul

echo Opening test page...
start http://localhost:3000/test-auth

echo.
echo ✅ Setup complete! Check the browser and server windows.
echo Press any key to exit...
pause >nul
