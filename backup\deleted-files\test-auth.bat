@echo off
echo 🧪 Newzora Authentication Test
echo ==============================

echo.
echo 📡 Checking server status...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ Backend server is running'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host '❌ Backend server is not running'; Write-Host 'Please run start-servers.bat first' }"

echo.
echo 🔐 Testing login with admin account...
powershell -Command "try { $body = @{ identifier='<EMAIL>'; password='Admin123!'; rememberMe=$false } | ConvertTo-Json; $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10; $data = $response.Content | ConvertFrom-Json; if ($data.success) { Write-Host '✅ Admin login successful'; Write-Host 'User:' $data.data.user.username; Write-Host 'Role:' $data.data.user.role } else { Write-Host '❌ Login failed:' $data.message } } catch { Write-Host '❌ Login test failed:' $_.Exception.Message }"

echo.
echo 🔐 Testing login with user account...
powershell -Command "try { $body = @{ identifier='<EMAIL>'; password='User123!'; rememberMe=$false } | ConvertTo-Json; $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10; $data = $response.Content | ConvertFrom-Json; if ($data.success) { Write-Host '✅ User login successful'; Write-Host 'User:' $data.data.user.username; Write-Host 'Role:' $data.data.user.role } else { Write-Host '❌ Login failed:' $data.message } } catch { Write-Host '❌ Login test failed:' $_.Exception.Message }"

echo.
echo 💪 Testing password strength...
powershell -Command "try { $body = @{ password='TestPassword123!' } | ConvertTo-Json; $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/check-password-strength' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10; $data = $response.Content | ConvertFrom-Json; Write-Host '✅ Password strength test:' $data.data.strength '(' $data.data.isValid ')' } catch { Write-Host '❌ Password strength test failed:' $_.Exception.Message }"

echo.
echo 📊 Test Summary
echo ===============
echo If all tests show ✅, your authentication system is working!
echo.
echo 🌐 Next Steps:
echo =============
echo 1. Open browser: http://localhost:3000/test-auth
echo 2. Test the visual interface
echo 3. Try different user accounts
echo.
echo 📋 Available Test Accounts:
echo ==========================
echo Admin:     <EMAIL>      / Admin123!
echo Moderator: <EMAIL>  / Moderator123!
echo User 1:    <EMAIL>      / User123!
echo User 2:    <EMAIL>      / User123!
echo.
echo Press any key to exit...
pause >nul
