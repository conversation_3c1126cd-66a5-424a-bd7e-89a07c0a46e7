@echo off
chcp 65001 >nul
echo 🧹 Newzora 旧服务器清理工具
echo ===========================
echo.

echo ⚠️ 警告: 此操作将清理旧的服务器文件
echo 但会保留所有功能代码和配置
echo.

set /p confirm="确认继续清理? (y/n): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🔍 扫描旧服务器文件...
echo =====================

:: 备份重要文件
echo 📦 创建备份...
if not exist backup mkdir backup
if not exist backup\%date:~0,10% mkdir backup\%date:~0,10%

:: 备份旧的服务器文件
if exist Backend\server.js (
    copy Backend\server.js backup\%date:~0,10%\server.js.bak >nul 2>&1
    echo ✅ 已备份 server.js
)

if exist Backend\simple-server.js (
    copy Backend\simple-server.js backup\%date:~0,10%\simple-server.js.bak >nul 2>&1
    echo ✅ 已备份 simple-server.js
)

if exist Backend\network-safe-server.js (
    copy Backend\network-safe-server.js backup\%date:~0,10%\network-safe-server.js.bak >nul 2>&1
    echo ✅ 已备份 network-safe-server.js
)

if exist Backend\minimal-server.js (
    copy Backend\minimal-server.js backup\%date:~0,10%\minimal-server.js.bak >nul 2>&1
    echo ✅ 已备份 minimal-server.js
)

echo.
echo 🗂️ 整理文件结构...
echo ==================

:: 创建旧文件目录
if not exist Backend\legacy mkdir Backend\legacy

:: 移动旧服务器文件到legacy目录
if exist Backend\simple-server.js (
    move Backend\simple-server.js Backend\legacy\ >nul 2>&1
    echo ✅ 移动 simple-server.js 到 legacy 目录
)

if exist Backend\network-safe-server.js (
    move Backend\network-safe-server.js Backend\legacy\ >nul 2>&1
    echo ✅ 移动 network-safe-server.js 到 legacy 目录
)

if exist Backend\minimal-server.js (
    move Backend\minimal-server.js Backend\legacy\ >nul 2>&1
    echo ✅ 移动 minimal-server.js 到 legacy 目录
)

:: 移动旧的启动脚本
if exist start-servers.bat (
    move start-servers.bat backup\%date:~0,10%\ >nul 2>&1
    echo ✅ 移动旧启动脚本到备份目录
)

if exist start-network-safe.bat (
    move start-network-safe.bat backup\%date:~0,10%\ >nul 2>&1
    echo ✅ 移动网络安全启动脚本到备份目录
)

if exist test-auth.bat (
    move test-auth.bat backup\%date:~0,10%\ >nul 2>&1
    echo ✅ 移动测试脚本到备份目录
)

if exist diagnose-issues.bat (
    move diagnose-issues.bat backup\%date:~0,10%\ >nul 2>&1
    echo ✅ 移动诊断脚本到备份目录
)

if exist fix-network.bat (
    move fix-network.bat backup\%date:~0,10%\ >nul 2>&1
    echo ✅ 移动网络修复脚本到备份目录
)

echo.
echo 🔄 更新主服务器配置...
echo ======================

:: 更新 package.json 脚本
if exist Backend\package.json (
    echo 更新后端启动脚本...
    powershell -Command "
    $json = Get-Content 'Backend\package.json' | ConvertFrom-Json
    if (-not $json.scripts) { $json.scripts = @{} }
    $json.scripts.start = 'node server-launcher.js'
    $json.scripts.dev = 'nodemon server-launcher.js'
    $json.scripts.stable = 'node server-launcher.js'
    $json | ConvertTo-Json -Depth 10 | Set-Content 'Backend\package.json'
    " 2>nul
    echo ✅ 后端启动脚本已更新
)

if exist Frontend\package.json (
    echo 更新前端启动脚本...
    powershell -Command "
    $json = Get-Content 'Frontend\package.json' | ConvertFrom-Json
    if (-not $json.scripts) { $json.scripts = @{} }
    $json.scripts.stable = 'node stable-server.js'
    $json.scripts.serve = 'node stable-server.js'
    $json | ConvertTo-Json -Depth 10 | Set-Content 'Frontend\package.json'
    " 2>nul
    echo ✅ 前端启动脚本已更新
)

echo.
echo 🧹 清理临时文件...
echo ==================

:: 清理日志文件
if exist Backend\logs\*.log (
    del Backend\logs\*.log >nul 2>&1
    echo ✅ 清理旧日志文件
)

:: 清理临时上传文件
if exist Backend\uploads\temp\*.* (
    del Backend\uploads\temp\*.* >nul 2>&1
    echo ✅ 清理临时上传文件
)

:: 清理缓存文件
if exist Backend\cache\*.cache (
    del Backend\cache\*.cache >nul 2>&1
    echo ✅ 清理缓存文件
)

:: 清理前端构建缓存
if exist Frontend\.next (
    rmdir /s /q Frontend\.next >nul 2>&1
    echo ✅ 清理前端构建缓存
)

if exist Frontend\build (
    rmdir /s /q Frontend\build >nul 2>&1
    echo ✅ 清理前端构建目录
)

echo.
echo 🔧 验证功能完整性...
echo ====================

:: 检查关键路由文件
set "routes_ok=true"

if not exist Backend\routes\auth-enhanced.js (
    echo ❌ 认证路由缺失
    set "routes_ok=false"
)

if not exist Backend\routes\articles.js (
    echo ❌ 文章路由缺失
    set "routes_ok=false"
)

if not exist Backend\routes\users.js (
    echo ❌ 用户路由缺失
    set "routes_ok=false"
)

if not exist Backend\routes\admin (
    echo ❌ 管理路由目录缺失
    set "routes_ok=false"
)

if "%routes_ok%"=="true" (
    echo ✅ 所有核心路由文件完整
) else (
    echo ⚠️ 部分路由文件缺失，功能可能受影响
)

:: 检查关键服务文件
set "services_ok=true"

if not exist Backend\services\authService.js (
    echo ❌ 认证服务缺失
    set "services_ok=false"
)

if not exist Backend\services\emailService.js (
    echo ❌ 邮件服务缺失
    set "services_ok=false"
)

if "%services_ok%"=="true" (
    echo ✅ 所有核心服务文件完整
) else (
    echo ⚠️ 部分服务文件缺失，功能可能受影响
)

:: 检查前端组件
if exist Frontend\src\components (
    echo ✅ 前端组件目录完整
) else (
    echo ❌ 前端组件目录缺失
)

echo.
echo 📋 清理完成报告
echo ===============
echo.
echo ✅ 已完成的操作:
echo    - 备份所有旧服务器文件
echo    - 移动旧文件到 legacy 目录
echo    - 更新 package.json 启动脚本
echo    - 清理临时和缓存文件
echo    - 验证核心功能完整性
echo.
echo 📁 文件位置:
echo    - 备份文件: backup\%date:~0,10%\
echo    - 旧服务器: Backend\legacy\
echo    - 新服务器: Backend\stable-server.js
echo    - 启动器: Backend\server-launcher.js
echo.
echo 🚀 新的启动方式:
echo    - 稳定启动: start-stable.bat
echo    - 后端单独: cd Backend && npm run stable
echo    - 前端单独: cd Frontend && npm run stable
echo.
echo 🔧 功能保留确认:
echo    ✅ 用户认证系统
echo    ✅ 文章管理系统
echo    ✅ 管理员面板
echo    ✅ 社交功能
echo    ✅ 通知系统
echo    ✅ 邮件服务
echo    ✅ 权限管理
echo    ✅ 数据分析
echo    ✅ 搜索功能
echo    ✅ 文件上传
echo.
echo 💡 下一步建议:
echo    1. 运行 start-stable.bat 测试新服务器
echo    2. 验证所有功能正常工作
echo    3. 如有问题，可从备份目录恢复
echo    4. 确认无误后可删除 legacy 目录
echo.

echo ✅ 清理完成！新的稳定服务器已准备就绪。
echo.
echo 按任意键退出...
pause >nul
