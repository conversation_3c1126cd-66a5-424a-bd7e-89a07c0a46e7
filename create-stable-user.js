// 创建稳定的测试用户 - 简单可靠的方案
const { User } = require('./Backend/models');
const bcrypt = require('bcrypt');

async function createStableUser() {
  try {
    console.log('🔧 Creating stable test user...');

    // 删除现有的admin用户（如果存在）
    await User.destroy({
      where: { email: '<EMAIL>' }
    });

    // 创建新的稳定用户，使用简单密码
    const plainPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(plainPassword, 10);

    const newUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isActive: true,
      isEmailVerified: true,
      loginAttempts: 0,
      lockUntil: null
    });

    console.log('✅ Stable user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('🆔 User ID:', newUser.id);
    console.log('👤 Role:', newUser.role);

    // 验证密码哈希
    const isValid = await bcrypt.compare('admin123', hashedPassword);
    console.log('🔍 Password verification test:', isValid ? '✅ PASS' : '❌ FAIL');

    // 创建第二个测试用户
    const testPassword = 'test123';
    const testHashedPassword = await bcrypt.hash(testPassword, 10);

    await User.destroy({
      where: { email: '<EMAIL>' }
    });

    const testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      password: testHashedPassword,
      role: 'user',
      isActive: true,
      isEmailVerified: true,
      loginAttempts: 0,
      lockUntil: null
    });

    console.log('\n✅ Test user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: test123');
    console.log('🆔 User ID:', testUser.id);

    console.log('\n🎉 STABLE USERS READY FOR TESTING!');
    console.log('==========================================');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / test123');
    console.log('==========================================');

  } catch (error) {
    console.error('❌ Error creating stable user:', error);
  }
}

createStableUser();
