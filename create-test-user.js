// 创建测试用户
const { User } = require('./Backend/models');

async function createTestUser() {
  try {
    console.log('🔄 Creating test user...');

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.email);
      console.log('📧 Email:', existingUser.email);
      console.log('👤 Username:', existingUser.username);
      console.log('🔑 Password: password123');
      return;
    }

    // 创建新用户
    const testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123', // 这会被模型的beforeCreate hook自动加密
      display_name: 'Test User',
      is_active: true,
      role: 'user'
    });

    console.log('✅ Test user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('👤 Username: testuser');
    console.log('🔑 Password: password123');
    console.log('🆔 User ID:', testUser.id);

    // 创建管理员用户
    const existingAdmin = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!existingAdmin) {
      const adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        display_name: 'Admin User',
        is_active: true,
        role: 'admin'
      });

      console.log('✅ Admin user created successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('👤 Username: admin');
      console.log('🔑 Password: admin123');
      console.log('🆔 User ID:', adminUser.id);
    } else {
      console.log('✅ Admin user already exists:', existingAdmin.email);
    }

  } catch (error) {
    console.error('❌ Error creating test user:', error);
  }
}

createTestUser();
