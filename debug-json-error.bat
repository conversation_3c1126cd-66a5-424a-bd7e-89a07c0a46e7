@echo off
chcp 65001 >nul
echo 🔍 Newzora JSON解析错误诊断
echo ===========================
echo.

echo 📋 问题描述: "Unexpected token '<', '<!DOCTYPE '... is not valid JSON"
echo 这通常表示API请求返回了HTML而不是JSON数据
echo.

echo 🔍 开始诊断...
echo ===============

:: 检查后端服务器状态
echo 1. 检查后端服务器状态:
echo -------------------------
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 5
    Write-Host '✅ 后端服务器运行正常'
    Write-Host '   状态码:' $response.StatusCode
    Write-Host '   内容类型:' $response.Headers.'Content-Type'
    Write-Host '   响应长度:' $response.Content.Length
    
    # 显示响应内容的前100个字符
    $preview = $response.Content.Substring(0, [Math]::Min(100, $response.Content.Length))
    Write-Host '   响应预览:' $preview
} catch {
    Write-Host '❌ 后端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

echo.
echo 2. 检查认证端点:
echo ----------------
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body '{}' -ContentType 'application/json' -UseBasicParsing -TimeoutSec 5
    Write-Host '✅ 认证端点可访问'
    Write-Host '   状态码:' $response.StatusCode
} catch {
    Write-Host '❌ 认证端点访问失败'
    Write-Host '   状态码:' $_.Exception.Response.StatusCode
    Write-Host '   错误:' $_.Exception.Message
    
    # 检查是否返回HTML
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        if ($responseBody -like '*<!DOCTYPE*' -or $responseBody -like '*<html*') {
            Write-Host '⚠️ 检测到HTML响应，这是问题的根源'
            Write-Host '   响应内容:' $responseBody.Substring(0, [Math]::Min(200, $responseBody.Length))
        }
    }
}
" 2>nul

echo.
echo 3. 检查前端服务器状态:
echo ----------------------
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 5
    Write-Host '✅ 前端服务器运行正常'
    Write-Host '   状态码:' $response.StatusCode
} catch {
    Write-Host '❌ 前端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

echo.
echo 4. 检查端口占用:
echo ---------------
echo 检查端口5000 (后端):
netstat -an | findstr :5000
if %errorlevel% == 0 (
    echo ✅ 端口5000有进程监听
) else (
    echo ❌ 端口5000没有进程监听
)

echo.
echo 检查端口3000 (前端):
netstat -an | findstr :3000
if %errorlevel% == 0 (
    echo ✅ 端口3000有进程监听
) else (
    echo ❌ 端口3000没有进程监听
)

echo.
echo 5. 检查CORS配置:
echo ---------------
powershell -Command "
try {
    $headers = @{
        'Origin' = 'http://localhost:3000'
        'Content-Type' = 'application/json'
    }
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -Headers $headers -UseBasicParsing -TimeoutSec 5
    Write-Host '✅ CORS配置正常'
    if ($response.Headers.'Access-Control-Allow-Origin') {
        Write-Host '   CORS头:' $response.Headers.'Access-Control-Allow-Origin'
    }
} catch {
    Write-Host '❌ CORS配置可能有问题'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

echo.
echo 📊 诊断结果和解决方案
echo ====================
echo.
echo 💡 常见原因和解决方案:
echo.
echo 1. 后端服务器未启动:
echo    解决: 运行 start-stable.bat 或 cd Backend && npm run stable
echo.
echo 2. API路由配置错误:
echo    解决: 检查 Backend/stable-server.js 中的路由配置
echo.
echo 3. 前端API请求URL错误:
echo    解决: 检查前端代码中的API_URL配置
echo.
echo 4. CORS配置问题:
echo    解决: 检查后端CORS设置，确保允许前端域名
echo.
echo 5. 代理配置错误:
echo    解决: 检查前端代理设置，确保正确转发API请求
echo.
echo 🔧 立即修复步骤:
echo ================
echo.
echo 步骤1: 重启后端服务器
echo    cd Backend
echo    taskkill /F /IM node.exe
echo    npm run stable
echo.
echo 步骤2: 重启前端服务器  
echo    cd Frontend
echo    npm run stable
echo.
echo 步骤3: 清除浏览器缓存
echo    按 Ctrl+Shift+R 强制刷新页面
echo.
echo 步骤4: 检查浏览器控制台
echo    按 F12 打开开发者工具
echo    查看 Network 标签中的请求详情
echo.

echo 按任意键退出诊断...
pause >nul
