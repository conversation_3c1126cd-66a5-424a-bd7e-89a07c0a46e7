# Newzora Development Environment Docker Compose Configuration
# For local development with hot reload and debugging features

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: newzora-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-newzora_dev}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-dev_password_123}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./Backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - newzora-dev-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: newzora-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - onenews-dev-network

  # 后端服务（开发模式）
  backend:
    build:
      context: ./Backend
      dockerfile: Dockerfile.dev
    container_name: onenews-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-onenews_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-wasd080980!}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      SESSION_SECRET: ${SESSION_SECRET:-dev-session-secret}
      EMAIL_PROVIDER: ${EMAIL_PROVIDER:-gmail}
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS:-your_app_password}
      EMAIL_FROM: ${EMAIL_FROM:-OneNews <<EMAIL>>}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-your_google_client_id}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET:-your_google_client_secret}
      FACEBOOK_APP_ID: ${FACEBOOK_APP_ID:-your_facebook_app_id}
      FACEBOOK_APP_SECRET: ${FACEBOOK_APP_SECRET:-your_facebook_app_secret}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
    volumes:
      - ./Backend:/app
      - /app/node_modules
      - backend_dev_uploads:/app/uploads
    ports:
      - "5000:5000"
      - "9229:9229"  # Node.js 调试端口
    networks:
      - onenews-dev-network
    depends_on:
      - postgres
      - redis
    command: npm run dev

  # 前端服务（开发模式）
  frontend:
    build:
      context: ./Frontend
      dockerfile: Dockerfile.dev
    container_name: onenews-frontend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:5000/api}
      NEXT_PUBLIC_SOCKET_URL: ${NEXT_PUBLIC_SOCKET_URL:-http://localhost:5000}
      WATCHPACK_POLLING: true
    volumes:
      - ./Frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    networks:
      - onenews-dev-network
    depends_on:
      - backend
    command: npm run dev

  # 数据库管理工具 (可选)
  adminer:
    image: adminer:latest
    container_name: onenews-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - onenews-dev-network
    depends_on:
      - postgres

  # Redis 管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: onenews-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - onenews-dev-network
    depends_on:
      - redis

# 数据卷
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  backend_dev_uploads:
    driver: local

# 网络
networks:
  onenews-dev-network:
    driver: bridge
