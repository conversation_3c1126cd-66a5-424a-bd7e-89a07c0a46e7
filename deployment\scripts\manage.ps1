# OneNews Project Management Script
param(
    [Parameter(Position=0)]
    [ValidateSet("start", "stop", "restart", "clean", "verify", "help")]
    [string]$Action = "help",
    
    [Parameter(Position=1)]
    [ValidateSet("dev", "prod", "local")]
    [string]$Environment = "local",
    
    [switch]$DryRun
)

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  $Title" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

function Write-Success {
    param([string]$Message)
    Write-Host "Success: $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "Error: $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "Info: $Message" -ForegroundColor Cyan
}

# Start project
function Start-Project {
    param([string]$Env)
    
    Write-Header "Starting OneNews Project ($Env environment)"
    
    switch ($Env) {
        "local" {
            Start-LocalEnvironment
        }
        "dev" {
            Start-DockerEnvironment "dev"
        }
        "prod" {
            Start-DockerEnvironment "prod"
        }
    }
}

# Start local development environment
function Start-LocalEnvironment {
    Write-Info "Starting local development environment..."
    
    # Start backend
    Write-Info "Starting backend server..."
    Set-Location Backend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal
    Set-Location ..
    
    # Wait for backend to start
    Start-Sleep -Seconds 3
    
    # Start frontend
    Write-Info "Starting frontend server..."
    Set-Location Frontend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal
    Set-Location ..
    
    Write-Success "Project started successfully!"
    Write-Info "Frontend: http://localhost:3000"
    Write-Info "Backend: http://localhost:5000"
}

# Start Docker environment
function Start-DockerEnvironment {
    param([string]$Env)
    
    Write-Info "Starting Docker environment ($Env)..."
    
    $composeFile = if ($Env -eq "dev") { "config/docker-compose.dev.yml" } else { "config/docker-compose.yml" }
    
    Write-Info "Using configuration file: $composeFile"
    docker-compose -f $composeFile up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker environment started successfully!"
    } else {
        Write-Error "Failed to start Docker environment"
    }
}

# Stop project
function Stop-Project {
    Write-Header "Stopping OneNews Project"
    
    # Stop Docker containers
    if (Test-Path "config/docker-compose.yml") {
        Write-Info "Stopping Docker containers..."
        docker-compose -f config/docker-compose.yml down
        docker-compose -f config/docker-compose.dev.yml down
    }
    
    Write-Success "Project stopped"
}

# Project verification function
function Invoke-ProjectVerification {
    Write-Header "Verifying OneNews Project Status"
    
    $issues = @()
    
    # Check critical files
    $criticalFiles = @(
        "Frontend/package.json",
        "Backend/package.json",
        "Frontend/src/app/layout.tsx",
        "Frontend/src/app/page.tsx",
        "Backend/server.js",
        "README.md"
    )
    
    Write-Info "Checking critical files..."
    foreach ($file in $criticalFiles) {
        if (Test-Path $file) {
            Write-Success "Exists: $file"
        } else {
            Write-Error "Missing: $file"
            $issues += "Missing critical file: $file"
        }
    }
    
    # Check dependency installation
    Write-Info "Checking dependency installation..."
    if (Test-Path "Backend/node_modules") {
        Write-Success "Backend dependencies installed"
    } else {
        Write-Error "Backend dependencies not installed"
        $issues += "Backend dependencies not installed"
    }
    
    if (Test-Path "Frontend/node_modules") {
        Write-Success "Frontend dependencies installed"
    } else {
        Write-Error "Frontend dependencies not installed"
        $issues += "Frontend dependencies not installed"
    }
    
    # Generate report
    Write-Host ""
    if ($issues.Count -eq 0) {
        Write-Success "Project status is good, all checks passed!"
    } else {
        Write-Error "Found $($issues.Count) issues:"
        foreach ($issue in $issues) {
            Write-Error "  - $issue"
        }
    }
}

# Project cleanup function
function Invoke-ProjectCleanup {
    Write-Header "Cleaning OneNews Project"
    
    if ($DryRun) {
        Write-Host "Preview mode - files will not be actually deleted" -ForegroundColor Yellow
    }
    
    # Define items to clean
    $itemsToClean = @(
        "Backend/.nyc_output",
        "Frontend/.next",
        "Backend/node_modules/.cache",
        "Frontend/node_modules/.cache"
    )
    
    foreach ($item in $itemsToClean) {
        if (Test-Path $item) {
            if ($DryRun) {
                Write-Host "Will clean: $item" -ForegroundColor Yellow
            } else {
                try {
                    Remove-Item $item -Recurse -Force
                    Write-Success "Cleaned: $item"
                } catch {
                    Write-Error "Failed to clean: $item"
                }
            }
        }
    }
    
    Write-Success "Project cleanup completed"
}

# Show help information
function Show-Help {
    Write-Header "OneNews Project Management Tool"
    
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\scripts\manage.ps1 <action> [environment] [options]" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  start     Start project" -ForegroundColor White
    Write-Host "  stop      Stop project" -ForegroundColor White
    Write-Host "  restart   Restart project" -ForegroundColor White
    Write-Host "  clean     Clean project files" -ForegroundColor White
    Write-Host "  verify    Verify project status" -ForegroundColor White
    Write-Host "  help      Show help information" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Environments:" -ForegroundColor Yellow
    Write-Host "  local     Local development environment (default)" -ForegroundColor White
    Write-Host "  dev       Docker development environment" -ForegroundColor White
    Write-Host "  prod      Docker production environment" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\scripts\manage.ps1 start local    # Start local development" -ForegroundColor White
    Write-Host "  .\scripts\manage.ps1 verify         # Verify project status" -ForegroundColor White
    Write-Host "  .\scripts\manage.ps1 clean -DryRun  # Preview cleanup" -ForegroundColor White
}

# Main logic
switch ($Action) {
    "start" {
        Start-Project $Environment
    }
    "stop" {
        Stop-Project
    }
    "restart" {
        Stop-Project
        Start-Sleep -Seconds 2
        Start-Project $Environment
    }
    "clean" {
        Invoke-ProjectCleanup
    }
    "verify" {
        Invoke-ProjectVerification
    }
    "help" {
        Show-Help
    }
    default {
        Show-Help
    }
}
