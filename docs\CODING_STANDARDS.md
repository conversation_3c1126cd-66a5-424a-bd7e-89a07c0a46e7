# 📋 Newzora项目代码规范

## 🎯 规范目标
- 确保代码一致性和可维护性
- 提高团队协作效率
- 减少代码审查时间
- 提升项目质量

---

## 📁 文件命名规范

### 前端文件命名
```typescript
// 页面文件 - 使用 page.tsx
app/login/page.tsx
app/article/[id]/page.tsx

// 组件文件 - 使用 PascalCase
components/Header.tsx
components/ArticleCard.tsx
components/social/FollowButton.tsx

// 工具文件 - 使用 camelCase
utils/formatDate.ts
services/apiClient.ts
contexts/AuthContext.tsx

// 类型文件 - 使用 camelCase
types/index.ts
types/apiTypes.ts
```

### 后端文件命名
```javascript
// 路由文件 - 使用 camelCase
routes/auth.js
routes/articles.js
routes/admin/users.js

// 模型文件 - 使用 PascalCase
models/User.js
models/Article.js
models/Permission.js

// 服务文件 - 使用 camelCase
services/emailService.js
services/authService.js

// 配置文件 - 使用 camelCase
config/database.js
config/security.js

// 中间件文件 - 使用 camelCase
middleware/auth.js
middleware/rateLimiter.js
```

---

## 🏗️ 目录结构规范

### 前端目录结构
```
Frontend/src/
├── app/                    # Next.js 15 App Router页面
│   ├── (auth)/            # 认证相关页面组
│   ├── admin/             # 管理员页面
│   └── api/               # API路由
├── components/            # 可复用组件
│   ├── ui/                # 基础UI组件
│   ├── forms/             # 表单组件
│   ├── social/            # 社交功能组件
│   └── admin/             # 管理员组件
├── contexts/              # React Context
├── hooks/                 # 自定义Hooks
├── lib/                   # 第三方库配置
├── services/              # API服务
├── types/                 # TypeScript类型
├── utils/                 # 工具函数
└── data/                  # 静态数据
```

### 后端目录结构
```
Backend/
├── config/                # 配置文件
├── middleware/            # 中间件
├── models/                # 数据模型
├── routes/                # API路由
│   └── admin/             # 管理员路由
├── services/              # 业务逻辑服务
├── scripts/               # 脚本工具
├── templates/             # 邮件模板
│   └── email/             # 邮件模板
└── tests/                 # 测试文件
```

---

## 💻 代码风格规范

### TypeScript/JavaScript规范

#### 变量命名
```typescript
// ✅ 正确 - 使用 camelCase
const userName = 'john_doe';
const isLoggedIn = true;
const articleCount = 10;

// ❌ 错误
const user_name = 'john_doe';
const IsLoggedIn = true;
const article-count = 10;
```

#### 常量命名
```typescript
// ✅ 正确 - 使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.newzora.com';
const MAX_FILE_SIZE = 10 * 1024 * 1024;
const EMAIL_TYPES = {
  VERIFICATION: 'verification',
  PASSWORD_RESET: 'password_reset'
};

// ❌ 错误
const apiBaseUrl = 'https://api.newzora.com';
const maxFileSize = 10 * 1024 * 1024;
```

#### 函数命名
```typescript
// ✅ 正确 - 使用动词开头的 camelCase
const getUserById = (id: string) => { /* ... */ };
const validateEmail = (email: string) => { /* ... */ };
const handleSubmit = () => { /* ... */ };

// ❌ 错误
const userById = (id: string) => { /* ... */ };
const email_validation = (email: string) => { /* ... */ };
const Submit = () => { /* ... */ };
```

#### 组件命名
```typescript
// ✅ 正确 - 使用 PascalCase
const ArticleCard = ({ article }: { article: Article }) => {
  return <div>{article.title}</div>;
};

const UserProfile = () => {
  return <div>Profile</div>;
};

// ❌ 错误
const articleCard = ({ article }) => { /* ... */ };
const user_profile = () => { /* ... */ };
```

#### 接口和类型命名
```typescript
// ✅ 正确 - 使用 PascalCase
interface User {
  id: string;
  username: string;
  email: string;
}

type ArticleStatus = 'draft' | 'published' | 'archived';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// ❌ 错误
interface user {
  id: string;
  username: string;
}

type article_status = 'draft' | 'published';
```

---

## 🎨 React组件规范

### 组件结构
```typescript
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { Article } from '@/types';

// 1. 接口定义
interface ArticleCardProps {
  article: Article;
  showAuthor?: boolean;
  onLike?: (articleId: string) => void;
}

// 2. 组件实现
const ArticleCard: React.FC<ArticleCardProps> = ({
  article,
  showAuthor = true,
  onLike
}) => {
  // 3. Hooks
  const { user } = useAuth();
  const [isLiked, setIsLiked] = useState(false);

  // 4. Effects
  useEffect(() => {
    // 检查用户是否已点赞
    checkIfLiked();
  }, [article.id, user]);

  // 5. 事件处理函数
  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike?.(article.id);
  };

  const checkIfLiked = () => {
    // 实现逻辑
  };

  // 6. 渲染函数
  return (
    <div className="article-card">
      <h3>{article.title}</h3>
      {showAuthor && (
        <p>By {article.author.username}</p>
      )}
      <button onClick={handleLike}>
        {isLiked ? '❤️' : '🤍'} {article.likesCount}
      </button>
    </div>
  );
};

export default ArticleCard;
```

### Hooks使用规范
```typescript
// ✅ 正确 - 自定义Hook命名以use开头
const useArticles = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchArticles = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/articles');
      const data = await response.json();
      setArticles(data);
    } catch (error) {
      console.error('Failed to fetch articles:', error);
    } finally {
      setLoading(false);
    }
  };

  return { articles, loading, fetchArticles };
};

// ❌ 错误
const getArticles = () => { /* ... */ };
const articlesHook = () => { /* ... */ };
```

---

## 🔧 API设计规范

### RESTful API规范
```javascript
// ✅ 正确 - RESTful路由设计
GET    /api/articles           # 获取文章列表
GET    /api/articles/:id       # 获取单个文章
POST   /api/articles           # 创建文章
PUT    /api/articles/:id       # 更新文章
DELETE /api/articles/:id       # 删除文章

GET    /api/users/:id/articles # 获取用户的文章
POST   /api/articles/:id/like  # 点赞文章

// ❌ 错误
GET    /api/getArticles
POST   /api/createArticle
GET    /api/article_by_id/:id
```

### API响应格式
```javascript
// ✅ 正确 - 统一的响应格式
{
  "success": true,
  "data": {
    "id": "123",
    "title": "Article Title",
    "content": "Article content..."
  },
  "message": "Article retrieved successfully"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "title",
        "message": "Title is required"
      }
    ]
  }
}

// ❌ 错误 - 不一致的响应格式
{
  "status": "ok",
  "result": { /* data */ }
}
```

### 错误处理规范
```javascript
// ✅ 正确 - 统一的错误处理
router.post('/articles', async (req, res) => {
  try {
    const { title, content } = req.body;
    
    // 输入验证
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Title and content are required'
        }
      });
    }

    const article = await Article.create({ title, content });
    
    res.status(201).json({
      success: true,
      data: article,
      message: 'Article created successfully'
    });
  } catch (error) {
    console.error('Error creating article:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create article'
      }
    });
  }
});
```

---

## 📝 注释规范

### 函数注释
```typescript
/**
 * 获取用户的文章列表
 * @param userId - 用户ID
 * @param options - 查询选项
 * @param options.page - 页码，默认为1
 * @param options.limit - 每页数量，默认为10
 * @param options.status - 文章状态过滤
 * @returns Promise<Article[]> 文章列表
 */
const getUserArticles = async (
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: ArticleStatus;
  } = {}
): Promise<Article[]> => {
  // 实现逻辑
};
```

### 组件注释
```typescript
/**
 * 文章卡片组件
 * 
 * 用于展示文章的基本信息，包括标题、作者、点赞数等
 * 支持点赞功能和作者信息的显示控制
 * 
 * @example
 * ```tsx
 * <ArticleCard 
 *   article={article} 
 *   showAuthor={true}
 *   onLike={handleLike}
 * />
 * ```
 */
const ArticleCard: React.FC<ArticleCardProps> = ({ ... }) => {
  // 组件实现
};
```

---

## 🧪 测试规范

### 测试文件命名
```
components/ArticleCard.test.tsx
services/authService.test.js
utils/formatDate.test.ts
```

### 测试结构
```typescript
describe('ArticleCard', () => {
  describe('渲染测试', () => {
    it('应该正确渲染文章标题', () => {
      // 测试实现
    });

    it('应该在showAuthor为true时显示作者信息', () => {
      // 测试实现
    });
  });

  describe('交互测试', () => {
    it('应该在点击点赞按钮时调用onLike回调', () => {
      // 测试实现
    });
  });
});
```

---

## 📦 Git提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具或辅助工具的变动

### 提交示例
```
feat(auth): add email verification functionality

- Add email verification service
- Create email templates
- Implement verification API endpoints
- Add frontend verification page

Closes #123
```

---

## 🎯 代码审查清单

### 功能性
- [ ] 功能是否按预期工作
- [ ] 是否处理了所有边界情况
- [ ] 错误处理是否完善

### 代码质量
- [ ] 代码是否遵循项目规范
- [ ] 是否有重复代码
- [ ] 变量和函数命名是否清晰

### 性能
- [ ] 是否有性能问题
- [ ] 是否有内存泄漏
- [ ] 数据库查询是否优化

### 安全性
- [ ] 是否有安全漏洞
- [ ] 输入验证是否充分
- [ ] 权限控制是否正确

### 测试
- [ ] 是否有足够的测试覆盖
- [ ] 测试是否通过
- [ ] 是否需要更新文档

通过遵循这些规范，我们可以确保Newzora项目的代码质量和团队协作效率！🚀
