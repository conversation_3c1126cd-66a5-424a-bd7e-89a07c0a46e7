# 📚 Newzora 项目文档

欢迎来到 **Newzora** 内容平台项目文档！

## 🎯 项目概述

Newzora 是一个现代化的内容创作与分享平台，采用 Next.js 15 + Node.js + PostgreSQL 技术栈构建。

### ✨ 核心特性
- 🔐 **完整的用户认证系统** - JWT + Supabase Auth + 社交登录
- ✍️ **富文本编辑器** - 基于 TipTap 的高级编辑器，支持草稿自动保存
- 🛡️ **权限管理系统** - 基于角色的访问控制 (RBAC)
- 📧 **邮件服务集成** - 邮箱验证、密码重置、通知邮件
- 👥 **社交功能** - 关注、评论、分享、实时通知
- 📊 **内容管理** - 文章发布、审核、版本控制
- 🎨 **响应式设计** - 完美适配桌面端和移动端

### 📊 开发进度
- **整体完成度**: 68.6% (118/172 功能)
- **前端完成度**: 84.4% (38/45 功能)
- **后端完成度**: 90.4% (47/52 功能)

---

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Context + Hooks
- **认证**: Supabase Auth
- **编辑器**: TipTap (富文本编辑)

### 后端技术栈
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: PostgreSQL + Sequelize ORM
- **认证**: JWT + Passport.js
- **邮件**: Nodemailer + SendGrid
- **文件上传**: Multer + Cloudinary

### 数据库设计
- **用户系统**: User, UserProfile, UserRole
- **内容系统**: Article, Draft, Comment, MediaFile
- **权限系统**: Role, Permission, RolePermission
- **社交系统**: Follow, Message, Share, Activity
- **通知系统**: Notification, NotificationPreference

---

## 🚀 快速开始

### 环境要求
- **Node.js**: v22.16.0+
- **npm**: v10.9.2+
- **PostgreSQL**: 13+
- **Git**: 最新版本

### 1. 克隆项目
```bash
git clone <repository-url>
cd Newzora
```

### 2. 安装依赖
```bash
# 后端依赖
cd Backend
npm install

# 前端依赖
cd ../Frontend
npm install
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp Backend/.env.example Backend/.env
cp Frontend/.env.example Frontend/.env

# 配置数据库连接、Supabase密钥等
```

### 4. 数据库初始化
```bash
cd Backend
npm run db:init
npm run db:seed
```

### 5. 启动开发服务器
```bash
# 后端服务 (端口: 5000)
cd Backend
npm run dev

# 前端服务 (端口: 3000)
cd Frontend
npm run dev
```

### 6. 访问应用
- **前端**: http://localhost:3000
- **后端API**: http://localhost:5000/api
- **API文档**: http://localhost:5000/api/docs

---

## 📁 项目结构

### 前端结构 (`Frontend/src/`)
```
├── app/                    # Next.js 15 App Router页面
│   ├── (auth)/            # 认证相关页面
│   ├── admin/             # 管理员后台
│   ├── article/           # 文章相关页面
│   ├── drafts/            # 草稿管理
│   └── ...                # 其他功能页面
├── components/            # 可复用组件
│   ├── ui/                # 基础UI组件
│   ├── social/            # 社交功能组件
│   └── admin/             # 管理员组件
├── contexts/              # React Context
├── types/                 # TypeScript类型定义
├── utils/                 # 工具函数
└── data/                  # 模拟数据
```

### 后端结构 (`Backend/`)
```
├── config/                # 配置文件
├── middleware/            # 中间件
├── models/                # 数据模型 (Sequelize)
├── routes/                # API路由
│   └── admin/             # 管理员API
├── services/              # 业务逻辑服务
├── scripts/               # 数据库脚本
└── templates/             # 邮件模板
```

---

## 🔧 当前开发重点

### 🔥 高优先级任务
1. **邮件服务完善**
   - ✅ 邮件配置和服务类
   - 🔄 邮件模板系统 (MJML + Handlebars)
   - ❌ 前端邮件验证页面

2. **数据库连接修复**
   - ❌ PostgreSQL连接问题解决
   - ❌ Supabase数据库配置
   - ❌ 数据持久化测试

3. **管理员前端界面**
   - ❌ 用户管理界面
   - ❌ 角色权限管理界面
   - ❌ 系统统计面板

### � 中优先级任务
4. **AI功能集成**
   - ❌ 内容审核AI
   - ❌ 智能推荐算法
   - ❌ 内容分类AI

5. **数据分析系统**
   - ❌ 用户行为分析
   - ❌ 内容统计报表
   - ❌ 实时数据看板

6. **性能优化**
   - ❌ 缓存策略实现
   - ❌ CDN集成
   - ❌ 数据库查询优化

---

## 📋 开发文档

### 项目管理文档
- [📊 功能状态矩阵](FEATURE_STATUS_MATRIX.md) - 详细的功能完成度追踪
- [🗺️ 开发路线图](NEWZORA_DEVELOPMENT_ROADMAP.md) - 完整的开发计划
- [🧹 项目结构整理](CLEANED_PROJECT_STRUCTURE.md) - 清理后的项目结构
- [📋 代码规范](CODING_STANDARDS.md) - 开发规范和最佳实践

### 技术文档
- [🔐 认证系统](setup/OAUTH_SETUP.md) - Supabase Auth配置
- [📧 邮件服务](setup/EMAIL_SERVICE_SETUP.md) - 邮件服务配置
- [🛡️ 权限系统](api/PERMISSION_SYSTEM.md) - RBAC权限控制
- [📝 API文档](api/API_DOCUMENTATION.md) - 完整的API接口文档

### 部署文档
- [🐳 Docker部署](deployment/DOCKER_DEPLOYMENT.md) - 容器化部署
- [🌐 Nginx配置](deployment/NGINX_DEPLOYMENT.md) - 反向代理配置
- [🚀 生产环境](deployment/PRODUCTION_SETUP.md) - 生产环境部署

---

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd Backend
npm test

# 前端测试
cd Frontend
npm test

# 端到端测试
npm run test:e2e
```

### 测试覆盖率
- **单元测试**: 目标 80%，当前 30%
- **集成测试**: 目标 60%，当前 20%
- **E2E测试**: 目标 40%，当前 10%

---

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 遵循 [代码规范文档](CODING_STANDARDS.md)
- 使用 TypeScript 进行类型检查
- 通过 ESLint 和 Prettier 格式化代码
- 编写单元测试和集成测试

### 提交规范
```
feat(scope): add new feature
fix(scope): fix bug
docs(scope): update documentation
style(scope): format code
refactor(scope): refactor code
test(scope): add tests
chore(scope): update build tools
```

---

## � 联系方式

- **项目维护者**: Newzora Team
- **邮箱**: <EMAIL>
- **文档更新**: 2024年12月

---

## �📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

---

## 🎯 下一步计划

1. **本周目标**: 完成邮件服务和数据库集成
2. **本月目标**: 完成管理员界面和AI功能
3. **季度目标**: 项目上线和性能优化

让我们一起构建一个优秀的内容平台！🚀
