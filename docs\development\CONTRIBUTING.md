# 贡献指南

感谢您对OneNews项目的关注！我们欢迎所有形式的贡献。

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 15+
- Git

### 本地开发设置

1. **克隆仓库**
```bash
git clone https://github.com/Jacken22/OneNews.git
cd OneNews
```

2. **安装依赖**
```bash
# 后端
cd Backend
npm install

# 前端
cd ../Frontend
npm install
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp Backend/.env.example Backend/.env
cp Frontend/.env.example Frontend/.env.local

# 编辑环境变量文件，填入实际配置
```

4. **启动开发服务器**
```bash
# 使用管理脚本（推荐）
.\start.ps1

# 或手动启动
# 后端
cd Backend && npm start

# 前端
cd Frontend && npm run dev
```

## 📝 贡献流程

### 1. 报告问题
- 使用GitHub Issues报告bug
- 提供详细的重现步骤
- 包含环境信息（操作系统、Node.js版本等）

### 2. 功能请求
- 在Issues中描述新功能
- 说明使用场景和预期效果
- 讨论实现方案

### 3. 代码贡献

#### 分支策略
- `main`: 主分支，稳定版本
- `develop`: 开发分支，新功能集成
- `feature/*`: 功能分支
- `bugfix/*`: 修复分支

#### 提交流程
1. Fork项目到您的GitHub账户
2. 创建功能分支：`git checkout -b feature/your-feature-name`
3. 进行开发并提交：`git commit -m "feat: add your feature"`
4. 推送分支：`git push origin feature/your-feature-name`
5. 创建Pull Request

#### 提交信息规范
使用[Conventional Commits](https://www.conventionalcommits.org/)格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(auth): add social login support
fix(api): resolve user profile update issue
docs: update installation guide
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd Backend
npm test

# 前端测试
cd Frontend
npm test

# 端到端测试
npm run test:e2e
```

### 测试覆盖率
- 新功能需要包含相应的测试
- 保持测试覆盖率在80%以上
- 修复bug时需要添加回归测试

## 📋 代码规范

### JavaScript/TypeScript
- 使用ESLint和Prettier进行代码格式化
- 遵循项目的ESLint配置
- 使用TypeScript类型注解

### 文件命名
- 组件文件：PascalCase（如：`UserProfile.tsx`）
- 工具函数：camelCase（如：`formatDate.js`）
- 常量文件：UPPER_SNAKE_CASE（如：`API_ENDPOINTS.js`）

### 目录结构
```
Frontend/src/
├── components/     # 可复用组件
├── pages/         # 页面组件
├── hooks/         # 自定义Hooks
├── utils/         # 工具函数
├── types/         # TypeScript类型定义
└── styles/        # 样式文件

Backend/
├── routes/        # 路由定义
├── models/        # 数据模型
├── middleware/    # 中间件
├── services/      # 业务逻辑
└── utils/         # 工具函数
```

## 🔍 代码审查

### Pull Request要求
- 清晰的标题和描述
- 关联相关的Issue
- 包含测试用例
- 通过所有CI检查
- 至少一个维护者的审查

### 审查清单
- [ ] 代码符合项目规范
- [ ] 功能正常工作
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响可接受

## 📚 文档

### 文档类型
- API文档：使用JSDoc注释
- 用户文档：Markdown格式
- 开发文档：项目Wiki

### 文档更新
- 新功能需要更新相关文档
- API变更需要更新API文档
- 重大变更需要更新README

## 🤝 社区

### 沟通渠道
- GitHub Issues：问题报告和功能讨论
- GitHub Discussions：一般讨论和问答
- Pull Request：代码审查和技术讨论

### 行为准则
- 尊重所有贡献者
- 建设性的反馈
- 包容和友好的环境

## 📄 许可证

通过贡献代码，您同意您的贡献将在MIT许可证下发布。

## 🙏 致谢

感谢所有为OneNews项目做出贡献的开发者！

---

如有任何问题，请随时在Issues中提出或联系项目维护者。
