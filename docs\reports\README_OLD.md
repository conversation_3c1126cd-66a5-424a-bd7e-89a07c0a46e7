# Newzora - Modern News Content Platform

[![CI/CD](https://github.com/Jacken22/Newzora/actions/workflows/ci.yml/badge.svg)](https://github.com/Jacken22/Newzora/actions/workflows/ci.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)

A fully-featured modern news content platform with user authentication, social features, content management, data analytics, and real-time notification system.

## ⚡ Quick Start

### 🔧 System Requirements
- Node.js 18+
- PostgreSQL 15+
- Git

### 🚀 One-Click Launch
```bash
# Clone the project
git clone https://github.com/Jacken22/Newzora.git
cd Newzora

# Use management script to start (recommended)
powershell -ExecutionPolicy Bypass -File start.ps1

# Or manual startup
# 1. Install dependencies
cd Backend && npm install
cd ../Frontend && npm install

# 2. Configure database
# Edit database configuration in Backend/.env file

# 3. Start services
cd ../Backend && npm start
cd ../Frontend && npm run dev
```

### 🧪 Test System
- **System Health Check**: http://localhost:3000/system-health
- **Authentication Test**: http://localhost:3000/auth-test-final
- **Project Status**: http://localhost:3000/project-status
- **Login Page**: http://localhost:3000/auth/login (Test account: <EMAIL> / Qw12345)

## 🚀 Tech Stack

### Frontend
- **Framework**: Next.js 15 (React 19)
- **Styling**: Tailwind CSS 4
- **Language**: TypeScript
- **State Management**: React Hooks
- **Image Processing**: Next.js Image Component
- **Real-time Communication**: Socket.IO Client

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL
- **ORM**: Sequelize
- **Authentication**: JWT + Passport.js
- **File Upload**: Multer + Sharp
- **Real-time Communication**: Socket.IO
- **Push Notifications**: Web Push API
- **Email Service**: Nodemailer
- **Social Login**: Google OAuth, Facebook OAuth

## 📁 Project Structure

```
Newzora/
├── 📁 Frontend/                    # Next.js 15 Frontend Application
│   ├── 📁 src/
│   │   ├── 📁 app/                 # App Router Pages
│   │   │   ├── layout.tsx          # Root Layout
│   │   │   ├── page.tsx            # Home Page
│   │   │   ├── globals.css         # Global Styles
│   │   │   ├── 📁 login/           # Login Page
│   │   │   ├── 📁 register/        # Registration Page
│   │   │   ├── 📁 content/         # Content Management
│   │   │   ├── 📁 social/          # Social Feature Pages
│   │   │   ├── 📁 profile/         # User Profile Pages
│   │   │   ├── 📁 article/         # Article Detail Pages
│   │   │   ├── 📁 search/          # Search Result Pages
│   │   │   ├── 📁 notifications/   # Notification Pages
│   │   │   └── 📁 settings/        # Settings Pages
│   │   ├── 📁 components/          # React Components
│   │   │   ├── Header.tsx          # Page Header
│   │   │   ├── ArticleCard.tsx     # Article Card
│   │   │   ├── NotificationBell.tsx # Notification Bell
│   │   │   ├── RichTextEditor.tsx  # Rich Text Editor
│   │   │   ├── 📁 content/         # Content Management Components
│   │   │   └── 📁 social/          # Social Feature Components
│   │   ├── 📁 contexts/            # React Context
│   │   │   ├── AuthContext.tsx     # Authentication Context
│   │   │   └── NotificationContext.tsx # Notification Context
│   │   ├── 📁 services/            # Frontend Services
│   │   │   └── socketService.ts    # WebSocket Service
│   │   ├── 📁 data/                # Mock Data
│   │   │   └── mockArticles.ts     # Sample Articles
│   │   └── 📁 types/               # TypeScript Type Definitions
│   │       └── index.ts
│   ├── 📁 public/                  # Static Assets
│   ├── package.json                # Dependencies Configuration
│   ├── next.config.js              # Next.js Configuration
│   ├── tailwind.config.js          # Tailwind CSS Configuration
│   └── tsconfig.json               # TypeScript Configuration
├── 📁 Backend/                     # Node.js + Express Backend
│   ├── 📁 config/                  # Configuration Files
│   │   ├── database.js             # Database Configuration
│   │   ├── passport.js             # Authentication Strategy
│   │   ├── security.js             # Security Configuration
│   │   └── logger.js               # Logging Configuration
│   ├── 📁 models/                  # Sequelize Data Models
│   │   ├── User.js                 # User Model
│   │   ├── Article.js              # Article Model
│   │   ├── Comment.js              # Comment Model
│   │   ├── Follow.js               # Follow Relationship Model
│   │   ├── Message.js              # Private Message Model
│   │   ├── Notification.js         # Notification Model
│   │   ├── Draft.js                # Draft Model
│   │   ├── MediaFile.js            # Media File Model
│   │   └── associations.js         # Model Associations
│   ├── 📁 routes/                  # API Routes
│   │   ├── users.js                # User Related API
│   │   ├── articles.js             # Article Related API
│   │   ├── comments.js             # Comment Related API
│   │   ├── follows.js              # Follow Related API
│   │   ├── messages.js             # Private Message API
│   │   ├── notifications.js        # Notification Related API
│   │   ├── drafts.js               # Draft Related API
│   │   ├── media.js                # Media File API
│   │   ├── reviews.js              # Content Review API
│   │   └── analytics.js            # Data Analytics API
│   ├── 📁 middleware/              # Middleware
│   │   ├── auth.js                 # Authentication Middleware
│   │   ├── upload.js               # File Upload Middleware
│   │   ├── rateLimiter.js          # Rate Limiting Middleware
│   │   └── logging.js              # Logging Middleware
│   ├── 📁 services/                # Business Services
│   │   ├── emailService.js         # Email Service
│   │   ├── notificationService.js  # Notification Service
│   │   ├── pushService.js          # Push Service
│   │   ├── socketService.js        # WebSocket Service
│   │   └── aiModerationService.js  # AI Content Moderation
│   ├── 📁 scripts/                 # Utility Scripts
│   │   ├── seed.js                 # Data Seeding
│   │   └── database-monitor.js     # Database Monitoring
│   ├── 📁 migrations/              # Database Migrations
│   ├── 📁 seeders/                 # Data Seeders
│   ├── 📁 uploads/                 # File Upload Directory
│   ├── server.js                   # Server Entry Point
│   ├── package.json                # Dependencies Configuration
│   └── .env                        # Environment Variables
├── 📁 config/                      # Project Configuration
│   ├── docker-compose.yml          # Docker Compose Configuration
│   ├── docker-compose.dev.yml      # Development Docker Configuration
│   └── 📁 nginx/                   # Nginx Configuration
├── 📁 docs/                        # Project Documentation
│   ├── README.md                   # Documentation Home
│   ├── 📁 setup/                   # Installation Configuration Docs
│   └── 📁 deployment/              # Deployment Documentation
├── 📁 scripts/                     # Build and Deployment Scripts
│   ├── deploy-production.sh        # Production Deployment
│   └── manage.ps1                  # Project Management Script
├── 📁 tests/                       # Test Files
│   ├── 📁 integration/             # Integration Tests
│   └── 📁 data/                    # Test Data
├── 📁 tools/                       # Development Tools
│   ├── 📁 docker/                  # Docker Configuration
│   └── 📁 scripts/                 # Build Scripts
├── README.md                       # Project Documentation
├── package.json                    # Root Package Configuration
├── start.ps1                       # PowerShell Startup Script
├── .gitignore                      # Git Ignore Rules
├── LICENSE                         # MIT License
└── docker-compose.prod.yml         # Production Docker Configuration
```

## ✨ Features

### 🎯 Core Features
- ✅ **Responsive Design** - Perfect adaptation for desktop and mobile
- ✅ **Article Management** - Article display, category filtering, search functionality
- ✅ **User Authentication** - Registration, login, password reset, email verification
- ✅ **社交登录** - Google OAuth、Facebook OAuth
- ✅ **社交功能** - 用户关注、私信、动态时间线
- ✅ **内容管理** - 文章草稿、富文本编辑、媒体上传
- ✅ **实时通知** - WebSocket实时通知、邮件通知、推送通知
- ✅ **数据分析** - 用户行为分析、阅读统计、内容推荐

### 🔧 技术特点
- �️ **微服务架构** - 前后端分离，模块化设计
- � **安全认证** - JWT令牌、密码加密、权限控制
- � **实时通信** - Socket.IO双向通信
- 📧 **多渠道通知** - Web、邮件、推送三合一
- 🗄️ **数据库优化** - PostgreSQL + Sequelize ORM
- 📊 **数据分析** - 用户行为跟踪、统计分析
- 🎨 **现代化UI** - Tailwind CSS、响应式设计

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

**Windows 批处理脚本：**
```bash
# 双击运行或在命令行执行
start.bat
```

**PowerShell 脚本：**
```powershell
# 在PowerShell中执行
.\start.ps1
```

### 方法二：手动启动

**启动后端服务：**
```bash
cd Backend
npm install
npm run dev
```
后端将在 http://localhost:5000 运行

**启动前端服务：**
```bash
cd Frontend
npm install
npm run dev
```
前端将在 http://localhost:3000 运行

### 数据库设置
确保PostgreSQL正在运行，并创建数据库：
```sql
CREATE DATABASE contenthub;
```

运行数据库种子数据：
```bash
cd Backend
npm run seed
```

## 📡 API 端点

### 文章相关
- `GET /api/articles` - 获取文章列表
- `GET /api/articles/:id` - 获取单篇文章
- `POST /api/articles` - 创建文章
- `PUT /api/articles/:id` - 更新文章
- `DELETE /api/articles/:id` - 删除文章

### 用户认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/forgot-password` - 忘记密码
- `POST /api/auth/reset-password` - 重置密码
- `GET /api/auth/verify-email/:token` - 邮箱验证

### 社交功能
- `POST /api/social/follow/:userId` - 关注用户
- `DELETE /api/social/unfollow/:userId` - 取消关注
- `GET /api/social/followers/:userId` - 获取关注者列表
- `GET /api/social/following/:userId` - 获取关注列表
- `POST /api/messages` - 发送私信
- `GET /api/messages/:userId` - 获取与用户的聊天记录

### 通知系统
- `GET /api/notifications` - 获取通知列表
- `PUT /api/notifications/:id/read` - 标记通知已读
- `POST /api/notifications/push-subscription` - 订阅推送通知
- `GET /api/notifications/vapid-public-key` - 获取VAPID公钥

### 内容管理
- `POST /api/upload/image` - 上传图片
- `POST /api/upload/video` - 上传视频
- `POST /api/upload/audio` - 上传音频
- `GET /api/drafts` - 获取草稿列表
- `POST /api/drafts` - 保存草稿

## ⚙️ 环境变量

在 `Backend/.env` 文件中配置：
```env
# 服务器配置
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# PostgreSQL 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=contenthub
DB_USER=postgres
DB_PASSWORD=wasd080980!

# JWT 认证配置
JWT_SECRET=your_jwt_secret_key_here_make_it_very_long_and_secure
JWT_EXPIRES_IN=7d

# 邮件服务配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=OneNews <<EMAIL>>

# OAuth 社交登录配置
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# 安全配置
BCRYPT_ROUNDS=12
PASSWORD_RESET_EXPIRES=3600000
EMAIL_VERIFICATION_EXPIRES=86400000
```

## 开发说明

- 使用PostgreSQL数据库存储数据
- 所有组件都是TypeScript编写
- 使用Tailwind CSS进行样式设计
- 遵循现代React最佳实践
- 使用Sequelize ORM进行数据库操作

## 已完成功能

### ✅ 核心功能
- [x] 响应式首页设计，完全匹配设计图
- [x] 文章列表展示和分页
- [x] 分类筛选功能
- [x] 搜索功能（实时搜索）
- [x] 文章详情页面
- [x] 搜索结果页面
- [x] 404错误页面
- [x] 加载更多文章功能
- [x] 文章卡片点击跳转
- [x] 相关文章推荐

### ✅ 技术实现
- [x] Next.js 15 + React 18 前端架构
- [x] Express.js + Node.js 后端API
- [x] PostgreSQL + Sequelize ORM 数据库
- [x] TypeScript 类型安全
- [x] Tailwind CSS 响应式设计
- [x] 完整的数据库种子数据
- [x] CORS 跨域支持
- [x] 现代化组件架构

### ✅ 用户体验
- [x] 流畅的页面切换
- [x] 加载状态指示
- [x] 搜索高亮和结果统计
- [x] 移动端友好设计
- [x] 悬停效果和动画
- [x] 错误处理和友好提示

## 项目演示

1. **首页**: http://localhost:3000
   - 展示所有功能：分类筛选、搜索、文章列表

2. **文章详情**: http://localhost:3000/article/1
   - 完整的文章阅读体验

3. **搜索页面**: http://localhost:3000/search?q=technology
   - 搜索结果展示和无结果处理

4. **API测试**: http://localhost:5000/api/articles
   - 后端API接口测试

## 📋 开发进度

### ✅ 已完成功能

#### 🏗️ 基础架构
- [x] Next.js 15 + React 19 前端架构
- [x] Express.js + Node.js 后端API
- [x] PostgreSQL + Sequelize ORM 数据库
- [x] TypeScript 类型安全
- [x] Tailwind CSS 响应式设计

#### 🔐 认证授权系统
- [x] JWT 令牌认证
- [x] 用户注册/登录/登出
- [x] 密码重置功能
- [x] 邮箱验证系统
- [x] Google OAuth 社交登录
- [x] Facebook OAuth 社交登录
- [x] 基于角色的权限控制
- [x] 管理员后台权限

#### 👥 社交功能系统
- [x] 用户关注/取消关注
- [x] 关注者/关注列表
- [x] 私信系统
- [x] 用户活动时间线
- [x] 社交分享功能
- [x] 用户标签系统

#### 📝 内容管理系统
- [x] 文章草稿功能
- [x] 富文本编辑器
- [x] 图片/音频/视频上传（10分钟内）
- [x] 文件管理系统
- [x] AI + 人工智能内容审核
- [x] 内容分类和标签

#### 🔔 通知系统
- [x] WebSocket 实时通知
- [x] 邮件通知系统
- [x] Web Push 推送通知
- [x] 通知偏好设置
- [x] 多渠道通知集成
- [x] 通知历史记录

#### 📊 数据分析系统
- [x] 用户行为分析
- [x] 文章阅读统计
- [x] 热门内容推荐算法
- [x] 搜索分析
- [x] 用户画像分析
- [x] 数据可视化

### 🚧 进行中功能

#### 🎨 用户体验优化
- [ ] 深色模式支持
- [ ] 多语言支持（中英文）
- [ ] 无障碍功能
- [ ] 离线阅读功能
- [ ] PWA 支持
- [ ] 性能优化

#### 🖥️ 前端界面开发
- [ ] 认证界面完善
- [ ] 社交功能前端
- [ ] 内容管理前端
- [ ] 数据分析仪表板
- [ ] 管理员后台界面

## ✅ 系统状态验证

项目已完成根目录优化和结构清理，所有功能正常运行：

### 🟢 后端服务器状态
- ✅ PostgreSQL 数据库连接：正常
- ✅ 数据库同步：成功（19个表）
- ✅ Socket.IO 服务器：正常运行
- ✅ Web Push 服务：已初始化
- ✅ API 端点：正常响应
- ✅ 服务器端口：5000

### 🟢 前端应用状态
- ✅ Next.js 开发服务器：正常运行
- ✅ Turbopack 编译：成功
- ✅ 应用端口：3006 (自动分配)
- ✅ 热重载：已启用
- ✅ 内容管理系统：完全集成

### 🧹 项目优化完成
- ✅ 清理调试和测试文件：已完成
- ✅ 删除临时文件：已完成
- ✅ 优化目录结构：已完成
- ✅ 保留所有核心功能：已确认
- ✅ 项目结构文档：已更新

### 🛠️ 项目管理工具

项目包含以下管理脚本：
- `start.bat` - Windows 批处理启动脚本
- `start.ps1` - PowerShell 增强启动脚本（带错误检查和彩色输出）
- `cleanup.ps1` - 项目清理和优化脚本（新增）

### ⚠️ 注意事项
- 邮件服务配置：需要配置真实的 Gmail 凭据（不影响核心功能）
- 前端端口：由于端口3000被占用，自动使用3006端口

### 📊 功能验证测试

**API 测试示例：**
```powershell
# 测试文章API
Invoke-WebRequest -Uri "http://localhost:5000/api/articles" -Method GET

# 测试用户注册
Invoke-WebRequest -Uri "http://localhost:5000/api/auth/register" -Method POST -ContentType "application/json" -Body '{"username":"test","email":"<EMAIL>","password":"password123"}'
```

**前端访问：**
- 主页：http://localhost:3000
- 文章页面：http://localhost:3000/articles
- 用户登录：http://localhost:3000/login

### �📅 下一步计划
- [ ] 移动端应用开发
- [ ] 微信小程序版本
- [ ] 内容推荐算法优化
- [ ] 实时协作编辑
- [ ] 区块链内容确权
- [ ] AI 智能写作助手

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
