# OneNews 邮件服务配置指南

## 📧 邮件服务选择

### 选项1：Gmail SMTP（推荐用于开发和小规模部署）

#### 优点
- 免费且可靠
- 配置简单
- 适合个人项目和小团队

#### 缺点
- 每日发送限制（500封/天）
- 需要Google账户
- 可能被标记为垃圾邮件

#### 配置步骤

1. **启用两步验证**
   - 登录Google账户
   - 前往 [Google账户安全设置](https://myaccount.google.com/security)
   - 启用两步验证

2. **生成应用专用密码**
   - 在安全设置中找到"应用专用密码"
   - 选择"邮件"和"其他（自定义名称）"
   - 输入"OneNews"作为应用名称
   - 复制生成的16位密码

3. **配置环境变量**
   ```bash
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-digit-app-password
   EMAIL_FROM=OneNews <<EMAIL>>
   ```

### 选项2：SendGrid（推荐用于生产环境）

#### 优点
- 专业的邮件服务
- 高送达率
- 详细的分析报告
- 免费套餐（100封/天）
- 付费套餐支持大量发送

#### 缺点
- 需要注册和验证
- 付费套餐成本较高

#### 配置步骤

1. **注册SendGrid账户**
   - 访问 [SendGrid官网](https://sendgrid.com/)
   - 注册免费账户
   - 验证邮箱地址

2. **创建API密钥**
   - 登录SendGrid控制台
   - 前往 Settings > API Keys
   - 点击"Create API Key"
   - 选择"Restricted Access"
   - 给予"Mail Send"权限
   - 复制生成的API密钥

3. **验证发送域名**
   - 前往 Settings > Sender Authentication
   - 添加并验证您的域名
   - 配置DNS记录

4. **配置环境变量**
   ```bash
   EMAIL_HOST=smtp.sendgrid.net
   EMAIL_PORT=587
   EMAIL_USER=apikey
   EMAIL_PASS=your-sendgrid-api-key
   EMAIL_FROM=OneNews <<EMAIL>>
   ```

### 选项3：Amazon SES（推荐用于AWS部署）

#### 优点
- 与AWS生态系统集成
- 成本效益高
- 高可靠性和扩展性

#### 缺点
- 配置相对复杂
- 需要AWS账户
- 初期有发送限制

#### 配置步骤

1. **设置AWS SES**
   - 登录AWS控制台
   - 前往Simple Email Service (SES)
   - 验证发送邮箱地址或域名

2. **创建SMTP凭据**
   - 在SES控制台中创建SMTP凭据
   - 记录用户名和密码

3. **配置环境变量**
   ```bash
   EMAIL_HOST=email-smtp.us-east-1.amazonaws.com
   EMAIL_PORT=587
   EMAIL_USER=your-smtp-username
   EMAIL_PASS=your-smtp-password
   EMAIL_FROM=OneNews <<EMAIL>>
   ```

## 🧪 邮件服务测试

### 测试脚本

创建测试脚本来验证邮件配置：

```javascript
// Backend/scripts/testEmail.js
const { sendTestEmail } = require('../services/emailService');

async function testEmailService() {
  try {
    await sendTestEmail('<EMAIL>');
    console.log('✅ 邮件发送成功！');
  } catch (error) {
    console.error('❌ 邮件发送失败:', error.message);
  }
}

testEmailService();
```

### 测试命令

```bash
cd Backend
node scripts/testEmail.js
```

## 🔧 邮件模板配置

### HTML邮件模板

OneNews使用HTML邮件模板提供更好的用户体验：

1. **欢迎邮件模板** - 用户注册后发送
2. **邮箱验证模板** - 验证邮箱地址
3. **密码重置模板** - 密码重置链接
4. **通知邮件模板** - 系统通知

### 自定义邮件模板

可以在 `Backend/templates/email/` 目录下自定义邮件模板。

## 🚨 常见问题解决

### Gmail相关问题

1. **"Less secure app access"错误**
   - 解决方案：使用应用专用密码而不是账户密码

2. **"Authentication failed"错误**
   - 检查用户名和密码是否正确
   - 确保启用了两步验证
   - 重新生成应用专用密码

3. **发送限制**
   - Gmail每日限制500封邮件
   - 考虑升级到专业邮件服务

### SendGrid相关问题

1. **API密钥无效**
   - 确保API密钥有正确的权限
   - 检查密钥是否已过期

2. **域名验证失败**
   - 检查DNS记录是否正确配置
   - 等待DNS传播完成（可能需要24-48小时）

3. **邮件被标记为垃圾邮件**
   - 完成域名验证
   - 配置SPF、DKIM、DMARC记录

### 通用问题

1. **连接超时**
   - 检查防火墙设置
   - 确认SMTP端口未被阻止

2. **SSL/TLS错误**
   - 使用正确的端口（587用于STARTTLS，465用于SSL）
   - 检查SSL证书配置

## 📊 邮件服务监控

### 发送统计

监控以下指标：
- 发送成功率
- 退信率
- 打开率
- 点击率

### 日志记录

确保记录邮件发送日志：
- 发送时间
- 收件人
- 邮件类型
- 发送状态
- 错误信息

## 🔒 安全最佳实践

1. **使用环境变量**存储敏感信息
2. **定期轮换**API密钥和密码
3. **限制发送频率**防止滥用
4. **验证收件人**邮箱地址
5. **使用HTTPS**保护邮件链接
6. **实施退订机制**遵守法规要求

## 📈 生产环境建议

1. **使用专业邮件服务**（SendGrid、AWS SES等）
2. **配置域名验证**提高送达率
3. **设置邮件队列**处理大量发送
4. **实施重试机制**处理临时失败
5. **监控邮件指标**优化发送效果

---

**选择建议：**
- **开发环境**：Gmail SMTP
- **小规模生产**：SendGrid免费套餐
- **大规模生产**：SendGrid付费套餐或AWS SES
- **企业级部署**：自建邮件服务器或企业邮件服务
