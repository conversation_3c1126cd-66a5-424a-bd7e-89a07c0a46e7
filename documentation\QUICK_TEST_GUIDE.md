# Newzora 快速测试指南

## 🚀 快速开始

### 1. 启动服务器
```bash
# 一键启动（推荐）
start-stable.bat

# 或手动启动
cd Backend && npm run stable
cd Frontend && npm run stable
```

### 2. 访问测试页面
```
http://localhost:3000/test-auth
```

## 👤 测试账户

### 管理员账户
| 用户名 | 邮箱 | 密码 | 权限 |
|--------|------|------|------|
| `admin_test` | `<EMAIL>` | `Admin123!` | 完整系统权限 |
| `sysadmin_test` | `<EMAIL>` | `SysAdmin123!` | 系统管理权限 |

### 内容管理员账户
| 用户名 | 邮箱 | 密码 | 权限 |
|--------|------|------|------|
| `moderator_test` | `<EMAIL>` | `Moderator123!` | 内容管理权限 |
| `editor_chief` | `<EMAIL>` | `Editor123!` | 编辑管理权限 |

### 普通用户账户
| 用户名 | 邮箱 | 密码 | 用途 |
|--------|------|------|------|
| `user_test1` | `<EMAIL>` | `User123!` | 基础功能测试 |
| `user_test2` | `<EMAIL>` | `User123!` | 社交功能测试 |
| `author_test` | `<EMAIL>` | `Author123!` | 内容创作测试 |
| `reader_test` | `<EMAIL>` | `Reader123!` | 阅读功能测试 |

### 特殊状态账户
| 用户名 | 邮箱 | 密码 | 状态 | 用途 |
|--------|------|------|------|------|
| `unverified_test` | `<EMAIL>` | `Unverified123!` | 📧 未验证 | 邮箱验证测试 |
| `inactive_test` | `<EMAIL>` | `Inactive123!` | ❌ 已停用 | 账户状态测试 |

## 🧪 测试场景

### 基础功能测试 ✅

#### 1. 登录测试
```bash
# 管理员邮箱登录
POST /api/auth-enhanced/login
{
  "identifier": "<EMAIL>",
  "password": "Admin123!",
  "rememberMe": false
}

# 用户名登录
POST /api/auth-enhanced/login
{
  "identifier": "user_test1",
  "password": "User123!",
  "rememberMe": true
}
```

#### 2. 注册测试
```bash
POST /api/auth-enhanced/register
{
  "username": "newuser_test",
  "email": "<EMAIL>",
  "password": "NewUser123!",
  "firstName": "New",
  "lastName": "User",
  "acceptTerms": true
}
```

#### 3. 密码找回测试
```bash
POST /api/auth-enhanced/forgot-password
{
  "email": "<EMAIL>"
}
```

#### 4. 密码强度测试
```bash
POST /api/auth-enhanced/check-password-strength
{
  "password": "TestPassword123!"
}
```

### 异常情况测试 ⚠️

#### 1. 错误密码
- 使用正确邮箱 + 错误密码
- 预期：401 Unauthorized

#### 2. 不存在用户
- 使用不存在的邮箱
- 预期：401 Unauthorized

#### 3. 重复注册
- 使用已存在的用户名或邮箱
- 预期：400 Bad Request

#### 4. 弱密码
- 使用简单密码如 "123456"
- 预期：400 Bad Request

#### 5. 未验证用户
- 使用 `<EMAIL>`
- 预期：登录成功但有验证提示

#### 6. 停用账户
- 使用 `<EMAIL>`
- 预期：401 Unauthorized

### 权限测试 🔧

#### 1. 管理员权限
```bash
# 登录管理员账户
<EMAIL> / Admin123!

# 访问管理面板
GET /api/admin/dashboard
```

#### 2. 普通用户权限
```bash
# 登录普通用户
<EMAIL> / User123!

# 尝试访问管理功能（应被拒绝）
GET /api/admin/users
```

### 安全测试 🛡️

#### 1. 速率限制
- 快速连续发送10次错误登录请求
- 预期：触发速率限制 (429 Too Many Requests)

#### 2. Token验证
```bash
# 获取Token后验证
GET /api/auth-enhanced/me
Authorization: Bearer YOUR_ACCESS_TOKEN
```

#### 3. Token刷新
```bash
POST /api/auth-enhanced/refresh-token
{
  "refreshToken": "YOUR_REFRESH_TOKEN"
}
```

## 🌐 前端界面测试

### 访问测试页面
```
http://localhost:3000/test-auth
```

### 测试步骤
1. **快速登录测试**
   - 点击任意测试账户的"Quick Login"按钮
   - 验证登录成功提示
   - 检查用户信息显示

2. **手动登录测试**
   - 切换到"Login Test"标签
   - 输入邮箱和密码
   - 测试"Remember me"功能
   - 测试"Forgot Password"功能

3. **注册测试**
   - 切换到"Register Test"标签
   - 填写完整注册信息
   - 测试密码强度验证
   - 验证表单验证功能

4. **错误处理测试**
   - 输入错误密码
   - 输入不存在的用户
   - 测试网络错误处理

## 📊 测试检查清单

### 基础功能 ✅
- [ ] 管理员登录成功
- [ ] 普通用户登录成功
- [ ] 用户名登录成功
- [ ] 邮箱登录成功
- [ ] 新用户注册成功
- [ ] 密码强度验证正常

### 安全功能 ✅
- [ ] 错误密码被拒绝
- [ ] 不存在用户被拒绝
- [ ] 重复注册被拒绝
- [ ] 弱密码被拒绝
- [ ] 速率限制生效
- [ ] Token验证正常

### 用户体验 ✅
- [ ] 错误提示清晰
- [ ] 成功提示及时
- [ ] 加载状态显示
- [ ] 表单验证友好
- [ ] 响应式设计正常

### 权限控制 ✅
- [ ] 管理员权限正确
- [ ] 普通用户权限限制
- [ ] 未验证用户处理
- [ ] 停用账户拒绝

## 🔧 故障排除

### 常见问题

#### 1. 服务器无法访问
```bash
# 检查服务器状态
curl http://localhost:5000/api/health

# 或使用PowerShell
Invoke-WebRequest -Uri "http://localhost:5000/api/health"
```

#### 2. 登录失败
- 检查用户名/邮箱是否正确
- 检查密码是否正确
- 查看浏览器控制台错误
- 检查网络连接

#### 3. 注册失败
- 检查密码强度要求
- 确认用户名/邮箱未被使用
- 验证必填字段完整

#### 4. 前端页面无法访问
```bash
# 检查前端服务器
curl http://localhost:3000/health
```

### 调试工具

#### 1. 浏览器开发者工具
- Network标签：查看API请求
- Console标签：查看JavaScript错误
- Application标签：查看本地存储

#### 2. 服务器日志
- 查看后端服务器窗口输出
- 检查错误日志文件

#### 3. API测试工具
- Postman：测试API端点
- curl：命令行API测试
- 浏览器：直接访问GET端点

## 📞 获取帮助

### 自助诊断
```bash
# 运行网络诊断
network-doctor.bat

# 验证功能完整性
verify-features.bat

# 查看测试账户信息
node Backend/scripts/simple-test-accounts.js
```

### 重置测试环境
```bash
# 重置测试数据
node Backend/scripts/reset-test-data.js

# 重新创建测试账户
node Backend/scripts/create-test-accounts.js
```

## 🎉 测试完成

完成所有测试后，您应该能够：
- ✅ 使用各种测试账户成功登录
- ✅ 注册新用户账户
- ✅ 验证密码相关功能
- ✅ 确认安全机制正常工作
- ✅ 验证权限控制有效
- ✅ 确认用户界面响应正常

恭喜！Newzora认证系统已准备就绪！🎊
