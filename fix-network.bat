@echo off
echo 🔧 Newzora Network Fix Utility
echo ===============================
echo.
echo ⚠️ This script will attempt to fix common network issues
echo Some operations require administrator privileges
echo.
echo 📋 What this script will do:
echo 1. Clean DNS cache
echo 2. Reset network stack
echo 3. Kill conflicting processes
echo 4. Configure firewall
echo 5. Test network connectivity
echo.
pause

echo.
echo 🧹 Cleaning DNS Cache
echo =====================
ipconfig /flushdns
echo ✅ DNS cache flushed

echo.
echo 🔄 Resetting Network Stack
echo ==========================
echo Resetting Winsock catalog...
netsh winsock reset
echo.
echo Resetting TCP/IP stack...
netsh int ip reset
echo ✅ Network stack reset (restart required for full effect)

echo.
echo 🚪 Checking Port Conflicts
echo ==========================
echo Killing processes that might conflict with ports 3000 and 5000...

for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
    echo Found process %%a using port 5000
    tasklist | findstr %%a
    echo Terminating process %%a...
    taskkill /PID %%a /F 2>nul
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
    echo Found process %%a using port 3000
    tasklist | findstr %%a
    echo Terminating process %%a...
    taskkill /PID %%a /F 2>nul
)

echo ✅ Port cleanup completed

echo.
echo 🔥 Firewall Configuration
echo =========================
echo Adding Node.js to Windows Firewall exceptions...
netsh advfirewall firewall add rule name="Node.js Server" dir=in action=allow protocol=TCP localport=5000
netsh advfirewall firewall add rule name="Node.js Development" dir=in action=allow protocol=TCP localport=3000
echo ✅ Firewall rules added

echo.
echo 🌐 Testing Network Configuration
echo ================================
echo Testing localhost resolution...
nslookup localhost

echo.
echo Testing port binding...
node -e "
const net = require('net');
const testPort = (port) => {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, 'localhost', () => {
      console.log('✅ Port', port, 'is bindable');
      server.close();
      resolve(true);
    });
    server.on('error', (err) => {
      console.log('❌ Port', port, 'error:', err.message);
      resolve(false);
    });
  });
};

Promise.all([testPort(5000), testPort(3000)]).then(() => {
  console.log('Port binding test completed');
});
"

echo.
echo 📊 Network Fix Summary
echo ======================
echo ✅ DNS cache cleared
echo ✅ Network stack reset
echo ✅ Port conflicts resolved
echo ✅ Firewall rules added
echo ✅ Network configuration tested
echo.
echo 🔄 Next Steps:
echo =============
echo 1. Restart your computer (recommended)
echo 2. Or restart network adapter manually
echo 3. Run start-servers.bat to test
echo.
echo Press any key to exit...
pause >nul
