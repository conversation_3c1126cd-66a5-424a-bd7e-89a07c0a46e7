@echo off
echo 🌐 Newzora Network Diagnosis
echo ============================

echo.
echo 📡 Network Interface Information
echo ================================
ipconfig | findstr /C:"IPv4" /C:"Subnet"

echo.
echo 🔍 Localhost Resolution Test
echo ============================
echo Testing localhost resolution...
nslookup localhost
echo.
echo Testing 127.0.0.1 ping...
ping -n 2 127.0.0.1

echo.
echo 🚪 Port Availability Check
echo ==========================
echo Checking if port 5000 is available...
netstat -an | findstr :5000
if %errorlevel% == 0 (
    echo ⚠️ Port 5000 is in use by another process
    echo Finding process using port 5000...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
        echo Process ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo ✅ Port 5000 is available
)

echo.
echo Checking if port 3000 is available...
netstat -an | findstr :3000
if %errorlevel% == 0 (
    echo ⚠️ Port 3000 is in use by another process
    echo Finding process using port 3000...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        echo Process ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo ✅ Port 3000 is available
)

echo.
echo 🔥 Firewall Status Check
echo ========================
echo Checking Windows Firewall status...
netsh advfirewall show allprofiles state

echo.
echo 🌍 Network Connectivity Test
echo ============================
echo Testing external connectivity...
ping -n 2 *******

echo.
echo 📊 Active Network Connections
echo =============================
echo Current listening ports...
netstat -an | findstr LISTENING | findstr /E ":80 :443 :3000 :5000 :8000 :8080"

echo.
echo 🔧 Node.js Network Test
echo =======================
echo Testing Node.js network capabilities...
node -e "const net = require('net'); const server = net.createServer(); server.listen(0, 'localhost', () => { console.log('✅ Node.js can bind to localhost:', server.address()); server.close(); });"

echo.
echo 🏥 Localhost HTTP Test
echo ======================
echo Testing if we can make HTTP requests to localhost...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost' -UseBasicParsing -TimeoutSec 2 -ErrorAction Stop; Write-Host '✅ Localhost HTTP accessible' } catch { Write-Host '❌ Localhost HTTP not accessible:' $_.Exception.Message }"

echo.
echo 📋 Network Diagnosis Complete
echo =============================
echo.
echo 💡 Common Network Issues:
echo =========================
echo 1. Firewall blocking connections
echo 2. Antivirus software interference
echo 3. VPN or proxy conflicts
echo 4. Windows network adapter issues
echo 5. Localhost resolution problems
echo 6. Port conflicts with other applications
echo.
echo 🔧 Recommended Solutions:
echo ========================
echo 1. Temporarily disable Windows Firewall
echo 2. Add Node.js to firewall exceptions
echo 3. Check antivirus real-time protection
echo 4. Restart network adapter
echo 5. Flush DNS cache: ipconfig /flushdns
echo 6. Reset Winsock: netsh winsock reset
echo.
echo Press any key to continue...
pause >nul
