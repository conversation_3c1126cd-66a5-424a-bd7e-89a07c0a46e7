@echo off
chcp 65001 >nul
echo 🔧 Newzora 网络诊断医生
echo ========================
echo.

echo 📋 系统信息检查
echo ===============
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo.

echo Node.js 版本:
node --version 2>nul || echo ❌ Node.js 未安装

echo npm 版本:
npm --version 2>nul || echo ❌ npm 未安装

echo.
echo 🌐 网络接口信息
echo ===============
ipconfig | findstr /C:"IPv4" /C:"子网掩码" /C:"默认网关"

echo.
echo 🔍 DNS 解析测试
echo ===============
echo 测试 localhost 解析:
nslookup localhost 2>nul || echo ❌ localhost 解析失败

echo.
echo 测试外网 DNS:
nslookup google.com 2>nul || echo ❌ 外网 DNS 解析失败

echo.
echo 🚪 端口占用检查
echo ===============
echo 检查端口 5000:
netstat -an | findstr :5000
if %errorlevel% == 0 (
    echo ⚠️ 端口 5000 被占用
    echo 占用进程:
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
        tasklist | findstr %%a 2>nul
    )
) else (
    echo ✅ 端口 5000 可用
)

echo.
echo 检查端口 3000:
netstat -an | findstr :3000
if %errorlevel% == 0 (
    echo ⚠️ 端口 3000 被占用
    echo 占用进程:
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        tasklist | findstr %%a 2>nul
    )
) else (
    echo ✅ 端口 3000 可用
)

echo.
echo 🔥 防火墙状态
echo =============
netsh advfirewall show allprofiles state 2>nul || echo ❌ 无法检查防火墙状态

echo.
echo 🌍 网络连通性测试
echo =================
echo 测试本地回环:
ping -n 2 127.0.0.1 | findstr "TTL" >nul
if %errorlevel% == 0 (
    echo ✅ 本地回环正常
) else (
    echo ❌ 本地回环异常
)

echo.
echo 测试外网连接:
ping -n 2 ******* | findstr "TTL" >nul
if %errorlevel% == 0 (
    echo ✅ 外网连接正常
) else (
    echo ❌ 外网连接异常
)

echo.
echo 🔧 Node.js 网络能力测试
echo ======================
node -e "
const net = require('net');
const http = require('http');

console.log('测试 TCP 绑定能力...');
const server = net.createServer();
server.listen(0, 'localhost', () => {
    const port = server.address().port;
    console.log('✅ TCP 绑定成功，端口:', port);
    server.close();
    
    console.log('测试 HTTP 服务器能力...');
    const httpServer = http.createServer((req, res) => {
        res.end('OK');
    });
    
    httpServer.listen(0, 'localhost', () => {
        const httpPort = httpServer.address().port;
        console.log('✅ HTTP 服务器创建成功，端口:', httpPort);
        httpServer.close();
        
        console.log('✅ Node.js 网络功能正常');
    });
});

server.on('error', (err) => {
    console.log('❌ TCP 绑定失败:', err.message);
});
" 2>nul

echo.
echo 🏥 数据库连接测试
echo =================
if exist Backend\config\database-enhanced.js (
    echo 测试数据库连接...
    cd Backend
    node -e "
    const { sequelize } = require('./config/database-enhanced');
    sequelize.authenticate()
        .then(() => {
            console.log('✅ 数据库连接成功');
            return sequelize.close();
        })
        .catch(err => {
            console.log('❌ 数据库连接失败:', err.message);
            if (err.message.includes('ECONNREFUSED')) {
                console.log('💡 PostgreSQL 服务可能未启动');
            }
        })
        .finally(() => process.exit(0));
    " 2>nul
    cd ..
) else (
    echo ⚠️ 数据库配置文件不存在
)

echo.
echo 📊 诊断结果汇总
echo ===============

echo.
echo 🔧 常见问题解决方案
echo ==================
echo.
echo 1. 端口被占用:
echo    - 使用任务管理器结束占用进程
echo    - 或重启计算机释放所有端口
echo.
echo 2. 防火墙阻止:
echo    - 临时关闭 Windows 防火墙测试
echo    - 添加 Node.js 到防火墙例外
echo.
echo 3. 网络适配器问题:
echo    - 重启网络适配器
echo    - 刷新 DNS 缓存: ipconfig /flushdns
echo    - 重置网络栈: netsh winsock reset
echo.
echo 4. 代理/VPN 冲突:
echo    - 临时关闭代理软件测试
echo    - 检查系统代理设置
echo    - 确保 VPN 不影响本地连接
echo.
echo 5. 杀毒软件干扰:
echo    - 临时关闭实时保护测试
echo    - 添加项目目录到白名单
echo.
echo 6. 数据库连接问题:
echo    - 确保 PostgreSQL 服务正在运行
echo    - 检查数据库连接字符串
echo    - 验证数据库用户权限
echo.

echo 💡 自动修复选项
echo ================
echo.
set /p choice="是否尝试自动修复网络问题? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🔄 执行自动修复...
    
    echo 1. 刷新 DNS 缓存...
    ipconfig /flushdns >nul 2>&1
    echo ✅ DNS 缓存已刷新
    
    echo 2. 释放占用端口...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    echo ✅ 端口已释放
    
    echo 3. 添加防火墙规则...
    netsh advfirewall firewall add rule name="Newzora Backend" dir=in action=allow protocol=TCP localport=5000 >nul 2>&1
    netsh advfirewall firewall add rule name="Newzora Frontend" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1
    echo ✅ 防火墙规则已添加
    
    echo.
    echo ✅ 自动修复完成！
    echo 💡 建议重启计算机以确保所有更改生效
)

echo.
echo 📋 诊断完成
echo ===========
echo 如果问题仍然存在，请：
echo 1. 重启计算机
echo 2. 检查网络设备驱动
echo 3. 联系网络管理员
echo 4. 查看详细错误日志
echo.

echo 按任意键退出...
pause >nul
