{"name": "newzora", "version": "1.0.0", "description": "Modern News Content Platform - Full Stack Application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd Backend && npm run dev", "dev:frontend": "cd Frontend && npm run dev", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd Backend && npm start", "start:frontend": "cd Frontend && npm start", "build": "npm run build:frontend", "build:frontend": "cd Frontend && npm run build", "build:production": "npm run build:frontend && npm run build:backend", "build:backend": "cd Backend && npm run build", "install:all": "npm install && cd Backend && npm install && cd ../Frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd Backend && rm -rf node_modules package-lock.json", "clean:frontend": "cd Frontend && rm -rf node_modules package-lock.json .next", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd Backend && npm test", "test:frontend": "cd Frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd Backend && npm run lint", "lint:frontend": "cd Frontend && npm run lint", "deploy": "npm run build:production && npm run start:production", "start:production": "NODE_ENV=production concurrently \"npm run start:backend\" \"npm run start:frontend\"", "docker:build": "docker-compose -f deployment/docker/docker-compose.yml build", "docker:up": "docker-compose -f deployment/docker/docker-compose.yml up -d", "docker:down": "docker-compose -f deployment/docker/docker-compose.yml down", "docker:logs": "docker-compose -f deployment/docker/docker-compose.yml logs -f", "health:check": "curl -f http://localhost:5000/api/health && curl -f http://localhost:3000", "start:stable": "scripts/start-stable.bat", "test:pre-launch": "powershell -ExecutionPolicy Bypass -File scripts/run-pre-launch-tests.ps1", "test:automated": "node scripts/automated-testing-suite.js", "diagnose:network": "scripts/network-doctor.bat", "verify:features": "scripts/verify-features.bat"}, "keywords": ["news", "content", "platform", "nextjs", "express", "postgresql", "typescript", "tailwindcss"], "author": "Newzora Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/Jacken22/Newzora.git"}, "workspaces": ["Frontend", "Backend"], "dependencies": {"@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.50.4", "@tiptap/extension-code-block-lowlight": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-table-cell": "^2.25.0", "@tiptap/extension-table-header": "^2.25.0", "@tiptap/extension-table-row": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "bcrypt": "^6.0.0", "colors": "^1.4.0", "handlebars": "^4.7.8", "highlight.js": "^11.11.1", "lowlight": "^3.3.0", "mjml": "^4.15.3", "mjml-handlebars": "^0.3.2", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "react-dropzone": "^14.3.8", "typescript": "^5.8.3"}}