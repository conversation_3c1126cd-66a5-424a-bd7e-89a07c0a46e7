// 重置所有用户的登录尝试次数和锁定状态
const { User } = require('./Backend/models');

async function resetLoginAttempts() {
  try {
    console.log('🔄 Resetting all user login attempts and locks...');

    // 重置所有用户的登录尝试次数和锁定状态
    const result = await User.update(
      {
        loginAttempts: 0,
        lockUntil: null
      },
      {
        where: {} // 更新所有用户
      }
    );

    console.log(`✅ Reset login attempts for ${result[0]} users`);

    // 特别检查管理员用户
    const adminUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (adminUser) {
      console.log('👤 Admin user status:');
      console.log('📧 Email:', adminUser.email);
      console.log('🔒 Login attempts:', adminUser.loginAttempts);
      console.log('⏰ Lock until:', adminUser.lockUntil);
      console.log('🔓 Is active:', adminUser.isActive);
      console.log('✅ Email verified:', adminUser.isEmailVerified);
    }

    console.log('\n🎉 All users are now unlocked and ready for testing!');
    console.log('📧 Test with: <EMAIL>');
    console.log('🔑 Password: Admin123!');

  } catch (error) {
    console.error('❌ Error resetting login attempts:', error);
  }
}

resetLoginAttempts();
