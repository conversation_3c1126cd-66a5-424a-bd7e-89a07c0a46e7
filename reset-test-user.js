// 重置测试用户密码
const { User } = require('./Backend/models');
const bcrypt = require('bcrypt');

async function resetTestUser() {
  try {
    console.log('🔄 Resetting test user password...');

    // 查找管理员用户
    const adminUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (adminUser) {
      // 重置密码为 Admin123!
      const hashedPassword = await bcrypt.hash('Admin123!', 10);
      await adminUser.update({
        password: hashedPassword,
        loginAttempts: 0,
        lockUntil: null
      });

      console.log('✅ Admin user password reset successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: Admin123!');
    } else {
      console.log('❌ Admin user not found, creating new one...');
      
      // 创建新的管理员用户
      const newAdmin = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123!', // 这会被模型的beforeCreate hook自动加密
        role: 'admin',
        isActive: true,
        isEmailVerified: true
      });

      console.log('✅ New admin user created!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: Admin123!');
      console.log('🆔 User ID:', newAdmin.id);
    }

    // 查找或创建普通测试用户
    let testUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (testUser) {
      const hashedPassword = await bcrypt.hash('test123', 10);
      await testUser.update({
        password: hashedPassword,
        loginAttempts: 0,
        lockUntil: null
      });
      console.log('✅ Test user password reset!');
    } else {
      testUser = await User.create({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'test123',
        role: 'user',
        isActive: true,
        isEmailVerified: true
      });
      console.log('✅ New test user created!');
    }

    console.log('📧 Test Email: <EMAIL>');
    console.log('🔑 Test Password: test123');

  } catch (error) {
    console.error('❌ Error resetting test user:', error);
  }
}

resetTestUser();
