// 修复稳定登录 - 直接更新密码，不删除用户
const { User } = require('./Backend/models');
const bcrypt = require('bcrypt');

async function fixStableLogin() {
  try {
    console.log('🔧 Fixing stable login...');

    // 查找现有的admin用户
    let adminUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (adminUser) {
      console.log('👤 Found existing admin user');
      
      // 使用简单密码 admin123
      const plainPassword = 'admin123';
      const hashedPassword = await bcrypt.hash(plainPassword, 10);
      
      // 更新用户信息
      await adminUser.update({
        password: hashedPassword,
        loginAttempts: 0,
        lockUntil: null,
        isActive: true,
        isEmailVerified: true
      });

      console.log('✅ Admin user updated successfully!');
    } else {
      console.log('👤 Creating new admin user');
      
      // 创建新用户
      const plainPassword = 'admin123';
      const hashedPassword = await bcrypt.hash(plainPassword, 10);
      
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        isActive: true,
        isEmailVerified: true,
        loginAttempts: 0,
        lockUntil: null
      });

      console.log('✅ New admin user created!');
    }

    // 验证密码
    const isValid = await bcrypt.compare('admin123', adminUser.password);
    console.log('🔍 Password verification:', isValid ? '✅ VALID' : '❌ INVALID');

    console.log('\n🎉 STABLE LOGIN READY!');
    console.log('======================');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role:', adminUser.role);
    console.log('🔓 Active:', adminUser.isActive);
    console.log('✅ Verified:', adminUser.isEmailVerified);
    console.log('🔒 Login attempts:', adminUser.loginAttempts);
    console.log('======================');

  } catch (error) {
    console.error('❌ Error fixing login:', error);
  }
}

fixStableLogin();
