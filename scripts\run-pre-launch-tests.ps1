# Newzora 上线前完整功能测试 PowerShell 脚本

Write-Host "🚀 Newzora 上线前完整功能测试" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host ""

# 测试准备检查
Write-Host "📋 测试准备检查" -ForegroundColor Yellow
Write-Host "==============" -ForegroundColor Yellow

# 1. 检查Node.js环境
Write-Host "1. 检查Node.js环境..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 环境正常: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或不可用" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 2. 检查项目依赖
Write-Host ""
Write-Host "2. 检查项目依赖..." -ForegroundColor Cyan

# 检查后端依赖
Set-Location "..\Backend"
if (-not (Test-Path "node_modules")) {
    Write-Host "安装后端依赖..." -ForegroundColor Yellow
    npm install
}
Write-Host "✅ 后端依赖完整" -ForegroundColor Green

# 检查前端依赖
Set-Location "..\Frontend"
if (-not (Test-Path "node_modules")) {
    Write-Host "安装前端依赖..." -ForegroundColor Yellow
    npm install
}
Write-Host "✅ 前端依赖完整" -ForegroundColor Green

Set-Location ".."

# 3. 启动测试环境
Write-Host ""
Write-Host "3. 启动测试环境..." -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

# 停止现有Node进程
Write-Host "停止现有Node进程..." -ForegroundColor Cyan
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
} catch {
    # 忽略错误，可能没有运行的Node进程
}

# 启动后端服务器
Write-Host "启动后端服务器..." -ForegroundColor Cyan
Set-Location "..\Backend"
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    node server-launcher.js
}

# 等待后端启动
Write-Host "等待后端服务器启动 (20秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# 启动前端服务器
Write-Host "启动前端服务器..." -ForegroundColor Cyan
Set-Location "..\Frontend"
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    node simple-frontend.js
}

# 等待前端启动
Write-Host "等待前端服务器启动 (10秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Set-Location ".."

# 4. 验证服务器状态
Write-Host ""
Write-Host "4. 验证服务器状态..." -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

# 测试后端连接
Write-Host "测试后端连接..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ 后端服务器运行正常" -ForegroundColor Green
    
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   状态: $($data.status)" -ForegroundColor Gray
    
    if ($data.database -and $data.database.status) {
        Write-Host "   数据库: $($data.database.status)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 后端服务器连接失败" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red
    
    # 清理后台任务
    if ($backendJob) { Stop-Job $backendJob; Remove-Job $backendJob }
    if ($frontendJob) { Stop-Job $frontendJob; Remove-Job $frontendJob }
    
    Write-Host "💡 请检查后端服务器是否正常启动" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 测试前端连接
Write-Host "测试前端连接..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ 前端服务器运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ 前端服务器连接失败" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 执行自动化测试套件
Write-Host ""
Write-Host "🧪 开始执行自动化测试套件" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

Set-Location "..\Backend"
try {
    $testResult = & node "../scripts/automated-testing-suite.js"
    $testExitCode = $LASTEXITCODE
} catch {
    Write-Host "❌ 测试执行失败: $($_.Exception.Message)" -ForegroundColor Red
    $testExitCode = 1
}

Set-Location ".."

# 6. 显示测试结果
Write-Host ""
Write-Host "📊 测试执行完成" -ForegroundColor Yellow
Write-Host "==============" -ForegroundColor Yellow

if ($testExitCode -eq 0) {
    Write-Host "✅ 所有测试通过！" -ForegroundColor Green
    Write-Host "🎉 Newzora 已准备好上线！" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📋 上线前最终检查清单:" -ForegroundColor Yellow
    Write-Host "========================" -ForegroundColor Yellow
    Write-Host "[ ] 生产环境服务器准备就绪" -ForegroundColor Gray
    Write-Host "[ ] 域名和DNS配置完成" -ForegroundColor Gray
    Write-Host "[ ] SSL证书安装和配置" -ForegroundColor Gray
    Write-Host "[ ] 数据库生产环境迁移" -ForegroundColor Gray
    Write-Host "[ ] 环境变量配置正确" -ForegroundColor Gray
    Write-Host "[ ] 备份策略实施" -ForegroundColor Gray
    Write-Host "[ ] 监控和日志系统配置" -ForegroundColor Gray
    Write-Host "[ ] 错误报告和通知设置" -ForegroundColor Gray
    Write-Host "[ ] 性能监控配置" -ForegroundColor Gray
    Write-Host "[ ] 安全策略实施" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "🚀 建议上线步骤:" -ForegroundColor Yellow
    Write-Host "===============" -ForegroundColor Yellow
    Write-Host "1. 部署到预生产环境进行最终验证" -ForegroundColor Gray
    Write-Host "2. 执行数据库迁移和初始化" -ForegroundColor Gray
    Write-Host "3. 配置生产环境变量" -ForegroundColor Gray
    Write-Host "4. 部署应用到生产服务器" -ForegroundColor Gray
    Write-Host "5. 配置负载均衡和CDN" -ForegroundColor Gray
    Write-Host "6. 执行生产环境冒烟测试" -ForegroundColor Gray
    Write-Host "7. 监控系统启动和运行状态" -ForegroundColor Gray
    Write-Host "8. 正式发布和公告" -ForegroundColor Gray
} else {
    Write-Host "❌ 测试发现问题，需要修复后再上线" -ForegroundColor Red
    Write-Host ""
    
    Write-Host "🔧 问题修复建议:" -ForegroundColor Yellow
    Write-Host "===============" -ForegroundColor Yellow
    Write-Host "1. 查看详细测试报告: Backend/test-reports/" -ForegroundColor Gray
    Write-Host "2. 修复失败的测试用例" -ForegroundColor Gray
    Write-Host "3. 重新运行测试验证修复" -ForegroundColor Gray
    Write-Host "4. 确保所有测试通过后再考虑上线" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "💡 常见问题排查:" -ForegroundColor Yellow
    Write-Host "===============" -ForegroundColor Yellow
    Write-Host "- 检查数据库连接和配置" -ForegroundColor Gray
    Write-Host "- 验证API端点和路由配置" -ForegroundColor Gray
    Write-Host "- 确认用户权限和认证流程" -ForegroundColor Gray
    Write-Host "- 检查第三方服务集成" -ForegroundColor Gray
    Write-Host "- 验证邮件服务配置" -ForegroundColor Gray
}

# 显示环境信息
Write-Host ""
Write-Host "📍 测试环境信息:" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "后端服务器: http://localhost:5000" -ForegroundColor Gray
Write-Host "前端应用:   http://localhost:3000" -ForegroundColor Gray
Write-Host "测试页面:   http://localhost:3000/test-auth" -ForegroundColor Gray
Write-Host "管理面板:   http://localhost:3000/admin" -ForegroundColor Gray
Write-Host ""

Write-Host "🧪 手动测试建议:" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "1. 访问前端应用，测试用户界面" -ForegroundColor Gray
Write-Host "2. 使用测试账户验证登录流程" -ForegroundColor Gray
Write-Host "3. 创建和编辑文章测试内容管理" -ForegroundColor Gray
Write-Host "4. 测试社交功能（点赞、评论、关注）" -ForegroundColor Gray
Write-Host "5. 验证管理员功能和权限控制" -ForegroundColor Gray
Write-Host "6. 测试移动端响应式设计" -ForegroundColor Gray
Write-Host "7. 验证邮件通知功能" -ForegroundColor Gray
Write-Host "8. 测试搜索和筛选功能" -ForegroundColor Gray
Write-Host ""

Write-Host "📧 测试账户信息:" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "管理员: <EMAIL> / Admin123!" -ForegroundColor Gray
Write-Host "用户:   <EMAIL> / User123!" -ForegroundColor Gray
Write-Host "编辑:   <EMAIL> / Moderator123!" -ForegroundColor Gray
Write-Host ""

if ($testExitCode -eq 0) {
    Write-Host "🎯 恭喜！Newzora 已通过所有上线前测试！" -ForegroundColor Green
    Write-Host "🚀 系统已准备好正式上线！" -ForegroundColor Green
} else {
    Write-Host "⚠️ 请修复测试中发现的问题后重新测试" -ForegroundColor Yellow
}

Write-Host ""
$keepRunning = Read-Host "是否保持测试服务器运行以便手动测试? (y/n)"

if ($keepRunning -ne "y" -and $keepRunning -ne "Y") {
    Write-Host "停止测试服务器..." -ForegroundColor Cyan
    
    # 停止后台任务
    if ($backendJob) {
        Stop-Job $backendJob -ErrorAction SilentlyContinue
        Remove-Job $backendJob -ErrorAction SilentlyContinue
    }
    if ($frontendJob) {
        Stop-Job $frontendJob -ErrorAction SilentlyContinue
        Remove-Job $frontendJob -ErrorAction SilentlyContinue
    }
    
    # 停止Node进程
    try {
        Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
    } catch {
        # 忽略错误
    }
    
    Write-Host "✅ 测试服务器已停止" -ForegroundColor Green
} else {
    Write-Host "✅ 测试服务器继续运行，您可以进行手动测试" -ForegroundColor Green
    Write-Host "💡 测试完成后请手动关闭服务器窗口" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "测试完成！感谢使用Newzora测试套件。" -ForegroundColor Green
