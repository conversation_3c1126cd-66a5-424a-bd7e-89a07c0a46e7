#!/usr/bin/env node

/**
 * Newzora 简化功能测试
 * 测试核心功能是否正常工作
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Newzora 功能测试开始');
console.log('=' .repeat(50));

// 测试结果
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

function test(name, testFn) {
  testResults.total++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${name}`);
      testResults.passed++;
      testResults.details.push({ name, status: 'PASS', message: 'OK' });
    } else {
      console.log(`❌ ${name} - Test returned false`);
      testResults.failed++;
      testResults.details.push({ name, status: 'FAIL', message: 'Test returned false' });
    }
  } catch (error) {
    console.log(`❌ ${name} - ${error.message}`);
    testResults.failed++;
    testResults.details.push({ name, status: 'FAIL', message: error.message });
  }
}

// 1. 测试项目结构
console.log('\n📁 测试项目结构...');

test('Backend目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend'));
});

test('Frontend目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend'));
});

test('Backend package.json存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/package.json'));
});

test('Frontend package.json存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/package.json'));
});

test('Backend stable-server.js存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/stable-server.js'));
});

// 2. 测试Frontend文件结构
console.log('\n🎨 测试Frontend结构...');

test('Frontend src目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src'));
});

test('Frontend app目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/app'));
});

test('Frontend components目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/components'));
});

test('Frontend services目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/services'));
});

test('apiService.ts存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/services/apiService.ts'));
});

// 3. 测试新创建的页面
console.log('\n📄 测试新页面文件...');

test('文章详情页存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/app/article/[id]/page.tsx'));
});

test('用户资料页存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/app/profile/[username]/page.tsx'));
});

test('模板测试页存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/app/template-test/page.tsx'));
});

test('API健康检查端点存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/app/api/health/route.ts'));
});

// 4. 测试Backend路由文件
console.log('\n🔗 测试Backend路由...');

test('Backend routes目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/routes'));
});

test('认证路由存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/routes/auth-enhanced.js'));
});

test('文章路由存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/routes/articles.js'));
});

test('用户路由存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/routes/users.js'));
});

// 5. 测试配置文件
console.log('\n⚙️  测试配置文件...');

test('Frontend next.config.js存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/next.config.js'));
});

test('Frontend tailwind.config.js存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/tailwind.config.js'));
});

test('Frontend tsconfig.json存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/tsconfig.json'));
});

test('Backend config目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../Backend/config'));
});

// 6. 测试数据文件
console.log('\n📊 测试数据文件...');

test('mockArticles数据存在', () => {
  return fs.existsSync(path.join(__dirname, '../Frontend/src/data/mockArticles.ts'));
});

// 7. 测试脚本文件整理
console.log('\n📜 测试脚本整理...');

test('scripts目录存在', () => {
  return fs.existsSync(path.join(__dirname, '../scripts'));
});

test('API路由测试脚本存在', () => {
  return fs.existsSync(path.join(__dirname, './test-api-routes.js'));
});

// 8. 测试文件内容
console.log('\n📝 测试文件内容...');

test('apiService.ts包含正确的API基础URL', () => {
  const content = fs.readFileSync(path.join(__dirname, '../Frontend/src/services/apiService.ts'), 'utf8');
  return content.includes('localhost:5000/api') || content.includes('NEXT_PUBLIC_API_URL');
});

test('文章详情页包含Newzora品牌', () => {
  const content = fs.readFileSync(path.join(__dirname, '../Frontend/src/app/article/[id]/page.tsx'), 'utf8');
  return content.includes('Newzora');
});

test('用户资料页包含作品集网格', () => {
  const content = fs.readFileSync(path.join(__dirname, '../Frontend/src/app/profile/[username]/page.tsx'), 'utf8');
  return content.includes('grid') && content.includes('portfolioItems');
});

// 输出测试结果
console.log('\n' + '='.repeat(50));
console.log('📊 测试结果汇总');
console.log('='.repeat(50));

console.log(`总测试数: ${testResults.total}`);
console.log(`通过: ${testResults.passed}`);
console.log(`失败: ${testResults.failed}`);
console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

if (testResults.failed > 0) {
  console.log('\n❌ 失败的测试:');
  testResults.details
    .filter(detail => detail.status === 'FAIL')
    .forEach((detail, index) => {
      console.log(`${index + 1}. ${detail.name}: ${detail.message}`);
    });
}

console.log('\n🎯 功能状态评估:');

if (testResults.failed === 0) {
  console.log('✅ 所有核心功能文件就位，项目结构完整！');
} else if (testResults.passed > testResults.failed) {
  console.log('⚠️  大部分功能正常，少数问题需要关注。');
} else {
  console.log('🚨 多个核心文件缺失，需要修复项目结构。');
}

console.log('\n🚀 下一步建议:');
console.log('1. 手动启动Backend: 在Backend目录运行 node stable-server.js');
console.log('2. 手动启动Frontend: 在Frontend目录运行 npm run dev');
console.log('3. 访问 http://localhost:3000 测试前端');
console.log('4. 访问 http://localhost:3000/template-test 测试新功能');
console.log('5. 访问 http://localhost:3000/article/1 测试文章详情页');
console.log('6. 访问 http://localhost:3000/profile/sophia-carter 测试用户资料页');

console.log('\n💡 手动测试清单:');
console.log('□ 首页加载正常');
console.log('□ 探索页面正常');
console.log('□ 文章详情页显示正确');
console.log('□ 用户资料页作品集显示');
console.log('□ 导航链接工作正常');
console.log('□ 响应式设计正常');

// 保存测试报告
const reportPath = path.join(__dirname, '../test-reports/function-test-report.json');
const reportDir = path.dirname(reportPath);

if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

const report = {
  timestamp: new Date().toISOString(),
  summary: testResults,
  details: testResults.details,
  recommendations: [
    '手动启动服务器进行完整测试',
    '验证所有页面路由正常工作',
    '测试API连接和数据获取',
    '确认响应式设计在不同设备上正常'
  ]
};

fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
console.log(`\n📄 详细报告已保存到: ${reportPath}`);

process.exit(testResults.failed > 0 ? 1 : 0);
