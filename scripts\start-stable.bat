@echo off
chcp 65001 >nul
echo 🚀 Newzora 稳定服务器启动器
echo ============================
echo.

:: 检查Node.js
echo 📋 检查系统环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或不在PATH中
    echo 请安装 Node.js 16+ 版本
    pause
    exit /b 1
)

echo ✅ Node.js 已安装: 
node --version

:: 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo ✅ npm 已安装: 
npm --version

:: 检查端口占用
echo.
echo 🔍 检查端口占用...
netstat -an | findstr :5000 >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口 5000 已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        echo 终止进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
    )
)

netstat -an | findstr :3000 >nul
if %errorlevel% == 0 (
    echo ⚠️ 端口 3000 已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
        echo 终止进程 %%a...
        taskkill /PID %%a /F >nul 2>&1
    )
)

echo ✅ 端口检查完成

:: 设置环境变量
echo.
echo 🔧 配置环境变量...
copy Backend\.env.stable Backend\.env >nul 2>&1
copy Frontend\.env.stable Frontend\.env >nul 2>&1
echo ✅ 环境配置已更新

:: 检查依赖
echo.
echo 📦 检查项目依赖...

cd ..\Backend
if not exist node_modules (
    echo 📥 安装后端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 后端依赖已存在
)

cd ..\Frontend
if not exist node_modules (
    echo 📥 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 前端依赖已存在
)

cd ..

:: 启动数据库检查
echo.
echo 🗄️ 检查数据库连接...
cd ..\Backend
node -e "
const { sequelize } = require('./config/database-enhanced');
sequelize.authenticate()
  .then(() => {
    console.log('✅ 数据库连接成功');
    process.exit(0);
  })
  .catch(err => {
    console.log('❌ 数据库连接失败:', err.message);
    console.log('💡 请确保 PostgreSQL 正在运行');
    process.exit(1);
  });
" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ 数据库连接失败，但服务器仍会启动
    echo 💡 请检查 PostgreSQL 服务状态
)

cd ..

:: 启动服务器
echo.
echo 🚀 启动服务器...
echo ================

:: 启动后端服务器
echo 📡 启动后端服务器...
cd ..\Backend
start "Newzora Backend (Stable)" cmd /k "node server-launcher.js"
echo ✅ 后端服务器启动中...

:: 等待后端启动
echo ⏳ 等待后端服务器启动...
timeout /t 8 /nobreak >nul

:: 测试后端连接
echo 🧪 测试后端连接...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 后端服务器运行正常'
    $data = $response.Content | ConvertFrom-Json
    Write-Host '   状态:' $data.status
    Write-Host '   数据库:' $data.database.status
} catch {
    Write-Host '⚠️ 后端服务器可能还在启动中'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

:: 启动前端服务器
echo.
echo 🎨 启动前端服务器...
cd ..\Frontend
start "Newzora Frontend (Stable)" cmd /k "node simple-frontend.js"
echo ✅ 前端服务器启动中...

:: 等待前端启动
echo ⏳ 等待前端服务器启动...
timeout /t 5 /nobreak >nul

:: 测试前端连接
echo 🧪 测试前端连接...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 前端服务器运行正常'
} catch {
    Write-Host '⚠️ 前端服务器可能还在启动中'
}
" 2>nul

cd ..

:: 显示启动信息
echo.
echo 🎉 Newzora 稳定服务器启动完成！
echo ================================
echo.
echo 📍 访问地址:
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:5000
echo    健康检查: http://localhost:5000/api/health
echo    测试页面: http://localhost:3000/test-auth
echo.
echo 👤 测试账户:
echo    管理员: <EMAIL> / Admin123!
echo    用户:   <EMAIL> / User123!
echo.
echo 🌐 网络支持:
echo    ✅ 支持本地网络访问
echo    ✅ 支持翻墙网络环境
echo    ✅ 支持多网卡环境
echo    ✅ 支持代理服务器
echo.
echo 🔧 服务器特性:
echo    ✅ 自动错误恢复
echo    ✅ 优雅关闭处理
echo    ✅ 实时健康监控
echo    ✅ 完整功能保留
echo    ✅ 生产级稳定性
echo.
echo 💡 使用提示:
echo    - 两个服务器窗口会保持打开状态
echo    - 按 Ctrl+C 可以停止对应服务器
echo    - 服务器支持自动重启和错误恢复
echo    - 所有原有功能都已保留
echo.
echo 🌍 打开浏览器测试...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ 启动完成！服务器正在运行中...
echo 按任意键退出启动器（服务器将继续运行）
pause >nul
