#!/usr/bin/env node

/**
 * Newzora API路由测试工具
 * 测试前后端API对接是否正常
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_BASE = 'http://localhost:3000';

// 测试配置
const testConfig = {
  timeout: 10000,
  retries: 3,
  delay: 1000
};

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 工具函数
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = `[${timestamp}]`;
  
  switch (type) {
    case 'success':
      console.log(`${prefix} ✅ ${message}`.green);
      break;
    case 'error':
      console.log(`${prefix} ❌ ${message}`.red);
      break;
    case 'warning':
      console.log(`${prefix} ⚠️  ${message}`.yellow);
      break;
    case 'info':
    default:
      console.log(`${prefix} ℹ️  ${message}`.blue);
      break;
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 测试函数
async function testEndpoint(name, url, method = 'GET', data = null, headers = {}) {
  testResults.total++;
  
  try {
    log(`Testing ${name}...`, 'info');
    
    const config = {
      method,
      url,
      timeout: testConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    if (response.status >= 200 && response.status < 300) {
      log(`${name} - Status: ${response.status}`, 'success');
      testResults.passed++;
      return { success: true, data: response.data, status: response.status };
    } else {
      throw new Error(`Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    const errorMsg = error.response ? 
      `${error.response.status} - ${error.response.statusText}` : 
      error.message;
    
    log(`${name} - Error: ${errorMsg}`, 'error');
    testResults.failed++;
    testResults.errors.push({ name, error: errorMsg });
    return { success: false, error: errorMsg };
  }
}

// 主要测试套件
async function runApiTests() {
  console.log('🚀 Starting Newzora API Route Tests'.cyan.bold);
  console.log('=' .repeat(50).cyan);
  
  // 1. 测试服务器健康状态
  log('Testing server health...', 'info');
  await testEndpoint('Backend Health Check', `${API_BASE}/health`);
  await testEndpoint('Frontend Health Check', `${FRONTEND_BASE}/api/health`);
  
  await sleep(testConfig.delay);
  
  // 2. 测试认证路由
  log('Testing authentication routes...', 'info');
  await testEndpoint('Auth Status', `${API_BASE}/auth/status`);
  await testEndpoint('Auth Enhanced Status', `${API_BASE}/auth-enhanced/status`);
  
  // 测试登录（使用测试凭据）
  const loginData = {
    identifier: '<EMAIL>',
    password: 'admin123'
  };
  const loginResult = await testEndpoint(
    'Login Test', 
    `${API_BASE}/auth-enhanced/login`, 
    'POST', 
    loginData
  );
  
  let authToken = null;
  if (loginResult.success && loginResult.data && loginResult.data.data) {
    authToken = loginResult.data.data.tokens?.accessToken;
    log('Auth token obtained for protected route tests', 'success');
  }
  
  await sleep(testConfig.delay);
  
  // 3. 测试文章路由
  log('Testing article routes...', 'info');
  await testEndpoint('Get Articles', `${API_BASE}/articles`);
  await testEndpoint('Get Article Categories', `${API_BASE}/categories`);
  await testEndpoint('Get Article Tags', `${API_BASE}/tags`);
  
  // 测试单个文章
  await testEndpoint('Get Article by ID', `${API_BASE}/articles/1`);
  
  await sleep(testConfig.delay);
  
  // 4. 测试用户路由
  log('Testing user routes...', 'info');
  await testEndpoint('Get Users', `${API_BASE}/users`);
  
  // 如果有认证token，测试受保护的路由
  if (authToken) {
    const authHeaders = { 'Authorization': `Bearer ${authToken}` };
    await testEndpoint('Get User Profile', `${API_BASE}/users/profile`, 'GET', null, authHeaders);
    await testEndpoint('Get User Notifications', `${API_BASE}/notifications`, 'GET', null, authHeaders);
  }
  
  await sleep(testConfig.delay);
  
  // 5. 测试搜索路由
  log('Testing search routes...', 'info');
  await testEndpoint('Search Articles', `${API_BASE}/search?q=test&type=articles`);
  await testEndpoint('Search Users', `${API_BASE}/search?q=admin&type=users`);
  
  await sleep(testConfig.delay);
  
  // 6. 测试媒体路由
  log('Testing media routes...', 'info');
  await testEndpoint('Get Media Files', `${API_BASE}/media`);
  
  // 7. 测试社交功能路由
  log('Testing social routes...', 'info');
  await testEndpoint('Get Activities', `${API_BASE}/activities`);
  await testEndpoint('Get Follows', `${API_BASE}/follows`);
  
  await sleep(testConfig.delay);
  
  // 8. 测试管理员路由（如果有权限）
  if (authToken) {
    log('Testing admin routes...', 'info');
    const authHeaders = { 'Authorization': `Bearer ${authToken}` };
    await testEndpoint('Admin Dashboard', `${API_BASE}/admin/dashboard`, 'GET', null, authHeaders);
    await testEndpoint('Admin Users', `${API_BASE}/admin/users`, 'GET', null, authHeaders);
  }
  
  // 9. 测试前端页面路由
  log('Testing frontend page routes...', 'info');
  await testEndpoint('Homepage', `${FRONTEND_BASE}/`);
  await testEndpoint('Explore Page', `${FRONTEND_BASE}/explore`);
  await testEndpoint('Article Detail Page', `${FRONTEND_BASE}/article/1`);
  await testEndpoint('Profile Page', `${FRONTEND_BASE}/profile/sophia-carter`);
  await testEndpoint('Template Test Page', `${FRONTEND_BASE}/template-test`);
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50).cyan);
  console.log('📊 Test Results Summary'.cyan.bold);
  console.log('='.repeat(50).cyan);
  
  console.log(`Total Tests: ${testResults.total}`.white);
  console.log(`Passed: ${testResults.passed}`.green);
  console.log(`Failed: ${testResults.failed}`.red);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`.yellow);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ Failed Tests:'.red.bold);
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.name}: ${error.error}`.red);
    });
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! API routes are working correctly.'.green.bold);
  } else if (testResults.passed > testResults.failed) {
    console.log('\n⚠️  Most tests passed, but some issues need attention.'.yellow.bold);
  } else {
    console.log('\n🚨 Many tests failed. Please check server configuration.'.red.bold);
  }
  
  console.log('\n💡 Next Steps:'.blue.bold);
  console.log('1. Start backend server: cd Backend && npm run dev');
  console.log('2. Start frontend server: cd Frontend && npm run dev');
  console.log('3. Re-run this test: node scripts/test-api-routes.js');
  
  return testResults;
}

// 运行测试
if (require.main === module) {
  runApiTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { runApiTests, testEndpoint };
