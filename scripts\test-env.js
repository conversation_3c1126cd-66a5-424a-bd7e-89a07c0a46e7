// 测试环境变量
console.log('🔍 Environment check:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('Is development?', process.env.NODE_ENV === 'development');

// 测试硬编码验证逻辑
const testEmail = '<EMAIL>';
const testPassword = 'admin123';

console.log('\n🧪 Testing hardcoded logic:');
if (process.env.NODE_ENV === 'development') {
  if ((testEmail === '<EMAIL>' && testPassword === 'admin123') ||
      (testEmail === '<EMAIL>' && testPassword === 'test123')) {
    console.log('✅ Hardcoded credentials would work');
  } else {
    console.log('❌ Hardcoded credentials would fail');
  }
} else {
  console.log('⚠️ Not in development mode');
}
