// 测试登录功能
const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('🧪 Testing login functionality...');
    
    // 测试前端硬编码登录
    console.log('\n1️⃣ Testing frontend hardcoded login logic...');
    const email = '<EMAIL>';
    const password = 'admin123';
    
    if ((email.toLowerCase() === '<EMAIL>' && password === 'admin123') ||
        (email.toLowerCase() === '<EMAIL>' && password === 'test123')) {
      console.log('✅ Frontend hardcoded logic: PASS');
    } else {
      console.log('❌ Frontend hardcoded logic: FAIL');
    }
    
    // 测试后端API
    console.log('\n2️⃣ Testing backend API...');
    const response = await fetch('http://localhost:5000/api/auth-enhanced/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    const data = await response.json();
    console.log('📡 API Response Status:', response.status);
    console.log('📡 API Response Data:', data);
    
    if (response.status === 200 && data.success) {
      console.log('✅ Backend API login: PASS');
    } else {
      console.log('❌ Backend API login: FAIL');
    }
    
    // 测试前端页面
    console.log('\n3️⃣ Testing frontend page...');
    const pageResponse = await fetch('http://localhost:3000/login');
    console.log('📄 Login page status:', pageResponse.status);
    
    if (pageResponse.status === 200) {
      console.log('✅ Login page accessible: PASS');
    } else {
      console.log('❌ Login page accessible: FAIL');
    }
    
    console.log('\n🎯 Test Summary:');
    console.log('==================');
    console.log('Frontend: http://localhost:3000');
    console.log('Backend: http://localhost:5000');
    console.log('Login credentials: <EMAIL> / admin123');
    console.log('==================');
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testLogin();
