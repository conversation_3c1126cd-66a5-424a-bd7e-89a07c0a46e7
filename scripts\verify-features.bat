@echo off
chcp 65001 >nul
echo 🧪 Newzora 功能完整性验证
echo =========================
echo.

echo 📋 开始功能验证测试...
echo.

:: 检查服务器是否运行
echo 🔍 检查服务器状态...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 5
    Write-Host '✅ 后端服务器运行正常'
    $data = $response.Content | ConvertFrom-Json
    Write-Host '   状态:' $data.status
    Write-Host '   数据库:' $data.database.status
    Write-Host '   运行时间:' ([math]::Round($data.uptime)) '秒'
} catch {
    Write-Host '❌ 后端服务器未运行'
    Write-Host '💡 请先运行: start-stable.bat'
    exit 1
}
" 2>nul

if %errorlevel% neq 0 (
    echo.
    echo ❌ 服务器未运行，无法进行功能验证
    echo 请先启动服务器: start-stable.bat
    pause
    exit /b 1
)

echo.
echo 🔐 测试认证系统...
echo ==================

:: 测试用户登录
echo 测试管理员登录...
powershell -Command "
try {
    $body = @{
        identifier = '<EMAIL>'
        password = 'Admin123!'
        rememberMe = $false
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    if ($data.success) {
        Write-Host '✅ 管理员登录成功'
        Write-Host '   用户:' $data.data.user.username
        Write-Host '   角色:' $data.data.user.role
        Write-Host '   Token:' ($data.data.tokens.accessToken -ne $null)
    } else {
        Write-Host '❌ 管理员登录失败:' $data.message
    }
} catch {
    Write-Host '❌ 登录测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 测试普通用户登录...
powershell -Command "
try {
    $body = @{
        identifier = '<EMAIL>'
        password = 'User123!'
        rememberMe = $false
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/login' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    if ($data.success) {
        Write-Host '✅ 普通用户登录成功'
        Write-Host '   用户:' $data.data.user.username
        Write-Host '   角色:' $data.data.user.role
    } else {
        Write-Host '❌ 普通用户登录失败:' $data.message
    }
} catch {
    Write-Host '❌ 用户登录测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 💪 测试密码强度验证...
powershell -Command "
try {
    $body = @{ password = 'TestPassword123!' } | ConvertTo-Json
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth-enhanced/check-password-strength' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host '✅ 密码强度验证:' $data.data.strength '(' ($data.data.isValid -eq $true ? '有效' : '无效') ')'
} catch {
    Write-Host '❌ 密码强度测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 📰 测试文章系统...
echo ==================

echo 测试文章列表获取...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/articles' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    if ($data.success -or $response.StatusCode -eq 200) {
        Write-Host '✅ 文章列表获取成功'
        if ($data.data) {
            Write-Host '   文章数量:' $data.data.Count
        }
    } else {
        Write-Host '❌ 文章列表获取失败'
    }
} catch {
    Write-Host '❌ 文章系统测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 🏷️ 测试分类系统...
echo ==================

echo 测试分类列表获取...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/categories' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    if ($response.StatusCode -eq 200) {
        Write-Host '✅ 分类列表获取成功'
        if ($data -is [array]) {
            Write-Host '   分类数量:' $data.Count
        }
    } else {
        Write-Host '❌ 分类列表获取失败'
    }
} catch {
    Write-Host '❌ 分类系统测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 👥 测试用户管理...
echo ==================

echo 测试用户列表获取...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/users' -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host '✅ 用户系统可访问'
    } else {
        Write-Host '⚠️ 用户系统需要认证'
    }
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host '✅ 用户系统安全验证正常 (需要认证)'
    } else {
        Write-Host '❌ 用户系统测试失败:' $_.Exception.Message
    }
}
" 2>nul

echo.
echo 🔧 测试管理员功能...
echo ====================

echo 测试管理员仪表板...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/admin/dashboard' -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host '✅ 管理员仪表板可访问'
    } else {
        Write-Host '⚠️ 管理员仪表板需要认证'
    }
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host '✅ 管理员功能安全验证正常 (需要认证)'
    } else {
        Write-Host '❌ 管理员功能测试失败:' $_.Exception.Message
    }
}
" 2>nul

echo.
echo 🔍 测试搜索功能...
echo ==================

echo 测试搜索接口...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/search?q=test' -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host '✅ 搜索功能可用'
    } else {
        Write-Host '⚠️ 搜索功能可能需要配置'
    }
} catch {
    Write-Host '⚠️ 搜索功能测试失败 (可能需要配置):' $_.Exception.Message
}
" 2>nul

echo.
echo 📧 测试邮件服务...
echo ==================

echo 测试邮件配置...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/email/test' -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host '✅ 邮件服务配置正常'
    } else {
        Write-Host '⚠️ 邮件服务可能需要配置'
    }
} catch {
    Write-Host '⚠️ 邮件服务测试失败 (需要SMTP配置):' $_.Exception.Message
}
" 2>nul

echo.
echo 🌐 测试前端服务器...
echo ====================

echo 测试前端健康检查...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host '✅ 前端服务器运行正常'
    Write-Host '   状态:' $data.status
    Write-Host '   后端连接:' $data.backend
} catch {
    Write-Host '❌ 前端服务器测试失败:' $_.Exception.Message
}
" 2>nul

echo.
echo 📊 功能验证总结
echo ===============
echo.
echo ✅ 已验证的核心功能:
echo    - 后端服务器健康状态
echo    - 用户认证系统 (登录/密码验证)
echo    - 文章管理系统
echo    - 分类管理系统
echo    - 用户管理系统 (安全验证)
echo    - 管理员功能 (权限控制)
echo    - 前端服务器状态
echo.
echo ⚠️ 需要配置的功能:
echo    - 搜索功能 (可能需要搜索引擎配置)
echo    - 邮件服务 (需要SMTP配置)
echo.
echo 🔧 高级功能 (需要认证测试):
echo    - 社交功能 (关注/消息)
echo    - 通知系统
echo    - 文件上传
echo    - 数据分析
echo    - 权限管理
echo.
echo 💡 测试建议:
echo    1. 使用浏览器访问 http://localhost:3000/test-auth
echo    2. 测试完整的用户注册/登录流程
echo    3. 验证管理员面板功能
echo    4. 测试文章创建和编辑
echo    5. 检查所有前端组件功能
echo.

echo ✅ 功能验证完成！
echo.
echo 🌍 打开测试页面进行手动验证...
timeout /t 3 /nobreak >nul
start http://localhost:3000/test-auth

echo.
echo 按任意键退出...
pause >nul
