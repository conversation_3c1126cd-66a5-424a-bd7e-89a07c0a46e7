@echo off
echo 🚀 启动Newzora Next.js应用
echo ========================
echo.

echo 📍 当前目录: %CD%
echo.

echo 🔍 检查Node.js版本...
node --version
echo.

echo 📦 检查npm版本...
npm --version
echo.

echo 🏗️ 进入前端目录...
cd Frontend
echo 📍 前端目录: %CD%
echo.

echo 📋 检查package.json...
if exist package.json (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
    pause
    exit /b 1
)
echo.

echo 📦 检查node_modules...
if exist node_modules (
    echo ✅ node_modules 存在
) else (
    echo 📥 安装依赖...
    npm install
)
echo.

echo 🚀 启动Next.js开发服务器...
echo 📍 访问地址: http://localhost:3001
echo.
npx next dev --port 3001

pause
