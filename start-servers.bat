@echo off
echo 🚀 Starting Newzora Servers
echo ===========================

echo.
echo 📡 Checking if servers are already running...
netstat -ano | findstr :5000 >nul
if %errorlevel% == 0 (
    echo ⚠️ Backend server is already running on port 5000
) else (
    echo ✅ Port 5000 is available
)

netstat -ano | findstr :3000 >nul
if %errorlevel% == 0 (
    echo ⚠️ Frontend server is already running on port 3000
) else (
    echo ✅ Port 3000 is available
)

echo.
echo 🔧 Starting Backend Server...
echo ==============================
cd Backend
start "Newzora Backend" cmd /k "node simple-server.js"
echo ✅ Backend server starting in new window...

echo.
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo.
echo 🎨 Starting Frontend Server...
echo ==============================
cd ..\Frontend
start "Newzora Frontend" cmd /k "npm run dev"
echo ✅ Frontend server starting in new window...

echo.
echo 🎉 Servers Started!
echo ===================
echo Backend:  http://localhost:5000
echo Frontend: http://localhost:3000
echo Health:   http://localhost:5000/api/health
echo Test:     http://localhost:3000/test-auth
echo.
echo 📋 Test Accounts:
echo ================
echo Admin:    <EMAIL>     / Admin123!
echo User:     <EMAIL>     / User123!
echo.
echo Press any key to continue...
pause >nul
