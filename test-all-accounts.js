// 测试所有账户的登录功能
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:5000/api';

// 测试账户列表
const testAccounts = [
  { email: '<EMAIL>', password: 'Admin123!', role: 'admin', username: 'admin_test' },
  { email: '<EMAIL>', password: 'Moderator123!', role: 'moderator', username: 'moderator_test' },
  { email: '<EMAIL>', password: 'Editor123!', role: 'editor', username: 'editor_test' },
  { email: '<EMAIL>', password: 'User123!', role: 'user', username: 'user_test1' },
  { email: '<EMAIL>', password: 'User123!', role: 'user', username: 'user_test2' },
  { email: '<EMAIL>', password: 'Unverified123!', role: 'user', username: 'unverified_test' },
  { email: '<EMAIL>', password: 'Inactive123!', role: 'user', username: 'inactive_test' },
  // 向后兼容的账户
  { email: '<EMAIL>', password: 'test123', role: 'user', username: 'test_user' }
];

async function testLogin(account) {
  try {
    console.log(`\n🧪 Testing login for ${account.email}...`);
    
    // 测试邮箱登录
    const emailResponse = await fetch(`${API_BASE}/auth-enhanced/debug-login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: account.email,
        password: account.password
      })
    });
    
    const emailData = await emailResponse.json();
    
    if (emailResponse.ok && emailData.success) {
      console.log(`✅ Email login successful for ${account.email}`);
      console.log(`   Role: ${emailData.data.user.role}`);
      console.log(`   Username: ${emailData.data.user.username}`);
    } else {
      console.log(`❌ Email login failed for ${account.email}: ${emailData.message}`);
    }
    
    // 测试用户名登录
    const usernameResponse = await fetch(`${API_BASE}/auth-enhanced/debug-login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: account.username,
        password: account.password
      })
    });
    
    const usernameData = await usernameResponse.json();
    
    if (usernameResponse.ok && usernameData.success) {
      console.log(`✅ Username login successful for ${account.username}`);
    } else {
      console.log(`❌ Username login failed for ${account.username}: ${usernameData.message}`);
    }
    
    return { email: emailResponse.ok, username: usernameResponse.ok };
    
  } catch (error) {
    console.log(`❌ Network error testing ${account.email}: ${error.message}`);
    return { email: false, username: false };
  }
}

async function testHealthCheck() {
  try {
    console.log('🏥 Testing health check...');
    const response = await fetch(`${API_BASE}/health`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Health check passed');
      console.log(`   Status: ${data.status}`);
      console.log(`   Environment: ${data.environment}`);
      return true;
    } else {
      console.log('❌ Health check failed');
      return false;
    }
  } catch (error) {
    console.log(`❌ Health check error: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting comprehensive account testing...');
  console.log('==========================================');
  
  // 首先测试健康检查
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('\n❌ Server health check failed. Please ensure the backend server is running.');
    return;
  }
  
  // 测试所有账户
  const results = [];
  for (const account of testAccounts) {
    const result = await testLogin(account);
    results.push({ account, result });
  }
  
  // 汇总结果
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  
  let totalTests = 0;
  let passedTests = 0;
  
  results.forEach(({ account, result }) => {
    totalTests += 2; // 邮箱和用户名登录
    if (result.email) passedTests++;
    if (result.username) passedTests++;
    
    const emailStatus = result.email ? '✅' : '❌';
    const usernameStatus = result.username ? '✅' : '❌';
    
    console.log(`${account.email}:`);
    console.log(`  Email login: ${emailStatus}`);
    console.log(`  Username login: ${usernameStatus}`);
  });
  
  console.log(`\n🎯 Overall Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! API routes are working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the server logs for details.');
  }
}

// 运行测试
runAllTests().catch(console.error);
