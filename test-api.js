// 测试API连接
const fetch = require('node-fetch');

async function testAPIs() {
  console.log('🧪 Testing Newzora APIs...\n');

  // 1. 测试健康检查
  try {
    console.log('1. Testing Health Check...');
    const healthResponse = await fetch('http://localhost:5000/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health Check:', healthData.status);
  } catch (error) {
    console.log('❌ Health Check failed:', error.message);
  }

  // 2. 测试文章列表
  try {
    console.log('\n2. Testing Articles List...');
    const articlesResponse = await fetch('http://localhost:5000/api/articles');
    const articlesData = await articlesResponse.json();
    console.log('✅ Articles List:', articlesData.articles?.length || 0, 'articles');
  } catch (error) {
    console.log('❌ Articles List failed:', error.message);
  }

  // 3. 测试单篇文章
  try {
    console.log('\n3. Testing Single Article...');
    const articleResponse = await fetch('http://localhost:5000/api/articles/1');
    const articleData = await articleResponse.json();
    console.log('✅ Single Article:', articleData.title || 'No title');
  } catch (error) {
    console.log('❌ Single Article failed:', error.message);
  }

  // 4. 测试登录API
  try {
    console.log('\n4. Testing Login API...');
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    const loginData = await loginResponse.json();
    console.log('✅ Login API Response:', loginData);
  } catch (error) {
    console.log('❌ Login API failed:', error.message);
  }

  // 5. 测试注册API
  try {
    console.log('\n5. Testing Register API...');
    const registerResponse = await fetch('http://localhost:5000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        username: 'newuser',
        display_name: 'New User'
      })
    });
    const registerData = await registerResponse.json();
    console.log('✅ Register API Response:', registerData);
  } catch (error) {
    console.log('❌ Register API failed:', error.message);
  }
}

testAPIs();
