@echo off
chcp 65001 >nul
echo 🧪 Newzora 完整功能测试套件
echo ===========================
echo.

echo 📋 测试流程说明:
echo 1. 创建测试账户
echo 2. 启动稳定服务器
echo 3. 运行后端API测试
echo 4. 验证前端功能
echo 5. 生成测试报告
echo.

set /p confirm="开始完整测试? (y/n): "
if /i not "%confirm%"=="y" (
    echo 测试已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始完整测试流程...
echo =====================

:: 步骤1: 检查环境
echo.
echo 📋 步骤1: 检查测试环境
echo ======================
echo 检查Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装
    pause
    exit /b 1
)
echo ✅ Node.js 已安装

echo 检查npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)
echo ✅ npm 已安装

:: 步骤2: 安装依赖
echo.
echo 📦 步骤2: 检查项目依赖
echo ======================
cd Backend
if not exist node_modules (
    echo 安装后端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 后端依赖完整

cd ..\Frontend
if not exist node_modules (
    echo 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 前端依赖完整

cd ..

:: 步骤3: 创建测试账户
echo.
echo 👥 步骤3: 创建测试账户
echo ======================
cd Backend
echo 创建测试账户...
node scripts/create-test-accounts.js
if %errorlevel% neq 0 (
    echo ⚠️ 测试账户创建失败，但继续测试
) else (
    echo ✅ 测试账户创建成功
)

:: 步骤4: 启动后端服务器
echo.
echo 🚀 步骤4: 启动后端服务器
echo ========================
echo 启动稳定后端服务器...
start "Newzora Backend Test" cmd /k "node server-launcher.js"
echo ✅ 后端服务器启动中...

:: 等待服务器启动
echo ⏳ 等待服务器启动 (15秒)...
timeout /t 15 /nobreak >nul

:: 步骤5: 测试服务器连接
echo.
echo 🔍 步骤5: 测试服务器连接
echo ========================
echo 测试后端健康状态...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 后端服务器运行正常'
    $data = $response.Content | ConvertFrom-Json
    Write-Host '   状态:' $data.status
    Write-Host '   数据库:' $data.database.status
    Write-Host '   运行时间:' ([math]::Round($data.uptime)) '秒'
} catch {
    Write-Host '❌ 后端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
    exit 1
}
" 2>nul

if %errorlevel% neq 0 (
    echo ❌ 后端服务器未正常启动
    echo 💡 请检查服务器窗口的错误信息
    pause
    exit /b 1
)

:: 步骤6: 运行后端API测试
echo.
echo 🧪 步骤6: 运行后端API测试
echo =========================
echo 执行综合认证功能测试...
node scripts/comprehensive-auth-test.js
set "api_test_result=%errorlevel%"

if %api_test_result% equ 0 (
    echo ✅ 后端API测试全部通过
) else (
    echo ⚠️ 后端API测试部分失败
)

:: 步骤7: 启动前端服务器
echo.
echo 🎨 步骤7: 启动前端服务器
echo ========================
cd ..\Frontend
echo 启动前端服务器...
start "Newzora Frontend Test" cmd /k "node stable-server.js"
echo ✅ 前端服务器启动中...

:: 等待前端启动
echo ⏳ 等待前端服务器启动 (10秒)...
timeout /t 10 /nobreak >nul

:: 步骤8: 测试前端连接
echo.
echo 🌐 步骤8: 测试前端连接
echo ======================
echo 测试前端服务器...
powershell -Command "
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 10
    Write-Host '✅ 前端服务器运行正常'
    $data = $response.Content | ConvertFrom-Json
    Write-Host '   状态:' $data.status
    Write-Host '   后端连接:' $data.backend
} catch {
    Write-Host '❌ 前端服务器连接失败'
    Write-Host '   错误:' $_.Exception.Message
}
" 2>nul

cd ..

:: 步骤9: 生成测试报告
echo.
echo 📊 步骤9: 生成测试报告
echo ======================

echo.
echo 🎉 完整测试流程执行完成！
echo ==========================
echo.
echo 📍 服务器状态:
echo    后端服务器: http://localhost:5000
echo    前端服务器: http://localhost:3000
echo    健康检查:   http://localhost:5000/api/health
echo.
echo 🧪 测试结果:
if %api_test_result% equ 0 (
    echo    ✅ 后端API测试: 全部通过
) else (
    echo    ⚠️ 后端API测试: 部分失败
)
echo    📊 详细报告: Backend/test-reports/
echo.
echo 👤 测试账户:
echo    管理员:     <EMAIL> / Admin123!
echo    系统管理员: <EMAIL> / SysAdmin123!
echo    内容管理员: <EMAIL> / Moderator123!
echo    编辑主管:   <EMAIL> / Editor123!
echo    用户1:      <EMAIL> / User123!
echo    用户2:      <EMAIL> / User123!
echo    作者:       <EMAIL> / Author123!
echo    读者:       <EMAIL> / Reader123!
echo    未验证用户: <EMAIL> / Unverified123!
echo.
echo 🔧 手动测试步骤:
echo    1. 访问测试页面: http://localhost:3000/test-auth
echo    2. 使用上述账户测试登录功能
echo    3. 测试注册新用户
echo    4. 测试密码找回功能
echo    5. 验证不同角色的权限
echo    6. 测试邮箱验证流程
echo.
echo 📋 功能测试清单:
echo    □ 管理员登录和权限
echo    □ 普通用户登录
echo    □ 用户名和邮箱登录
echo    □ 新用户注册
echo    □ 密码强度验证
echo    □ 忘记密码功能
echo    □ 邮箱验证
echo    □ 错误处理
echo    □ 安全机制
echo    □ 用户界面响应
echo.
echo 🌍 自动打开测试页面...
timeout /t 3 /nobreak >nul
start http://localhost:3000/test-auth

echo.
echo 💡 测试提示:
echo    - 两个服务器窗口将保持运行
echo    - 使用 Ctrl+C 可以停止服务器
echo    - 测试报告保存在 Backend/test-reports/ 目录
echo    - 如遇问题，请查看服务器窗口的错误信息
echo.

if %api_test_result% equ 0 (
    echo ✅ 所有自动化测试通过！可以开始手动测试。
) else (
    echo ⚠️ 部分自动化测试失败，请检查后再进行手动测试。
)

echo.
echo 按任意键退出测试启动器（服务器将继续运行）...
pause >nul
