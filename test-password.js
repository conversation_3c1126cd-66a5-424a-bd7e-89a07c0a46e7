// 测试密码验证
const { User } = require('./Backend/models');
const bcrypt = require('bcrypt');

async function testPassword() {
  try {
    console.log('🔍 Testing password verification...');

    // 查找管理员用户
    const user = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', user.email);
    console.log('👤 Username:', user.username);
    console.log('🔐 Stored password hash:', user.password);
    console.log('🔓 Is active:', user.isActive);
    console.log('🔒 Login attempts:', user.loginAttempts);
    console.log('⏰ Lock until:', user.lockUntil);

    // 测试不同的密码
    const testPasswords = [
      'Admin123!',
      'admin123',
      'Admin123',
      'password123',
      'admin'
    ];

    for (const testPassword of testPasswords) {
      console.log(`\n🧪 Testing password: "${testPassword}"`);
      try {
        const isValid = await bcrypt.compare(testPassword, user.password);
        console.log(`   Result: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
        
        if (isValid) {
          console.log('🎉 CORRECT PASSWORD FOUND!');
          break;
        }
      } catch (error) {
        console.log(`   Error: ${error.message}`);
      }
    }

    // 手动创建新的哈希来测试
    console.log('\n🔧 Creating new hash for Admin123!...');
    const newHash = await bcrypt.hash('Admin123!', 10);
    console.log('🔐 New hash:', newHash);
    
    const testNewHash = await bcrypt.compare('Admin123!', newHash);
    console.log('✅ New hash test:', testNewHash ? 'VALID' : 'INVALID');

    // 更新用户密码为新的哈希
    console.log('\n🔄 Updating user password...');
    await user.update({
      password: newHash,
      loginAttempts: 0,
      lockUntil: null
    });
    console.log('✅ Password updated successfully!');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testPassword();
