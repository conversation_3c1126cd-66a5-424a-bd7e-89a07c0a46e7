{"total": 16, "passed": 2, "failed": 10, "skipped": 4, "tests": [{"name": "后端健康检查", "status": "failed", "message": "服务器连接失败", "details": "", "timestamp": "2025-07-11T10:25:05.092Z"}, {"name": "admin用户登录", "status": "failed", "message": "登录失败", "details": "", "timestamp": "2025-07-11T10:25:05.098Z"}, {"name": "user用户登录", "status": "failed", "message": "登录失败", "details": "", "timestamp": "2025-07-11T10:25:05.099Z"}, {"name": "moderator用户登录", "status": "failed", "message": "登录失败", "details": "", "timestamp": "2025-07-11T10:25:05.104Z"}, {"name": "弱密码拒绝", "status": "failed", "message": "密码验证不符合预期", "details": null, "timestamp": "2025-07-11T10:25:05.106Z"}, {"name": "强密码接受", "status": "failed", "message": "密码验证不符合预期", "details": null, "timestamp": "2025-07-11T10:25:05.108Z"}, {"name": "用户注册", "status": "failed", "message": "用户注册失败", "details": "", "timestamp": "2025-07-11T10:25:05.109Z"}, {"name": "文章管理测试", "status": "skipped", "message": "用户未登录，跳过文章管理测试", "details": null, "timestamp": "2025-07-11T10:25:05.110Z"}, {"name": "用户管理测试", "status": "skipped", "message": "用户未登录，跳过用户管理测试", "details": null, "timestamp": "2025-07-11T10:25:05.110Z"}, {"name": "社交功能测试", "status": "skipped", "message": "用户未登录，跳过社交功能测试", "details": null, "timestamp": "2025-07-11T10:25:05.111Z"}, {"name": "管理员功能测试", "status": "skipped", "message": "管理员未登录，跳过管理员功能测试", "details": null, "timestamp": "2025-07-11T10:25:05.111Z"}, {"name": "API响应时间", "status": "failed", "message": "响应时间过长: 3ms", "details": null, "timestamp": "2025-07-11T10:25:05.115Z"}, {"name": "并发请求处理", "status": "failed", "message": "0/5 个请求成功", "details": null, "timestamp": "2025-07-11T10:25:05.124Z"}, {"name": "未授权访问防护", "status": "failed", "message": "未授权访问防护失败", "details": null, "timestamp": "2025-07-11T10:25:05.127Z"}, {"name": "SQL注入防护", "status": "passed", "message": "SQL注入攻击被正确处理", "details": null, "timestamp": "2025-07-11T10:25:05.130Z"}, {"name": "XSS防护", "status": "passed", "message": "XSS攻击被正确处理", "details": null, "timestamp": "2025-07-11T10:25:05.131Z"}], "startTime": "2025-07-11T10:25:05.060Z", "endTime": "2025-07-11T10:25:05.132Z"}