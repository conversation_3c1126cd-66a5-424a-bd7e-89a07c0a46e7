{"total": 16, "passed": 5, "failed": 7, "skipped": 4, "tests": [{"name": "后端健康检查", "status": "failed", "message": "服务器连接失败", "details": "Request failed with status code 400", "timestamp": "2025-07-11T10:58:00.876Z"}, {"name": "admin用户登录", "status": "failed", "message": "登录失败", "details": "Invalid credentials", "timestamp": "2025-07-11T10:58:01.367Z"}, {"name": "user用户登录", "status": "failed", "message": "登录失败", "details": "Invalid credentials", "timestamp": "2025-07-11T10:58:01.630Z"}, {"name": "moderator用户登录", "status": "failed", "message": "登录失败", "details": "Invalid credentials", "timestamp": "2025-07-11T10:58:01.929Z"}, {"name": "弱密码拒绝", "status": "passed", "message": "密码: 123", "details": null, "timestamp": "2025-07-11T10:58:01.933Z"}, {"name": "强密码接受", "status": "passed", "message": "密码: StrongPassword123!", "details": null, "timestamp": "2025-07-11T10:58:01.935Z"}, {"name": "用户注册", "status": "passed", "message": "新用户 testuser_1752231481935 注册成功", "details": null, "timestamp": "2025-07-11T10:58:02.459Z"}, {"name": "文章管理测试", "status": "skipped", "message": "用户未登录，跳过文章管理测试", "details": null, "timestamp": "2025-07-11T10:58:02.461Z"}, {"name": "用户管理测试", "status": "skipped", "message": "用户未登录，跳过用户管理测试", "details": null, "timestamp": "2025-07-11T10:58:02.461Z"}, {"name": "社交功能测试", "status": "skipped", "message": "用户未登录，跳过社交功能测试", "details": null, "timestamp": "2025-07-11T10:58:02.461Z"}, {"name": "管理员功能测试", "status": "skipped", "message": "管理员未登录，跳过管理员功能测试", "details": null, "timestamp": "2025-07-11T10:58:02.462Z"}, {"name": "API响应时间", "status": "failed", "message": "响应时间过长: 11ms", "details": null, "timestamp": "2025-07-11T10:58:02.474Z"}, {"name": "并发请求处理", "status": "failed", "message": "0/5 个请求成功", "details": null, "timestamp": "2025-07-11T10:58:02.493Z"}, {"name": "未授权访问防护", "status": "failed", "message": "未授权访问防护失败", "details": null, "timestamp": "2025-07-11T10:58:02.496Z"}, {"name": "SQL注入防护", "status": "passed", "message": "SQL注入攻击被正确处理", "details": null, "timestamp": "2025-07-11T10:58:02.499Z"}, {"name": "XSS防护", "status": "passed", "message": "XSS攻击被正确处理", "details": null, "timestamp": "2025-07-11T10:58:02.501Z"}], "startTime": "2025-07-11T10:58:00.786Z", "endTime": "2025-07-11T10:58:02.502Z"}