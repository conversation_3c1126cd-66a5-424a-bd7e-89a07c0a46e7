<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Article Debug Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .result.error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .result.info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.online { background: #d1fae5; color: #065f46; }
        .status.offline { background: #fee2e2; color: #991b1b; }
        .status.loading { background: #fef3c7; color: #92400e; }
        .link-test {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background: #6366f1;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
        }
        .link-test:hover {
            background: #4f46e5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Article Debug Test</h1>
        <p>Comprehensive testing of article functionality and API endpoints</p>

        <div class="test-section">
            <h3>🌐 Backend API Status</h3>
            <button class="test-button" onclick="testBackendHealth()">Test Backend Health</button>
            <button class="test-button" onclick="testArticleAPI()">Test Article API</button>
            <button class="test-button" onclick="testCommentsAPI()">Test Comments API</button>
            <span id="backend-status" class="status loading">Testing...</span>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📄 Frontend Page Tests</h3>
            <p>Test all article-related pages:</p>
            <a href="http://localhost:3000/" class="link-test" target="_blank">Home Page</a>
            <a href="http://localhost:3000/explore" class="link-test" target="_blank">Explore Page</a>
            <a href="http://localhost:3000/article/1" class="link-test" target="_blank">Article #1</a>
            <a href="http://localhost:3000/article/2" class="link-test" target="_blank">Article #2</a>
            <a href="http://localhost:3000/article/3" class="link-test" target="_blank">Article #3</a>
            <span id="frontend-status" class="status loading">Click links to test</span>
        </div>

        <div class="test-section">
            <h3>🔧 Debug Information</h3>
            <button class="test-button" onclick="checkFrontendConsole()">Check Frontend Console</button>
            <button class="test-button" onclick="testImageLoading()">Test Image Loading</button>
            <button class="test-button" onclick="testFullFlow()">Test Complete Flow</button>
            <div id="debug-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="summary-result" class="result info">
                <strong>Test Summary:</strong>
                • Backend API: <span id="api-status">Not tested</span>
                • Article Loading: <span id="article-status">Not tested</span>
                • Comments Loading: <span id="comments-status">Not tested</span>
                • Image Loading: <span id="image-status">Not tested</span>
                • Frontend Pages: <span id="pages-status">Not tested</span>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            api: false,
            article: false,
            comments: false,
            images: false,
            pages: false
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function updateStatus(elementId, text, className) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status ${className}`;
        }

        function updateSummary() {
            document.getElementById('api-status').textContent = testResults.api ? '✅ Working' : '❌ Failed';
            document.getElementById('article-status').textContent = testResults.article ? '✅ Working' : '❌ Failed';
            document.getElementById('comments-status').textContent = testResults.comments ? '✅ Working' : '❌ Failed';
            document.getElementById('image-status').textContent = testResults.images ? '✅ Working' : '❌ Failed';
            document.getElementById('pages-status').textContent = testResults.pages ? '✅ Working' : '❌ Failed';
        }

        async function testBackendHealth() {
            try {
                updateStatus('backend-status', 'Testing...', 'loading');
                const response = await fetch('http://localhost:5000/api/articles');
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('api-result', `✅ Backend is healthy!\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                    updateStatus('backend-status', 'Online', 'online');
                    testResults.api = true;
                } else {
                    showResult('api-result', `❌ Backend error: ${response.status} ${response.statusText}`, 'error');
                    updateStatus('backend-status', 'Error', 'offline');
                }
            } catch (error) {
                showResult('api-result', `❌ Network error: ${error.message}`, 'error');
                updateStatus('backend-status', 'Offline', 'offline');
            }
            updateSummary();
        }

        async function testArticleAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/articles/1');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', `✅ Article API Working!\nArticle: ${data.title}\nAuthor: ${typeof data.author === 'object' ? data.author.name : data.author}\nContent Length: ${data.content.length} chars`, 'success');
                    testResults.article = true;
                } else {
                    showResult('api-result', `❌ Article API Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ Network Error: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testCommentsAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/comments/article/1');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', `✅ Comments API Working!\nFound ${data.length} comments\nFirst comment: ${data[0]?.content || 'No comments'}`, 'success');
                    testResults.comments = true;
                } else {
                    showResult('api-result', `❌ Comments API Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ Network Error: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testImageLoading() {
            const testImages = [
                'https://ui-avatars.com/api/?name=Test+User&background=6366f1&color=fff&size=64',
                'https://picsum.photos/400/250?random=1',
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop'
            ];

            let results = [];
            
            for (const imageUrl of testImages) {
                try {
                    const img = new Image();
                    const loadPromise = new Promise((resolve, reject) => {
                        img.onload = () => resolve(`✅ ${imageUrl}`);
                        img.onerror = () => reject(`❌ ${imageUrl}`);
                        setTimeout(() => reject(`⏱️ Timeout: ${imageUrl}`), 5000);
                    });
                    img.src = imageUrl;
                    const result = await loadPromise;
                    results.push(result);
                } catch (error) {
                    results.push(error);
                }
            }

            showResult('debug-result', `Image Loading Test Results:\n${results.join('\n')}`, 'info');
            testResults.images = results.filter(r => r.includes('✅')).length > 0;
            updateSummary();
        }

        function checkFrontendConsole() {
            const instructions = `
🔍 Frontend Console Check Instructions:

1. Open browser developer tools (F12)
2. Go to Console tab
3. Navigate to http://localhost:3000/article/1
4. Look for any error messages

Common issues to check:
• Network errors (failed API calls)
• JavaScript errors (component issues)
• Image loading errors (Next.js Image config)
• CORS errors (cross-origin requests)

If you see errors, copy them and we can fix them!
            `;
            showResult('debug-result', instructions, 'info');
        }

        async function testFullFlow() {
            showResult('debug-result', '🔄 Running complete test flow...', 'info');
            
            await testBackendHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testArticleAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCommentsAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testImageLoading();
            
            const allPassed = Object.values(testResults).every(result => result);
            const passedCount = Object.values(testResults).filter(result => result).length;
            
            showResult('debug-result', 
                `🎯 Complete Test Results:\n` +
                `✅ Passed: ${passedCount}/5 tests\n` +
                `${allPassed ? '🎉 All tests passed!' : '⚠️ Some tests failed - check individual results'}`, 
                allPassed ? 'success' : 'error'
            );
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('🔍 Article Debug Test loaded');
            setTimeout(testBackendHealth, 1000);
        });
    </script>
</body>
</html>
