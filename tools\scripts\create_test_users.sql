-- Newzora 测试用户创建脚本
-- 密码都是对应的明文密码经过bcrypt加密后的结果

-- 管理员账户
INSERT INTO users (username, email, password, role, "isEmailVerified", "createdAt", "updatedAt") 
VALUES ('admin', '<EMAIL>', '$2a$12$lNjz.GqeZNaLztfTo6Qew./fkiezgpo4JVYe.XOmuwDXEsQoeKBve', 'admin', true, NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 编辑账户
INSERT INTO users (username, email, password, role, "isEmailVerified", "createdAt", "updatedAt") 
VALUES ('editor', '<EMAIL>', '$2a$12$lNjz.GqeZNaLztfTo6Qew./fkiezgpo4JVYe.XOmuwDXEsQoeKBve', 'moderator', true, NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 普通用户账户
INSERT INTO users (username, email, password, role, "isEmailVerified", "createdAt", "updatedAt") 
VALUES ('john_doe', '<EMAIL>', '$2a$12$lNjz.GqeZNaLztfTo6Qew./fkiezgpo4JVYe.XOmuwDXEsQoeKBve', 'user', true, NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

INSERT INTO users (username, email, password, role, "isEmailVerified", "createdAt", "updatedAt") 
VALUES ('jane_smith', '<EMAIL>', '$2a$12$lNjz.GqeZNaLztfTo6Qew./fkiezgpo4JVYe.XOmuwDXEsQoeKBve', 'user', true, NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

INSERT INTO users (username, email, password, role, "isEmailVerified", "createdAt", "updatedAt") 
VALUES ('demo_user', '<EMAIL>', '$2a$12$lNjz.GqeZNaLztfTo6Qew./fkiezgpo4JVYe.XOmuwDXEsQoeKBve', 'user', true, NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 查看创建的用户
SELECT id, username, email, role, "isEmailVerified" FROM users ORDER BY id;
