<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Newzora 认证状态调试工具</h1>
    
    <div class="section info">
        <h3>📋 当前状态</h3>
        <div id="currentStatus">检查中...</div>
    </div>

    <div class="section">
        <h3>🔐 测试登录</h3>
        <input type="email" id="email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="password" placeholder="密码" value="Demo123456!">
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult"></div>
    </div>

    <div class="section">
        <h3>🏠 测试主页认证状态</h3>
        <button onclick="testHomePage()">检查主页</button>
        <div id="homePageResult"></div>
    </div>

    <div class="section">
        <h3>✍️ 测试创建页面</h3>
        <button onclick="testCreatePage()">检查创建页面</button>
        <div id="createPageResult"></div>
    </div>

    <div class="section">
        <h3>🧹 清理工具</h3>
        <button onclick="clearStorage()">清除本地存储</button>
        <button onclick="checkStorage()">检查本地存储</button>
        <div id="storageResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        function updateStatus() {
            const token = localStorage.getItem('auth_token');
            const user = localStorage.getItem('auth_user');
            
            let status = '<strong>本地存储状态:</strong><br>';
            status += `Token: ${token ? '✅ 存在 (' + token.substring(0, 20) + '...)' : '❌ 不存在'}<br>`;
            status += `User: ${user ? '✅ 存在' : '❌ 不存在'}<br>`;
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    status += `<pre>${JSON.stringify(userData, null, 2)}</pre>`;
                } catch (e) {
                    status += '❌ 用户数据格式错误';
                }
            }
            
            document.getElementById('currentStatus').innerHTML = status;
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '🔄 登录中...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success && data.token) {
                    localStorage.setItem('auth_token', data.token);
                    if (data.user) {
                        localStorage.setItem('auth_user', JSON.stringify(data.user));
                    }
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 登录成功!<br>
                            Token: ${data.token.substring(0, 20)}...<br>
                            User: ${data.user?.email || 'N/A'}
                        </div>
                    `;
                    updateStatus();
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 登录失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testHomePage() {
            const resultDiv = document.getElementById('homePageResult');
            resultDiv.innerHTML = '🔄 检查中...';
            
            try {
                const response = await fetch('http://localhost:3000/', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const html = await response.text();
                const hasSignIn = html.includes('Sign In') || html.includes('sign-in');
                const hasUserMenu = html.includes('user-menu') || html.includes('avatar');
                
                resultDiv.innerHTML = `
                    <div class="${hasSignIn ? 'error' : 'success'}">
                        主页状态:<br>
                        包含登录按钮: ${hasSignIn ? '❌ 是' : '✅ 否'}<br>
                        包含用户菜单: ${hasUserMenu ? '✅ 是' : '❌ 否'}<br>
                        响应状态: ${response.status}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 无法访问主页: ${error.message}</div>`;
            }
        }

        async function testCreatePage() {
            const resultDiv = document.getElementById('createPageResult');
            resultDiv.innerHTML = '🔄 检查中...';
            
            try {
                const response = await fetch('http://localhost:3000/create', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (response.status === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 创建页面可访问<br>
                            状态码: ${response.status}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 创建页面访问失败<br>
                            状态码: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 无法访问创建页面: ${error.message}</div>`;
            }
        }

        function clearStorage() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
            document.getElementById('storageResult').innerHTML = '<div class="success">✅ 本地存储已清除</div>';
            updateStatus();
        }

        function checkStorage() {
            const items = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                items.push(`${key}: ${value?.substring(0, 50)}${value?.length > 50 ? '...' : ''}`);
            }
            
            document.getElementById('storageResult').innerHTML = `
                <div class="info">
                    <strong>本地存储内容:</strong><br>
                    <pre>${items.join('\n')}</pre>
                </div>
            `;
        }

        // 初始化
        updateStatus();
        
        // 每5秒更新一次状态
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
