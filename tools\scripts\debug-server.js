#!/usr/bin/env node

/**
 * 调试服务器启动问题
 */

console.log('🔍 Starting server debug...');

// 设置错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

try {
  console.log('📦 Loading environment variables...');
  require('dotenv').config();
  console.log('✅ Environment loaded');
  console.log('🔧 NODE_ENV:', process.env.NODE_ENV);
  console.log('🔧 USE_REAL_SUPABASE:', process.env.USE_REAL_SUPABASE);
  
  console.log('📦 Loading Express...');
  const express = require('express');
  console.log('✅ Express loaded');
  
  console.log('📦 Loading CORS...');
  const cors = require('cors');
  console.log('✅ CORS loaded');
  
  console.log('📦 Loading security middleware...');
  const { applySecurityMiddleware } = require('./middleware/security');
  console.log('✅ Security middleware loaded');
  
  console.log('📦 Loading auth service...');
  const AuthService = require('./services/authService');
  console.log('✅ Auth service loaded');
  
  console.log('📦 Loading database config...');
  const { sequelize, testConnection } = require('./config/database');
  console.log('✅ Database config loaded');
  
  console.log('📦 Loading Supabase config...');
  const { testSupabaseConnection } = require('./config/supabase');
  console.log('✅ Supabase config loaded');
  
  console.log('📦 Loading routes...');
  const authRoutes = require('./routes/auth');
  console.log('✅ Auth routes loaded');
  
  const articlesRoutes = require('./routes/articles');
  console.log('✅ Articles routes loaded');
  
  console.log('🚀 Creating Express app...');
  const app = express();
  
  console.log('🔧 Applying middleware...');
  app.use(cors());
  app.use(express.json());
  
  console.log('🔧 Applying security middleware...');
  applySecurityMiddleware(app);
  
  console.log('🔧 Setting up routes...');
  app.use('/api/auth', authRoutes);
  app.use('/api/articles', articlesRoutes);
  
  app.get('/debug', (req, res) => {
    res.json({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      env: process.env.NODE_ENV 
    });
  });
  
  console.log('🚀 Starting server...');
  const PORT = process.env.PORT || 5000;
  
  const server = app.listen(PORT, () => {
    console.log(`✅ Debug server running on port ${PORT}`);
    console.log(`🌐 Debug endpoint: http://localhost:${PORT}/debug`);
    
    // 测试基本连接
    setTimeout(async () => {
      try {
        console.log('🔍 Testing connections...');
        
        // 测试数据库连接
        await testConnection();
        console.log('✅ Database connection OK');
        
        // 测试Supabase连接
        await testSupabaseConnection();
        console.log('✅ Supabase connection OK');
        
        console.log('🎉 All systems operational!');
        
      } catch (error) {
        console.error('❌ Connection test failed:', error);
      }
    }, 2000);
  });
  
  server.on('error', (error) => {
    console.error('❌ Server error:', error);
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use`);
    }
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Debug failed:', error);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
