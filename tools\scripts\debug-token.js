#!/usr/bin/env node

/**
 * 调试Token问题
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const API_BASE = 'http://localhost:5000/api';

async function debugToken() {
  try {
    console.log('🔍 调试Token问题...\n');

    // 加载环境变量
    require('dotenv').config({ path: './Backend/.env' });
    console.log('环境变量加载状态:');
    console.log('JWT_SECRET:', process.env.JWT_SECRET ? `已设置 (${process.env.JWT_SECRET.substring(0, 10)}...)` : '未设置');
    console.log('USE_REAL_SUPABASE:', process.env.USE_REAL_SUPABASE);
    console.log();

    // 1. 登录获取token
    console.log('1. 登录获取token...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'demo123456'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 登录失败:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    console.log('Token (前20字符):', token.substring(0, 20) + '...');
    console.log('Token长度:', token.length);

    // 2. 解码token查看内容
    console.log('\n2. 解码token内容...');
    try {
      const decoded = jwt.decode(token, { complete: true });
      console.log('Token Header:', JSON.stringify(decoded.header, null, 2));
      console.log('Token Payload:', JSON.stringify(decoded.payload, null, 2));
    } catch (decodeError) {
      console.error('❌ Token解码失败:', decodeError.message);
    }

    // 3. 验证token
    console.log('\n3. 验证token...');
    try {
      const jwtSecret = process.env.JWT_SECRET || 'mock-jwt-secret';
      console.log('使用的JWT_SECRET:', jwtSecret ? '已设置' : '未设置');
      
      const verified = jwt.verify(token, jwtSecret);
      console.log('✅ Token验证成功');
      console.log('验证后的payload:', JSON.stringify(verified, null, 2));
    } catch (verifyError) {
      console.error('❌ Token验证失败:', verifyError.message);
    }

    // 4. 测试API调用
    console.log('\n4. 测试API调用...');
    
    // 测试 /auth/me
    try {
      const meResponse = await axios.get(`${API_BASE}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ /auth/me 成功:', meResponse.status);
      console.log('用户信息:', JSON.stringify(meResponse.data.user, null, 2));
    } catch (meError) {
      console.error('❌ /auth/me 失败:', meError.response?.status, meError.response?.data?.message);
    }

    // 测试 /users/profile
    try {
      const profileResponse = await axios.get(`${API_BASE}/users/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ /users/profile 成功:', profileResponse.status);
      console.log('用户资料:', JSON.stringify(profileResponse.data.user, null, 2));
    } catch (profileError) {
      console.error('❌ /users/profile 失败:', profileError.response?.status, profileError.response?.data?.message);
    }

    // 测试文章创建
    try {
      const createResponse = await axios.post(`${API_BASE}/articles`, {
        title: '测试文章',
        content: '这是一篇测试文章的内容',
        category: 'Technology'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ 文章创建成功:', createResponse.status);
      console.log('创建的文章:', JSON.stringify(createResponse.data, null, 2));
    } catch (createError) {
      console.error('❌ 文章创建失败:', createError.response?.status, createError.response?.data?.message);
    }

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
}

debugToken();
