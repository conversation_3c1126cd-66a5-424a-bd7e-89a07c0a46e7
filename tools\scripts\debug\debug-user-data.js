const User = require('../../Backend/models/User');

async function debugUserData() {
  try {
    console.log('Checking user data...');
    
    // 查询所有用户
    const allUsers = await User.findAll();
    console.log('All users:');
    allUsers.forEach(user => {
      console.log(`ID: ${user.id}, Username: "${user.username}", Email: "${user.email}"`);
      console.log(`Username type: ${typeof user.username}, Email type: ${typeof user.email}`);
    });
    
    // 测试不同的查询方式
    console.log('\n--- Testing different queries ---');
    
    // 1. 直接查询username = 'admin'
    const test1 = await User.findOne({ where: { username: 'admin' } });
    console.log('Query username = "admin":', test1 ? test1.username : 'null');
    
    // 2. 查询email = 'admin'
    const test2 = await User.findOne({ where: { email: 'admin' } });
    console.log('Query email = "admin":', test2 ? test2.username : 'null');
    
    // 3. 查询email = '<EMAIL>'
    const test3 = await User.findOne({ where: { email: '<EMAIL>' } });
    console.log('Query email = "<EMAIL>":', test3 ? test3.username : 'null');
    
    // 4. 测试OR查询
    const { Op } = require('sequelize');
    const test4 = await User.findOne({
      where: {
        [Op.or]: [
          { email: 'admin' },
          { username: 'admin' }
        ]
      }
    });
    console.log('OR query (email="admin" OR username="admin"):', test4 ? test4.username : 'null');
    
    // 5. 测试带toLowerCase的OR查询
    const test5 = await User.findOne({
      where: {
        [Op.or]: [
          { email: 'admin'.toLowerCase() },
          { username: 'admin' }
        ]
      }
    });
    console.log('OR query with toLowerCase:', test5 ? test5.username : 'null');
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

debugUserData();
