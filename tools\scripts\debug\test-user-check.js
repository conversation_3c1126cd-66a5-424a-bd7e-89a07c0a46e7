const User = require('../../Backend/models/User');

async function checkUser() {
  try {
    console.log('Checking for admin user...');
    
    // 直接查询所有用户
    const allUsers = await User.findAll();
    console.log('All users count:', allUsers.length);
    
    if (allUsers.length > 0) {
      allUsers.forEach(user => {
        console.log(`User: ${user.username}, Email: ${user.email}, ID: ${user.id}`);
      });
    }
    
    // 查找admin用户
    const adminUser = await User.findByEmailOrUsername('admin');
    console.log('Admin user found:', adminUser ? adminUser.username : 'null');
    
    // 直接用Sequelize查询
    const directQuery = await User.findOne({
      where: {
        username: 'admin'
      }
    });
    console.log('Direct query result:', directQuery ? directQuery.username : 'null');
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkUser();
