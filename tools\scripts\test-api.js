// Test script to create articles via API
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const API_BASE = 'http://localhost:5000/api';

async function createTestArticle() {
  try {
    const articleData = {
      title: "The Future of Artificial Intelligence: Transforming Our World",
      description: "Explore how AI is revolutionizing industries and shaping the future of human civilization.",
      content: `
        <p>Artificial Intelligence (AI) has emerged as one of the most transformative technologies of our time. From healthcare to transportation, from education to entertainment, AI is reshaping every aspect of our lives.</p>
        
        <h2>The Current State of AI</h2>
        <p>Today's AI systems can process vast amounts of data, recognize patterns, and make decisions with unprecedented speed and accuracy. Machine learning algorithms are powering everything from recommendation systems to autonomous vehicles.</p>
        
        <h2>Impact on Industries</h2>
        <p>Healthcare professionals are using AI to diagnose diseases earlier and more accurately. Financial institutions leverage AI for fraud detection and risk assessment. Manufacturing companies employ AI for predictive maintenance and quality control.</p>
        
        <h2>Challenges and Opportunities</h2>
        <p>While AI presents incredible opportunities, it also raises important questions about privacy, employment, and ethical considerations. As we advance, it's crucial to develop AI responsibly and ensure its benefits are accessible to all.</p>
        
        <h2>Looking Ahead</h2>
        <p>The future of AI holds immense promise. As technology continues to evolve, we can expect even more sophisticated applications that will further enhance human capabilities and solve complex global challenges.</p>
      `,
      category: "Technology",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop",
      author: "Dr. Sarah Chen",
      readTime: 8,
      tags: ["AI", "Technology", "Future", "Innovation"],
      featured: true,
      published: true
    };

    console.log('Creating test article...');
    const response = await fetch(`${API_BASE}/articles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(articleData)
    });

    if (response.ok) {
      const article = await response.json();
      console.log('Article created successfully:', article.id);
      
      // Create test comments
      const comments = [
        {
          articleId: article.id,
          author: "Alex Johnson",
          content: "This is an excellent overview of AI's current state and future potential. The section on healthcare applications particularly resonated with me as someone working in medical technology."
        },
        {
          articleId: article.id,
          author: "Maria Rodriguez", 
          content: "Great article! I appreciate how you balanced the opportunities with the challenges. The ethical considerations around AI development are indeed crucial."
        },
        {
          articleId: article.id,
          author: "David Kim",
          content: "As an AI researcher, I found this piece to be both accessible and informative. It's a great introduction for those new to the field while still offering valuable insights for professionals."
        }
      ];

      console.log('Creating test comments...');
      for (const commentData of comments) {
        const commentResponse = await fetch(`${API_BASE}/comments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(commentData)
        });

        if (commentResponse.ok) {
          const comment = await commentResponse.json();
          console.log('Comment created:', comment.id);
        } else {
          console.error('Failed to create comment:', await commentResponse.text());
        }
      }

      console.log('Test data created successfully!');
      console.log(`Visit: http://localhost:3000/article/${article.id}`);
      
    } else {
      console.error('Failed to create article:', response.status, await response.text());
    }

  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Test fetching an article
async function testFetchArticle(id = 1) {
  try {
    console.log(`\nTesting fetch article ${id}...`);
    const response = await fetch(`${API_BASE}/articles/${id}`);
    
    if (response.ok) {
      const article = await response.json();
      console.log('Article fetched successfully:');
      console.log('- Title:', article.title);
      console.log('- Author:', article.author);
      console.log('- Views:', article.views);
      console.log('- Likes:', article.likes);
    } else {
      console.log('Article not found or error:', response.status);
    }
  } catch (error) {
    console.error('Error fetching article:', error.message);
  }
}

// Test fetching comments
async function testFetchComments(articleId = 1) {
  try {
    console.log(`\nTesting fetch comments for article ${articleId}...`);
    const response = await fetch(`${API_BASE}/comments/article/${articleId}`);
    
    if (response.ok) {
      const comments = await response.json();
      console.log(`Found ${comments.length} comments:`);
      comments.forEach((comment, index) => {
        console.log(`${index + 1}. ${comment.author}: ${comment.content.substring(0, 50)}...`);
      });
    } else {
      console.log('Comments not found or error:', response.status);
    }
  } catch (error) {
    console.error('Error fetching comments:', error.message);
  }
}

// Run tests
async function runTests() {
  await createTestArticle();
  await testFetchArticle(1);
  await testFetchComments(1);
}

runTests();
