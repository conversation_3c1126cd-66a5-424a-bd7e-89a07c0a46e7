// Test script to verify the complete article flow
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const API_BASE = 'http://localhost:5000/api';

async function testCompleteArticleFlow() {
  console.log('🧪 Testing Complete Article Flow\n');

  try {
    // 1. Test fetching articles list
    console.log('1️⃣ Testing articles list...');
    const articlesResponse = await fetch(`${API_BASE}/articles`);
    if (articlesResponse.ok) {
      const articles = await articlesResponse.json();
      console.log(`✅ Found ${articles.data.length} articles`);
      
      if (articles.data.length > 0) {
        const firstArticle = articles.data[0];
        console.log(`   First article: "${firstArticle.title}" (ID: ${firstArticle.id})`);
        
        // 2. Test fetching specific article
        console.log('\n2️⃣ Testing article details...');
        const articleResponse = await fetch(`${API_BASE}/articles/${firstArticle.id}`);
        if (articleResponse.ok) {
          const article = await articleResponse.json();
          console.log(`✅ Article details loaded successfully`);
          console.log(`   Title: ${article.title}`);
          console.log(`   Author: ${article.author}`);
          console.log(`   Views: ${article.views}`);
          console.log(`   Likes: ${article.likes}`);
          
          // 3. Test liking the article
          console.log('\n3️⃣ Testing article like functionality...');
          const likeResponse = await fetch(`${API_BASE}/articles/${firstArticle.id}/like`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });
          if (likeResponse.ok) {
            const likeData = await likeResponse.json();
            console.log(`✅ Article liked successfully! New likes: ${likeData.likes}`);
          } else {
            console.log('❌ Failed to like article');
          }
          
          // 4. Test fetching comments
          console.log('\n4️⃣ Testing comments...');
          const commentsResponse = await fetch(`${API_BASE}/comments/article/${firstArticle.id}`);
          if (commentsResponse.ok) {
            const comments = await commentsResponse.json();
            console.log(`✅ Found ${comments.length} comments`);
            comments.forEach((comment, index) => {
              console.log(`   ${index + 1}. ${comment.author}: ${comment.content.substring(0, 50)}...`);
            });
            
            // 5. Test adding a comment
            console.log('\n5️⃣ Testing comment creation...');
            const newCommentData = {
              articleId: firstArticle.id,
              author: "Test User",
              content: "This is a test comment to verify the functionality works correctly!"
            };
            
            const commentResponse = await fetch(`${API_BASE}/comments`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(newCommentData)
            });
            
            if (commentResponse.ok) {
              const newComment = await commentResponse.json();
              console.log(`✅ Comment created successfully! ID: ${newComment.id}`);
              
              // 6. Test liking the comment
              console.log('\n6️⃣ Testing comment like functionality...');
              const commentLikeResponse = await fetch(`${API_BASE}/comments/${newComment.id}/like`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              
              if (commentLikeResponse.ok) {
                const commentLikeData = await commentLikeResponse.json();
                console.log(`✅ Comment liked successfully! New likes: ${commentLikeData.likes}`);
              } else {
                console.log('❌ Failed to like comment');
              }
            } else {
              console.log('❌ Failed to create comment');
            }
          } else {
            console.log('❌ Failed to fetch comments');
          }
        } else {
          console.log('❌ Failed to fetch article details');
        }
      } else {
        console.log('⚠️ No articles found in database');
      }
    } else {
      console.log('❌ Failed to fetch articles list');
    }

    console.log('\n🎉 Article flow test completed!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Articles list retrieval');
    console.log('   ✅ Article details retrieval');
    console.log('   ✅ Article like functionality');
    console.log('   ✅ Comments retrieval');
    console.log('   ✅ Comment creation');
    console.log('   ✅ Comment like functionality');
    
    console.log('\n🌐 Frontend URLs to test:');
    console.log('   📄 Homepage: http://localhost:3000');
    console.log('   📰 Article Detail: http://localhost:3000/article/1');
    console.log('   📰 Latest Article: http://localhost:3000/article/3');

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  }
}

// Additional test for creating more sample content
async function createMoreSampleContent() {
  console.log('\n📝 Creating additional sample content...');
  
  const sampleArticles = [
    {
      title: "The Art of Minimalist Design: Less is More",
      description: "Discover how minimalist design principles can create powerful and elegant user experiences.",
      content: `
        <p>Minimalist design has become a dominant force in modern digital design, emphasizing simplicity, clarity, and functionality. This approach strips away unnecessary elements to focus on what truly matters.</p>
        
        <h2>Core Principles</h2>
        <p>The foundation of minimalist design rests on several key principles: simplicity, negative space, limited color palette, and purposeful typography. Each element serves a specific function.</p>
        
        <h2>Benefits</h2>
        <p>Minimalist design improves user experience by reducing cognitive load, increasing loading speeds, and creating timeless aesthetics that don't quickly become outdated.</p>
        
        <h2>Implementation</h2>
        <p>Start by removing unnecessary elements, use plenty of white space, choose a limited color scheme, and ensure every design element has a clear purpose.</p>
      `,
      category: "Lifestyle",
      image: "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=800&h=400&fit=crop",
      author: "Jessica Wong",
      readTime: 5,
      tags: ["Design", "Minimalism", "UX", "UI"],
      featured: false,
      published: true
    },
    {
      title: "Exploring Global Cuisines: A Culinary Journey",
      description: "Take a delicious journey around the world through traditional dishes and cooking techniques.",
      content: `
        <p>Food is one of the most powerful ways to experience different cultures. Each cuisine tells a story of history, geography, and tradition passed down through generations.</p>
        
        <h2>Asian Flavors</h2>
        <p>From the umami-rich dishes of Japan to the spicy curries of Thailand, Asian cuisine offers incredible diversity in flavors, techniques, and ingredients.</p>
        
        <h2>Mediterranean Delights</h2>
        <p>The Mediterranean diet, rich in olive oil, fresh vegetables, and seafood, represents not just healthy eating but a lifestyle centered around community and enjoyment.</p>
        
        <h2>Cooking at Home</h2>
        <p>Start your culinary journey by experimenting with basic techniques and authentic ingredients. Don't be afraid to make mistakes – they're part of the learning process.</p>
      `,
      category: "Food",
      image: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=400&fit=crop",
      author: "Marco Rossi",
      readTime: 7,
      tags: ["Food", "Culture", "Cooking", "Travel"],
      featured: true,
      published: true
    }
  ];

  for (const articleData of sampleArticles) {
    try {
      const response = await fetch(`${API_BASE}/articles`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(articleData)
      });

      if (response.ok) {
        const article = await response.json();
        console.log(`✅ Created article: "${article.title}" (ID: ${article.id})`);
      } else {
        console.log(`❌ Failed to create article: "${articleData.title}"`);
      }
    } catch (error) {
      console.log(`❌ Error creating article: ${error.message}`);
    }
  }
}

// Run all tests
async function runAllTests() {
  await createMoreSampleContent();
  await testCompleteArticleFlow();
}

runAllTests();
