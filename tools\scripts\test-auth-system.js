#!/usr/bin/env node

/**
 * Newzora 登录注册系统测试脚本
 * 用于验证认证系统的完整性和功能
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Newzora 登录注册系统检查开始...\n');

// 检查后端文件
function checkBackendFiles() {
  console.log('📁 检查后端文件结构...');
  
  const backendFiles = [
    'Backend/server.js',
    'Backend/routes/users.js',
    'Backend/models/User.js',
    'Backend/config/database.js',
    'Backend/config/passport.js',
    'Backend/middleware/auth.js',
    'Backend/.env'
  ];

  let allFilesExist = true;
  
  backendFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

// 检查前端文件
function checkFrontendFiles() {
  console.log('\n📁 检查前端文件结构...');
  
  const frontendFiles = [
    'Frontend/src/app/login/page.tsx',
    'Frontend/src/app/register/page.tsx',
    'Frontend/src/contexts/AuthContext.tsx',
    'Frontend/src/components/Header.tsx'
  ];

  let allFilesExist = true;
  
  frontendFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

// 检查环境配置
function checkEnvironmentConfig() {
  console.log('\n🔧 检查环境配置...');
  
  try {
    const envPath = 'Backend/.env';
    if (!fs.existsSync(envPath)) {
      console.log('  ❌ .env 文件不存在');
      return false;
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
      'DB_NAME',
      'DB_HOST',
      'DB_USER',
      'DB_PASSWORD',
      'JWT_SECRET',
      'EMAIL_HOST',
      'EMAIL_FROM'
    ];

    let allVarsPresent = true;
    
    requiredVars.forEach(varName => {
      if (envContent.includes(`${varName}=`)) {
        console.log(`  ✅ ${varName}`);
      } else {
        console.log(`  ❌ ${varName} - 环境变量缺失`);
        allVarsPresent = false;
      }
    });

    // 检查数据库名称是否为 newzora
    if (envContent.includes('DB_NAME=newzora')) {
      console.log('  ✅ 数据库名称已更新为 newzora');
    } else {
      console.log('  ⚠️  数据库名称需要更新为 newzora');
    }

    // 检查邮件品牌名称
    if (envContent.includes('Newzora')) {
      console.log('  ✅ 邮件品牌名称已更新为 Newzora');
    } else {
      console.log('  ⚠️  邮件品牌名称需要更新为 Newzora');
    }

    return allVarsPresent;
  } catch (error) {
    console.log(`  ❌ 读取环境配置失败: ${error.message}`);
    return false;
  }
}

// 检查Docker配置
function checkDockerConfig() {
  console.log('\n🐳 检查Docker配置...');
  
  try {
    const dockerComposePath = 'config/docker-compose.yml';
    if (!fs.existsSync(dockerComposePath)) {
      console.log('  ❌ docker-compose.yml 文件不存在');
      return false;
    }

    const dockerContent = fs.readFileSync(dockerComposePath, 'utf8');
    
    // 检查容器名称是否已更新
    const containerNames = [
      'newzora-postgres',
      'newzora-backend', 
      'newzora-frontend',
      'newzora-nginx'
    ];

    let allNamesUpdated = true;
    
    containerNames.forEach(name => {
      if (dockerContent.includes(name)) {
        console.log(`  ✅ ${name}`);
      } else {
        console.log(`  ❌ ${name} - 容器名称未更新`);
        allNamesUpdated = false;
      }
    });

    // 检查网络名称
    if (dockerContent.includes('newzora-network')) {
      console.log('  ✅ newzora-network');
    } else {
      console.log('  ❌ newzora-network - 网络名称未更新');
      allNamesUpdated = false;
    }

    return allNamesUpdated;
  } catch (error) {
    console.log(`  ❌ 读取Docker配置失败: ${error.message}`);
    return false;
  }
}

// 检查API路由
function checkAPIRoutes() {
  console.log('\n📡 检查API路由配置...');
  
  try {
    const usersRoutePath = 'Backend/routes/users.js';
    if (!fs.existsSync(usersRoutePath)) {
      console.log('  ❌ users.js 路由文件不存在');
      return false;
    }

    const routeContent = fs.readFileSync(usersRoutePath, 'utf8');
    
    const requiredRoutes = [
      "router.post('/register'",
      "router.post('/login'",
      "router.post('/forgot-password'",
      "router.post('/reset-password'",
      "router.get('/profile'",
      "router.put('/profile'"
    ];

    let allRoutesPresent = true;
    
    requiredRoutes.forEach(route => {
      if (routeContent.includes(route)) {
        console.log(`  ✅ ${route.replace("router.", "").replace("'", "")}`);
      } else {
        console.log(`  ❌ ${route} - 路由缺失`);
        allRoutesPresent = false;
      }
    });

    return allRoutesPresent;
  } catch (error) {
    console.log(`  ❌ 读取路由配置失败: ${error.message}`);
    return false;
  }
}

// 检查前端页面
function checkFrontendPages() {
  console.log('\n🎨 检查前端页面配置...');
  
  try {
    // 检查登录页面
    const loginPagePath = 'Frontend/src/app/login/page.tsx';
    if (fs.existsSync(loginPagePath)) {
      const loginContent = fs.readFileSync(loginPagePath, 'utf8');
      if (loginContent.includes('useAuth') && loginContent.includes('validateEmail')) {
        console.log('  ✅ 登录页面 - 包含认证逻辑和验证');
      } else {
        console.log('  ⚠️  登录页面 - 缺少部分功能');
      }
    } else {
      console.log('  ❌ 登录页面不存在');
      return false;
    }

    // 检查注册页面
    const registerPagePath = 'Frontend/src/app/register/page.tsx';
    if (fs.existsSync(registerPagePath)) {
      const registerContent = fs.readFileSync(registerPagePath, 'utf8');
      if (registerContent.includes('useAuth') && registerContent.includes('validatePassword')) {
        console.log('  ✅ 注册页面 - 包含认证逻辑和验证');
      } else {
        console.log('  ⚠️  注册页面 - 缺少部分功能');
      }
    } else {
      console.log('  ❌ 注册页面不存在');
      return false;
    }

    // 检查AuthContext
    const authContextPath = 'Frontend/src/contexts/AuthContext.tsx';
    if (fs.existsSync(authContextPath)) {
      const authContent = fs.readFileSync(authContextPath, 'utf8');
      if (authContent.includes('login') && authContent.includes('register') && authContent.includes('logout')) {
        console.log('  ✅ AuthContext - 包含完整认证方法');
      } else {
        console.log('  ⚠️  AuthContext - 缺少部分认证方法');
      }
    } else {
      console.log('  ❌ AuthContext不存在');
      return false;
    }

    return true;
  } catch (error) {
    console.log(`  ❌ 检查前端页面失败: ${error.message}`);
    return false;
  }
}

// 生成测试报告
function generateReport(results) {
  console.log('\n📊 测试结果汇总:');
  console.log('================================');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(result => result).length;
  const failedTests = totalTests - passedTests;
  
  console.log(`总测试项: ${totalTests}`);
  console.log(`通过: ${passedTests} ✅`);
  console.log(`失败: ${failedTests} ❌`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  console.log('\n详细结果:');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`  ${passed ? '✅' : '❌'} ${test}`);
  });

  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！登录注册系统完整且配置正确。');
    console.log('💡 系统已准备好进行开发和测试。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述问题。');
    console.log('💡 建议先解决配置问题再进行功能测试。');
  }
}

// 主测试函数
function runTests() {
  const results = {
    '后端文件结构': checkBackendFiles(),
    '前端文件结构': checkFrontendFiles(),
    '环境配置': checkEnvironmentConfig(),
    'Docker配置': checkDockerConfig(),
    'API路由': checkAPIRoutes(),
    '前端页面': checkFrontendPages()
  };

  generateReport(results);
  
  return results;
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
