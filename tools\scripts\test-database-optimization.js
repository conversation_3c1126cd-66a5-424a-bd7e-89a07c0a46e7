/**
 * OneNews 数据库优化测试脚本
 * 测试数据库配置、连接池和性能优化
 */

const { sequelize, testConnection, healthCheck, getPerformanceStats } = require('../Backend/config/database');
const fs = require('fs').promises;
const path = require('path');

// 测试配置
const testConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  testTimeout: 30000
};

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) { colorLog(`✅ ${message}`, 'green'); }
function error(message) { colorLog(`❌ ${message}`, 'red'); }
function warning(message) { colorLog(`⚠️  ${message}`, 'yellow'); }
function info(message) { colorLog(`ℹ️  ${message}`, 'blue'); }
function debug(message) { colorLog(`🔍 ${message}`, 'cyan'); }

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// 记录测试结果
function recordTest(testName, passed, message = '', details = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    success(`${testName}: ${message || 'PASSED'}`);
  } else {
    testResults.failed++;
    error(`${testName}: ${message || 'FAILED'}`);
  }
  
  testResults.details.push({
    test: testName,
    passed,
    message,
    details,
    timestamp: new Date().toISOString()
  });
}

// 测试 1: 数据库连接测试
async function testDatabaseConnection() {
  info('测试 1: 数据库连接测试');
  
  try {
    const result = await testConnection();
    recordTest('数据库连接', result, result ? '连接成功' : '连接失败');
    return result;
  } catch (error) {
    recordTest('数据库连接', false, `连接异常: ${error.message}`);
    return false;
  }
}

// 测试 2: 健康检查测试
async function testHealthCheck() {
  info('测试 2: 数据库健康检查');
  
  try {
    const health = await healthCheck();
    const isHealthy = health.status === 'healthy';
    
    recordTest('健康检查', isHealthy, 
      isHealthy ? `响应时间: ${health.responseTime}ms` : `状态: ${health.status}`,
      health
    );
    
    if (isHealthy) {
      debug(`数据库版本: ${health.version}`);
      debug(`连接池状态 - 总计: ${health.pool.total}, 使用中: ${health.pool.used}, 等待: ${health.pool.waiting}`);
    }
    
    return isHealthy;
  } catch (error) {
    recordTest('健康检查', false, `健康检查异常: ${error.message}`);
    return false;
  }
}

// 测试 3: 连接池性能测试
async function testConnectionPool() {
  info('测试 3: 连接池性能测试');
  
  try {
    const startTime = Date.now();
    const promises = [];
    
    // 创建多个并发查询来测试连接池
    for (let i = 0; i < 10; i++) {
      promises.push(
        sequelize.query('SELECT NOW() as current_time, ? as query_id', {
          replacements: [i],
          type: sequelize.QueryTypes.SELECT
        })
      );
    }
    
    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;
    const avgTime = totalTime / results.length;
    
    const success = results.length === 10 && avgTime < 1000;
    recordTest('连接池性能', success, 
      `10个并发查询完成，总时间: ${totalTime}ms, 平均: ${avgTime.toFixed(2)}ms`,
      { totalTime, avgTime, queryCount: results.length }
    );
    
    return success;
  } catch (error) {
    recordTest('连接池性能', false, `连接池测试异常: ${error.message}`);
    return false;
  }
}

// 测试 4: 数据库性能统计
async function testPerformanceStats() {
  info('测试 4: 数据库性能统计');
  
  try {
    const stats = await getPerformanceStats();
    const hasStats = stats && stats.connections && stats.database_size;
    
    recordTest('性能统计', hasStats, 
      hasStats ? `数据库大小: ${stats.database_size}, 连接数: ${stats.connections.total_connections}` : '统计信息获取失败',
      stats
    );
    
    if (hasStats) {
      debug(`活跃连接: ${stats.connections.active_connections}`);
      debug(`空闲连接: ${stats.connections.idle_connections}`);
      debug(`前10个表: ${stats.top_tables.length} 个表`);
    }
    
    return hasStats;
  } catch (error) {
    recordTest('性能统计', false, `性能统计异常: ${error.message}`);
    return false;
  }
}

// 测试 5: 索引使用情况测试
async function testIndexUsage() {
  info('测试 5: 索引使用情况测试');
  
  try {
    const query = `
      SELECT
        schemaname,
        relname as tablename,
        indexrelname as indexname,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes
      WHERE schemaname = 'public'
      ORDER BY idx_scan DESC
      LIMIT 10;
    `;
    
    const indexes = await sequelize.query(query, { type: sequelize.QueryTypes.SELECT });
    const hasIndexes = indexes && indexes.length > 0;
    
    recordTest('索引使用情况', hasIndexes, 
      hasIndexes ? `找到 ${indexes.length} 个索引` : '未找到索引信息',
      indexes
    );
    
    if (hasIndexes) {
      debug(`最活跃的索引: ${indexes[0].indexname} (扫描次数: ${indexes[0].idx_scan})`);
    }
    
    return hasIndexes;
  } catch (error) {
    recordTest('索引使用情况', false, `索引测试异常: ${error.message}`);
    return false;
  }
}

// 测试 6: 慢查询检测测试
async function testSlowQueryDetection() {
  info('测试 6: 慢查询检测测试');
  
  try {
    // 尝试查询 pg_stat_statements (如果启用)
    const query = `
      SELECT 
        query,
        calls,
        total_time,
        mean_time
      FROM pg_stat_statements 
      WHERE mean_time > 100
      ORDER BY mean_time DESC 
      LIMIT 5;
    `;
    
    const slowQueries = await sequelize.query(query, { type: sequelize.QueryTypes.SELECT });
    
    recordTest('慢查询检测', true, 
      `慢查询检测功能正常，找到 ${slowQueries.length} 个慢查询`,
      slowQueries
    );
    
    if (slowQueries.length > 0) {
      warning(`发现 ${slowQueries.length} 个慢查询，建议优化`);
    }
    
    return true;
  } catch (error) {
    // pg_stat_statements 可能未启用，这是正常的
    recordTest('慢查询检测', true, 
      'pg_stat_statements 未启用 (这是正常的)',
      { note: 'pg_stat_statements extension not enabled' }
    );
    return true;
  }
}

// 测试 7: 数据库配置验证
async function testDatabaseConfiguration() {
  info('测试 7: 数据库配置验证');
  
  try {
    const configQueries = [
      "SHOW max_connections;",
      "SHOW shared_buffers;",
      "SHOW work_mem;",
      "SHOW maintenance_work_mem;",
      "SHOW effective_cache_size;"
    ];
    
    const configs = {};
    for (const query of configQueries) {
      try {
        const result = await sequelize.query(query, { type: sequelize.QueryTypes.SELECT });
        const key = query.replace('SHOW ', '').replace(';', '');
        configs[key] = result[0][key];
      } catch (err) {
        debug(`配置查询失败: ${query} - ${err.message}`);
      }
    }
    
    const hasConfigs = Object.keys(configs).length > 0;
    recordTest('数据库配置', hasConfigs, 
      hasConfigs ? `获取到 ${Object.keys(configs).length} 个配置项` : '配置获取失败',
      configs
    );
    
    if (hasConfigs) {
      debug(`最大连接数: ${configs.max_connections || 'N/A'}`);
      debug(`共享缓冲区: ${configs.shared_buffers || 'N/A'}`);
      debug(`工作内存: ${configs.work_mem || 'N/A'}`);
    }
    
    return hasConfigs;
  } catch (error) {
    recordTest('数据库配置', false, `配置验证异常: ${error.message}`);
    return false;
  }
}

// 生成测试报告
async function generateTestReport() {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      success_rate: ((testResults.passed / testResults.total) * 100).toFixed(2)
    },
    tests: testResults.details,
    environment: {
      node_version: process.version,
      platform: process.platform,
      database_host: process.env.DB_HOST || 'localhost',
      database_name: process.env.DB_NAME || 'onenews'
    }
  };
  
  // 保存报告到文件
  const reportDir = './logs';
  const reportFile = path.join(reportDir, `database-optimization-test-${new Date().toISOString().split('T')[0]}.json`);
  
  try {
    await fs.mkdir(reportDir, { recursive: true });
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
    info(`测试报告已保存: ${reportFile}`);
  } catch (error) {
    warning(`保存测试报告失败: ${error.message}`);
  }
  
  return report;
}

// 主测试函数
async function runAllTests() {
  colorLog('\n🚀 OneNews 数据库优化测试开始', 'blue');
  colorLog('=====================================', 'blue');
  
  const startTime = Date.now();
  
  try {
    // 运行所有测试
    await testDatabaseConnection();
    await testHealthCheck();
    await testConnectionPool();
    await testPerformanceStats();
    await testIndexUsage();
    await testSlowQueryDetection();
    await testDatabaseConfiguration();
    
    const totalTime = Date.now() - startTime;
    
    // 生成报告
    const report = await generateTestReport();
    
    // 输出测试结果
    colorLog('\n📊 测试结果汇总', 'blue');
    colorLog('================', 'blue');
    colorLog(`总测试数: ${report.summary.total}`, 'cyan');
    colorLog(`通过: ${report.summary.passed}`, 'green');
    colorLog(`失败: ${report.summary.failed}`, 'red');
    colorLog(`成功率: ${report.summary.success_rate}%`, 'cyan');
    colorLog(`总耗时: ${totalTime}ms`, 'cyan');
    
    if (report.summary.failed === 0) {
      success('\n🎉 所有测试通过！数据库优化配置正常');
    } else {
      warning(`\n⚠️  有 ${report.summary.failed} 个测试失败，请检查配置`);
    }
    
    return report.summary.failed === 0;
    
  } catch (error) {
    error(`测试执行失败: ${error.message}`);
    return false;
  } finally {
    // 关闭数据库连接
    try {
      await sequelize.close();
      debug('数据库连接已关闭');
    } catch (error) {
      warning(`关闭数据库连接失败: ${error.message}`);
    }
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  warning('\n收到中断信号，正在关闭...');
  try {
    await sequelize.close();
  } catch (error) {
    // 忽略关闭错误
  }
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      error(`测试运行失败: ${error.message}`);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testDatabaseConnection,
  testHealthCheck,
  testConnectionPool,
  testPerformanceStats
};
