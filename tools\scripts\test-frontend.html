<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>🧪 Newzora 前端功能测试</h1>
    
    <div class="section info">
        <h3>📋 测试说明</h3>
        <p>这个页面将测试前端的各种功能，包括页面加载、认证状态、API调用等。</p>
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🧹 清除结果</button>
    </div>

    <div class="section">
        <h3>🌐 页面加载测试</h3>
        <button onclick="testPageLoading()">测试页面加载</button>
        <div id="pageLoadingResults"></div>
    </div>

    <div class="section">
        <h3>🔐 认证功能测试</h3>
        <button onclick="testAuthentication()">测试认证功能</button>
        <div id="authResults"></div>
    </div>

    <div class="section">
        <h3>✍️ 创建功能测试</h3>
        <button onclick="testCreateFunction()">测试创建功能</button>
        <div id="createResults"></div>
    </div>

    <div class="section">
        <h3>📊 测试结果汇总</h3>
        <div id="summaryResults"></div>
    </div>

    <script>
        let testResults = [];

        function addTestResult(testName, passed, message) {
            testResults.push({ name: testName, passed, message });
            console.log(`${passed ? '✅' : '❌'} ${testName}: ${message}`);
        }

        function displayResult(containerId, testName, passed, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>${passed ? '✅' : '❌'} ${testName}</strong><br>
                ${message}
            `;
            container.appendChild(resultDiv);
        }

        async function testPageLoading() {
            const container = document.getElementById('pageLoadingResults');
            container.innerHTML = '<p>🔄 测试页面加载...</p>';

            const pages = [
                { name: '主页', url: 'http://localhost:3000/' },
                { name: '登录页', url: 'http://localhost:3000/login' },
                { name: '注册页', url: 'http://localhost:3000/register' },
                { name: '创建页', url: 'http://localhost:3000/create' },
                { name: '探索页', url: 'http://localhost:3000/explore' }
            ];

            container.innerHTML = '';

            for (const page of pages) {
                try {
                    const response = await fetch(page.url, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    
                    // 由于CORS限制，我们无法获取真实状态，但如果没有抛出错误，说明页面可访问
                    addTestResult(`页面加载 - ${page.name}`, true, '页面可访问');
                    displayResult('pageLoadingResults', `页面加载 - ${page.name}`, true, '页面可访问');
                } catch (error) {
                    addTestResult(`页面加载 - ${page.name}`, false, `无法访问: ${error.message}`);
                    displayResult('pageLoadingResults', `页面加载 - ${page.name}`, false, `无法访问: ${error.message}`);
                }
            }
        }

        async function testAuthentication() {
            const container = document.getElementById('authResults');
            container.innerHTML = '<p>🔄 测试认证功能...</p>';

            try {
                // 测试登录API
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123456'
                    })
                });

                container.innerHTML = '';

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.success && loginData.token) {
                        addTestResult('API登录', true, '登录成功，获得token');
                        displayResult('authResults', 'API登录', true, '登录成功，获得token');

                        // 测试token验证
                        const meResponse = await fetch('http://localhost:5000/api/auth/me', {
                            headers: {
                                'Authorization': `Bearer ${loginData.token}`
                            }
                        });

                        if (meResponse.ok) {
                            const userData = await meResponse.json();
                            addTestResult('Token验证', true, `用户信息获取成功: ${userData.user?.email}`);
                            displayResult('authResults', 'Token验证', true, `用户信息获取成功: ${userData.user?.email}`);
                        } else {
                            addTestResult('Token验证', false, 'Token验证失败');
                            displayResult('authResults', 'Token验证', false, 'Token验证失败');
                        }
                    } else {
                        addTestResult('API登录', false, loginData.message || '登录失败');
                        displayResult('authResults', 'API登录', false, loginData.message || '登录失败');
                    }
                } else {
                    addTestResult('API登录', false, `HTTP ${loginResponse.status}`);
                    displayResult('authResults', 'API登录', false, `HTTP ${loginResponse.status}`);
                }

                // 测试本地存储
                const hasToken = localStorage.getItem('auth_token');
                const hasUser = localStorage.getItem('auth_user');
                
                addTestResult('本地存储检查', hasToken && hasUser, 
                    `Token: ${hasToken ? '存在' : '不存在'}, User: ${hasUser ? '存在' : '不存在'}`);
                displayResult('authResults', '本地存储检查', hasToken && hasUser, 
                    `Token: ${hasToken ? '存在' : '不存在'}, User: ${hasUser ? '存在' : '不存在'}`);

            } catch (error) {
                container.innerHTML = '';
                addTestResult('认证测试', false, `网络错误: ${error.message}`);
                displayResult('authResults', '认证测试', false, `网络错误: ${error.message}`);
            }
        }

        async function testCreateFunction() {
            const container = document.getElementById('createResults');
            container.innerHTML = '<p>🔄 测试创建功能...</p>';

            try {
                // 首先获取token
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123456'
                    })
                });

                container.innerHTML = '';

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.success && loginData.token) {
                        // 测试文章创建
                        const createResponse = await fetch('http://localhost:5000/api/articles', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${loginData.token}`
                            },
                            body: JSON.stringify({
                                title: '前端测试文章',
                                content: '这是一篇通过前端测试创建的文章',
                                category: 'Technology'
                            })
                        });

                        if (createResponse.ok) {
                            const createData = await createResponse.json();
                            addTestResult('文章创建', true, `文章创建成功: ${createData.article?.title}`);
                            displayResult('createResults', '文章创建', true, `文章创建成功: ${createData.article?.title}`);
                        } else {
                            const errorData = await createResponse.json();
                            addTestResult('文章创建', false, `创建失败: ${errorData.message}`);
                            displayResult('createResults', '文章创建', false, `创建失败: ${errorData.message}`);
                        }
                    } else {
                        addTestResult('文章创建', false, '无法获取认证token');
                        displayResult('createResults', '文章创建', false, '无法获取认证token');
                    }
                } else {
                    addTestResult('文章创建', false, '登录失败，无法测试创建功能');
                    displayResult('createResults', '文章创建', false, '登录失败，无法测试创建功能');
                }

            } catch (error) {
                container.innerHTML = '';
                addTestResult('创建功能测试', false, `网络错误: ${error.message}`);
                displayResult('createResults', '创建功能测试', false, `网络错误: ${error.message}`);
            }
        }

        function updateSummary() {
            const container = document.getElementById('summaryResults');
            const total = testResults.length;
            const passed = testResults.filter(r => r.passed).length;
            const failed = total - passed;
            const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;

            container.innerHTML = `
                <div class="info">
                    <h4>📊 测试统计</h4>
                    <p><strong>总测试数:</strong> ${total}</p>
                    <p><strong>通过:</strong> <span style="color: green;">${passed}</span></p>
                    <p><strong>失败:</strong> <span style="color: red;">${failed}</span></p>
                    <p><strong>成功率:</strong> ${successRate}%</p>
                </div>
            `;
        }

        async function runAllTests() {
            testResults = [];
            clearResults();
            
            console.log('🚀 开始运行所有测试...');
            
            await testPageLoading();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCreateFunction();
            
            updateSummary();
            console.log('✅ 所有测试完成');
        }

        function clearResults() {
            document.getElementById('pageLoadingResults').innerHTML = '';
            document.getElementById('authResults').innerHTML = '';
            document.getElementById('createResults').innerHTML = '';
            document.getElementById('summaryResults').innerHTML = '';
            testResults = [];
        }

        // 页面加载时显示当前状态
        window.onload = function() {
            const hasToken = localStorage.getItem('auth_token');
            const hasUser = localStorage.getItem('auth_user');
            
            if (hasToken && hasUser) {
                document.querySelector('.section.info').innerHTML += `
                    <div style="margin-top: 10px; padding: 10px; background-color: #d4edda; border-radius: 5px;">
                        ✅ 检测到已登录状态
                    </div>
                `;
            } else {
                document.querySelector('.section.info').innerHTML += `
                    <div style="margin-top: 10px; padding: 10px; background-color: #fff3cd; border-radius: 5px;">
                        ⚠️ 未检测到登录状态
                    </div>
                `;
            }
        };
    </script>
</body>
</html>
