/**
 * OneNews 监控和日志系统测试脚本
 * 测试日志记录、监控端点、错误处理等功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const BASE_URL = process.env.API_URL || 'http://localhost:5000';
const TEST_RESULTS = [];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logTest = (testName, passed, details = '') => {
  const status = passed ? '✅ PASSED' : '❌ FAILED';
  const color = passed ? 'green' : 'red';
  log(`${status} ${testName}`, color);
  if (details) {
    log(`   ${details}`, 'blue');
  }
  
  TEST_RESULTS.push({
    test: testName,
    passed,
    details,
    timestamp: new Date().toISOString()
  });
};

/**
 * 测试基础健康检查端点
 */
async function testHealthCheck() {
  try {
    const response = await axios.get(`${BASE_URL}/api/monitoring/health`);
    
    const isHealthy = response.status === 200 && response.data.status === 'healthy';
    const hasRequiredFields = response.data.timestamp && 
                             response.data.uptime !== undefined &&
                             response.data.database;
    
    logTest(
      'Health Check Endpoint',
      isHealthy && hasRequiredFields,
      `Status: ${response.data.status}, Uptime: ${response.data.uptime}s`
    );
    
    return isHealthy && hasRequiredFields;
    
  } catch (error) {
    logTest('Health Check Endpoint', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 测试日志文件创建
 */
async function testLogFileCreation() {
  const logDir = path.join(__dirname, '../Backend/logs');
  const expectedLogFiles = [
    'combined.log',
    'error.log',
    'warn.log',
    'access.log',
    'database.log',
    'security.log',
    'performance.log'
  ];
  
  let allFilesExist = true;
  const missingFiles = [];
  
  for (const logFile of expectedLogFiles) {
    const filePath = path.join(logDir, logFile);
    if (!fs.existsSync(filePath)) {
      allFilesExist = false;
      missingFiles.push(logFile);
    }
  }
  
  logTest(
    'Log Files Creation',
    allFilesExist,
    allFilesExist ? 'All log files created' : `Missing: ${missingFiles.join(', ')}`
  );
  
  return allFilesExist;
}

/**
 * 测试API请求日志记录
 */
async function testRequestLogging() {
  try {
    // 发送一个测试请求
    const testEndpoint = `${BASE_URL}/api/monitoring/health`;
    await axios.get(testEndpoint);
    
    // 等待日志写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 检查访问日志
    const accessLogPath = path.join(__dirname, '../Backend/logs/access.log');
    if (fs.existsSync(accessLogPath)) {
      const logContent = fs.readFileSync(accessLogPath, 'utf8');
      const hasRecentLog = logContent.includes('/api/monitoring/health');
      
      logTest(
        'Request Logging',
        hasRecentLog,
        hasRecentLog ? 'Request logged successfully' : 'No recent request found in logs'
      );
      
      return hasRecentLog;
    } else {
      logTest('Request Logging', false, 'Access log file not found');
      return false;
    }
    
  } catch (error) {
    logTest('Request Logging', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 测试错误日志记录
 */
async function testErrorLogging() {
  try {
    // 发送一个会产生错误的请求
    try {
      await axios.get(`${BASE_URL}/api/nonexistent-endpoint`);
    } catch (error) {
      // 预期的404错误
    }
    
    // 等待日志写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 检查错误日志或综合日志
    const combinedLogPath = path.join(__dirname, '../Backend/logs/combined.log');
    if (fs.existsSync(combinedLogPath)) {
      const logContent = fs.readFileSync(combinedLogPath, 'utf8');
      const hasErrorLog = logContent.includes('404') || logContent.includes('nonexistent-endpoint');
      
      logTest(
        'Error Logging',
        hasErrorLog,
        hasErrorLog ? 'Error logged successfully' : 'No error found in logs'
      );
      
      return hasErrorLog;
    } else {
      logTest('Error Logging', false, 'Combined log file not found');
      return false;
    }
    
  } catch (error) {
    logTest('Error Logging', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 测试性能监控
 */
async function testPerformanceMonitoring() {
  try {
    const startTime = Date.now();
    
    // 发送多个请求来生成性能数据
    const requests = [];
    for (let i = 0; i < 5; i++) {
      requests.push(axios.get(`${BASE_URL}/api/monitoring/health`));
    }
    
    await Promise.all(requests);
    const totalTime = Date.now() - startTime;
    
    // 等待性能日志写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 检查性能日志
    const perfLogPath = path.join(__dirname, '../Backend/logs/performance.log');
    if (fs.existsSync(perfLogPath)) {
      const logContent = fs.readFileSync(perfLogPath, 'utf8');
      const hasPerformanceData = logContent.includes('request_duration') || 
                                logContent.includes('system_health');
      
      logTest(
        'Performance Monitoring',
        hasPerformanceData,
        `Performance data logged (${totalTime}ms for 5 requests)`
      );
      
      return hasPerformanceData;
    } else {
      logTest('Performance Monitoring', false, 'Performance log file not found');
      return false;
    }
    
  } catch (error) {
    logTest('Performance Monitoring', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 测试日志轮转和清理
 */
async function testLogRotation() {
  try {
    const logDir = path.join(__dirname, '../Backend/logs');
    const logFiles = fs.readdirSync(logDir);
    
    // 检查是否有日志文件
    const hasLogFiles = logFiles.length > 0;
    
    // 检查日志文件大小（应该有合理的大小限制）
    let totalSize = 0;
    for (const file of logFiles) {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    }
    
    const totalSizeMB = totalSize / (1024 * 1024);
    const reasonableSize = totalSizeMB < 100; // 小于100MB认为合理
    
    logTest(
      'Log Rotation',
      hasLogFiles && reasonableSize,
      `${logFiles.length} log files, total size: ${totalSizeMB.toFixed(2)}MB`
    );
    
    return hasLogFiles && reasonableSize;
    
  } catch (error) {
    logTest('Log Rotation', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 测试系统指标收集
 */
async function testSystemMetrics() {
  try {
    // 等待系统健康监控运行
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const perfLogPath = path.join(__dirname, '../Backend/logs/performance.log');
    if (fs.existsSync(perfLogPath)) {
      const logContent = fs.readFileSync(perfLogPath, 'utf8');
      const hasSystemHealth = logContent.includes('system_health');
      
      logTest(
        'System Metrics Collection',
        hasSystemHealth,
        hasSystemHealth ? 'System health metrics collected' : 'No system health data found'
      );
      
      return hasSystemHealth;
    } else {
      logTest('System Metrics Collection', false, 'Performance log file not found');
      return false;
    }
    
  } catch (error) {
    logTest('System Metrics Collection', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  const totalTests = TEST_RESULTS.length;
  const passedTests = TEST_RESULTS.filter(result => result.passed).length;
  const failedTests = totalTests - passedTests;
  const successRate = ((passedTests / totalTests) * 100).toFixed(2);
  
  log('\n' + '='.repeat(60), 'blue');
  log('📊 监控和日志系统测试报告', 'blue');
  log('='.repeat(60), 'blue');
  
  log(`总测试数: ${totalTests}`, 'blue');
  log(`通过: ${passedTests}`, 'green');
  log(`失败: ${failedTests}`, failedTests > 0 ? 'red' : 'blue');
  log(`成功率: ${successRate}%`, successRate === '100.00' ? 'green' : 'yellow');
  
  if (failedTests > 0) {
    log('\n❌ 失败的测试:', 'red');
    TEST_RESULTS.filter(result => !result.passed).forEach(result => {
      log(`  - ${result.test}: ${result.details}`, 'red');
    });
  }
  
  // 保存测试报告
  const reportPath = path.join(__dirname, '../Backend/logs/monitoring-test-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: parseFloat(successRate)
    },
    results: TEST_RESULTS
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📄 详细报告已保存: ${reportPath}`, 'blue');
  
  return successRate === '100.00';
}

/**
 * 主测试函数
 */
async function runTests() {
  log('🚀 Newzora Monitoring and Logging System Test Started', 'blue');
  log('=' .repeat(50), 'blue');
  
  // 等待服务器启动
  log('⏳ 等待服务器启动...', 'yellow');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 运行所有测试
  await testHealthCheck();
  await testLogFileCreation();
  await testRequestLogging();
  await testErrorLogging();
  await testPerformanceMonitoring();
  await testLogRotation();
  await testSystemMetrics();
  
  // 生成报告
  const allTestsPassed = generateTestReport();
  
  if (allTestsPassed) {
    log('\n🎉 All tests passed! Monitoring and logging system working normally', 'green');
    process.exit(0);
  } else {
    log('\n⚠️  Some tests failed, please check system configuration', 'yellow');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log(`❌ Test execution failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testHealthCheck,
  testLogFileCreation,
  testRequestLogging,
  testErrorLogging,
  testPerformanceMonitoring,
  testLogRotation,
  testSystemMetrics
};
