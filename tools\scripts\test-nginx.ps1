# OneNews Nginx 配置测试脚本 (PowerShell)
# 验证 Nginx 配置文件的正确性

param(
    [switch]$Detailed = $false
)

# 颜色函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Cyan"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️  $Message" "Yellow" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️  $Message" "Blue" }

Write-ColorOutput "OneNews Nginx 配置测试" "Blue"
Write-Host ""

$script:TestResults = @{
    Passed = 0
    Failed = 0
    Warnings = 0
}

# 检查配置文件完整性
function Test-ConfigFiles {
    Write-Info "检查配置文件完整性..."
    
    $requiredFiles = @(
        "nginx/nginx.conf",
        "nginx/conf.d/default.conf", 
        "nginx/conf.d/ssl.conf",
        "nginx/ssl/README.md"
    )
    
    $missingFiles = @()
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Success "$file 存在"
            $script:TestResults.Passed++
        } else {
            Write-Error "$file 不存在"
            $missingFiles += $file
            $script:TestResults.Failed++
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "缺少配置文件: $($missingFiles -join ', ')"
        return $false
    }
    
    return $true
}

# 检查配置文件语法
function Test-ConfigSyntax {
    Write-Info "测试配置文件语法..."
    
    try {
        # 检查是否有Docker
        $dockerInstalled = Get-Command docker -ErrorAction SilentlyContinue
        
        if ($dockerInstalled) {
            Write-Info "使用 Docker 容器测试配置..."
            
            $currentPath = (Get-Location).Path
            $nginxConfPath = Join-Path $currentPath "nginx\nginx.conf"
            $confDPath = Join-Path $currentPath "nginx\conf.d"
            
            # 转换Windows路径为Docker路径格式
            $nginxConfPath = $nginxConfPath.Replace('\', '/').Replace('C:', '/c')
            $confDPath = $confDPath.Replace('\', '/').Replace('C:', '/c')
            
            $dockerCmd = "docker run --rm -v `"$($nginxConfPath):/etc/nginx/nginx.conf:ro`" -v `"$($confDPath):/etc/nginx/conf.d:ro`" nginx:alpine nginx -t"
            
            $result = Invoke-Expression $dockerCmd 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Nginx 配置语法正确"
                $script:TestResults.Passed++
                return $true
            } else {
                Write-Error "Nginx 配置语法错误"
                Write-Host $result
                $script:TestResults.Failed++
                return $false
            }
        } else {
            Write-Warning "Docker 未安装，跳过语法测试"
            $script:TestResults.Warnings++
            return $true
        }
    } catch {
        Write-Error "配置语法测试失败: $($_.Exception.Message)"
        $script:TestResults.Failed++
        return $false
    }
}

# 检查上游服务器配置
function Test-UpstreamConfig {
    Write-Info "检查上游服务器配置..."
    
    $nginxConf = Get-Content "nginx/nginx.conf" -Raw
    
    if ($nginxConf -match "upstream backend") {
        Write-Success "后端上游服务器配置正确"
        $script:TestResults.Passed++
    } else {
        Write-Error "缺少后端上游服务器配置"
        $script:TestResults.Failed++
    }
    
    if ($nginxConf -match "upstream frontend") {
        Write-Success "前端上游服务器配置正确"
        $script:TestResults.Passed++
    } else {
        Write-Error "缺少前端上游服务器配置"
        $script:TestResults.Failed++
    }
}

# 检查安全配置
function Test-SecurityConfig {
    Write-Info "检查安全配置..."
    
    $defaultConf = Get-Content "nginx/conf.d/default.conf" -Raw
    $sslConf = Get-Content "nginx/conf.d/ssl.conf" -Raw
    
    $securityHeaders = @(
        "X-Frame-Options",
        "X-XSS-Protection", 
        "X-Content-Type-Options",
        "Referrer-Policy",
        "Content-Security-Policy"
    )
    
    foreach ($header in $securityHeaders) {
        if ($defaultConf -match $header) {
            Write-Success "$header 已配置"
            $script:TestResults.Passed++
        } else {
            Write-Warning "$header 未配置"
            $script:TestResults.Warnings++
        }
    }
    
    # 检查 SSL 配置
    if ($sslConf -match "ssl_protocols") {
        Write-Success "SSL 协议配置正确"
        $script:TestResults.Passed++
    } else {
        Write-Warning "SSL 协议未配置"
        $script:TestResults.Warnings++
    }
    
    if ($sslConf -match "Strict-Transport-Security") {
        Write-Success "HSTS 已配置"
        $script:TestResults.Passed++
    } else {
        Write-Warning "HSTS 未配置"
        $script:TestResults.Warnings++
    }
}

# 检查性能优化配置
function Test-PerformanceConfig {
    Write-Info "检查性能优化配置..."
    
    $nginxConf = Get-Content "nginx/nginx.conf" -Raw
    $defaultConf = Get-Content "nginx/conf.d/default.conf" -Raw
    
    if ($nginxConf -match "gzip on") {
        Write-Success "Gzip 压缩已启用"
        $script:TestResults.Passed++
    } else {
        Write-Warning "Gzip 压缩未启用"
        $script:TestResults.Warnings++
    }
    
    if ($nginxConf -match "keepalive") {
        Write-Success "Keep-alive 已配置"
        $script:TestResults.Passed++
    } else {
        Write-Warning "Keep-alive 未配置"
        $script:TestResults.Warnings++
    }
    
    if ($defaultConf -match "expires") {
        Write-Success "缓存策略已配置"
        $script:TestResults.Passed++
    } else {
        Write-Warning "缓存策略未配置"
        $script:TestResults.Warnings++
    }
}

# 检查负载均衡配置
function Test-LoadBalancing {
    Write-Info "检查负载均衡配置..."
    
    $nginxConf = Get-Content "nginx/nginx.conf" -Raw
    
    if ($nginxConf -match "least_conn") {
        Write-Success "负载均衡算法已配置"
        $script:TestResults.Passed++
    } else {
        Write-Warning "负载均衡算法未配置"
        $script:TestResults.Warnings++
    }
    
    if ($nginxConf -match "max_fails") {
        Write-Success "健康检查已配置"
        $script:TestResults.Passed++
    } else {
        Write-Warning "健康检查未配置"
        $script:TestResults.Warnings++
    }
}

# 生成配置报告
function New-ConfigReport {
    Write-Info "生成配置报告..."
    
    $reportFile = "nginx-config-report.txt"
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $report = @"
OneNews Nginx 配置报告
生成时间: $timestamp
========================

配置文件检查:
$(Get-ChildItem nginx -Recurse | Format-Table -AutoSize | Out-String)

主配置文件内容:
------------------------
$(Get-Content nginx/nginx.conf -Raw)

默认站点配置:
------------------------
$(Get-Content nginx/conf.d/default.conf -Raw)

SSL 配置:
------------------------
$(Get-Content nginx/conf.d/ssl.conf -Raw)
"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Success "配置报告已生成: $reportFile"
}

# 主函数
function Main {
    Write-Info "开始 Nginx 配置测试..."
    Write-Host ""
    
    $allPassed = $true
    
    # 运行所有检查
    if (-not (Test-ConfigFiles)) { $allPassed = $false }
    if (-not (Test-ConfigSyntax)) { $allPassed = $false }
    Test-UpstreamConfig
    Test-SecurityConfig  
    Test-PerformanceConfig
    Test-LoadBalancing
    
    # 生成报告
    if ($Detailed) {
        New-ConfigReport
    }
    
    # 输出结果
    Write-Host ""
    Write-ColorOutput "========================" "Blue"
    Write-ColorOutput "测试结果统计:" "Blue"
    Write-Success "通过: $($script:TestResults.Passed)"
    Write-Error "失败: $($script:TestResults.Failed)" 
    Write-Warning "警告: $($script:TestResults.Warnings)"
    
    $total = $script:TestResults.Passed + $script:TestResults.Failed + $script:TestResults.Warnings
    if ($total -gt 0) {
        $successRate = [math]::Round(($script:TestResults.Passed / $total) * 100, 1)
        Write-Info "成功率: $successRate%"
    }
    
    Write-Host ""
    if ($script:TestResults.Failed -eq 0) {
        Write-Success "🎉 Nginx 配置测试通过！"
        Write-Success "您的 Nginx 配置已准备好用于生产环境。"
        return 0
    } else {
        Write-Error "⚠️  部分 Nginx 配置测试失败。"
        Write-Warning "请检查上述错误并修复配置。"
        return 1
    }
}

# 运行主函数
$exitCode = Main
exit $exitCode
