#!/bin/bash

# OneNews Nginx 配置测试脚本
# 验证 Nginx 配置文件的正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}OneNews Nginx 配置测试${NC}\n"

# 检查 Nginx 是否安装
check_nginx() {
    if command -v nginx &> /dev/null; then
        echo -e "${GREEN}✅ Nginx 已安装${NC}"
        nginx -v
    else
        echo -e "${YELLOW}⚠️  Nginx 未安装，将使用 Docker 容器测试${NC}"
        return 1
    fi
}

# 测试配置文件语法
test_config_syntax() {
    echo -e "\n${BLUE}🔍 测试配置文件语法...${NC}"
    
    if check_nginx; then
        # 使用本地 Nginx 测试
        if nginx -t -c "$(pwd)/nginx/nginx.conf"; then
            echo -e "${GREEN}✅ Nginx 配置语法正确${NC}"
        else
            echo -e "${RED}❌ Nginx 配置语法错误${NC}"
            return 1
        fi
    else
        # 使用 Docker 容器测试
        echo -e "${YELLOW}使用 Docker 容器测试配置...${NC}"
        
        docker run --rm \
            -v "$(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro" \
            -v "$(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro" \
            nginx:alpine nginx -t
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Nginx 配置语法正确${NC}"
        else
            echo -e "${RED}❌ Nginx 配置语法错误${NC}"
            return 1
        fi
    fi
}

# 检查配置文件完整性
check_config_files() {
    echo -e "\n${BLUE}📋 检查配置文件完整性...${NC}"
    
    local files=(
        "nginx/nginx.conf"
        "nginx/conf.d/default.conf"
        "nginx/conf.d/ssl.conf"
        "nginx/ssl/README.md"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ $file 存在${NC}"
        else
            echo -e "${RED}❌ $file 不存在${NC}"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}缺少配置文件: ${missing_files[*]}${NC}"
        return 1
    fi
}

# 检查上游服务器配置
check_upstream_config() {
    echo -e "\n${BLUE}🔗 检查上游服务器配置...${NC}"
    
    if grep -q "upstream backend" nginx/nginx.conf; then
        echo -e "${GREEN}✅ 后端上游服务器配置正确${NC}"
    else
        echo -e "${RED}❌ 缺少后端上游服务器配置${NC}"
        return 1
    fi
    
    if grep -q "upstream frontend" nginx/nginx.conf; then
        echo -e "${GREEN}✅ 前端上游服务器配置正确${NC}"
    else
        echo -e "${RED}❌ 缺少前端上游服务器配置${NC}"
        return 1
    fi
}

# 检查安全配置
check_security_config() {
    echo -e "\n${BLUE}🔒 检查安全配置...${NC}"
    
    local security_headers=(
        "X-Frame-Options"
        "X-XSS-Protection"
        "X-Content-Type-Options"
        "Referrer-Policy"
        "Content-Security-Policy"
    )
    
    for header in "${security_headers[@]}"; do
        if grep -q "$header" nginx/conf.d/default.conf; then
            echo -e "${GREEN}✅ $header 已配置${NC}"
        else
            echo -e "${YELLOW}⚠️  $header 未配置${NC}"
        fi
    done
    
    # 检查 SSL 配置
    if grep -q "ssl_protocols" nginx/conf.d/ssl.conf; then
        echo -e "${GREEN}✅ SSL 协议配置正确${NC}"
    else
        echo -e "${YELLOW}⚠️  SSL 协议未配置${NC}"
    fi
    
    if grep -q "Strict-Transport-Security" nginx/conf.d/ssl.conf; then
        echo -e "${GREEN}✅ HSTS 已配置${NC}"
    else
        echo -e "${YELLOW}⚠️  HSTS 未配置${NC}"
    fi
}

# 检查性能优化配置
check_performance_config() {
    echo -e "\n${BLUE}⚡ 检查性能优化配置...${NC}"
    
    if grep -q "gzip on" nginx/nginx.conf; then
        echo -e "${GREEN}✅ Gzip 压缩已启用${NC}"
    else
        echo -e "${YELLOW}⚠️  Gzip 压缩未启用${NC}"
    fi
    
    if grep -q "keepalive" nginx/nginx.conf; then
        echo -e "${GREEN}✅ Keep-alive 已配置${NC}"
    else
        echo -e "${YELLOW}⚠️  Keep-alive 未配置${NC}"
    fi
    
    if grep -q "expires" nginx/conf.d/default.conf; then
        echo -e "${GREEN}✅ 缓存策略已配置${NC}"
    else
        echo -e "${YELLOW}⚠️  缓存策略未配置${NC}"
    fi
}

# 检查负载均衡配置
check_load_balancing() {
    echo -e "\n${BLUE}⚖️  检查负载均衡配置...${NC}"
    
    if grep -q "least_conn" nginx/nginx.conf; then
        echo -e "${GREEN}✅ 负载均衡算法已配置${NC}"
    else
        echo -e "${YELLOW}⚠️  负载均衡算法未配置${NC}"
    fi
    
    if grep -q "max_fails" nginx/nginx.conf; then
        echo -e "${GREEN}✅ 健康检查已配置${NC}"
    else
        echo -e "${YELLOW}⚠️  健康检查未配置${NC}"
    fi
}

# 生成配置报告
generate_report() {
    echo -e "\n${BLUE}📊 生成配置报告...${NC}"
    
    local report_file="nginx-config-report.txt"
    
    {
        echo "OneNews Nginx 配置报告"
        echo "生成时间: $(date)"
        echo "========================"
        echo ""
        echo "配置文件检查:"
        ls -la nginx/
        echo ""
        echo "主配置文件内容:"
        echo "------------------------"
        cat nginx/nginx.conf
        echo ""
        echo "默认站点配置:"
        echo "------------------------"
        cat nginx/conf.d/default.conf
        echo ""
        echo "SSL 配置:"
        echo "------------------------"
        cat nginx/conf.d/ssl.conf
    } > "$report_file"
    
    echo -e "${GREEN}✅ 配置报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始 Nginx 配置测试...${NC}\n"
    
    local exit_code=0
    
    # 运行所有检查
    check_config_files || exit_code=1
    test_config_syntax || exit_code=1
    check_upstream_config || exit_code=1
    check_security_config || exit_code=1
    check_performance_config || exit_code=1
    check_load_balancing || exit_code=1
    
    # 生成报告
    generate_report
    
    # 输出结果
    echo -e "\n${BLUE}========================${NC}"
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}🎉 所有 Nginx 配置测试通过！${NC}"
        echo -e "${GREEN}您的 Nginx 配置已准备好用于生产环境。${NC}"
    else
        echo -e "${RED}⚠️  部分 Nginx 配置测试失败。${NC}"
        echo -e "${YELLOW}请检查上述错误并修复配置。${NC}"
    fi
    
    exit $exit_code
}

# 运行主函数
main "$@"
