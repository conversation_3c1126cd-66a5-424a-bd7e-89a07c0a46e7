<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Bell Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .check {
            color: #10b981;
            margin-right: 8px;
        }
        .cross {
            color: #ef4444;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notification Bell Test</h1>
        <p>Test checklist for the notification bell functionality</p>

        <div class="test-section">
            <h3>📋 Manual Test Checklist</h3>
            <ul class="checklist">
                <li><span class="check">✓</span> Navigate to <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li><span class="check">✓</span> Look for the bell icon in the header (top right area)</li>
                <li><span class="check">✓</span> Click the bell icon to open notification dropdown</li>
                <li><span class="check">✓</span> Verify notifications are displayed in the dropdown</li>
                <li><span class="check">✓</span> Check if unread count badge appears on the bell</li>
                <li><span class="check">✓</span> Click on a notification to mark it as read</li>
                <li><span class="check">✓</span> Use "Mark all read" button</li>
                <li><span class="check">✓</span> Click "View all notifications" to go to notifications page</li>
                <li><span class="check">✓</span> Click outside dropdown to close it</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 API Test</h3>
            <button class="button" onclick="testNotificationAPI()">Test Notification API</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Quick Links</h3>
            <button class="button" onclick="openHomePage()">Open Homepage</button>
            <button class="button" onclick="openNotificationsPage()">Open Notifications Page</button>
            <div id="link-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🐛 Troubleshooting</h3>
            <p><strong>If the bell icon is not clickable:</strong></p>
            <ul>
                <li>Check browser console for JavaScript errors</li>
                <li>Verify NotificationProvider is properly configured</li>
                <li>Ensure the API server is running on port 5000</li>
                <li>Check if user is authenticated (bell only shows for logged-in users)</li>
            </ul>
            
            <p><strong>If avatars are not loading:</strong></p>
            <ul>
                <li>Check network tab for failed image requests</li>
                <li>Verify fallback avatar system is working</li>
                <li>Test with different notification types</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        async function testNotificationAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/notifications');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', 
                        `✅ API Working! Found ${data.notifications.length} notifications\n` +
                        `First notification: ${data.notifications[0]?.title}`,
                        'success'
                    );
                } else {
                    showResult('api-result', `❌ API Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ Network Error: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            showResult('link-result', '🌐 Opened homepage - check for bell icon in header', 'info');
        }

        function openNotificationsPage() {
            window.open('http://localhost:3000/notifications', '_blank');
            showResult('link-result', '🌐 Opened notifications page', 'info');
        }

        // Auto-test API on page load
        window.addEventListener('load', () => {
            console.log('🔔 Notification Bell Test Page Loaded');
            testNotificationAPI();
        });
    </script>
</body>
</html>
