<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newzora Notifications Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #374151;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.danger {
            background: #ef4444;
        }
        .button.danger:hover {
            background: #dc2626;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Newzora Notifications API Test</h1>
            <p>Test the notification system functionality</p>
        </div>

        <div class="test-section">
            <h3>📥 Fetch Notifications</h3>
            <button class="button" onclick="fetchNotifications()">Get All Notifications</button>
            <button class="button" onclick="fetchUnreadCount()">Get Unread Count</button>
            <div id="fetch-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>✅ Mark as Read</h3>
            <button class="button" onclick="markAsRead(1)">Mark Notification #1 as Read</button>
            <button class="button" onclick="markAllAsRead()">Mark All as Read</button>
            <div id="read-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🗑️ Delete Notifications</h3>
            <button class="button danger" onclick="deleteNotification(1)">Delete Notification #1</button>
            <button class="button danger" onclick="deleteNotification(2)">Delete Notification #2</button>
            <div id="delete-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Frontend Links</h3>
            <button class="button" onclick="openNotificationsPage()">Open Notifications Page</button>
            <button class="button" onclick="openHomePage()">Open Homepage</button>
            <div id="link-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        async function fetchNotifications() {
            try {
                const response = await fetch(`${API_BASE}/notifications`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('fetch-result', 
                        `✅ Found ${data.notifications.length} notifications:\n` +
                        data.notifications.map(n => `- ${n.title}: ${n.content}`).join('\n'),
                        'success'
                    );
                } else {
                    showResult('fetch-result', `❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('fetch-result', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function fetchUnreadCount() {
            try {
                const response = await fetch(`${API_BASE}/notifications/unread-count`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('fetch-result', `✅ Unread count: ${data.count}`, 'success');
                } else {
                    showResult('fetch-result', `❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('fetch-result', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function markAsRead(id) {
            try {
                const response = await fetch(`${API_BASE}/notifications/${id}/read`, {
                    method: 'PUT'
                });
                const data = await response.json();
                
                if (response.ok) {
                    showResult('read-result', `✅ Notification ${id} marked as read`, 'success');
                } else {
                    showResult('read-result', `❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('read-result', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function markAllAsRead() {
            try {
                const response = await fetch(`${API_BASE}/notifications/mark-all-read`, {
                    method: 'PUT'
                });
                const data = await response.json();
                
                if (response.ok) {
                    showResult('read-result', `✅ All notifications marked as read (${data.updatedCount} updated)`, 'success');
                } else {
                    showResult('read-result', `❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('read-result', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function deleteNotification(id) {
            try {
                const response = await fetch(`${API_BASE}/notifications/${id}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (response.ok) {
                    showResult('delete-result', `✅ Notification ${id} deleted successfully`, 'success');
                } else {
                    showResult('delete-result', `❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('delete-result', `❌ Network error: ${error.message}`, 'error');
            }
        }

        function openNotificationsPage() {
            window.open('http://localhost:3000/notifications', '_blank');
            showResult('link-result', '🌐 Opened notifications page in new tab', 'info');
        }

        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
            showResult('link-result', '🌐 Opened homepage in new tab', 'info');
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🔔 Newzora Notifications Test Page Loaded');
            console.log('📱 Frontend: http://localhost:3000/notifications');
            console.log('🔧 Backend API: http://localhost:5000/api/notifications');
        });
    </script>
</body>
</html>
