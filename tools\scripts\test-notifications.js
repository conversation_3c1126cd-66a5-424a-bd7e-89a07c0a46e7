// Test script to create sample notifications
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const API_BASE = 'http://localhost:5000/api';

async function createTestNotifications() {
  console.log('🔔 Creating test notifications...\n');

  // Sample notification data matching the design
  const notifications = [
    {
      userId: 1, // Assuming user ID 1 exists
      type: 'comment',
      title: 'New Comment',
      content: '<PERSON> replied to your comment: "Great insights!"',
      priority: 'normal',
      data: {
        fromUser: 'Sophia',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
        commentId: 1,
        articleId: 1
      }
    },
    {
      userId: 1,
      type: 'like',
      title: 'Post Liked',
      content: '<PERSON> liked your post about travel tips.',
      priority: 'normal',
      data: {
        fromUser: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
        articleId: 2
      }
    },
    {
      userId: 1,
      type: 'follow',
      title: 'New Follower',
      content: 'You have a new follower, Olivia!',
      priority: 'normal',
      data: {
        fromUser: 'Olivia',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
        userId: 3
      }
    },
    {
      userId: 1,
      type: 'system',
      title: 'System Announcement',
      content: 'New feature update!',
      priority: 'normal',
      data: {
        updateType: 'feature',
        version: '2.1.0'
      }
    },
    {
      userId: 1,
      type: 'comment',
      title: 'Mentioned in Comment',
      content: 'Ethan mentioned you in a comment.',
      priority: 'normal',
      data: {
        fromUser: 'Ethan',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face',
        commentId: 2,
        articleId: 3
      }
    },
    {
      userId: 1,
      type: 'article_published',
      title: 'Collection Update',
      content: 'Your post about photography was added to a collection.',
      priority: 'normal',
      data: {
        collectionName: 'Photography Inspiration',
        articleId: 4
      }
    }
  ];

  try {
    console.log('Creating notifications...');
    
    for (const notification of notifications) {
      try {
        const response = await fetch(`${API_BASE}/notifications`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Note: In a real app, you'd need proper authentication
            'Authorization': 'Bearer test-token'
          },
          body: JSON.stringify(notification)
        });

        if (response.ok) {
          const created = await response.json();
          console.log(`✅ Created notification: ${notification.title}`);
        } else {
          console.log(`❌ Failed to create notification: ${notification.title} (${response.status})`);
        }
      } catch (error) {
        console.log(`❌ Error creating notification: ${notification.title} - ${error.message}`);
      }
    }

    console.log('\n🎉 Test notifications creation completed!');
    console.log('\n📱 You can now visit: http://localhost:3000/notifications');

  } catch (error) {
    console.error('❌ Error in test script:', error.message);
  }
}

// Test fetching notifications
async function testFetchNotifications() {
  try {
    console.log('\n📥 Testing notification fetch...');
    
    const response = await fetch(`${API_BASE}/notifications`, {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Found ${data.notifications?.length || 0} notifications`);
      
      if (data.notifications?.length > 0) {
        console.log('📋 Sample notifications:');
        data.notifications.slice(0, 3).forEach((notif, index) => {
          console.log(`   ${index + 1}. ${notif.title}: ${notif.content}`);
        });
      }
    } else {
      console.log(`❌ Failed to fetch notifications: ${response.status}`);
    }
  } catch (error) {
    console.error('❌ Error fetching notifications:', error.message);
  }
}

// Test marking notification as read
async function testMarkAsRead() {
  try {
    console.log('\n✅ Testing mark as read...');
    
    // First get notifications to find one to mark as read
    const fetchResponse = await fetch(`${API_BASE}/notifications`, {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json',
      },
    });

    if (fetchResponse.ok) {
      const data = await fetchResponse.json();
      const unreadNotification = data.notifications?.find(n => !n.isRead);
      
      if (unreadNotification) {
        const markResponse = await fetch(`${API_BASE}/notifications/${unreadNotification.id}/read`, {
          method: 'PUT',
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          },
        });

        if (markResponse.ok) {
          console.log(`✅ Marked notification ${unreadNotification.id} as read`);
        } else {
          console.log(`❌ Failed to mark notification as read: ${markResponse.status}`);
        }
      } else {
        console.log('ℹ️ No unread notifications found');
      }
    }
  } catch (error) {
    console.error('❌ Error testing mark as read:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await createTestNotifications();
  await testFetchNotifications();
  await testMarkAsRead();
  
  console.log('\n🏁 All tests completed!');
  console.log('\n🌐 Frontend URLs to test:');
  console.log('   📱 Notifications Page: http://localhost:3000/notifications');
  console.log('   🏠 Homepage: http://localhost:3000');
}

runAllTests();
