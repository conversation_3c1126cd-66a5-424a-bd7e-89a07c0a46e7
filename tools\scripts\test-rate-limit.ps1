# 测试速率限制是否已禁用

Write-Host "🔍 测试Newzora速率限制配置" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$testData = @{
    identifier = "testuser"
    password = "Test123456!"
} | ConvertTo-Json

Write-Host "`n📊 进行3次快速登录测试..." -ForegroundColor Yellow

for ($i = 1; $i -le 3; $i++) {
    Write-Host "`n🔄 测试 $i/3:" -ForegroundColor White
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/api/users/login" -Method POST -Body $testData -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 登录成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 登录响应异常 - 状态码: $($response.StatusCode)" -ForegroundColor Yellow
        }
    }
    catch {
        $errorMessage = $_.Exception.Message
        if ($errorMessage -like "*rate*limit*" -or $errorMessage -like "*429*") {
            Write-Host "❌ 速率限制仍然生效: $errorMessage" -ForegroundColor Red
        } else {
            Write-Host "❌ 其他错误: $errorMessage" -ForegroundColor Red
        }
    }
    
    if ($i -lt 3) {
        Write-Host "⏳ 等待1秒..." -ForegroundColor Gray
        Start-Sleep -Seconds 1
    }
}

Write-Host "`n🎯 测试完成!" -ForegroundColor Cyan

# 测试健康检查
Write-Host "`n🔍 测试API健康状态..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/health" -Method GET -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ API健康检查成功 - 状态码: $($healthResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ API健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Summary:" -ForegroundColor Magenta
Write-Host "- 3 successful logins = rate limit disabled" -ForegroundColor White
Write-Host "- Rate limit errors = config not applied" -ForegroundColor White
Write-Host "- Dev environment should allow fast testing" -ForegroundColor White
