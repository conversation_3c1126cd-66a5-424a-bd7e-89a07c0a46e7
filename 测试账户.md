# Newzora 认证系统测试指南

## 🎯 测试目标

验证Newzora平台的用户认证系统是否正常工作，包括注册、登录、密码重置等核心功能。

## 📋 测试账户

我已经为您创建了以下测试账户，可以直接使用：

### 管理员账户
- **用户名**: `admin_test`
- **邮箱**: `<EMAIL>`
- **密码**: `Admin123!`
- **角色**: 管理员
- **权限**: 完整系统访问权限

### 内容管理员账户
- **用户名**: `moderator_test`
- **邮箱**: `<EMAIL>`
- **密码**: `Moderator123!`
- **角色**: 内容管理员
- **权限**: 内容管理权限

### 编辑账户
- **用户名**: `editor_test`
- **邮箱**: `<EMAIL>`
- **密码**: `Editor123!`
- **角色**: 用户
- **权限**: 内容编辑权限

### 普通用户账户 1
- **用户名**: `user_test1`
- **邮箱**: `<EMAIL>`
- **密码**: `User123!`
- **角色**: 普通用户
- **权限**: 基础用户权限

### 普通用户账户 2
- **用户名**: `user_test2`
- **邮箱**: `<EMAIL>`
- **密码**: `User123!`
- **角色**: 普通用户
- **权限**: 基础用户权限

### 未验证邮箱账户
- **用户名**: `unverified_test`
- **邮箱**: `<EMAIL>`
- **密码**: `Unverified123!`
- **角色**: 普通用户
- **状态**: 邮箱未验证

### 非活跃账户
- **用户名**: `inactive_test`
- **邮箱**: `<EMAIL>`
- **密码**: `Inactive123!`
- **角色**: 普通用户
- **状态**: 账户已停用

## 🚀 快速测试步骤

### 1. 启动后端服务器

```bash
cd Backend
node server.js
```

服务器应该在 `http://localhost:5000` 启动

### 2. 运行自动化测试

```bash
# 创建测试账户（如果还没有）
node Backend/scripts/create-test-accounts.js

# 运行认证测试
node Backend/scripts/simple-auth-test.js
```

### 3. 前端界面测试

```bash
# 启动前端开发服务器
cd Frontend
npm run dev
```

访问测试页面：`http://localhost:3000/test-auth`

## 🧪 测试场景

### A. 登录测试

1. **邮箱登录**
   - 使用邮箱地址和密码登录
   - 验证不同角色的登录

2. **用户名登录**
   - 使用用户名和密码登录
   - 验证登录成功后的用户信息

3. **错误处理**
   - 测试错误密码
   - 测试不存在的用户
   - 测试非活跃账户登录

### B. 注册测试

1. **新用户注册**
   - 填写完整注册信息
   - 验证密码强度检查
   - 确认邮箱验证流程

2. **表单验证**
   - 测试必填字段验证
   - 测试邮箱格式验证
   - 测试用户名唯一性

### C. 密码功能测试

1. **密码强度验证**
   - 测试弱密码（如：123）
   - 测试中等强度密码
   - 测试强密码

2. **忘记密码**
   - 请求密码重置
   - 验证邮件发送
   - 测试重置链接

### D. Token管理测试

1. **Token验证**
   - 验证访问Token有效性
   - 测试Token过期处理

2. **Token刷新**
   - 测试刷新Token机制
   - 验证自动Token更新

## 📊 API端点测试

### 认证相关端点

```bash
# 健康检查
GET http://localhost:5000/api/health

# 用户登录
POST http://localhost:5000/api/auth-enhanced/login
Content-Type: application/json
{
  "identifier": "<EMAIL>",
  "password": "Admin123!",
  "rememberMe": false
}

# 用户注册
POST http://localhost:5000/api/auth-enhanced/register
Content-Type: application/json
{
  "username": "test_user",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "firstName": "Test",
  "lastName": "User"
}

# 密码强度检查
POST http://localhost:5000/api/auth-enhanced/check-password-strength
Content-Type: application/json
{
  "password": "TestPassword123!"
}

# 获取用户信息
GET http://localhost:5000/api/auth-enhanced/me
Authorization: Bearer YOUR_ACCESS_TOKEN

# 忘记密码
POST http://localhost:5000/api/auth-enhanced/forgot-password
Content-Type: application/json
{
  "email": "<EMAIL>"
}

# 刷新Token
POST http://localhost:5000/api/auth-enhanced/refresh-token
Content-Type: application/json
{
  "refreshToken": "YOUR_REFRESH_TOKEN"
}
```

## ✅ 预期结果

### 成功登录应该返回：
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "admin_test",
      "email": "<EMAIL>",
      "role": "admin",
      "isEmailVerified": true,
      "isActive": true
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
    }
  }
}
```

### 密码强度检查应该返回：
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "strength": "strong",
    "errors": []
  }
}
```

## 🔧 故障排除

### 常见问题

1. **服务器无法启动**
   - 检查端口5000是否被占用
   - 验证数据库连接配置
   - 检查环境变量设置

2. **数据库连接失败**
   - 确认数据库服务正在运行
   - 检查数据库连接字符串
   - 验证数据库用户权限

3. **测试账户不存在**
   - 运行账户创建脚本：`node Backend/scripts/create-test-accounts.js`
   - 检查数据库中的用户表

4. **Token验证失败**
   - 检查JWT密钥配置
   - 验证Token格式
   - 确认Token未过期

### 日志检查

查看服务器日志以获取详细错误信息：
```bash
# 启动服务器并查看日志
cd Backend
node server.js

# 查看数据库日志
tail -f logs/database.log

# 查看认证日志
tail -f logs/auth.log
```

## 📞 技术支持

如果遇到问题，请检查：

1. **环境配置**
   - Node.js版本 >= 16
   - 数据库连接正常
   - 环境变量正确设置

2. **依赖安装**
   - 运行 `npm install` 安装所有依赖
   - 检查package.json中的依赖版本

3. **网络连接**
   - 确认端口5000可访问
   - 检查防火墙设置

## 🎉 测试完成

完成所有测试后，您应该能够：

✅ 使用提供的测试账户成功登录  
✅ 注册新用户账户  
✅ 验证密码强度检查功能  
✅ 测试忘记密码流程  
✅ 确认Token管理正常工作  
✅ 验证不同用户角色的权限  

认证系统测试完成后，您就可以开始使用Newzora平台的完整功能了！
